# Configuration Django
DEBUG=True
SECRET_KEY=your-secret-key-here

# Base de données (SQLite par défaut)
DATABASE_URL=sqlite:///db.sqlite3

# Stripe (Paiements)
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Facebook API
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_ACCESS_TOKEN=your_facebook_access_token

# Google Analytics
GOOGLE_ANALYTICS_PROPERTY_ID=your_ga_property_id
GOOGLE_ANALYTICS_CREDENTIALS_PATH=path/to/credentials.json

# Email (optionnel)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password

# Redis (pour les WebSockets)
REDIS_URL=redis://localhost:6379/0

# Sécurité
ALLOWED_HOSTS=localhost,127.0.0.1

# Médias et statiques
MEDIA_ROOT=media/
STATIC_ROOT=staticfiles/
