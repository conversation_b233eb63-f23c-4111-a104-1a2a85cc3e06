"""
Formulaires pour la gestion des abonnements
"""
from django import forms
from django.core.validators import MinValueValidator
from decimal import Decimal
from .models import Abonnement, Description


class AbonnementForm(forms.ModelForm):
    """Formulaire pour créer/modifier un abonnement"""
    
    # Champs personnalisés
    features_text = forms.CharField(
        widget=forms.Textarea(attrs={
            'rows': 5,
            'placeholder': 'Une fonctionnalité par ligne\nExemple:\nAccès aux profils premium\nMessages illimités\nSupport prioritaire'
        }),
        required=False,
        help_text="Entrez une fonctionnalité par ligne",
        label="Fonctionnalités"
    )
    
    create_stripe = forms.BooleanField(
        required=False,
        initial=True,
        label="Créer automatiquement sur Stripe",
        help_text="Crée automatiquement le produit et le prix sur Stripe"
    )
    
    class Meta:
        model = Abonnement
        fields = [
            'nom', 'description_courte', 'prix_ttc', 'prix_ht', 'credits',
            'interval', 'interval_count', 'is_popular', 'is_active',
            'ordre_affichage', 'entreprise'
        ]
        
        widgets = {
            'nom': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Ex: Premium, Basic, VIP...'
            }),
            'description_courte': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Description courte du plan d\'abonnement'
            }),
            'prix_ttc': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'prix_ht': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'credits': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0'
            }),
            'interval': forms.Select(attrs={
                'class': 'form-select'
            }),
            'interval_count': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1'
            }),
            'ordre_affichage': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0'
            }),
            'is_popular': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'entreprise': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        
        labels = {
            'nom': 'Nom du plan',
            'description_courte': 'Description courte',
            'prix_ttc': 'Prix TTC (€)',
            'prix_ht': 'Prix HT (€)',
            'credits': 'Crédits inclus',
            'interval': 'Intervalle de facturation',
            'interval_count': 'Nombre d\'intervalles',
            'is_popular': 'Plan populaire',
            'is_active': 'Plan actif',
            'ordre_affichage': 'Ordre d\'affichage',
            'entreprise': 'Plan entreprise',
        }
        
        help_texts = {
            'prix_ht': 'Laissez vide pour calcul automatique depuis le TTC',
            'prix_ttc': 'Laissez vide pour calcul automatique depuis le HT',
            'credits': 'Nombre de crédits inclus dans ce plan',
            'interval_count': 'Ex: 3 pour "tous les 3 mois"',
            'is_popular': 'Affiche un badge "Populaire" sur ce plan',
            'ordre_affichage': 'Ordre d\'affichage sur la page de tarification (0 = premier)',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Si on modifie un abonnement existant, pré-remplir les fonctionnalités
        if self.instance and self.instance.pk:
            features = self.instance.get_features_list()
            self.fields['features_text'].initial = '\n'.join(features)
            
            # Masquer l'option de création Stripe si déjà synchronisé
            if self.instance.stripe_product_id:
                self.fields['create_stripe'].label = "Mettre à jour sur Stripe"
                self.fields['create_stripe'].help_text = "Met à jour le produit existant sur Stripe"
        
        # Validation conditionnelle des prix
        self.fields['prix_ttc'].required = False
        self.fields['prix_ht'].required = False
    
    def clean(self):
        cleaned_data = super().clean()
        prix_ttc = cleaned_data.get('prix_ttc')
        prix_ht = cleaned_data.get('prix_ht')
        
        # Au moins un prix doit être fourni
        if not prix_ttc and not prix_ht:
            raise forms.ValidationError("Vous devez fournir au moins le prix TTC ou le prix HT.")
        
        # Validation des prix positifs
        if prix_ttc is not None and prix_ttc <= 0:
            raise forms.ValidationError("Le prix TTC doit être positif.")
        
        if prix_ht is not None and prix_ht <= 0:
            raise forms.ValidationError("Le prix HT doit être positif.")
        
        return cleaned_data
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # Traiter les fonctionnalités
        features_text = self.cleaned_data.get('features_text', '')
        if features_text:
            features = [f.strip() for f in features_text.split('\n') if f.strip()]
            instance.features = features
        else:
            instance.features = []
        
        if commit:
            instance.save()
        
        return instance


class AbonnementFilterForm(forms.Form):
    """Formulaire de filtrage pour la liste des abonnements"""
    
    STATUT_CHOICES = [
        ('', 'Tous les statuts'),
        ('active', 'Actifs seulement'),
        ('inactive', 'Inactifs seulement'),
    ]
    
    STRIPE_CHOICES = [
        ('', 'Tous'),
        ('synced', 'Synchronisés avec Stripe'),
        ('not_synced', 'Non synchronisés'),
    ]
    
    TYPE_CHOICES = [
        ('', 'Tous les types'),
        ('standard', 'Standard'),
        ('entreprise', 'Entreprise'),
    ]
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Rechercher par nom...'
        }),
        label="Recherche"
    )
    
    statut = forms.ChoiceField(
        choices=STATUT_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="Statut"
    )
    
    stripe_sync = forms.ChoiceField(
        choices=STRIPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="Synchronisation Stripe"
    )
    
    type_plan = forms.ChoiceField(
        choices=TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="Type de plan"
    )
    
    interval = forms.ChoiceField(
        choices=[('', 'Tous les intervalles')] + Abonnement._meta.get_field('interval').choices,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="Intervalle"
    )


class BulkActionForm(forms.Form):
    """Formulaire pour les actions en lot"""
    
    ACTION_CHOICES = [
        ('', 'Choisir une action...'),
        ('activate', 'Activer'),
        ('deactivate', 'Désactiver'),
        ('sync_stripe', 'Synchroniser avec Stripe'),
        ('export', 'Exporter en CSV'),
    ]
    
    action = forms.ChoiceField(
        choices=ACTION_CHOICES,
        required=True,
        widget=forms.Select(attrs={
            'class': 'form-select',
            'id': 'bulk-action-select'
        }),
        label="Action"
    )
    
    selected_items = forms.CharField(
        widget=forms.HiddenInput(),
        required=True
    )


class AbonnementQuickEditForm(forms.ModelForm):
    """Formulaire d'édition rapide pour les propriétés de base"""
    
    class Meta:
        model = Abonnement
        fields = ['nom', 'prix_ttc', 'is_active', 'is_popular', 'ordre_affichage']
        
        widgets = {
            'nom': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'prix_ttc': forms.NumberInput(attrs={
                'class': 'form-control form-control-sm',
                'step': '0.01'
            }),
            'ordre_affichage': forms.NumberInput(attrs={
                'class': 'form-control form-control-sm'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_popular': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


class DescriptionForm(forms.ModelForm):
    """Formulaire pour gérer les descriptions/fonctionnalités"""
    
    class Meta:
        model = Description
        fields = ['description']
        
        widgets = {
            'description': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Ex: Accès aux profils premium'
            })
        }
        
        labels = {
            'description': 'Fonctionnalité'
        }


class AbonnementImportForm(forms.Form):
    """Formulaire pour importer des abonnements depuis un fichier CSV"""
    
    csv_file = forms.FileField(
        label="Fichier CSV",
        help_text="Format: nom,description_courte,prix_ttc,credits,interval",
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.csv'
        })
    )
    
    update_existing = forms.BooleanField(
        required=False,
        initial=False,
        label="Mettre à jour les abonnements existants",
        help_text="Si coché, met à jour les abonnements avec le même nom"
    )
    
    create_stripe = forms.BooleanField(
        required=False,
        initial=True,
        label="Créer automatiquement sur Stripe",
        help_text="Crée automatiquement les produits et prix sur Stripe"
    )
    
    def clean_csv_file(self):
        csv_file = self.cleaned_data['csv_file']
        
        if not csv_file.name.endswith('.csv'):
            raise forms.ValidationError("Le fichier doit être au format CSV.")
        
        if csv_file.size > 5 * 1024 * 1024:  # 5MB
            raise forms.ValidationError("Le fichier ne doit pas dépasser 5MB.")
        
        return csv_file
