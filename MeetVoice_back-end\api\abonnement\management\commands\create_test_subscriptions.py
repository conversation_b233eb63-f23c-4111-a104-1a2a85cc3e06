"""
Commande pour créer des abonnements de test
"""
from django.core.management.base import BaseCommand
from decimal import Decimal

from abonnement.models import Abonnement, Description


class Command(BaseCommand):
    help = 'Crée des abonnements de test pour le développement'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Supprime tous les abonnements existants avant de créer les nouveaux',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Suppression des abonnements existants...')
            Abonnement.objects.all().delete()
            Description.objects.all().delete()

        self.stdout.write('Création des abonnements de test...')

        # Créer les descriptions/fonctionnalités
        features_basic = [
            Description.objects.create(description="Accès aux profils de base"),
            Description.objects.create(description="Messages illimités"),
            Description.objects.create(description="Support par email"),
        ]

        features_premium = [
            Description.objects.create(description="Accès aux profils premium"),
            Description.objects.create(description="Messages illimités"),
            Description.objects.create(description="Appels vocaux"),
            Description.objects.create(description="Filtres avancés"),
            Description.objects.create(description="Support prioritaire"),
        ]

        features_vip = [
            Description.objects.create(description="Accès VIP complet"),
            Description.objects.create(description="Messages et appels illimités"),
            Description.objects.create(description="Coaching vocal personnalisé"),
            Description.objects.create(description="Profil mis en avant"),
            Description.objects.create(description="Support 24/7"),
            Description.objects.create(description="Événements exclusifs"),
        ]

        # Plan Basic
        basic = Abonnement.objects.create(
            nom="Basic",
            description_courte="Plan d'entrée pour découvrir Meet Voice",
            prix_ttc=Decimal('9.99'),
            credits=50,
            interval='month',
            interval_count=1,
            is_popular=False,
            is_active=True,
            ordre_affichage=1,
            features=[
                "Accès aux profils de base",
                "Messages illimités",
                "Support par email"
            ]
        )
        basic.description.set(features_basic)

        # Plan Premium (populaire)
        premium = Abonnement.objects.create(
            nom="Premium",
            description_courte="Le plan le plus populaire avec toutes les fonctionnalités essentielles",
            prix_ttc=Decimal('19.99'),
            credits=150,
            interval='month',
            interval_count=1,
            is_popular=True,
            is_active=True,
            ordre_affichage=2,
            features=[
                "Accès aux profils premium",
                "Messages illimités",
                "Appels vocaux",
                "Filtres avancés",
                "Support prioritaire"
            ]
        )
        premium.description.set(features_premium)

        # Plan VIP
        vip = Abonnement.objects.create(
            nom="VIP",
            description_courte="L'expérience ultime Meet Voice avec coaching personnalisé",
            prix_ttc=Decimal('39.99'),
            credits=500,
            interval='month',
            interval_count=1,
            is_popular=False,
            is_active=True,
            ordre_affichage=3,
            features=[
                "Accès VIP complet",
                "Messages et appels illimités",
                "Coaching vocal personnalisé",
                "Profil mis en avant",
                "Support 24/7",
                "Événements exclusifs"
            ]
        )
        vip.description.set(features_vip)

        # Plan Annuel Premium (avec réduction)
        premium_annual = Abonnement.objects.create(
            nom="Premium Annuel",
            description_courte="Plan Premium avec 2 mois gratuits",
            prix_ttc=Decimal('199.99'),  # Équivaut à 10 mois au lieu de 12
            credits=150,
            interval='year',
            interval_count=1,
            is_popular=False,
            is_active=True,
            ordre_affichage=4,
            features=[
                "Accès aux profils premium",
                "Messages illimités",
                "Appels vocaux",
                "Filtres avancés",
                "Support prioritaire",
                "2 mois gratuits"
            ]
        )
        premium_annual.description.set(features_premium)

        # Plan Entreprise
        entreprise = Abonnement.objects.create(
            nom="Entreprise",
            description_courte="Solution sur mesure pour les entreprises",
            prix_ttc=Decimal('99.99'),
            credits=1000,
            interval='month',
            interval_count=1,
            is_popular=False,
            is_active=True,
            ordre_affichage=5,
            entreprise=True,
            features=[
                "Accès illimité",
                "API dédiée",
                "Support dédié",
                "Intégration personnalisée",
                "Tableau de bord entreprise"
            ]
        )

        self.stdout.write(
            self.style.SUCCESS(
                f'Abonnements de test créés avec succès:\n'
                f'- {basic.nom}: {basic.prix_ttc}€/mois\n'
                f'- {premium.nom}: {premium.prix_ttc}€/mois (populaire)\n'
                f'- {vip.nom}: {vip.prix_ttc}€/mois\n'
                f'- {premium_annual.nom}: {premium_annual.prix_ttc}€/an\n'
                f'- {entreprise.nom}: {entreprise.prix_ttc}€/mois (entreprise)'
            )
        )

        self.stdout.write(
            self.style.WARNING(
                '\nPour synchroniser avec Stripe, exécutez:\n'
                'python manage.py sync_stripe --create-missing'
            )
        )
