"""
Commande de gestion Django pour synchroniser les données avec Stripe
"""
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
import stripe

from abonnement.models import Abonnement
from abonnement.services import StripeService


class Command(BaseCommand):
    help = 'Synchronise les abonnements Django avec Stripe'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-missing',
            action='store_true',
            help='Crée les produits et prix Stripe manquants pour les abonnements Django',
        )
        parser.add_argument(
            '--sync-from-stripe',
            action='store_true',
            help='Synchronise les données depuis Stripe vers Django',
        )
        parser.add_argument(
            '--abonnement-id',
            type=int,
            help='ID spécifique d\'un abonnement à synchroniser',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Affiche ce qui serait fait sans effectuer les modifications',
        )

    def handle(self, *args, **options):
        if not settings.STRIPE_SECRET_KEY:
            raise CommandError('STRIPE_SECRET_KEY n\'est pas configuré dans les settings')

        stripe.api_key = settings.STRIPE_SECRET_KEY

        if options['sync_from_stripe']:
            self.sync_from_stripe(options['dry_run'])
        elif options['create_missing']:
            self.create_missing_stripe_products(options['abonnement_id'], options['dry_run'])
        else:
            self.stdout.write(
                self.style.WARNING('Aucune action spécifiée. Utilisez --create-missing ou --sync-from-stripe')
            )

    def sync_from_stripe(self, dry_run=False):
        """Synchronise les données depuis Stripe vers Django"""
        self.stdout.write('Synchronisation depuis Stripe...')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('Mode dry-run activé - aucune modification ne sera effectuée'))
            return

        try:
            result = StripeService.sync_from_stripe()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Synchronisation terminée:\n'
                    f'- {result["products"]} produits synchronisés\n'
                    f'- {result["prices"]} prix synchronisés\n'
                    f'- {result["subscriptions"]} abonnements synchronisés'
                )
            )
        except Exception as e:
            raise CommandError(f'Erreur lors de la synchronisation: {e}')

    def create_missing_stripe_products(self, abonnement_id=None, dry_run=False):
        """Crée les produits et prix Stripe manquants"""
        if abonnement_id:
            abonnements = Abonnement.objects.filter(id=abonnement_id)
            if not abonnements.exists():
                raise CommandError(f'Abonnement avec l\'ID {abonnement_id} non trouvé')
        else:
            abonnements = Abonnement.objects.filter(is_active=True)

        self.stdout.write(f'Vérification de {abonnements.count()} abonnement(s)...')

        created_products = 0
        created_prices = 0
        errors = 0

        for abonnement in abonnements:
            self.stdout.write(f'Traitement de l\'abonnement: {abonnement.nom}')

            # Vérifier si le produit Stripe existe
            if not abonnement.stripe_product_id:
                if dry_run:
                    self.stdout.write(f'  [DRY-RUN] Créerait un produit Stripe pour: {abonnement.nom}')
                else:
                    self.stdout.write(f'  Création du produit Stripe...')
                    product_id = StripeService.create_product(abonnement)
                    if product_id:
                        created_products += 1
                        self.stdout.write(
                            self.style.SUCCESS(f'  ✓ Produit créé: {product_id}')
                        )
                    else:
                        errors += 1
                        self.stdout.write(
                            self.style.ERROR(f'  ✗ Erreur lors de la création du produit')
                        )
                        continue
            else:
                self.stdout.write(f'  ✓ Produit Stripe existe déjà: {abonnement.stripe_product_id}')

            # Vérifier si le prix Stripe existe
            if not abonnement.stripe_price_id:
                if dry_run:
                    self.stdout.write(f'  [DRY-RUN] Créerait un prix Stripe pour: {abonnement.nom}')
                else:
                    self.stdout.write(f'  Création du prix Stripe...')
                    price_id = StripeService.create_price(abonnement)
                    if price_id:
                        created_prices += 1
                        self.stdout.write(
                            self.style.SUCCESS(f'  ✓ Prix créé: {price_id}')
                        )
                    else:
                        errors += 1
                        self.stdout.write(
                            self.style.ERROR(f'  ✗ Erreur lors de la création du prix')
                        )
            else:
                self.stdout.write(f'  ✓ Prix Stripe existe déjà: {abonnement.stripe_price_id}')

        # Résumé
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'Mode dry-run - Aucune modification effectuée\n'
                    f'Produits qui seraient créés: {created_products}\n'
                    f'Prix qui seraient créés: {created_prices}'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Synchronisation terminée:\n'
                    f'- {created_products} produits créés\n'
                    f'- {created_prices} prix créés\n'
                    f'- {errors} erreurs'
                )
            )

    def list_stripe_products(self):
        """Liste les produits Stripe existants"""
        try:
            products = stripe.Product.list(limit=100)
            
            self.stdout.write('Produits Stripe existants:')
            for product in products.data:
                self.stdout.write(f'- {product.id}: {product.name} (actif: {product.active})')
                
                # Lister les prix associés
                prices = stripe.Price.list(product=product.id, limit=10)
                for price in prices.data:
                    amount = price.unit_amount / 100 if price.unit_amount else 0
                    self.stdout.write(
                        f'  Prix: {price.id} - {amount}€/{price.recurring.interval if price.recurring else "unique"}'
                    )
                    
        except Exception as e:
            raise CommandError(f'Erreur lors de la récupération des produits Stripe: {e}')

    def validate_stripe_integration(self):
        """Valide l'intégration Stripe"""
        self.stdout.write('Validation de l\'intégration Stripe...')
        
        # Vérifier la configuration
        if not settings.STRIPE_SECRET_KEY:
            self.stdout.write(self.style.ERROR('✗ STRIPE_SECRET_KEY non configuré'))
            return False
            
        if not settings.STRIPE_PUBLISHABLE_KEY:
            self.stdout.write(self.style.ERROR('✗ STRIPE_PUBLISHABLE_KEY non configuré'))
            return False
            
        self.stdout.write(self.style.SUCCESS('✓ Clés Stripe configurées'))
        
        # Tester la connexion à l'API Stripe
        try:
            stripe.api_key = settings.STRIPE_SECRET_KEY
            account = stripe.Account.retrieve()
            self.stdout.write(self.style.SUCCESS(f'✓ Connexion Stripe réussie (compte: {account.id})'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Erreur de connexion Stripe: {e}'))
            return False
            
        # Vérifier les abonnements Django
        abonnements_count = Abonnement.objects.count()
        abonnements_with_stripe = Abonnement.objects.exclude(stripe_product_id__isnull=True).count()
        
        self.stdout.write(f'Abonnements Django: {abonnements_count}')
        self.stdout.write(f'Abonnements avec produit Stripe: {abonnements_with_stripe}')
        
        if abonnements_count > 0 and abonnements_with_stripe == 0:
            self.stdout.write(
                self.style.WARNING('⚠ Aucun abonnement n\'a de produit Stripe associé')
            )
            
        return True
