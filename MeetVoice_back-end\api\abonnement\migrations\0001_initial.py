# Generated by Django 5.2.3 on 2025-06-17 02:26

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('compte', '0002_compte_audio_alter_compte_credit_alter_compte_sexe'),
    ]

    operations = [
        migrations.CreateModel(
            name='Description',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(default=None)),
            ],
        ),
        migrations.CreateModel(
            name='Abonnement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(default=None, max_length=100, null=True)),
                ('stripe_id', models.CharField(default=None, max_length=100, null=True)),
                ('prix_ht', models.FloatField(default=None)),
                ('prix_ttc', models.FloatField(default=None)),
                ('credits', models.FloatField(default=0)),
                ('interval', models.CharField(choices=[('day', 'Jour'), ('week', 'Semaine'), ('month', 'Mois'), ('year', 'Année')], default='month', max_length=20)),
                ('interval_count', models.PositiveIntegerField(default=1)),
                ('entreprise', models.BooleanField(default=False)),
                ('description', models.ManyToManyField(to='abonnement.description')),
            ],
        ),
        migrations.CreateModel(
            name='Facture',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stripe_id', models.CharField(blank=True, default=None, max_length=100, null=True)),
                ('paypal_id', models.CharField(blank=True, default=None, max_length=100, null=True)),
                ('number', models.CharField(default=None, max_length=3, unique=True)),
                ('date', models.DateTimeField(default=None)),
                ('informations_supplementaires', models.TextField(default=None, null=True)),
                ('prix_total_ht', models.FloatField(default=None, null=True)),
                ('prix_total_ttc', models.FloatField(default=None, null=True)),
                ('payer', models.BooleanField(default=False, null=True)),
                ('date_paiement', models.DateTimeField(blank=True, default=django.utils.timezone.now, null=True)),
                ('abonnement', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='abonnement.abonnement')),
                ('user', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, related_name='facture', to='compte.compte')),
            ],
        ),
    ]
