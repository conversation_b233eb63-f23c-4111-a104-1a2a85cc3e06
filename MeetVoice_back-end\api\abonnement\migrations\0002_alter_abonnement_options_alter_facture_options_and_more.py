# Generated by Django 5.2.3 on 2025-06-22 02:48

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('abonnement', '0001_initial'),
        ('compte', '0002_compte_audio_alter_compte_credit_alter_compte_sexe'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='abonnement',
            options={'ordering': ['ordre_affichage', 'prix_ttc'], 'verbose_name': 'Abonnement', 'verbose_name_plural': 'Abonnements'},
        ),
        migrations.AlterModelOptions(
            name='facture',
            options={'ordering': ['-date_creation'], 'verbose_name': 'Facture', 'verbose_name_plural': 'Factures'},
        ),
        migrations.RemoveField(
            model_name='facture',
            name='stripe_id',
        ),
        migrations.AddField(
            model_name='abonnement',
            name='date_creation',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='Date de création'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='abonnement',
            name='date_modification',
            field=models.DateTimeField(auto_now=True, verbose_name='Dernière modification'),
        ),
        migrations.AddField(
            model_name='abonnement',
            name='description_courte',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Description courte'),
        ),
        migrations.AddField(
            model_name='abonnement',
            name='features',
            field=models.JSONField(blank=True, default=list, verbose_name='Fonctionnalités'),
        ),
        migrations.AddField(
            model_name='abonnement',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='Plan actif'),
        ),
        migrations.AddField(
            model_name='abonnement',
            name='is_popular',
            field=models.BooleanField(default=False, verbose_name='Plan populaire'),
        ),
        migrations.AddField(
            model_name='abonnement',
            name='ordre_affichage',
            field=models.PositiveIntegerField(default=0, verbose_name="Ordre d'affichage"),
        ),
        migrations.AddField(
            model_name='abonnement',
            name='stripe_price_id',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='ID Prix Stripe'),
        ),
        migrations.AddField(
            model_name='abonnement',
            name='stripe_product_id',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='ID Produit Stripe'),
        ),
        migrations.AddField(
            model_name='facture',
            name='date_creation',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='Date de création'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='facture',
            name='date_echeance',
            field=models.DateTimeField(blank=True, null=True, verbose_name="Date d'échéance"),
        ),
        migrations.AddField(
            model_name='facture',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, verbose_name='Métadonnées'),
        ),
        migrations.AddField(
            model_name='facture',
            name='statut',
            field=models.CharField(choices=[('pending', 'En attente'), ('paid', 'Payée'), ('failed', 'Échec'), ('cancelled', 'Annulée'), ('refunded', 'Remboursée')], default='pending', max_length=20, verbose_name='Statut'),
        ),
        migrations.AddField(
            model_name='facture',
            name='stripe_invoice_id',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='ID Facture Stripe'),
        ),
        migrations.AddField(
            model_name='facture',
            name='stripe_payment_intent_id',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='ID Payment Intent Stripe'),
        ),
        migrations.AddField(
            model_name='facture',
            name='stripe_subscription_id',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='ID Abonnement Stripe'),
        ),
        migrations.AddField(
            model_name='facture',
            name='taux_tva',
            field=models.DecimalField(decimal_places=2, default=20.0, max_digits=5, verbose_name='Taux TVA (%)'),
        ),
        migrations.AlterField(
            model_name='abonnement',
            name='credits',
            field=models.PositiveIntegerField(default=0, verbose_name='Crédits inclus'),
        ),
        migrations.AlterField(
            model_name='abonnement',
            name='description',
            field=models.ManyToManyField(blank=True, to='abonnement.description', verbose_name='Fonctionnalités détaillées'),
        ),
        migrations.AlterField(
            model_name='abonnement',
            name='entreprise',
            field=models.BooleanField(default=False, verbose_name='Plan entreprise'),
        ),
        migrations.AlterField(
            model_name='abonnement',
            name='interval',
            field=models.CharField(choices=[('day', 'Jour'), ('week', 'Semaine'), ('month', 'Mois'), ('year', 'Année')], default='month', max_length=20, verbose_name='Intervalle de facturation'),
        ),
        migrations.AlterField(
            model_name='abonnement',
            name='interval_count',
            field=models.PositiveIntegerField(default=1, verbose_name="Nombre d'intervalles"),
        ),
        migrations.AlterField(
            model_name='abonnement',
            name='nom',
            field=models.CharField(default='Plan Standard', max_length=100, verbose_name='Nom du plan'),
        ),
        migrations.AlterField(
            model_name='abonnement',
            name='prix_ht',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Prix HT'),
        ),
        migrations.AlterField(
            model_name='abonnement',
            name='prix_ttc',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Prix TTC'),
        ),
        migrations.AlterField(
            model_name='abonnement',
            name='stripe_id',
            field=models.CharField(default=None, max_length=100, null=True, verbose_name='ID Stripe (legacy)'),
        ),
        migrations.AlterField(
            model_name='facture',
            name='abonnement',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='abonnement.abonnement', verbose_name='Abonnement'),
        ),
        migrations.AlterField(
            model_name='facture',
            name='date',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='Date de facturation'),
        ),
        migrations.AlterField(
            model_name='facture',
            name='date_paiement',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Date de paiement'),
        ),
        migrations.AlterField(
            model_name='facture',
            name='informations_supplementaires',
            field=models.TextField(blank=True, null=True, verbose_name='Informations supplémentaires'),
        ),
        migrations.AlterField(
            model_name='facture',
            name='number',
            field=models.CharField(max_length=20, unique=True, verbose_name='Numéro de facture'),
        ),
        migrations.AlterField(
            model_name='facture',
            name='payer',
            field=models.BooleanField(default=False, verbose_name='Payée'),
        ),
        migrations.AlterField(
            model_name='facture',
            name='paypal_id',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='ID PayPal'),
        ),
        migrations.AlterField(
            model_name='facture',
            name='prix_total_ht',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Prix total HT'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='facture',
            name='prix_total_ttc',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Prix total TTC'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='facture',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='factures', to='compte.compte', verbose_name='Utilisateur'),
        ),
        migrations.CreateModel(
            name='AbonnementUtilisateur',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stripe_subscription_id', models.CharField(blank=True, max_length=100, null=True)),
                ('statut', models.CharField(choices=[('active', 'Actif'), ('cancelled', 'Annulé'), ('past_due', 'En retard'), ('unpaid', 'Impayé'), ('trialing', "Période d'essai")], default='active', max_length=20)),
                ('date_debut', models.DateTimeField(default=django.utils.timezone.now)),
                ('date_fin', models.DateTimeField(blank=True, null=True)),
                ('date_prochaine_facturation', models.DateTimeField(blank=True, null=True)),
                ('credits_restants', models.PositiveIntegerField(default=0)),
                ('auto_renouvellement', models.BooleanField(default=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('date_modification', models.DateTimeField(auto_now=True)),
                ('abonnement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='abonnement.abonnement')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='abonnements_actifs', to='compte.compte')),
            ],
            options={
                'verbose_name': 'Abonnement utilisateur',
                'verbose_name_plural': 'Abonnements utilisateurs',
                'unique_together': {('user', 'abonnement', 'statut')},
            },
        ),
    ]
