from compte.models import <PERSON>mpte
from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator



class Description(models.Model):
    description = models.TextField(default=None)

    def __str__(self):
        return self.description

class Abonnement(models.Model):
    # Informations de base
    nom = models.CharField(max_length=100, default="Plan Standard", verbose_name="Nom du plan")
    description_courte = models.CharField(max_length=200, blank=True, null=True, verbose_name="Description courte")
    description = models.ManyToManyField(Description, blank=True, verbose_name="Fonctionnalités détaillées")

    # Intégration Stripe
    stripe_product_id = models.CharField(max_length=100, blank=True, null=True, verbose_name="ID Produit Stripe")
    stripe_price_id = models.CharField(max_length=100, blank=True, null=True, verbose_name="ID Prix Stripe")
    stripe_id = models.CharField(max_length=100, default=None, null=True, verbose_name="ID Stripe (legacy)")

    # Tarification
    prix_ht = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)], default=0, verbose_name="Prix HT")
    prix_ttc = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)], default=0, verbose_name="Prix TTC")
    credits = models.PositiveIntegerField(default=0, verbose_name="Crédits inclus")

    # Périodicité
    interval = models.CharField(
        max_length=20,
        choices=[
            ("day", "Jour"),
            ("week", "Semaine"),
            ("month", "Mois"),
            ("year", "Année"),
        ],
        default="month",
        verbose_name="Intervalle de facturation"
    )
    interval_count = models.PositiveIntegerField(default=1, verbose_name="Nombre d'intervalles")

    # Options d'affichage
    is_popular = models.BooleanField(default=False, verbose_name="Plan populaire")
    is_active = models.BooleanField(default=True, verbose_name="Plan actif")
    ordre_affichage = models.PositiveIntegerField(default=0, verbose_name="Ordre d'affichage")

    # Fonctionnalités (stockées en JSON)
    features = models.JSONField(default=list, blank=True, verbose_name="Fonctionnalités")   
    date_creation = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")
    date_modification = models.DateTimeField(auto_now=True, verbose_name="Dernière modification")

    class Meta:
        ordering = ['ordre_affichage', 'prix_ttc']
        verbose_name = "Abonnement"
        verbose_name_plural = "Abonnements"


    def __str__(self):
        return self.nom or f"Plan {self.id}"

    def save(self, *args, **kwargs):
        # Calcul automatique du prix HT si seul le TTC est fourni
        if self.prix_ttc > 0 and self.prix_ht == 0:
            self.prix_ht = round(float(self.prix_ttc) / 1.20, 2)
        # Calcul automatique du prix TTC si seul le HT est fourni
        elif self.prix_ht > 0 and self.prix_ttc == 0:
            self.prix_ttc = round(float(self.prix_ht) * 1.20, 2)
        super(Abonnement, self).save(*args, **kwargs)

    def get_features_list(self):
        """Retourne la liste des fonctionnalités"""
        if isinstance(self.features, list):
            return self.features
        return []

    def add_feature(self, feature):
        """Ajoute une fonctionnalité"""
        features = self.get_features_list()
        if feature not in features:
            features.append(feature)
            self.features = features
            self.save()

    def remove_feature(self, feature):
        """Supprime une fonctionnalité"""
        features = self.get_features_list()
        if feature in features:
            features.remove(feature)
            self.features = features
            self.save()

    def get_interval_display_fr(self):
        """Retourne l'affichage français de l'intervalle"""
        intervals = {
            'day': 'jour',
            'week': 'semaine',
            'month': 'mois',
            'year': 'an'
        }
        base = intervals.get(self.interval, self.interval)
        if self.interval_count > 1:
            if base == 'mois':
                return f"{self.interval_count} mois"
            elif base == 'an':
                return f"{self.interval_count} ans"
            else:
                return f"{self.interval_count} {base}s"
        return base

    def get_price_display(self):
        """Retourne l'affichage du prix avec devise"""
        return f"{self.prix_ttc}€/{self.get_interval_display_fr()}"

       

class Facture(models.Model):
    STATUT_CHOICES = [
        ('pending', 'En attente'),
        ('paid', 'Payée'),
        ('failed', 'Échec'),
        ('cancelled', 'Annulée'),
        ('refunded', 'Remboursée'),
    ]

    # Identifiants
    number = models.CharField(max_length=20, unique=True, verbose_name="Numéro de facture")
    stripe_invoice_id = models.CharField(max_length=100, blank=True, null=True, verbose_name="ID Facture Stripe")
    stripe_payment_intent_id = models.CharField(max_length=100, blank=True, null=True, verbose_name="ID Payment Intent Stripe")
    stripe_subscription_id = models.CharField(max_length=100, blank=True, null=True, verbose_name="ID Abonnement Stripe")
    paypal_id = models.CharField(max_length=100, blank=True, null=True, verbose_name="ID PayPal")

    # Relations
    user = models.ForeignKey(Compte, on_delete=models.CASCADE, related_name='factures', verbose_name="Utilisateur")
    abonnement = models.ForeignKey(Abonnement, on_delete=models.CASCADE, null=True, verbose_name="Abonnement")

    # Dates
    date_creation = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")
    date = models.DateTimeField(default=timezone.now, verbose_name="Date de facturation")
    date_paiement = models.DateTimeField(blank=True, null=True, verbose_name="Date de paiement")
    date_echeance = models.DateTimeField(blank=True, null=True, verbose_name="Date d'échéance")

    # Montants
    prix_total_ht = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)], verbose_name="Prix total HT")
    prix_total_ttc = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)], verbose_name="Prix total TTC")
    taux_tva = models.DecimalField(max_digits=5, decimal_places=2, default=20.00, verbose_name="Taux TVA (%)")

    # Statut et informations
    statut = models.CharField(max_length=20, choices=STATUT_CHOICES, default='pending', verbose_name="Statut")
    payer = models.BooleanField(default=False, verbose_name="Payée")  # Maintenu pour compatibilité
    informations_supplementaires = models.TextField(blank=True, null=True, verbose_name="Informations supplémentaires")

    # Métadonnées
    metadata = models.JSONField(default=dict, blank=True, verbose_name="Métadonnées")

    class Meta:
        ordering = ['-date_creation']
        verbose_name = "Facture"
        verbose_name_plural = "Factures"

    def __str__(self):
        return f"Facture {self.number} - {self.user.nom} {self.user.prenom}"

    @property
    def is_paid(self):
        """Vérifie si la facture est payée"""
        return self.statut == 'paid' or self.payer

    @property
    def is_overdue(self):
        """Vérifie si la facture est en retard"""
        if self.date_echeance and not self.is_paid:
            return timezone.now() > self.date_echeance
        return False
    
    def calculer_prix_total(self):
        """Calcule les prix totaux basés sur l'abonnement"""
        if self.abonnement:
            self.prix_total_ht = self.abonnement.prix_ht
            self.prix_total_ttc = self.abonnement.prix_ttc
            self.save()

    def marquer_comme_payee(self):
        """Marque la facture comme payée"""
        self.statut = 'paid'
        self.payer = True
        self.date_paiement = timezone.now()
        self.save()

    def marquer_comme_echouee(self):
        """Marque la facture comme échouée"""
        self.statut = 'failed'
        self.payer = False
        self.save()

    def generate_invoice_number(self):
        """Génère un numéro de facture unique"""
        # Format: YYYY-MM-XXXX (année-mois-numéro séquentiel)
        current_date = timezone.now()
        year_month = current_date.strftime('%Y-%m')

        # Trouver le dernier numéro pour ce mois
        last_invoice = Facture.objects.filter(
            number__startswith=year_month
        ).order_by('-number').first()

        if last_invoice:
            # Extraire le numéro séquentiel
            try:
                last_seq = int(last_invoice.number.split('-')[-1])
                new_seq = last_seq + 1
            except (ValueError, IndexError):
                new_seq = 1
        else:
            new_seq = 1

        self.number = f"{year_month}-{new_seq:04d}"

    def save(self, *args, **kwargs):
        # Générer le numéro de facture si nécessaire
        if not self.number:
            self.generate_invoice_number()

        # Calculer les prix si l'abonnement est défini et les prix ne sont pas définis
        if self.abonnement and (not self.prix_total_ttc or self.prix_total_ttc == 0):
            self.calculer_prix_total()

        # Synchroniser le statut avec le champ payer pour compatibilité
        if self.statut == 'paid':
            self.payer = True
        elif self.statut in ['pending', 'failed', 'cancelled']:
            self.payer = False

        super(Facture, self).save(*args, **kwargs)


# Modèle pour gérer les abonnements actifs des utilisateurs
class AbonnementUtilisateur(models.Model):
    STATUT_CHOICES = [
        ('active', 'Actif'),
        ('cancelled', 'Annulé'),
        ('past_due', 'En retard'),
        ('unpaid', 'Impayé'),
        ('trialing', 'Période d\'essai'),
    ]

    user = models.ForeignKey(Compte, on_delete=models.CASCADE, related_name='abonnements_actifs')
    abonnement = models.ForeignKey(Abonnement, on_delete=models.CASCADE)
    stripe_subscription_id = models.CharField(max_length=100, blank=True, null=True)

    statut = models.CharField(max_length=20, choices=STATUT_CHOICES, default='active')
    date_debut = models.DateTimeField(default=timezone.now)
    date_fin = models.DateTimeField(blank=True, null=True)
    date_prochaine_facturation = models.DateTimeField(blank=True, null=True)

    credits_restants = models.PositiveIntegerField(default=0)
    auto_renouvellement = models.BooleanField(default=True)

    date_creation = models.DateTimeField(auto_now_add=True)
    date_modification = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Abonnement utilisateur"
        verbose_name_plural = "Abonnements utilisateurs"
        unique_together = ['user', 'abonnement', 'statut']

    def __str__(self):
        return f"{self.user.nom} - {self.abonnement.nom} ({self.statut})"

    def is_active(self):
        """Vérifie si l'abonnement est actif"""
        return self.statut == 'active' and (not self.date_fin or self.date_fin > timezone.now())

    def renouveler(self):
        """Renouvelle l'abonnement pour la prochaine période"""
        from datetime import timedelta

        if self.abonnement.interval == 'month':
            # Approximation : 1 mois = 30 jours
            days_to_add = 30 * self.abonnement.interval_count
            self.date_prochaine_facturation = self.date_prochaine_facturation + timedelta(days=days_to_add)
        elif self.abonnement.interval == 'year':
            # Approximation : 1 an = 365 jours
            days_to_add = 365 * self.abonnement.interval_count
            self.date_prochaine_facturation = self.date_prochaine_facturation + timedelta(days=days_to_add)
        elif self.abonnement.interval == 'week':
            days_to_add = 7 * self.abonnement.interval_count
            self.date_prochaine_facturation = self.date_prochaine_facturation + timedelta(days=days_to_add)
        elif self.abonnement.interval == 'day':
            days_to_add = self.abonnement.interval_count
            self.date_prochaine_facturation = self.date_prochaine_facturation + timedelta(days=days_to_add)

        # Réinitialiser les crédits
        self.credits_restants = self.abonnement.credits
        self.save()