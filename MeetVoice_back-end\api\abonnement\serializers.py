from rest_framework import serializers
from .models import Abonnement, Description, Facture, AbonnementUtilisateur


class DescriptionSerializer(serializers.ModelSerializer):
    """Serializer pour les descriptions des fonctionnalités"""
    
    class Meta:
        model = Description
        fields = ['id', 'description']


class AbonnementSerializer(serializers.ModelSerializer):
    """Serializer pour les abonnements"""
    
    description = DescriptionSerializer(many=True, read_only=True)
    prix_affiche = serializers.SerializerMethodField()
    
    class Meta:
        model = Abonnement
        fields = [
            'id',
            'nom',
            'description_courte',
            'description',
            'stripe_product_id',
            'stripe_price_id',
            'stripe_id',
            'prix_ht',
            'prix_ttc',
            'prix_affiche',
            'credits',
            'interval',
            'interval_count',
            'is_popular',
            'is_active',
            'ordre_affichage',
            'date_creation',
            'date_modification'
        ]
        read_only_fields = ['id', 'date_creation', 'date_modification']
    
    def get_prix_affiche(self, obj):
        """Retourne le prix formaté pour l'affichage"""
        if obj.prix_ttc:
            if obj.interval == 'month':
                return f"{obj.prix_ttc}€/mois"
            elif obj.interval == 'year':
                return f"{obj.prix_ttc}€/an"
            else:
                return f"{obj.prix_ttc}€"
        return "Prix sur demande"


class AbonnementListSerializer(serializers.ModelSerializer):
    """Serializer simplifié pour la liste des abonnements"""
    
    prix_affiche = serializers.SerializerMethodField()
    
    class Meta:
        model = Abonnement
        fields = [
            'id',
            'nom',
            'description_courte',
            'prix_ht',
            'prix_ttc',
            'prix_affiche',
            'credits',
            'is_popular',
            'is_active',
            'ordre_affichage',
            'interval',
            'interval_count'
        ]
    
    def get_prix_affiche(self, obj):
        """Retourne le prix formaté pour l'affichage"""
        if obj.prix_ttc:
            if obj.interval == 'month':
                return f"{obj.prix_ttc}€/mois"
            elif obj.interval == 'year':
                return f"{obj.prix_ttc}€/an"
            else:
                return f"{obj.prix_ttc}€"
        return "Prix sur demande"


class FactureSerializer(serializers.ModelSerializer):
    """Serializer pour les factures"""
    
    abonnement_nom = serializers.SerializerMethodField()
    user_nom = serializers.SerializerMethodField()
    
    class Meta:
        model = Facture
        fields = [
            'id',
            'number',
            'user',
            'user_nom',
            'abonnement',
            'abonnement_nom',
            'prix_ht',
            'taux_tva',
            'prix_tva',
            'prix_total_ttc',
            'devise',
            'statut',
            'payer',
            'date_creation',
            'date_echeance',
            'date_paiement',
            'stripe_payment_intent_id',
            'stripe_invoice_id',
            'notes'
        ]
        read_only_fields = ['id', 'number', 'date_creation']
    
    def get_abonnement_nom(self, obj):
        """Retourne le nom de l'abonnement"""
        return obj.abonnement.nom if obj.abonnement else None
    
    def get_user_nom(self, obj):
        """Retourne le nom complet de l'utilisateur"""
        if obj.user:
            return f"{obj.user.prenom} {obj.user.nom}"
        return None


class AbonnementUtilisateurSerializer(serializers.ModelSerializer):
    """Serializer pour les abonnements utilisateurs"""
    
    abonnement_nom = serializers.SerializerMethodField()
    user_nom = serializers.SerializerMethodField()
    is_active_status = serializers.SerializerMethodField()
    
    class Meta:
        model = AbonnementUtilisateur
        fields = [
            'id',
            'user',
            'user_nom',
            'abonnement',
            'abonnement_nom',
            'stripe_subscription_id',
            'statut',
            'is_active_status',
            'date_debut',
            'date_fin',
            'date_prochaine_facturation',
            'credits_restants',
            'auto_renouvellement',
            'date_creation',
            'date_modification'
        ]
        read_only_fields = ['id', 'date_creation', 'date_modification']
    
    def get_abonnement_nom(self, obj):
        """Retourne le nom de l'abonnement"""
        return obj.abonnement.nom if obj.abonnement else None
    
    def get_user_nom(self, obj):
        """Retourne le nom complet de l'utilisateur"""
        if obj.user:
            return f"{obj.user.prenom} {obj.user.nom}"
        return None
    
    def get_is_active_status(self, obj):
        """Retourne si l'abonnement est actif"""
        return obj.is_active()


class AbonnementCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création d'abonnements"""
    
    class Meta:
        model = Abonnement
        fields = [
            'nom',
            'description_courte',
            'prix_ht',
            'prix_ttc',
            'credits',
            'interval',
            'interval_count',
            'is_popular',
            'is_active',
            'ordre_affichage'
        ]
    
    def validate_nom(self, value):
        """Validation du nom"""
        if len(value.strip()) < 3:
            raise serializers.ValidationError("Le nom doit contenir au moins 3 caractères.")
        return value.strip()
    
    def validate(self, data):
        """Validation globale"""
        if not data.get('prix_ht') and not data.get('prix_ttc'):
            raise serializers.ValidationError("Au moins un prix (HT ou TTC) doit être défini.")
        return data


class FactureCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création de factures"""
    
    class Meta:
        model = Facture
        fields = [
            'user',
            'abonnement',
            'prix_ht',
            'taux_tva',
            'devise',
            'date_echeance',
            'notes'
        ]
    
    def validate_prix_ht(self, value):
        """Validation du prix HT"""
        if value <= 0:
            raise serializers.ValidationError("Le prix HT doit être supérieur à 0.")
        return value
