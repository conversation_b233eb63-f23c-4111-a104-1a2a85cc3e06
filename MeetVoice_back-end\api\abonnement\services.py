"""
Services d'intégration Stripe pour la gestion des abonnements
"""
import stripe
import logging
from decimal import Decimal
from django.conf import settings
from django.utils import timezone
from typing import Optional, Dict, Any, List

from .models import Abonnement, Facture, AbonnementUtilisateur
from compte.models import Compte

# Configuration Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY

logger = logging.getLogger(__name__)


class StripeService:
    """Service principal pour l'intégration Stripe"""
    
    @staticmethod
    def create_product(abonnement: Abonnement) -> Optional[str]:
        """
        Crée un produit Stripe pour un abonnement Django
        
        Args:
            abonnement: Instance d'Abonnement Django
            
        Returns:
            ID du produit Stripe créé ou None en cas d'erreur
        """
        try:
            product = stripe.Product.create(
                name=abonnement.nom,
                description=abonnement.description_courte or f"Abonnement {abonnement.nom}",
                metadata={
                    'django_abonnement_id': str(abonnement.id),
                    'credits': str(abonnement.credits),
                    'entreprise': str(abonnement.entreprise),
                }
            )
            
            # Mettre à jour l'abonnement Django avec l'ID Stripe
            abonnement.stripe_product_id = product.id
            abonnement.save()
            
            logger.info(f"Produit Stripe créé: {product.id} pour l'abonnement {abonnement.id}")
            return product.id
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur lors de la création du produit Stripe: {e}")
            return None
    
    @staticmethod
    def create_price(abonnement: Abonnement) -> Optional[str]:
        """
        Crée un prix Stripe pour un abonnement Django
        
        Args:
            abonnement: Instance d'Abonnement Django
            
        Returns:
            ID du prix Stripe créé ou None en cas d'erreur
        """
        try:
            # S'assurer qu'un produit Stripe existe
            if not abonnement.stripe_product_id:
                product_id = StripeService.create_product(abonnement)
                if not product_id:
                    return None
            
            # Convertir le prix en centimes
            unit_amount = int(float(abonnement.prix_ttc) * 100)
            
            price = stripe.Price.create(
                product=abonnement.stripe_product_id,
                unit_amount=unit_amount,
                currency='eur',
                recurring={
                    'interval': abonnement.interval,
                    'interval_count': abonnement.interval_count,
                },
                metadata={
                    'django_abonnement_id': str(abonnement.id),
                }
            )
            
            # Mettre à jour l'abonnement Django avec l'ID prix Stripe
            abonnement.stripe_price_id = price.id
            abonnement.save()
            
            logger.info(f"Prix Stripe créé: {price.id} pour l'abonnement {abonnement.id}")
            return price.id
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur lors de la création du prix Stripe: {e}")
            return None
    
    @staticmethod
    def update_product(abonnement: Abonnement) -> bool:
        """
        Met à jour un produit Stripe existant
        
        Args:
            abonnement: Instance d'Abonnement Django
            
        Returns:
            True si la mise à jour a réussi, False sinon
        """
        try:
            if not abonnement.stripe_product_id:
                logger.warning(f"Aucun produit Stripe associé à l'abonnement {abonnement.id}")
                return False
            
            stripe.Product.modify(
                abonnement.stripe_product_id,
                name=abonnement.nom,
                description=abonnement.description_courte or f"Abonnement {abonnement.nom}",
                active=abonnement.is_active,
                metadata={
                    'django_abonnement_id': str(abonnement.id),
                    'credits': str(abonnement.credits),
                    'entreprise': str(abonnement.entreprise),
                }
            )
            
            logger.info(f"Produit Stripe mis à jour: {abonnement.stripe_product_id}")
            return True
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur lors de la mise à jour du produit Stripe: {e}")
            return False
    
    @staticmethod
    def create_customer(user: Compte) -> Optional[str]:
        """
        Crée un client Stripe pour un utilisateur Django
        
        Args:
            user: Instance de Compte Django
            
        Returns:
            ID du client Stripe créé ou None en cas d'erreur
        """
        try:
            customer = stripe.Customer.create(
                email=user.email,
                name=f"{user.prenom} {user.nom}",
                metadata={
                    'django_user_id': str(user.id),
                }
            )
            
            logger.info(f"Client Stripe créé: {customer.id} pour l'utilisateur {user.id}")
            return customer.id
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur lors de la création du client Stripe: {e}")
            return None
    
    @staticmethod
    def create_subscription(user: Compte, abonnement: Abonnement, 
                          payment_method_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Crée un abonnement Stripe pour un utilisateur
        
        Args:
            user: Instance de Compte Django
            abonnement: Instance d'Abonnement Django
            payment_method_id: ID du moyen de paiement Stripe (optionnel)
            
        Returns:
            Dictionnaire avec les informations de l'abonnement créé ou None en cas d'erreur
        """
        try:
            # S'assurer qu'un prix Stripe existe
            if not abonnement.stripe_price_id:
                price_id = StripeService.create_price(abonnement)
                if not price_id:
                    return None
            
            # Créer ou récupérer le client Stripe
            customer_id = StripeService.create_customer(user)
            if not customer_id:
                return None
            
            subscription_params = {
                'customer': customer_id,
                'items': [{'price': abonnement.stripe_price_id}],
                'metadata': {
                    'django_user_id': str(user.id),
                    'django_abonnement_id': str(abonnement.id),
                }
            }
            
            # Ajouter le moyen de paiement si fourni
            if payment_method_id:
                subscription_params['default_payment_method'] = payment_method_id
            
            subscription = stripe.Subscription.create(**subscription_params)
            
            # Créer l'abonnement utilisateur Django
            abonnement_user = AbonnementUtilisateur.objects.create(
                user=user,
                abonnement=abonnement,
                stripe_subscription_id=subscription.id,
                statut='active' if subscription.status == 'active' else 'pending',
                date_debut=timezone.now(),
                credits_restants=abonnement.credits,
            )
            
            logger.info(f"Abonnement Stripe créé: {subscription.id} pour l'utilisateur {user.id}")
            
            return {
                'subscription_id': subscription.id,
                'status': subscription.status,
                'client_secret': subscription.latest_invoice.payment_intent.client_secret if hasattr(subscription.latest_invoice, 'payment_intent') else None,
                'django_abonnement_user_id': abonnement_user.id,
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur lors de la création de l'abonnement Stripe: {e}")
            return None
    
    @staticmethod
    def cancel_subscription(subscription_id: str) -> bool:
        """
        Annule un abonnement Stripe
        
        Args:
            subscription_id: ID de l'abonnement Stripe
            
        Returns:
            True si l'annulation a réussi, False sinon
        """
        try:
            subscription = stripe.Subscription.modify(
                subscription_id,
                cancel_at_period_end=True
            )
            
            # Mettre à jour l'abonnement utilisateur Django
            try:
                abonnement_user = AbonnementUtilisateur.objects.get(
                    stripe_subscription_id=subscription_id
                )
                abonnement_user.statut = 'cancelled'
                abonnement_user.auto_renouvellement = False
                abonnement_user.save()
            except AbonnementUtilisateur.DoesNotExist:
                logger.warning(f"Abonnement utilisateur Django non trouvé pour {subscription_id}")
            
            logger.info(f"Abonnement Stripe annulé: {subscription_id}")
            return True
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur lors de l'annulation de l'abonnement Stripe: {e}")
            return False
    
    @staticmethod
    def sync_from_stripe() -> Dict[str, int]:
        """
        Synchronise les données depuis Stripe vers Django
        
        Returns:
            Dictionnaire avec le nombre d'éléments synchronisés
        """
        synced = {'products': 0, 'prices': 0, 'subscriptions': 0}
        
        try:
            # Synchroniser les produits
            products = stripe.Product.list(limit=100)
            for product in products.data:
                if 'django_abonnement_id' in product.metadata:
                    try:
                        abonnement_id = int(product.metadata['django_abonnement_id'])
                        abonnement = Abonnement.objects.get(id=abonnement_id)
                        if abonnement.stripe_product_id != product.id:
                            abonnement.stripe_product_id = product.id
                            abonnement.save()
                            synced['products'] += 1
                    except (ValueError, Abonnement.DoesNotExist):
                        continue
            
            # Synchroniser les prix
            prices = stripe.Price.list(limit=100)
            for price in prices.data:
                if 'django_abonnement_id' in price.metadata:
                    try:
                        abonnement_id = int(price.metadata['django_abonnement_id'])
                        abonnement = Abonnement.objects.get(id=abonnement_id)
                        if abonnement.stripe_price_id != price.id:
                            abonnement.stripe_price_id = price.id
                            abonnement.save()
                            synced['prices'] += 1
                    except (ValueError, Abonnement.DoesNotExist):
                        continue
            
            logger.info(f"Synchronisation Stripe terminée: {synced}")
            return synced
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur lors de la synchronisation Stripe: {e}")
            return synced


class StripeWebhookService:
    """Service pour gérer les webhooks Stripe"""
    
    @staticmethod
    def handle_webhook(payload: str, sig_header: str) -> bool:
        """
        Traite un webhook Stripe
        
        Args:
            payload: Corps de la requête webhook
            sig_header: Signature du webhook
            
        Returns:
            True si le webhook a été traité avec succès, False sinon
        """
        try:
            # Vérifier la signature du webhook
            event = stripe.Webhook.construct_event(
                payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
            )
            
            # Traiter l'événement selon son type
            if event['type'] == 'invoice.payment_succeeded':
                return StripeWebhookService._handle_payment_succeeded(event['data']['object'])
            elif event['type'] == 'invoice.payment_failed':
                return StripeWebhookService._handle_payment_failed(event['data']['object'])
            elif event['type'] == 'customer.subscription.updated':
                return StripeWebhookService._handle_subscription_updated(event['data']['object'])
            elif event['type'] == 'customer.subscription.deleted':
                return StripeWebhookService._handle_subscription_deleted(event['data']['object'])
            else:
                logger.info(f"Événement webhook non géré: {event['type']}")
                return True
                
        except ValueError as e:
            logger.error(f"Payload webhook invalide: {e}")
            return False
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Signature webhook invalide: {e}")
            return False
        except Exception as e:
            logger.error(f"Erreur lors du traitement du webhook: {e}")
            return False
    
    @staticmethod
    def _handle_payment_succeeded(invoice_data: Dict[str, Any]) -> bool:
        """Traite un paiement réussi"""
        try:
            subscription_id = invoice_data.get('subscription')
            if subscription_id:
                abonnement_user = AbonnementUtilisateur.objects.get(
                    stripe_subscription_id=subscription_id
                )
                
                # Créer ou mettre à jour la facture
                facture, created = Facture.objects.get_or_create(
                    stripe_invoice_id=invoice_data['id'],
                    defaults={
                        'user': abonnement_user.user,
                        'abonnement': abonnement_user.abonnement,
                        'stripe_subscription_id': subscription_id,
                        'prix_total_ttc': Decimal(str(invoice_data['amount_paid'] / 100)),
                        'statut': 'paid',
                        'payer': True,
                        'date_paiement': timezone.now(),
                    }
                )
                
                if not created:
                    facture.marquer_comme_payee()
                
                # Renouveler l'abonnement utilisateur
                abonnement_user.renouveler()
                
                logger.info(f"Paiement traité avec succès pour l'abonnement {subscription_id}")
                return True
                
        except AbonnementUtilisateur.DoesNotExist:
            logger.warning(f"Abonnement utilisateur non trouvé pour {subscription_id}")
        except Exception as e:
            logger.error(f"Erreur lors du traitement du paiement réussi: {e}")
        
        return False
    
    @staticmethod
    def _handle_payment_failed(invoice_data: Dict[str, Any]) -> bool:
        """Traite un échec de paiement"""
        try:
            subscription_id = invoice_data.get('subscription')
            if subscription_id:
                # Mettre à jour le statut de l'abonnement
                abonnement_user = AbonnementUtilisateur.objects.get(
                    stripe_subscription_id=subscription_id
                )
                abonnement_user.statut = 'past_due'
                abonnement_user.save()
                
                # Créer ou mettre à jour la facture
                facture, created = Facture.objects.get_or_create(
                    stripe_invoice_id=invoice_data['id'],
                    defaults={
                        'user': abonnement_user.user,
                        'abonnement': abonnement_user.abonnement,
                        'stripe_subscription_id': subscription_id,
                        'prix_total_ttc': Decimal(str(invoice_data['amount_due'] / 100)),
                        'statut': 'failed',
                        'payer': False,
                    }
                )
                
                if not created:
                    facture.marquer_comme_echouee()
                
                logger.info(f"Échec de paiement traité pour l'abonnement {subscription_id}")
                return True
                
        except AbonnementUtilisateur.DoesNotExist:
            logger.warning(f"Abonnement utilisateur non trouvé pour {subscription_id}")
        except Exception as e:
            logger.error(f"Erreur lors du traitement de l'échec de paiement: {e}")
        
        return False
    
    @staticmethod
    def _handle_subscription_updated(subscription_data: Dict[str, Any]) -> bool:
        """Traite une mise à jour d'abonnement"""
        try:
            abonnement_user = AbonnementUtilisateur.objects.get(
                stripe_subscription_id=subscription_data['id']
            )
            
            # Mettre à jour le statut
            status_mapping = {
                'active': 'active',
                'past_due': 'past_due',
                'unpaid': 'unpaid',
                'canceled': 'cancelled',
                'trialing': 'trialing',
            }
            
            new_status = status_mapping.get(subscription_data['status'], 'active')
            abonnement_user.statut = new_status
            abonnement_user.save()
            
            logger.info(f"Abonnement mis à jour: {subscription_data['id']} -> {new_status}")
            return True
            
        except AbonnementUtilisateur.DoesNotExist:
            logger.warning(f"Abonnement utilisateur non trouvé pour {subscription_data['id']}")
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour de l'abonnement: {e}")
        
        return False
    
    @staticmethod
    def _handle_subscription_deleted(subscription_data: Dict[str, Any]) -> bool:
        """Traite une suppression d'abonnement"""
        try:
            abonnement_user = AbonnementUtilisateur.objects.get(
                stripe_subscription_id=subscription_data['id']
            )
            
            abonnement_user.statut = 'cancelled'
            abonnement_user.date_fin = timezone.now()
            abonnement_user.auto_renouvellement = False
            abonnement_user.save()
            
            logger.info(f"Abonnement supprimé: {subscription_data['id']}")
            return True
            
        except AbonnementUtilisateur.DoesNotExist:
            logger.warning(f"Abonnement utilisateur non trouvé pour {subscription_data['id']}")
        except Exception as e:
            logger.error(f"Erreur lors de la suppression de l'abonnement: {e}")
        
        return False
