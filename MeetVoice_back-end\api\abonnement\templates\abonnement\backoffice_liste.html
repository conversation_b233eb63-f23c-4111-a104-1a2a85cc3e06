{% extends 'backoffice/base.html' %}
{% load static %}

{% block page_title_main %}Abonnements{% endblock %}
{% block page_title_breadcrumb %}Gestion{% endblock %}
{% block page_title_header %}Abonnements{% endblock %}
{% block page_icon %}<i class="fas fa-credit-card me-2"></i>{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'abonnement/css/backoffice.css' %}">
<style>
.page-header h1 { font-size: 1.5rem !important; }
.card { margin-bottom: 1rem; }
.badge { font-size: 0.75rem; }
.stripe-status { font-size: 0.8rem; }
</style>
{% endblock %}

{% block backoffice_content %}
<!-- Actions principales -->
<div class="row mb-4">
    <div class="col-md-8">
        <button class="btn btn-primary" onclick="creerAbonnement()">
            <i class="fas fa-plus me-1"></i>Créer un abonnement
        </button>
        <button class="btn btn-outline-info" onclick="syncStripe()">
            <i class="fab fa-stripe me-1"></i>Synchroniser Stripe
        </button>
    </div>
    <div class="col-md-4 text-end">
        <button class="btn btn-success" onclick="location.reload()">
            <i class="fas fa-sync-alt me-1"></i>Actualiser
        </button>
    </div>
</div>

<!-- Statistiques -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-primary">{{ total_abonnements }}</h5>
                <small>Total Abonnements</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-success">{{ actifs }}</h5>
                <small>Actifs</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-warning">{{ avec_stripe }}</h5>
                <small>Avec Stripe</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-info">{{ utilisateurs_actifs }}</h5>
                <small>Utilisateurs Actifs</small>
            </div>
        </div>
    </div>
</div>

<!-- Liste des abonnements -->
{% if abonnements %}
    {% for abonnement in abonnements %}
    <div class="card mb-3">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <strong>{{ abonnement.nom }}</strong>
                {% if abonnement.is_popular %}
                    <span class="badge bg-warning text-dark ms-2">Populaire</span>
                {% endif %}
                {% if not abonnement.is_active %}
                    <span class="badge bg-secondary ms-2">Inactif</span>
                {% endif %}
            </div>
            <div>
                <span class="badge bg-primary">{{ abonnement.get_price_display }}</span>
                {% if abonnement.stripe_product_id %}
                    <span class="badge bg-success stripe-status">
                        <i class="fab fa-stripe me-1"></i>Synchronisé
                    </span>
                {% else %}
                    <span class="badge bg-danger stripe-status">
                        <i class="fas fa-exclamation-triangle me-1"></i>Non synchronisé
                    </span>
                {% endif %}
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <p class="card-text">{{ abonnement.description_courte|default:"Aucune description" }}</p>
                    <small class="text-muted">
                        <i class="fas fa-coins me-1"></i>{{ abonnement.credits }} crédits
                        <span class="ms-3">
                            <i class="fas fa-clock me-1"></i>{{ abonnement.get_interval_display_fr }}
                        </span>
                        {% if abonnement.entreprise %}
                        <span class="ms-3">
                            <i class="fas fa-building me-1"></i>Entreprise
                        </span>
                        {% endif %}
                    </small>
                </div>
                <div class="col-md-4">
                    <div class="d-flex flex-column gap-2">
                        <button class="btn btn-sm btn-outline-primary" onclick="modifierAbonnement({{ abonnement.id }})">
                            <i class="fas fa-edit me-1"></i>Modifier
                        </button>
                        {% if not abonnement.stripe_product_id %}
                        <button class="btn btn-sm btn-success" onclick="creerStripeProduct({{ abonnement.id }})">
                            <i class="fab fa-stripe me-1"></i>Créer dans Stripe
                        </button>
                        {% else %}
                        <button class="btn btn-sm btn-info" onclick="mettreAJourStripe({{ abonnement.id }})">
                            <i class="fab fa-stripe me-1"></i>Mettre à jour Stripe
                        </button>
                        {% endif %}
                        <button class="btn btn-sm btn-outline-secondary" onclick="voirDetails({{ abonnement.id }})">
                            <i class="fas fa-eye me-1"></i>Détails
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Informations Stripe -->
            {% if abonnement.stripe_product_id %}
            <div class="mt-3 p-2 bg-light rounded">
                <small class="text-muted">
                    <strong>Stripe:</strong>
                    Product ID: <code>{{ abonnement.stripe_product_id }}</code>
                    {% if abonnement.stripe_price_id %}
                    | Price ID: <code>{{ abonnement.stripe_price_id }}</code>
                    {% endif %}
                </small>
            </div>
            {% endif %}
        </div>
    </div>
    {% endfor %}
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
        <h5>Aucun abonnement</h5>
        <p class="text-muted">Créez votre premier abonnement pour commencer.</p>
        <button class="btn btn-primary" onclick="creerAbonnement()">
            <i class="fas fa-plus me-1"></i>Créer un abonnement
        </button>
    </div>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
function creerAbonnement() {
    // Rediriger vers le formulaire de création
    window.location.href = '{% url "backoffice:abonnements_create" %}';
}

function modifierAbonnement(id) {
    window.location.href = `/backoffice/abonnements/${id}/edit/`;
}

function creerStripeProduct(id) {
    if (confirm('Créer ce produit dans Stripe ?')) {
        fetch(`/abonnement/api/create-stripe-product/${id}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Produit créé dans Stripe avec succès !');
                location.reload();
            } else {
                alert('Erreur: ' + (data.error || 'Erreur inconnue'));
            }
        })
        .catch(error => {
            alert('Erreur de communication: ' + error);
        });
    }
}

function mettreAJourStripe(id) {
    if (confirm('Mettre à jour ce produit dans Stripe ?')) {
        // À implémenter
        alert('Fonctionnalité à développer');
    }
}

function syncStripe() {
    if (confirm('Synchroniser tous les produits avec Stripe ?')) {
        fetch('/abonnement/api/sync-stripe/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
        })
        .then(response => response.json())
        .then(data => {
            alert(`Synchronisation terminée:\n- Produits: ${data.products}\n- Prix: ${data.prices}`);
            location.reload();
        })
        .catch(error => {
            alert('Erreur de synchronisation: ' + error);
        });
    }
}

function voirDetails(id) {
    window.location.href = `/abonnement/${id}/`;
}
</script>
{% endblock %}
