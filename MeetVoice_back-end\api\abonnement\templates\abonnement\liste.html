{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if user.is_staff %}Gestion des Plans d'Abonnement - Back Office{% else %}Nos Plans d'Abonnement - Meet Voice{% endif %}
{% endblock %}

{% block head %}
<meta name="description" content="Découvrez nos plans d'abonnement Meet Voice. Trouvez le plan parfait pour vos besoins de rencontres vocales.">
<script src="https://js.stripe.com/v3/"></script>
{% if user.is_staff %}
<link rel="stylesheet" href="{% static 'backoffice/css/base.css' %}" />
{% endif %}
<style>
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-bg: #f8f9fa;
    --border-radius: 1rem;
    --shadow: 0 4px 15px rgba(0,0,0,0.1);
    --shadow-hover: 0 8px 25px rgba(0,0,0,0.15);
}

.pricing-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.pricing-container {
    padding: 4rem 0;
    background: var(--light-bg);
}

.pricing-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.pricing-card.popular {
    border: 3px solid var(--warning-color);
    transform: scale(1.05);
}

.pricing-card.popular::before {
    content: "POPULAIRE";
    position: absolute;
    top: 20px;
    right: -30px;
    background: var(--warning-color);
    color: white;
    padding: 5px 40px;
    font-size: 0.8rem;
    font-weight: bold;
    transform: rotate(45deg);
    z-index: 10;
}

.pricing-header {
    padding: 2rem;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.pricing-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.pricing-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.pricing-price {
    font-size: 3rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.pricing-period {
    color: #6c757d;
    font-size: 1rem;
}

.pricing-body {
    padding: 2rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.pricing-features {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
    flex-grow: 1;
}

.pricing-features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    align-items: center;
}

.pricing-features li:last-child {
    border-bottom: none;
}

.pricing-features .feature-icon {
    color: var(--success-color);
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

.pricing-footer {
    padding: 2rem;
    text-align: center;
    border-top: 1px solid #f1f3f4;
}

.btn-pricing {
    width: 100%;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: bold;
    border-radius: 50px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-pricing-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}

.btn-pricing-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-pricing-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-pricing-outline:hover {
    background: var(--primary-color);
    color: white;
}

.credits-badge {
    background: var(--warning-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 1rem;
}

.loading-spinner {
    display: none;
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.faq-section {
    padding: 4rem 0;
    background: white;
}

.faq-item {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.faq-header {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.faq-header:hover {
    background: #e9ecef;
}

.faq-body {
    padding: 1.5rem;
    display: none;
}

.testimonial-section {
    background: var(--light-bg);
    padding: 4rem 0;
}

.testimonial-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow);
    text-align: center;
    margin-bottom: 2rem;
}

.testimonial-text {
    font-style: italic;
    margin-bottom: 1rem;
    color: #6c757d;
}

.testimonial-author {
    font-weight: bold;
    color: var(--primary-color);
}

/* Styles pour l'interface employé */
.admin-interface {
    display: flex;
    min-height: 100vh;
}

.admin-sidebar {
    width: 250px;
    background: #2a1d34;
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.admin-content {
    margin-left: 250px;
    flex: 1;
    min-height: 100vh;
}

.admin-nav {
    padding: 1rem 0;
}

.admin-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
}

.admin-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-1px);
}

.admin-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.admin-nav .nav-link i {
    width: 20px;
    margin-right: 0.5rem;
}

.admin-header {
    background: white;
    padding: 1rem 2rem;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 2rem;
}

.admin-actions {
    background: white;
    padding: 1rem 2rem;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 1rem;
}

.card-admin-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.pricing-card:hover .card-admin-actions {
    opacity: 1;
}

.btn-admin {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    margin-left: 0.25rem;
}

@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .admin-sidebar.show {
        transform: translateX(0);
    }

    .admin-content {
        margin-left: 0;
    }

    .pricing-card.popular {
        transform: none;
        margin-bottom: 2rem;
    }

    .pricing-price {
        font-size: 2.5rem;
    }

    .pricing-hero {
        padding: 2rem 0;
    }

    .pricing-container {
        padding: 2rem 0;
    }
}
</style>
{% endblock %}

{% block content %}
{% if user.is_staff %}
<!-- Interface Employé avec Sidebar -->
<div class="admin-interface">
    <!-- Sidebar Navigation -->
    <nav class="admin-sidebar">
        <div class="text-center p-3 border-bottom">
            <h5 class="text-white mb-1">
                <i class="fas fa-cogs me-2"></i>Back Office
            </h5>
            <small class="text-white-50">Administration</small>
        </div>
        <ul class="nav flex-column admin-nav">
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:dashboard' %}">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{% url 'abonnement:liste' %}">
                    <i class="fas fa-tags"></i>Gestion Abonnements
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:traffic_manager' %}">
                    <i class="fas fa-chart-line"></i>Traffic Manager
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:google_analytics' %}">
                    <i class="fab fa-google"></i>Google Analytics
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:articles' %}">
                    <i class="fas fa-newspaper"></i>Articles
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:moderation_posts' %}">
                    <i class="fas fa-shield-alt"></i>Modération Posts
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:moderation_events' %}">
                    <i class="fas fa-calendar-check"></i>Modération Événements
                </a>
            </li>
            <hr style="border-color: rgba(255, 255, 255, 0.2)" />
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin:index' %}">
                    <i class="fas fa-tools"></i>Admin Django
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'index' %}">
                    <i class="fas fa-home"></i>Retour au site
                </a>
            </li>
        </ul>
    </nav>

    <!-- Contenu Principal -->
    <div class="admin-content">
        <!-- Header Admin -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-tags me-2"></i>Gestion des Plans d'Abonnement</h1>
                    <p class="text-muted mb-0">Interface administrative pour la gestion des abonnements</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="openCreateModal()">
                        <i class="fas fa-plus me-2"></i>Créer un Plan
                    </button>
                </div>
            </div>
        </div>

        <!-- Messages -->
        {% if messages %}
        <div class="container-fluid">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}
{% else %}
<!-- Interface Publique -->
<!-- Hero Section -->
<section class="pricing-hero">
    <div class="container">
        <h1 class="display-4 mb-4">Choisissez votre plan</h1>
        <p class="lead mb-0">Trouvez le plan parfait pour vos rencontres vocales</p>
        <p class="mb-0">Tous nos plans incluent un essai gratuit de 7 jours</p>
    </div>
</section>
{% endif %}

<!-- Pricing Section -->
<section class="pricing-container">
    <div class="{% if user.is_staff %}container-fluid{% else %}container{% endif %}">
        {% if abonnements %}
        <div class="row justify-content-center">
            {% for abonnement in abonnements %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="pricing-card {% if abonnement.is_popular %}popular{% endif %}" style="position: relative;">
                    <!-- Boutons Admin (visibles uniquement pour les employés) -->
                    {% if user.is_staff %}
                    <div class="card-admin-actions">
                        <button class="btn btn-sm btn-outline-primary btn-admin"
                                onclick="editAbonnement({{ abonnement.id }})"
                                title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger btn-admin"
                                onclick="deleteAbonnement({{ abonnement.id }}, '{{ abonnement.nom }}')"
                                title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    {% endif %}

                    <div class="pricing-header">
                        <h3 class="pricing-title">{{ abonnement.nom }}</h3>
                        {% if abonnement.description_courte %}
                        <p class="pricing-description">{{ abonnement.description_courte }}</p>
                        {% endif %}
                        <div class="pricing-price">{{ abonnement.prix_ttc }}€</div>
                        <div class="pricing-period">par {{ abonnement.get_interval_display_fr }}</div>
                        {% if abonnement.credits > 0 %}
                        <div class="credits-badge">{{ abonnement.credits }} crédits inclus</div>
                        {% endif %}

                        <!-- Informations Admin -->
                        {% if user.is_staff %}
                        <div class="mt-2">
                            <small class="text-muted">
                                ID: {{ abonnement.id }} |
                                {% if abonnement.stripe_product_id %}
                                <span class="text-success"><i class="fab fa-stripe"></i> Sync</span>
                                {% else %}
                                <span class="text-warning">Non sync</span>
                                {% endif %}
                            </small>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="pricing-body">
                        {% if abonnement.get_features_list %}
                        <ul class="pricing-features">
                            {% for feature in abonnement.get_features_list %}
                            <li>
                                <i class="fas fa-check feature-icon"></i>
                                {{ feature }}
                            </li>
                            {% endfor %}
                        </ul>
                        {% endif %}
                    </div>
                    
                    <div class="pricing-footer">
                        {% if user.is_staff %}
                        <!-- Interface Employé -->
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary btn-sm"
                                    onclick="editAbonnement({{ abonnement.id }})">
                                <i class="fas fa-edit me-2"></i>Modifier
                            </button>
                            {% if not abonnement.stripe_product_id %}
                            <button class="btn btn-outline-info btn-sm"
                                    onclick="syncStripe({{ abonnement.id }})">
                                <i class="fab fa-stripe me-2"></i>Sync Stripe
                            </button>
                            {% endif %}
                        </div>
                        {% elif user.is_authenticated %}
                        <!-- Interface Client Connecté -->
                        <button class="btn-pricing {% if abonnement.is_popular %}btn-pricing-primary{% else %}btn-pricing-outline{% endif %}"
                                onclick="selectPlan({{ abonnement.id }}, '{{ abonnement.nom }}', {{ abonnement.prix_ttc }})"
                                id="btn-{{ abonnement.id }}">
                            <span class="loading-spinner" id="spinner-{{ abonnement.id }}"></span>
                            Choisir ce plan
                        </button>
                        {% else %}
                        <!-- Interface Client Non Connecté -->
                        <a href="{% url 'login' %}?next={% url 'abonnement:liste' %}"
                           class="btn-pricing {% if abonnement.is_popular %}btn-pricing-primary{% else %}btn-pricing-outline{% endif %}">
                            Se connecter pour s'abonner
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="row">
            <div class="col-12 text-center">
                <h3>Aucun plan disponible pour le moment</h3>
                {% if user.is_staff %}
                <p class="text-muted">Créez votre premier plan d'abonnement pour commencer.</p>
                <button class="btn btn-primary" onclick="openCreateModal()">
                    <i class="fas fa-plus me-2"></i>Créer le premier plan
                </button>
                {% else %}
                <p class="text-muted">Nos plans d'abonnement seront bientôt disponibles.</p>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</section>

{% if user.is_staff %}
    </div> <!-- Fin admin-content -->
</div> <!-- Fin admin-interface -->
{% endif %}

{% if not user.is_staff %}
<!-- FAQ Section (uniquement pour les clients) -->
<section class="faq-section">
    <div class="container">
        <h2 class="text-center mb-5">Questions fréquentes</h2>
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="faq-item">
                    <div class="faq-header" onclick="toggleFaq(this)">
                        <h5 class="mb-0">
                            Comment fonctionne l'essai gratuit ?
                            <i class="fas fa-chevron-down float-end"></i>
                        </h5>
                    </div>
                    <div class="faq-body">
                        <p>Tous nos plans incluent un essai gratuit de 7 jours. Vous pouvez annuler à tout moment pendant cette période sans être facturé.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-header" onclick="toggleFaq(this)">
                        <h5 class="mb-0">
                            Puis-je changer de plan à tout moment ?
                            <i class="fas fa-chevron-down float-end"></i>
                        </h5>
                    </div>
                    <div class="faq-body">
                        <p>Oui, vous pouvez upgrader ou downgrader votre plan à tout moment. Les changements prennent effet immédiatement.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-header" onclick="toggleFaq(this)">
                        <h5 class="mb-0">
                            Comment annuler mon abonnement ?
                            <i class="fas fa-chevron-down float-end"></i>
                        </h5>
                    </div>
                    <div class="faq-body">
                        <p>Vous pouvez annuler votre abonnement à tout moment depuis votre espace personnel. L'annulation prend effet à la fin de votre période de facturation actuelle.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-header" onclick="toggleFaq(this)">
                        <h5 class="mb-0">
                            Quels moyens de paiement acceptez-vous ?
                            <i class="fas fa-chevron-down float-end"></i>
                        </h5>
                    </div>
                    <div class="faq-body">
                        <p>Nous acceptons toutes les cartes de crédit principales (Visa, Mastercard, American Express) via notre partenaire sécurisé Stripe.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endif %}
{% endblock %}

{% if user.is_staff %}
<!-- Modal de création d'abonnement -->
<div class="modal fade" id="createAbonnementModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Créer un Nouveau Plan d'Abonnement
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createAbonnementForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nom" class="form-label">Nom du plan *</label>
                                <input type="text" class="form-control" id="nom" name="nom" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="prix_ttc" class="form-label">Prix TTC (€) *</label>
                                <input type="number" class="form-control" id="prix_ttc" name="prix_ttc" step="0.01" min="0" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description_courte" class="form-label">Description courte</label>
                        <input type="text" class="form-control" id="description_courte" name="description_courte" maxlength="200">
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="credits" class="form-label">Crédits inclus</label>
                                <input type="number" class="form-control" id="credits" name="credits" min="0" value="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="interval" class="form-label">Intervalle</label>
                                <select class="form-control" id="interval" name="interval">
                                    <option value="month">Mensuel</option>
                                    <option value="year">Annuel</option>
                                    <option value="week">Hebdomadaire</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="ordre_affichage" class="form-label">Ordre d'affichage</label>
                                <input type="number" class="form-control" id="ordre_affichage" name="ordre_affichage" min="0" value="0">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="features" class="form-label">Fonctionnalités (une par ligne)</label>
                        <textarea class="form-control" id="features" name="features" rows="4" placeholder="Accès illimité aux profils&#10;Messagerie vocale&#10;Support prioritaire"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="is_popular" name="is_popular">
                                <label class="form-check-label" for="is_popular">Plan populaire</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">Plan actif</label>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="create_stripe" name="create_stripe" checked>
                            <label class="form-check-label" for="create_stripe">
                                <i class="fab fa-stripe me-1"></i>Créer automatiquement dans Stripe
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Créer le Plan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteAbonnementModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le plan "<span id="deleteAbonnementName"></span>" ?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Attention :</strong> Cette action supprimera également le produit correspondant dans Stripe.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" onclick="confirmDeleteAbonnement()">Supprimer</button>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% block extra_js %}
<!-- JavaScript séparé pour une meilleure organisation -->
<script src="{% static 'abonnement/js/liste.js' %}"></script>
<script>
// Configuration et initialisation spécifique à cette page
document.addEventListener('DOMContentLoaded', function() {
    // Initialiser Stripe avec la clé publique
    {% if stripe_publishable_key %}
    initializeStripe('{{ stripe_publishable_key }}');
    {% else %}
    console.warn('Clé publique Stripe non configurée');
    {% endif %}
});


</script>
{% endblock %}
