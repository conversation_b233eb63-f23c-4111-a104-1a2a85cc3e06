{% extends 'base.html' %}
{% load static %}

{% block title %}Mes Abonnements - Meet Voice{% endblock %}

{% block head %}
<style>
.subscription-container {
    padding: 2rem 0;
    min-height: 80vh;
}

.subscription-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.subscription-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.subscription-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
}

.subscription-body {
    padding: 2rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: bold;
    font-size: 0.9rem;
}

.status-active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-past_due {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.credits-display {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    text-align: center;
    margin: 1rem 0;
}

.credits-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.btn-manage {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: bold;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-manage:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.btn-cancel {
    background: transparent;
    color: #dc3545;
    border: 2px solid #dc3545;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: bold;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background: #dc3545;
    color: white;
    text-decoration: none;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.empty-icon {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
    text-align: center;
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    padding: 0.25rem 0;
    display: flex;
    align-items: center;
}

.feature-icon {
    color: #28a745;
    margin-right: 0.5rem;
}

.next-billing {
    background: #e3f2fd;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
}
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <h1 class="display-5 mb-3">Mes Abonnements</h1>
        <p class="lead mb-0">Gérez vos abonnements Meet Voice</p>
    </div>
</div>

<div class="subscription-container">
    <div class="container">
        {% if abonnements %}
        <div class="row">
            {% for abonnement_user in abonnements %}
            <div class="col-lg-6 mb-4">
                <div class="subscription-card">
                    <div class="subscription-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">{{ abonnement_user.abonnement.nom }}</h4>
                            <span class="status-badge status-{{ abonnement_user.statut }}">
                                {% if abonnement_user.statut == 'active' %}
                                    <i class="fas fa-check-circle me-1"></i>Actif
                                {% elif abonnement_user.statut == 'cancelled' %}
                                    <i class="fas fa-times-circle me-1"></i>Annulé
                                {% elif abonnement_user.statut == 'past_due' %}
                                    <i class="fas fa-exclamation-triangle me-1"></i>En retard
                                {% else %}
                                    {{ abonnement_user.get_statut_display }}
                                {% endif %}
                            </span>
                        </div>
                        <p class="mb-0 mt-2 opacity-75">
                            {{ abonnement_user.abonnement.prix_ttc }}€ / {{ abonnement_user.abonnement.get_interval_display_fr }}
                        </p>
                    </div>
                    
                    <div class="subscription-body">
                        <!-- Crédits restants -->
                        {% if abonnement_user.credits_restants > 0 %}
                        <div class="credits-display">
                            <div class="credits-number">{{ abonnement_user.credits_restants }}</div>
                            <div class="text-muted">crédits restants</div>
                        </div>
                        {% endif %}
                        
                        <!-- Informations de facturation -->
                        {% if abonnement_user.date_prochaine_facturation and abonnement_user.statut == 'active' %}
                        <div class="next-billing">
                            <h6><i class="fas fa-calendar me-2"></i>Prochaine facturation</h6>
                            <p class="mb-0">{{ abonnement_user.date_prochaine_facturation|date:"d F Y" }}</p>
                        </div>
                        {% endif %}
                        
                        <!-- Fonctionnalités -->
                        {% if abonnement_user.abonnement.get_features_list %}
                        <div class="mt-3">
                            <h6>Fonctionnalités incluses :</h6>
                            <ul class="feature-list">
                                {% for feature in abonnement_user.abonnement.get_features_list|slice:":4" %}
                                <li>
                                    <i class="fas fa-check feature-icon"></i>
                                    {{ feature }}
                                </li>
                                {% endfor %}
                                {% if abonnement_user.abonnement.get_features_list|length > 4 %}
                                <li class="text-muted">
                                    <i class="fas fa-ellipsis-h feature-icon"></i>
                                    et {{ abonnement_user.abonnement.get_features_list|length|add:"-4" }} autres
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                        {% endif %}
                        
                        <!-- Actions -->
                        <div class="mt-4 d-flex gap-2 flex-wrap">
                            {% if abonnement_user.statut == 'active' %}
                            <a href="{% url 'abonnement:mes_factures' %}" class="btn-manage">
                                <i class="fas fa-file-invoice me-2"></i>Voir les factures
                            </a>
                            {% if abonnement_user.auto_renouvellement %}
                            <button class="btn-cancel" onclick="cancelSubscription({{ abonnement_user.id }})">
                                <i class="fas fa-times me-2"></i>Annuler
                            </button>
                            {% endif %}
                            {% elif abonnement_user.statut == 'past_due' %}
                            <a href="#" class="btn-manage">
                                <i class="fas fa-credit-card me-2"></i>Mettre à jour le paiement
                            </a>
                            {% elif abonnement_user.statut == 'cancelled' %}
                            <a href="{% url 'abonnement:liste' %}" class="btn-manage">
                                <i class="fas fa-redo me-2"></i>Réactiver
                            </a>
                            {% endif %}
                        </div>
                        
                        <!-- Informations supplémentaires -->
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-calendar-plus me-1"></i>
                                Souscrit le {{ abonnement_user.date_creation|date:"d F Y" }}
                                {% if abonnement_user.stripe_subscription_id %}
                                <br><i class="fab fa-stripe me-1"></i>
                                Géré par Stripe
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Actions globales -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="{% url 'abonnement:liste' %}" class="btn-manage me-3">
                    <i class="fas fa-plus me-2"></i>Ajouter un abonnement
                </a>
                <a href="{% url 'abonnement:mes_factures' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-file-invoice me-2"></i>Toutes mes factures
                </a>
            </div>
        </div>
        
        {% else %}
        <!-- État vide -->
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-crown"></i>
            </div>
            <h3>Aucun abonnement actif</h3>
            <p class="text-muted mb-4">
                Vous n'avez pas encore d'abonnement Meet Voice. 
                Découvrez nos plans pour profiter de toutes nos fonctionnalités.
            </p>
            <a href="{% url 'abonnement:liste' %}" class="btn-manage">
                <i class="fas fa-tags me-2"></i>Voir nos plans
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function cancelSubscription(abonnementUserId) {
    console.log('Tentative d\'annulation de l\'abonnement:', abonnementUserId);

    if (!confirm('Êtes-vous sûr de vouloir annuler cet abonnement ? Cette action ne peut pas être annulée.')) {
        return;
    }

    // Vérifier que la fonction getCookie existe
    if (typeof getCookie !== 'function') {
        console.error('La fonction getCookie n\'est pas définie');
        alert('Erreur: fonction getCookie manquante');
        return;
    }

    const csrfToken = getCookie('csrftoken');
    console.log('Token CSRF:', csrfToken);

    if (!csrfToken) {
        console.error('Token CSRF non trouvé');
        alert('Erreur: token CSRF manquant');
        return;
    }

    fetch(`/abonnement/annuler/${abonnementUserId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': csrfToken,
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        console.log('Réponse reçue:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Données reçues:', data);
        if (data.success) {
            alert('Abonnement annulé avec succès !');
            location.reload();
        } else {
            alert('Erreur lors de l\'annulation: ' + (data.error || 'Erreur inconnue'));
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Une erreur est survenue lors de l\'annulation: ' + error.message);
    });
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Animation d'entrée
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.subscription-card');
    
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
