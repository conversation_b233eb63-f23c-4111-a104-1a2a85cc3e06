{% extends 'base.html' %}
{% load static %}

{% block title %}Mes Factures - Meet Voice{% endblock %}

{% block head %}
<style>
.invoice-container {
    padding: 2rem 0;
    min-height: 80vh;
}

.invoice-card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.invoice-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.invoice-header {
    padding: 1.5rem;
    border-bottom: 1px solid #f1f3f4;
}

.invoice-body {
    padding: 1.5rem;
}

.invoice-number {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #667eea;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-weight: bold;
    font-size: 0.8rem;
}

.status-paid {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.status-cancelled {
    background: #e2e3e5;
    color: #383d41;
}

.amount-display {
    font-size: 1.5rem;
    font-weight: bold;
    color: #28a745;
}

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
    text-align: center;
}

.filter-section {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.btn-download {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-download:hover {
    background: #5a6fd8;
    color: white;
    text-decoration: none;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.empty-icon {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.summary-cards {
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.summary-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.summary-label {
    color: #6c757d;
    font-size: 0.9rem;
}
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <h1 class="display-5 mb-3">Mes Factures</h1>
        <p class="lead mb-0">Consultez l'historique de vos paiements</p>
    </div>
</div>

<div class="invoice-container">
    <div class="container">
        
        <!-- Résumé -->
        <div class="summary-cards">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="summary-card">
                        <div class="summary-value text-primary">{{ factures.paginator.count }}</div>
                        <div class="summary-label">Total factures</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="summary-card">
                        <div class="summary-value text-success">
                            {{ factures.object_list|length|default:0 }}
                        </div>
                        <div class="summary-label">Factures payées</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="summary-card">
                        <div class="summary-value text-info">
                            {% for facture in factures %}
                                {% if facture.is_paid %}{{ facture.prix_total_ttc|add:0 }}{% endif %}
                            {% endfor %}€
                        </div>
                        <div class="summary-label">Total payé</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="summary-card">
                        <div class="summary-value text-warning">
                            {% now "Y" %}
                        </div>
                        <div class="summary-label">Année en cours</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtres -->
        <div class="filter-section">
            <form method="get" class="row align-items-end">
                <div class="col-md-3 mb-2">
                    <label for="statut" class="form-label">Statut</label>
                    <select name="statut" id="statut" class="form-select">
                        <option value="">Tous les statuts</option>
                        <option value="paid" {% if request.GET.statut == 'paid' %}selected{% endif %}>Payées</option>
                        <option value="pending" {% if request.GET.statut == 'pending' %}selected{% endif %}>En attente</option>
                        <option value="failed" {% if request.GET.statut == 'failed' %}selected{% endif %}>Échec</option>
                    </select>
                </div>
                <div class="col-md-3 mb-2">
                    <label for="annee" class="form-label">Année</label>
                    <select name="annee" id="annee" class="form-select">
                        <option value="">Toutes les années</option>
                        {% for year in years %}
                        <option value="{{ year }}" {% if request.GET.annee == year|stringformat:"s" %}selected{% endif %}>{{ year }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 mb-2">
                    <label for="search" class="form-label">Recherche</label>
                    <input type="text" name="search" id="search" class="form-control" 
                           placeholder="Numéro de facture..." value="{{ request.GET.search }}">
                </div>
                <div class="col-md-2 mb-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i>Filtrer
                    </button>
                </div>
            </form>
        </div>

        <!-- Liste des factures -->
        {% if factures %}
        <div class="row">
            {% for facture in factures %}
            <div class="col-12 mb-3">
                <div class="invoice-card">
                    <div class="invoice-header">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <div class="invoice-number">{{ facture.number }}</div>
                                <small class="text-muted">{{ facture.date|date:"d F Y" }}</small>
                            </div>
                            <div class="col-md-3">
                                <div class="fw-bold">{{ facture.abonnement.nom }}</div>
                                <small class="text-muted">{{ facture.abonnement.get_price_display }}</small>
                            </div>
                            <div class="col-md-2">
                                <div class="amount-display">{{ facture.prix_total_ttc }}€</div>
                                <small class="text-muted">TTC</small>
                            </div>
                            <div class="col-md-2">
                                <span class="status-badge status-{{ facture.statut }}">
                                    {% if facture.statut == 'paid' %}
                                        <i class="fas fa-check-circle me-1"></i>Payée
                                    {% elif facture.statut == 'pending' %}
                                        <i class="fas fa-clock me-1"></i>En attente
                                    {% elif facture.statut == 'failed' %}
                                        <i class="fas fa-times-circle me-1"></i>Échec
                                    {% elif facture.statut == 'cancelled' %}
                                        <i class="fas fa-ban me-1"></i>Annulée
                                    {% else %}
                                        {{ facture.get_statut_display }}
                                    {% endif %}
                                </span>
                            </div>
                            <div class="col-md-2 text-end">
                                {% if facture.is_paid %}
                                <a href="#" class="btn-download" onclick="downloadInvoice('{{ facture.id }}')">
                                    <i class="fas fa-download me-1"></i>PDF
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    {% if facture.date_paiement or facture.informations_supplementaires %}
                    <div class="invoice-body">
                        <div class="row">
                            {% if facture.date_paiement %}
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="fas fa-calendar-check me-1"></i>
                                    Payée le {{ facture.date_paiement|date:"d F Y à H:i" }}
                                </small>
                            </div>
                            {% endif %}
                            {% if facture.stripe_invoice_id %}
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="fab fa-stripe me-1"></i>
                                    Stripe: {{ facture.stripe_invoice_id }}
                                </small>
                            </div>
                            {% endif %}
                        </div>
                        {% if facture.informations_supplementaires %}
                        <div class="mt-2">
                            <small class="text-muted">{{ facture.informations_supplementaires }}</small>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if factures.has_other_pages %}
        <div class="pagination-wrapper">
            <nav aria-label="Navigation des factures">
                <ul class="pagination">
                    {% if factures.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ factures.previous_page_number }}{% if request.GET.statut %}&statut={{ request.GET.statut }}{% endif %}{% if request.GET.annee %}&annee={{ request.GET.annee }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for num in factures.paginator.page_range %}
                    {% if num == factures.number %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% else %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if request.GET.statut %}&statut={{ request.GET.statut }}{% endif %}{% if request.GET.annee %}&annee={{ request.GET.annee }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if factures.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ factures.next_page_number }}{% if request.GET.statut %}&statut={{ request.GET.statut }}{% endif %}{% if request.GET.annee %}&annee={{ request.GET.annee }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}

        {% else %}
        <!-- État vide -->
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-file-invoice"></i>
            </div>
            <h3>Aucune facture trouvée</h3>
            <p class="text-muted mb-4">
                {% if request.GET.statut or request.GET.annee or request.GET.search %}
                Aucune facture ne correspond à vos critères de recherche.
                {% else %}
                Vous n'avez pas encore de factures. Souscrivez à un abonnement pour commencer.
                {% endif %}
            </p>
            {% if not request.GET.statut and not request.GET.annee and not request.GET.search %}
            <a href="{% url 'abonnement:liste' %}" class="btn btn-primary">
                <i class="fas fa-tags me-2"></i>Voir nos plans
            </a>
            {% else %}
            <a href="{% url 'abonnement:mes_factures' %}" class="btn btn-secondary">
                <i class="fas fa-times me-2"></i>Effacer les filtres
            </a>
            {% endif %}
        </div>
        {% endif %}
        
        <!-- Actions -->
        <div class="text-center mt-4">
            <a href="{% url 'abonnement:mes_abonnements' %}" class="btn btn-outline-primary me-3">
                <i class="fas fa-crown me-2"></i>Mes abonnements
            </a>
            <a href="{% url 'abonnement:liste' %}" class="btn btn-outline-secondary">
                <i class="fas fa-tags me-2"></i>Voir tous les plans
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function downloadInvoice(factureId) {
    // Simuler le téléchargement de facture
    // Dans une vraie implémentation, cela ferait appel à une vue Django qui génère le PDF
    alert('Fonctionnalité de téléchargement PDF à implémenter');
    
    // Exemple d'implémentation :
    // window.open(`/abonnement/facture/${factureId}/pdf/`, '_blank');
}

// Animation d'entrée
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.invoice-card');
    
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateX(-30px)';
        
        setTimeout(() => {
            card.style.transition = 'opacity 0.4s ease, transform 0.4s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateX(0)';
        }, index * 50);
    });
});
</script>
{% endblock %}
