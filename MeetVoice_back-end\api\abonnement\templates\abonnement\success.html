{% extends 'base.html' %}
{% load static %}

{% block title %}Abonnement Confirmé - Meet Voice{% endblock %}

{% block head %}
<style>
.success-container {
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.success-card {
    background: white;
    color: #333;
    border-radius: 1rem;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    max-width: 600px;
    margin: 2rem;
}

.success-icon {
    font-size: 4rem;
    color: #28a745;
    margin-bottom: 1.5rem;
    animation: bounce 1s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.success-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: #28a745;
}

.success-message {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #6c757d;
}

.next-steps {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin: 2rem 0;
    text-align: left;
}

.step-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.5rem;
}

.step-icon {
    background: #667eea;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 0.9rem;
}

.btn-action {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: bold;
    text-decoration: none;
    display: inline-block;
    margin: 0.5rem;
    transition: all 0.3s ease;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-secondary:hover {
    background: #667eea;
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="success-container">
    <div class="success-card">
        <div class="success-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        
        <h1 class="success-title">Félicitations !</h1>
        
        <p class="success-message">
            Votre abonnement a été confirmé avec succès. Vous pouvez maintenant profiter de toutes les fonctionnalités de votre plan.
        </p>
        
        <div class="next-steps">
            <h5 class="mb-3"><i class="fas fa-rocket me-2"></i>Prochaines étapes</h5>
            
            <div class="step-item">
                <div class="step-icon">1</div>
                <div>
                    <strong>Complétez votre profil</strong><br>
                    <small class="text-muted">Ajoutez une photo et décrivez-vous pour attirer plus de matches</small>
                </div>
            </div>
            
            <div class="step-item">
                <div class="step-icon">2</div>
                <div>
                    <strong>Explorez les profils</strong><br>
                    <small class="text-muted">Découvrez les membres qui correspondent à vos critères</small>
                </div>
            </div>
            
            <div class="step-item">
                <div class="step-icon">3</div>
                <div>
                    <strong>Commencez à discuter</strong><br>
                    <small class="text-muted">Utilisez vos crédits pour entamer des conversations vocales</small>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="{% url 'profils' %}" class="btn-action">
                <i class="fas fa-users me-2"></i>Découvrir les profils
            </a>
            <a href="{% url 'abonnement:mes_abonnements' %}" class="btn-action btn-secondary">
                <i class="fas fa-cog me-2"></i>Gérer mon abonnement
            </a>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                <i class="fas fa-envelope me-1"></i>
                Un email de confirmation vous a été envoyé avec tous les détails de votre abonnement.
            </small>
        </div>
    </div>
</div>

<!-- Section d'aide -->
<section class="py-5 bg-light">
    <div class="container text-center">
        <h3 class="mb-4">Besoin d'aide ?</h3>
        <p class="mb-4">Notre équipe est là pour vous accompagner dans votre expérience Meet Voice.</p>
        
        <div class="row justify-content-center">
            <div class="col-md-4 mb-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-question-circle fa-2x text-primary mb-3"></i>
                        <h5>FAQ</h5>
                        <p class="text-muted">Consultez nos questions fréquentes</p>
                        <a href="#" class="btn btn-outline-primary">Voir la FAQ</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-headset fa-2x text-primary mb-3"></i>
                        <h5>Support</h5>
                        <p class="text-muted">Contactez notre équipe support</p>
                        <a href="#" class="btn btn-outline-primary">Nous contacter</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-book fa-2x text-primary mb-3"></i>
                        <h5>Guide</h5>
                        <p class="text-muted">Apprenez à utiliser Meet Voice</p>
                        <a href="#" class="btn btn-outline-primary">Voir le guide</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Animation d'entrée
document.addEventListener('DOMContentLoaded', function() {
    const card = document.querySelector('.success-card');
    card.style.opacity = '0';
    card.style.transform = 'translateY(30px)';
    
    setTimeout(() => {
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        card.style.opacity = '1';
        card.style.transform = 'translateY(0)';
    }, 100);
    
    // Confetti effect (optionnel)
    if (typeof confetti !== 'undefined') {
        confetti({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 }
        });
    }
});
</script>
{% endblock %}
