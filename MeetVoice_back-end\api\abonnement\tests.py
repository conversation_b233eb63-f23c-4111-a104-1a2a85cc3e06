"""
Tests pour l'application abonnement et l'intégration Stripe
"""
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from unittest.mock import patch, MagicMock
from decimal import Decimal
import json

from .models import Abonnement, Facture, AbonnementUtilisateur, Description
from .services import StripeService, StripeWebhookService
from compte.models import Compte

User = get_user_model()


class AbonnementModelTest(TestCase):
    """Tests pour le modèle Abonnement"""

    def setUp(self):
        self.description = Description.objects.create(
            description="Fonctionnalité test"
        )

    def test_create_abonnement(self):
        """Test de création d'un abonnement"""
        abonnement = Abonnement.objects.create(
            nom="Test Premium",
            description_courte="Plan de test",
            prix_ttc=Decimal('19.99'),
            credits=100,
            interval='month',
            is_active=True,
            features=["Feature 1", "Feature 2"]
        )

        self.assertEqual(abonnement.nom, "Test Premium")
        self.assertEqual(abonnement.prix_ttc, Decimal('19.99'))
        self.assertEqual(abonnement.prix_ht, Decimal('16.66'))  # Calculé automatiquement
        self.assertTrue(abonnement.is_active)
        self.assertEqual(len(abonnement.get_features_list()), 2)

    def test_price_calculation(self):
        """Test du calcul automatique des prix"""
        # Test calcul HT depuis TTC
        abonnement1 = Abonnement.objects.create(
            nom="Test 1",
            prix_ttc=Decimal('24.00'),
            credits=50
        )
        self.assertEqual(abonnement1.prix_ht, Decimal('20.00'))

        # Test calcul TTC depuis HT
        abonnement2 = Abonnement.objects.create(
            nom="Test 2",
            prix_ht=Decimal('20.00'),
            credits=50
        )
        self.assertEqual(abonnement2.prix_ttc, Decimal('24.00'))

    def test_features_management(self):
        """Test de gestion des fonctionnalités"""
        abonnement = Abonnement.objects.create(
            nom="Test Features",
            prix_ttc=Decimal('19.99'),
            credits=100
        )

        # Ajouter des fonctionnalités
        abonnement.add_feature("Feature 1")
        abonnement.add_feature("Feature 2")

        features = abonnement.get_features_list()
        self.assertIn("Feature 1", features)
        self.assertIn("Feature 2", features)

        # Supprimer une fonctionnalité
        abonnement.remove_feature("Feature 1")
        features = abonnement.get_features_list()
        self.assertNotIn("Feature 1", features)
        self.assertIn("Feature 2", features)

    def test_interval_display(self):
        """Test de l'affichage des intervalles"""
        abonnement_monthly = Abonnement.objects.create(
            nom="Monthly",
            prix_ttc=Decimal('19.99'),
            interval='month',
            interval_count=1
        )
        self.assertEqual(abonnement_monthly.get_interval_display_fr(), 'mois')

        abonnement_yearly = Abonnement.objects.create(
            nom="Yearly",
            prix_ttc=Decimal('199.99'),
            interval='year',
            interval_count=1
        )
        self.assertEqual(abonnement_yearly.get_interval_display_fr(), 'an')

        abonnement_3months = Abonnement.objects.create(
            nom="3 Months",
            prix_ttc=Decimal('49.99'),
            interval='month',
            interval_count=3
        )
        self.assertEqual(abonnement_3months.get_interval_display_fr(), '3 mois')


class FactureModelTest(TestCase):
    """Tests pour le modèle Facture"""

    def setUp(self):
        self.user = Compte.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            nom='Test',
            prenom='User'
        )

        self.abonnement = Abonnement.objects.create(
            nom="Test Plan",
            prix_ttc=Decimal('19.99'),
            credits=100
        )

    def test_create_facture(self):
        """Test de création d'une facture"""
        facture = Facture.objects.create(
            user=self.user,
            abonnement=self.abonnement,
            prix_total_ttc=Decimal('19.99'),
            prix_total_ht=Decimal('16.66')
        )

        self.assertTrue(facture.number)  # Numéro généré automatiquement
        self.assertEqual(facture.statut, 'pending')
        self.assertFalse(facture.is_paid)

    def test_facture_number_generation(self):
        """Test de génération des numéros de facture"""
        facture1 = Facture.objects.create(
            user=self.user,
            abonnement=self.abonnement,
            prix_total_ttc=Decimal('19.99')
        )

        facture2 = Facture.objects.create(
            user=self.user,
            abonnement=self.abonnement,
            prix_total_ttc=Decimal('29.99')
        )

        # Les numéros doivent être différents et suivre le format YYYY-MM-XXXX
        self.assertNotEqual(facture1.number, facture2.number)
        self.assertTrue(facture1.number.count('-') == 2)
        self.assertTrue(facture2.number.count('-') == 2)

    def test_facture_payment_status(self):
        """Test des statuts de paiement"""
        facture = Facture.objects.create(
            user=self.user,
            abonnement=self.abonnement,
            prix_total_ttc=Decimal('19.99')
        )

        # Marquer comme payée
        facture.marquer_comme_payee()
        self.assertTrue(facture.is_paid)
        self.assertEqual(facture.statut, 'paid')
        self.assertTrue(facture.payer)

        # Marquer comme échouée
        facture.marquer_comme_echouee()
        self.assertFalse(facture.is_paid)
        self.assertEqual(facture.statut, 'failed')
        self.assertFalse(facture.payer)


class StripeServiceTest(TestCase):
    """Tests pour le service Stripe"""

    def setUp(self):
        self.abonnement = Abonnement.objects.create(
            nom="Test Premium",
            description_courte="Plan de test",
            prix_ttc=Decimal('19.99'),
            credits=100,
            interval='month'
        )

        self.user = Compte.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            nom='Test',
            prenom='User'
        )

    @patch('stripe.Product.create')
    def test_create_product(self, mock_product_create):
        """Test de création d'un produit Stripe"""
        mock_product_create.return_value = MagicMock(id='prod_test123')

        product_id = StripeService.create_product(self.abonnement)

        self.assertEqual(product_id, 'prod_test123')
        self.assertEqual(self.abonnement.stripe_product_id, 'prod_test123')
        mock_product_create.assert_called_once()

    @patch('stripe.Price.create')
    @patch('stripe.Product.create')
    def test_create_price(self, mock_product_create, mock_price_create):
        """Test de création d'un prix Stripe"""
        mock_product_create.return_value = MagicMock(id='prod_test123')
        mock_price_create.return_value = MagicMock(id='price_test123')

        price_id = StripeService.create_price(self.abonnement)

        self.assertEqual(price_id, 'price_test123')
        self.assertEqual(self.abonnement.stripe_price_id, 'price_test123')
        mock_price_create.assert_called_once()

    @patch('stripe.Customer.create')
    def test_create_customer(self, mock_customer_create):
        """Test de création d'un client Stripe"""
        mock_customer_create.return_value = MagicMock(id='cus_test123')

        customer_id = StripeService.create_customer(self.user)

        self.assertEqual(customer_id, 'cus_test123')
        mock_customer_create.assert_called_once_with(
            email=self.user.email,
            name=f"{self.user.prenom} {self.user.nom}",
            metadata={'django_user_id': str(self.user.id)}
        )

    @patch('stripe.Subscription.create')
    @patch('stripe.Customer.create')
    @patch('stripe.Price.create')
    @patch('stripe.Product.create')
    def test_create_subscription(self, mock_product_create, mock_price_create,
                               mock_customer_create, mock_subscription_create):
        """Test de création d'un abonnement Stripe"""
        mock_product_create.return_value = MagicMock(id='prod_test123')
        mock_price_create.return_value = MagicMock(id='price_test123')
        mock_customer_create.return_value = MagicMock(id='cus_test123')
        mock_subscription_create.return_value = MagicMock(
            id='sub_test123',
            status='active'
        )

        result = StripeService.create_subscription(self.user, self.abonnement)

        self.assertIsNotNone(result)
        self.assertEqual(result['subscription_id'], 'sub_test123')
        self.assertEqual(result['status'], 'active')

        # Vérifier qu'un AbonnementUtilisateur a été créé
        abonnement_user = AbonnementUtilisateur.objects.get(
            user=self.user,
            abonnement=self.abonnement
        )
        self.assertEqual(abonnement_user.stripe_subscription_id, 'sub_test123')
        self.assertEqual(abonnement_user.statut, 'active')


class StripeWebhookServiceTest(TestCase):
    """Tests pour le service de webhooks Stripe"""

    def setUp(self):
        self.user = Compte.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            nom='Test',
            prenom='User'
        )

        self.abonnement = Abonnement.objects.create(
            nom="Test Premium",
            prix_ttc=Decimal('19.99'),
            credits=100
        )

        self.abonnement_user = AbonnementUtilisateur.objects.create(
            user=self.user,
            abonnement=self.abonnement,
            stripe_subscription_id='sub_test123',
            statut='active'
        )

    def test_handle_payment_succeeded(self):
        """Test de traitement d'un paiement réussi"""
        invoice_data = {
            'id': 'in_test123',
            'subscription': 'sub_test123',
            'amount_paid': 1999,  # 19.99€ en centimes
        }

        success = StripeWebhookService._handle_payment_succeeded(invoice_data)

        self.assertTrue(success)

        # Vérifier qu'une facture a été créée
        facture = Facture.objects.get(stripe_invoice_id='in_test123')
        self.assertTrue(facture.is_paid)
        self.assertEqual(facture.prix_total_ttc, Decimal('19.99'))

    def test_handle_payment_failed(self):
        """Test de traitement d'un échec de paiement"""
        invoice_data = {
            'id': 'in_test456',
            'subscription': 'sub_test123',
            'amount_due': 1999,
        }

        success = StripeWebhookService._handle_payment_failed(invoice_data)

        self.assertTrue(success)

        # Vérifier que l'abonnement utilisateur a été mis à jour
        self.abonnement_user.refresh_from_db()
        self.assertEqual(self.abonnement_user.statut, 'past_due')

        # Vérifier qu'une facture a été créée
        facture = Facture.objects.get(stripe_invoice_id='in_test456')
        self.assertFalse(facture.is_paid)
        self.assertEqual(facture.statut, 'failed')

    def test_handle_subscription_updated(self):
        """Test de traitement d'une mise à jour d'abonnement"""
        subscription_data = {
            'id': 'sub_test123',
            'status': 'past_due'
        }

        success = StripeWebhookService._handle_subscription_updated(subscription_data)

        self.assertTrue(success)

        # Vérifier que le statut a été mis à jour
        self.abonnement_user.refresh_from_db()
        self.assertEqual(self.abonnement_user.statut, 'past_due')

    def test_handle_subscription_deleted(self):
        """Test de traitement d'une suppression d'abonnement"""
        subscription_data = {
            'id': 'sub_test123'
        }

        success = StripeWebhookService._handle_subscription_deleted(subscription_data)

        self.assertTrue(success)

        # Vérifier que l'abonnement a été annulé
        self.abonnement_user.refresh_from_db()
        self.assertEqual(self.abonnement_user.statut, 'cancelled')
        self.assertFalse(self.abonnement_user.auto_renouvellement)
