"""
URLs pour l'application abonnement
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'abonnement'

# Configuration du router DRF
router = DefaultRouter()
router.register(r'abonnements', views.AbonnementViewSet)
router.register(r'factures', views.FactureViewSet)
router.register(r'abonnements-utilisateurs', views.AbonnementUtilisateurViewSet)
router.register(r'descriptions', views.DescriptionViewSet)

urlpatterns = [
    # API REST
    path('api/', include(router.urls)),

    # Pages publiques
    path('', views.AbonnementListView.as_view(), name='liste'),
    path('<int:abonnement_id>/', views.AbonnementDetailView.as_view(), name='detail'),
    
    # Gestion des abonnements utilisateur
    path('mes-abonnements/', views.UserSubscriptionsView.as_view(), name='mes_abonnements'),
    path('mes-factures/', views.UserInvoicesView.as_view(), name='mes_factures'),
    
    # Actions d'abonnement
    path('creer/', views.CreateSubscriptionView.as_view(), name='creer'),
    path('annuler/<int:subscription_id>/', views.CancelSubscriptionView.as_view(), name='annuler'),
    
    # Stripe Checkout
    path('checkout/', views.create_checkout_session, name='checkout'),
    path('success/', views.subscription_success, name='success'),
    path('cancel/', views.subscription_cancel, name='cancel'),
    
    # Webhooks Stripe
    path('webhook/stripe/', views.stripe_webhook, name='stripe_webhook'),
    
    # API pour le back-office
    path('api/sync-stripe/', views.SyncStripeView.as_view(), name='sync_stripe'),
    path('api/create-stripe-product/<int:abonnement_id>/', views.CreateStripeProductView.as_view(), name='create_stripe_product'),
]
