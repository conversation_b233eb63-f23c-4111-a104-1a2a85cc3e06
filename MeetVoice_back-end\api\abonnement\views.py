"""
Vues pour la gestion des abonnements et l'intégration Stripe
"""
import json
import logging
from decimal import Decimal
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.conf import settings
from django.utils.decorators import method_decorator
from django.views import View
from django.core.paginator import Paginator

# DRF imports
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend

from .models import Abonnement, Facture, AbonnementUtilisateur, Description
from .services import StripeService, StripeWebhookService
from compte.models import Compte
from .serializers import (
    AbonnementSerializer, AbonnementListSerializer, AbonnementCreateSerializer,
    FactureSerializer, FactureCreateSerializer, AbonnementUtilisateurSerializer,
    DescriptionSerializer
)

logger = logging.getLogger(__name__)


# ============================================================================
# API REST VIEWSETS
# ============================================================================

class AbonnementViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion des abonnements via API REST"""

    queryset = Abonnement.objects.all()
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active', 'is_popular', 'interval']
    search_fields = ['nom', 'description_courte']
    ordering_fields = ['nom', 'prix_ht', 'prix_ttc', 'ordre_affichage']
    ordering = ['ordre_affichage', 'prix_ht']

    def get_serializer_class(self):
        """Retourne le serializer approprié selon l'action"""
        if self.action == 'list':
            return AbonnementListSerializer
        elif self.action == 'create':
            return AbonnementCreateSerializer
        return AbonnementSerializer

    def get_queryset(self):
        """Filtre les abonnements selon les permissions"""
        queryset = Abonnement.objects.all()

        # Pour les utilisateurs non-staff, ne montrer que les abonnements actifs
        if not self.request.user.is_staff:
            queryset = queryset.filter(is_active=True)

        return queryset

    @action(detail=False, methods=['get'])
    def active(self, request):
        """Retourne uniquement les abonnements actifs"""
        active_abonnements = self.get_queryset().filter(is_active=True)
        serializer = self.get_serializer(active_abonnements, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def popular(self, request):
        """Retourne les abonnements populaires"""
        popular_abonnements = self.get_queryset().filter(is_popular=True, is_active=True)
        serializer = self.get_serializer(popular_abonnements, many=True)
        return Response(serializer.data)


class FactureViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion des factures via API REST"""

    queryset = Facture.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['statut', 'payer', 'abonnement']
    search_fields = ['number', 'user__nom', 'user__prenom', 'user__email']
    ordering_fields = ['date_creation', 'date_echeance', 'prix_total_ttc']
    ordering = ['-date_creation']

    def get_serializer_class(self):
        """Retourne le serializer approprié selon l'action"""
        if self.action == 'create':
            return FactureCreateSerializer
        return FactureSerializer

    def get_queryset(self):
        """Filtre les factures selon les permissions"""
        queryset = Facture.objects.all()

        # Les utilisateurs non-staff ne voient que leurs propres factures
        if not self.request.user.is_staff:
            queryset = queryset.filter(user=self.request.user)

        return queryset

    @action(detail=False, methods=['get'])
    def my_invoices(self, request):
        """Retourne les factures de l'utilisateur connecté"""
        user_factures = Facture.objects.filter(user=request.user)
        serializer = self.get_serializer(user_factures, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def unpaid(self, request):
        """Retourne les factures impayées"""
        unpaid_factures = self.get_queryset().filter(payer=False)
        serializer = self.get_serializer(unpaid_factures, many=True)
        return Response(serializer.data)


class AbonnementUtilisateurViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion des abonnements utilisateurs via API REST"""

    queryset = AbonnementUtilisateur.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['statut', 'abonnement', 'auto_renouvellement']
    search_fields = ['user__nom', 'user__prenom', 'user__email', 'abonnement__nom']
    ordering_fields = ['date_creation', 'date_debut', 'date_fin']
    ordering = ['-date_creation']

    serializer_class = AbonnementUtilisateurSerializer

    def get_queryset(self):
        """Filtre les abonnements selon les permissions"""
        queryset = AbonnementUtilisateur.objects.all()

        # Les utilisateurs non-staff ne voient que leurs propres abonnements
        if not self.request.user.is_staff:
            queryset = queryset.filter(user=self.request.user)

        return queryset

    @action(detail=False, methods=['get'])
    def my_subscriptions(self, request):
        """Retourne les abonnements de l'utilisateur connecté"""
        user_abonnements = AbonnementUtilisateur.objects.filter(user=request.user)
        serializer = self.get_serializer(user_abonnements, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def active_subscriptions(self, request):
        """Retourne les abonnements actifs"""
        active_abonnements = self.get_queryset().filter(statut='active')
        serializer = self.get_serializer(active_abonnements, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Annule un abonnement"""
        abonnement_user = self.get_object()

        # Vérifier que l'utilisateur peut annuler cet abonnement
        if not request.user.is_staff and abonnement_user.user != request.user:
            return Response(
                {'error': 'Vous ne pouvez pas annuler cet abonnement'},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            # Logique d'annulation (à implémenter selon les besoins)
            abonnement_user.statut = 'cancelled'
            abonnement_user.auto_renouvellement = False
            abonnement_user.save()

            return Response({'message': 'Abonnement annulé avec succès'})
        except Exception as e:
            return Response(
                {'error': f'Erreur lors de l\'annulation: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )


class DescriptionViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion des descriptions via API REST"""

    queryset = Description.objects.all()
    permission_classes = [IsAuthenticatedOrReadOnly]
    serializer_class = DescriptionSerializer
    filter_backends = [filters.SearchFilter]
    search_fields = ['description']


# ============================================================================
# VUES DJANGO TRADITIONNELLES (EXISTANTES)
# ============================================================================

class AbonnementListView(View):
    """Vue pour afficher la liste des abonnements disponibles"""

    def get(self, request):
        abonnements = Abonnement.objects.filter(is_active=True).order_by('ordre_affichage', 'prix_ttc')

        context = {
            'abonnements': abonnements,
            'stripe_publishable_key': settings.STRIPE_PUBLISHABLE_KEY,
        }
        return render(request, 'abonnement/liste.html', context)


class AbonnementDetailView(View):
    """Vue pour afficher les détails d'un abonnement"""

    def get(self, request, abonnement_id):
        abonnement = get_object_or_404(Abonnement, id=abonnement_id, is_active=True)

        context = {
            'abonnement': abonnement,
            'features': abonnement.get_features_list(),
            'stripe_publishable_key': settings.STRIPE_PUBLISHABLE_KEY,
        }
        return render(request, 'abonnement/detail.html', context)


@method_decorator(login_required, name='dispatch')
class CreateSubscriptionView(View):
    """Vue pour créer un nouvel abonnement utilisateur"""

    def post(self, request):
        try:
            data = json.loads(request.body)
            abonnement_id = data.get('abonnement_id')
            payment_method_id = data.get('payment_method_id')

            if not abonnement_id:
                return JsonResponse({'error': 'ID abonnement requis'}, status=400)

            abonnement = get_object_or_404(Abonnement, id=abonnement_id, is_active=True)
            user = request.user

            # Vérifier si l'utilisateur a déjà un abonnement actif
            existing_subscription = AbonnementUtilisateur.objects.filter(
                user=user,
                statut='active'
            ).first()

            if existing_subscription:
                return JsonResponse({
                    'error': 'Vous avez déjà un abonnement actif'
                }, status=400)

            # Créer l'abonnement Stripe
            result = StripeService.create_subscription(
                user=user,
                abonnement=abonnement,
                payment_method_id=payment_method_id
            )

            if result:
                return JsonResponse({
                    'success': True,
                    'subscription_id': result['subscription_id'],
                    'status': result['status'],
                    'client_secret': result.get('client_secret'),
                })
            else:
                return JsonResponse({
                    'error': 'Erreur lors de la création de l\'abonnement'
                }, status=500)

        except json.JSONDecodeError:
            return JsonResponse({'error': 'Données JSON invalides'}, status=400)
        except Exception as e:
            logger.error(f"Erreur lors de la création de l'abonnement: {e}")
            return JsonResponse({'error': 'Erreur interne'}, status=500)


@method_decorator(login_required, name='dispatch')
class CancelSubscriptionView(View):
    """Vue pour annuler un abonnement utilisateur"""

    def post(self, request, subscription_id):
        try:
            # Vérifier que l'abonnement appartient à l'utilisateur
            abonnement_user = get_object_or_404(
                AbonnementUtilisateur,
                id=subscription_id,
                user=request.user,
                statut='active'
            )

            # Annuler l'abonnement Stripe
            success = StripeService.cancel_subscription(abonnement_user.stripe_subscription_id)

            if success:
                messages.success(request, 'Votre abonnement a été annulé avec succès.')
                return JsonResponse({'success': True})
            else:
                return JsonResponse({
                    'error': 'Erreur lors de l\'annulation de l\'abonnement'
                }, status=500)

        except Exception as e:
            logger.error(f"Erreur lors de l'annulation de l'abonnement: {e}")
            return JsonResponse({'error': 'Erreur interne'}, status=500)


@method_decorator(login_required, name='dispatch')
class UserSubscriptionsView(View):
    """Vue pour afficher les abonnements de l'utilisateur"""

    def get(self, request):
        abonnements = AbonnementUtilisateur.objects.filter(
            user=request.user
        ).order_by('-date_creation')

        paginator = Paginator(abonnements, 10)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context = {
            'abonnements': page_obj,
        }
        return render(request, 'abonnement/mes_abonnements.html', context)


@method_decorator(login_required, name='dispatch')
class UserInvoicesView(View):
    """Vue pour afficher les factures de l'utilisateur"""

    def get(self, request):
        factures = Facture.objects.filter(
            user=request.user
        ).order_by('-date_creation')

        paginator = Paginator(factures, 20)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context = {
            'factures': page_obj,
        }
        return render(request, 'abonnement/mes_factures.html', context)


@csrf_exempt
@require_POST
def stripe_webhook(request):
    """
    Endpoint pour recevoir les webhooks Stripe
    """
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')

    if not sig_header:
        logger.warning("Webhook Stripe reçu sans signature")
        return HttpResponse(status=400)

    # Traiter le webhook
    success = StripeWebhookService.handle_webhook(payload.decode('utf-8'), sig_header)

    if success:
        return HttpResponse(status=200)
    else:
        return HttpResponse(status=400)


def create_checkout_session(request):
    """
    Crée une session Stripe Checkout pour un abonnement
    """
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            abonnement_id = data.get('abonnement_id')

            if not abonnement_id:
                return JsonResponse({'error': 'ID abonnement requis'}, status=400)

            abonnement = get_object_or_404(Abonnement, id=abonnement_id, is_active=True)

            # S'assurer qu'un prix Stripe existe
            if not abonnement.stripe_price_id:
                price_id = StripeService.create_price(abonnement)
                if not price_id:
                    return JsonResponse({
                        'error': 'Erreur lors de la création du prix'
                    }, status=500)

            import stripe
            stripe.api_key = settings.STRIPE_SECRET_KEY

            # Créer la session Checkout
            checkout_session = stripe.checkout.Session.create(
                payment_method_types=['card'],
                line_items=[{
                    'price': abonnement.stripe_price_id,
                    'quantity': 1,
                }],
                mode='subscription',
                success_url=request.build_absolute_uri('/abonnement/success/'),
                cancel_url=request.build_absolute_uri('/abonnement/cancel/'),
                metadata={
                    'django_abonnement_id': str(abonnement.id),
                    'django_user_id': str(request.user.id) if request.user.is_authenticated else '',
                }
            )

            return JsonResponse({
                'checkout_url': checkout_session.url,
                'session_id': checkout_session.id,
            })

        except json.JSONDecodeError:
            return JsonResponse({'error': 'Données JSON invalides'}, status=400)
        except Exception as e:
            logger.error(f"Erreur lors de la création de la session Checkout: {e}")
            return JsonResponse({'error': 'Erreur interne'}, status=500)

    return JsonResponse({'error': 'Méthode non autorisée'}, status=405)


def subscription_success(request):
    """Page de succès après souscription"""
    return render(request, 'abonnement/success.html')


def subscription_cancel(request):
    """Page d'annulation de souscription"""
    return render(request, 'abonnement/cancel.html')


# API Views pour le back-office
@method_decorator(login_required, name='dispatch')
class SyncStripeView(View):
    """Vue pour synchroniser les données avec Stripe"""

    def post(self, request):
        if not request.user.is_staff:
            return JsonResponse({'error': 'Accès non autorisé'}, status=403)

        try:
            result = StripeService.sync_from_stripe()
            return JsonResponse({
                'success': True,
                'synced': result,
                'message': f"Synchronisation terminée: {result['products']} produits, {result['prices']} prix, {result['subscriptions']} abonnements"
            })
        except Exception as e:
            logger.error(f"Erreur lors de la synchronisation Stripe: {e}")
            return JsonResponse({'error': 'Erreur lors de la synchronisation'}, status=500)


@method_decorator(login_required, name='dispatch')
class CreateStripeProductView(View):
    """Vue pour créer un produit Stripe depuis un abonnement Django"""

    def post(self, request, abonnement_id):
        if not request.user.is_staff:
            return JsonResponse({'error': 'Accès non autorisé'}, status=403)

        try:
            abonnement = get_object_or_404(Abonnement, id=abonnement_id)

            # Créer le produit Stripe
            product_id = StripeService.create_product(abonnement)
            if product_id:
                # Créer le prix Stripe
                price_id = StripeService.create_price(abonnement)
                if price_id:
                    return JsonResponse({
                        'success': True,
                        'product_id': product_id,
                        'price_id': price_id,
                        'message': 'Produit et prix Stripe créés avec succès'
                    })
                else:
                    return JsonResponse({
                        'error': 'Produit créé mais erreur lors de la création du prix'
                    }, status=500)
            else:
                return JsonResponse({
                    'error': 'Erreur lors de la création du produit Stripe'
                }, status=500)

        except Exception as e:
            logger.error(f"Erreur lors de la création du produit Stripe: {e}")
            return JsonResponse({'error': 'Erreur interne'}, status=500)
