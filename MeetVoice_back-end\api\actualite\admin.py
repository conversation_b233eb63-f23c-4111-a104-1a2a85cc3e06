from import_export.admin import ImportExportModelAdmin
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import Actualite
from .forms import ActualiteForm


@admin.register(Actualite)
class ActualiteAdmin(ImportExportModelAdmin):
    """Interface d'administration complète pour les articles"""

    form = ActualiteForm

    # Configuration de la liste
    list_display = [
        'titre_with_status', 'auteur_info', 'theme',
        'status_badge', 'access_count', 'mis_en_avant_icon',
        'date_publication', 'actions_column'
    ]

    list_display_links = ['titre_with_status']

    list_filter = [
        'status', 'theme', 'mis_en_avant',
        'date_publication', 'date_modification', 'auteur'
    ]

    search_fields = [
        'titre', 'slug', 'contenu', 'petit_description',
        'auteur__username', 'auteur__first_name', 'auteur__last_name',
        'redacteur', 'tags'
    ]

    list_editable = []
    list_per_page = 25

    # Configuration des champs
    fieldsets = (
        ('Informations principales', {
            'fields': ('titre', 'slug', 'auteur', 'status')
        }),
        ('Contenu', {
            'fields': ('contenu', 'petit_description'),
            'classes': ('wide',)
        }),
        ('Catégorisation', {
            'fields': ('theme', 'tags'),
            'classes': ('collapse',)
        }),
        ('Médias et collaborateurs', {
            'fields': ('photo', 'redacteur', 'collaborateur'),
            'classes': ('collapse',)
        }),
        ('Options de publication', {
            'fields': ('mis_en_avant',),
            'classes': ('collapse',)
        }),
        ('Statistiques', {
            'fields': ('access_count', 'date_publication', 'date_modification'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['date_publication', 'date_modification', 'access_count']

    # Actions personnalisées
    actions = [
        'make_published', 'make_draft', 'make_archived',
        'toggle_featured', 'reset_access_count'
    ]

    def titre_with_status(self, obj):
        """Affiche le titre avec un indicateur de statut"""
        status_colors = {
            'draft': '#ffc107',
            'published': '#28a745',
            'archived': '#6c757d'
        }
        color = status_colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="color: {};">●</span> {}',
            color, obj.titre
        )
    titre_with_status.short_description = 'Titre'
    titre_with_status.admin_order_field = 'titre'

    def auteur_info(self, obj):
        """Affiche les informations de l'auteur"""
        if obj.auteur.first_name and obj.auteur.last_name:
            return f"{obj.auteur.first_name} {obj.auteur.last_name}"
        return obj.auteur.username
    auteur_info.short_description = 'Auteur'
    auteur_info.admin_order_field = 'auteur__username'

    def status_badge(self, obj):
        """Affiche le statut avec un badge coloré"""
        status_styles = {
            'draft': 'background-color: #ffc107; color: #212529;',
            'published': 'background-color: #28a745; color: white;',
            'archived': 'background-color: #6c757d; color: white;'
        }
        style = status_styles.get(obj.status, 'background-color: #6c757d; color: white;')
        return format_html(
            '<span style="padding: 3px 8px; border-radius: 3px; font-size: 11px; {}">{}</span>',
            style, obj.get_status_display()
        )
    status_badge.short_description = 'Statut'
    status_badge.admin_order_field = 'status'

    def mis_en_avant_icon(self, obj):
        """Affiche une icône pour les articles mis en avant"""
        if obj.mis_en_avant:
            return format_html('<span style="color: #ffc107;">⭐</span>')
        return format_html('<span style="color: #dee2e6;">☆</span>')
    mis_en_avant_icon.short_description = 'Mis en avant'
    mis_en_avant_icon.admin_order_field = 'mis_en_avant'

    def actions_column(self, obj):
        """Colonne d'actions rapides"""
        view_url = reverse('admin:actualite_actualite_change', args=[obj.pk])
        return format_html(
            '<a href="{}" class="button">Modifier</a>',
            view_url
        )
    actions_column.short_description = 'Actions'

    # Actions personnalisées
    def make_published(self, request, queryset):
        """Publier les articles sélectionnés"""
        updated = queryset.update(status='published')
        self.message_user(request, f'{updated} article(s) publié(s).')
    make_published.short_description = "Publier les articles sélectionnés"

    def make_draft(self, request, queryset):
        """Mettre en brouillon les articles sélectionnés"""
        updated = queryset.update(status='draft')
        self.message_user(request, f'{updated} article(s) mis en brouillon.')
    make_draft.short_description = "Mettre en brouillon les articles sélectionnés"

    def make_archived(self, request, queryset):
        """Archiver les articles sélectionnés"""
        updated = queryset.update(status='archived')
        self.message_user(request, f'{updated} article(s) archivé(s).')
    make_archived.short_description = "Archiver les articles sélectionnés"

    def toggle_featured(self, request, queryset):
        """Basculer le statut "mis en avant" des articles sélectionnés"""
        for article in queryset:
            article.mis_en_avant = not article.mis_en_avant
            article.save()
        self.message_user(request, f'Statut "mis en avant" basculé pour {queryset.count()} article(s).')
    toggle_featured.short_description = "Basculer le statut 'mis en avant'"

    def reset_access_count(self, request, queryset):
        """Remettre à zéro le compteur de vues"""
        updated = queryset.update(access_count=0)
        self.message_user(request, f'Compteur de vues remis à zéro pour {updated} article(s).')
    reset_access_count.short_description = "Remettre à zéro le compteur de vues"

    def save_model(self, request, obj, form, change):
        """Personnalise la sauvegarde du modèle"""
        if not change:  # Nouvel article
            obj.auteur = request.user
        super().save_model(request, obj, form, change)
