#!/usr/bin/env python
"""
Analyseur de pertinence de contenu pour site de rencontre
"""
import re
import random
from typing import Dict, List, Tuple, Any
from datetime import datetime, timedelta
import calendar

class DatingContentAnalyzer:
    """Analyseur de pertinence pour contenu de site de rencontre"""
    
    def __init__(self):
        self.dating_categories = self._load_dating_categories()
        self.seasonal_trends = self._load_seasonal_trends()
        self.audience_segments = self._load_audience_segments()
        self.trending_topics = self._load_trending_topics()
        self.seo_keywords = self._load_seo_keywords()
        
    def _load_dating_categories(self) -> Dict[str, Dict]:
        """Catégories de contenu pour site de rencontre"""
        return {
            'amical': {
                'weight': 0.3,
                'keywords': ['amitié', 'amical', 'copains', 'sortir', 'activités', 'groupe', 'social'],
                'topics': [
                    'Comment se faire des amis à l\'âge adulte',
                    'Activités pour rencontrer de nouvelles personnes',
                    'Sorties entre amis : idées originales',
                    'Créer un cercle social après un déménagement',
                    'Applications pour se faire des amis',
                    'Événements sociaux dans votre ville'
                ]
            },
            'amour': {
                'weight': 0.5,
                'keywords': ['amour', 'relation', 'couple', 'romantique', 'séduction', 'dating'],
                'topics': [
                    'Conseils pour un premier rendez-vous réussi',
                    'Comment créer un profil de rencontre attractif',
                    'Signes qu\'une relation devient sérieuse',
                    'Dating en ligne : éviter les pièges',
                    'Reconquérir son ex : bonne ou mauvaise idée',
                    'Communication dans le couple : clés du succès'
                ]
            },
            'libertin': {
                'weight': 0.2,
                'keywords': ['libertin', 'échangisme', 'club', 'soirée', 'lifestyle', 'ouvert'],
                'topics': [
                    'Guide du débutant dans le lifestyle libertin',
                    'Étiquette et règles des clubs libertins',
                    'Communication dans les relations ouvertes',
                    'Sécurité et santé dans le lifestyle',
                    'Différence entre échangisme et libertinage',
                    'Trouver des événements libertins près de chez vous'
                ]
            }
        }
    
    def _load_seasonal_trends(self) -> Dict[int, List[str]]:
        """Tendances saisonnières par mois"""
        return {
            1: ['résolutions', 'nouveau départ', 'célibataire', 'bilan'],  # Janvier
            2: ['saint-valentin', 'amour', 'romantique', 'couple'],  # Février
            3: ['printemps', 'renouveau', 'sortir', 'activités'],  # Mars
            4: ['pâques', 'famille', 'rencontres', 'sorties'],  # Avril
            5: ['mai', 'fête des mères', 'sorties', 'terrasses'],  # Mai
            6: ['été', 'vacances', 'festivals', 'rencontres'],  # Juin
            7: ['vacances', 'plage', 'festivals', 'liberté'],  # Juillet
            8: ['vacances', 'détente', 'nouvelles expériences'],  # Août
            9: ['rentrée', 'nouveaux projets', 'activités'],  # Septembre
            10: ['automne', 'cocooning', 'relations sérieuses'],  # Octobre
            11: ['automne', 'réflexion', 'bilan relationnel'],  # Novembre
            12: ['fêtes', 'famille', 'bilan année', 'résolutions']  # Décembre
        }
    
    def _load_audience_segments(self) -> Dict[str, Dict]:
        """Segments d'audience et leurs préférences"""
        return {
            '18-25': {
                'weight': 0.25,
                'interests': ['apps', 'réseaux sociaux', 'sorties', 'études', 'premier emploi'],
                'content_style': 'décontracté',
                'topics': ['dating apps', 'vie étudiante', 'premier appartement']
            },
            '26-35': {
                'weight': 0.35,
                'interests': ['carrière', 'stabilité', 'relation sérieuse', 'voyage'],
                'content_style': 'professionnel',
                'topics': ['équilibre vie pro/perso', 'relation à distance', 'achat immobilier']
            },
            '36-45': {
                'weight': 0.25,
                'interests': ['famille', 'divorce', 'nouveau départ', 'maturité'],
                'content_style': 'mature',
                'topics': ['rencontres après divorce', 'famille recomposée', 'maturité émotionnelle']
            },
            '45+': {
                'weight': 0.15,
                'interests': ['sagesse', 'expérience', 'qualité', 'authenticité'],
                'content_style': 'sage',
                'topics': ['amour mature', 'seconde chance', 'authenticité']
            }
        }
    
    def _load_trending_topics(self) -> List[Dict]:
        """Sujets tendance actuels"""
        return [
            {'topic': 'intelligence artificielle dating', 'trend_score': 95, 'category': 'tech'},
            {'topic': 'dating apps alternatives', 'trend_score': 88, 'category': 'apps'},
            {'topic': 'slow dating mouvement', 'trend_score': 82, 'category': 'tendance'},
            {'topic': 'rencontres post-covid', 'trend_score': 79, 'category': 'société'},
            {'topic': 'ghosting et breadcrumbing', 'trend_score': 85, 'category': 'comportement'},
            {'topic': 'polyamour et relations ouvertes', 'trend_score': 77, 'category': 'lifestyle'},
            {'topic': 'dating écologique et éthique', 'trend_score': 73, 'category': 'valeurs'},
            {'topic': 'rencontres intergénérationnelles', 'trend_score': 68, 'category': 'société'},
            {'topic': 'sécurité rencontres en ligne', 'trend_score': 91, 'category': 'sécurité'},
            {'topic': 'burnout relationnel', 'trend_score': 76, 'category': 'psychologie'}
        ]
    
    def _load_seo_keywords(self) -> Dict[str, int]:
        """Mots-clés SEO avec volume de recherche estimé"""
        return {
            'site de rencontre': 12000,
            'rencontre en ligne': 8500,
            'comment draguer': 6200,
            'premier rendez-vous': 5800,
            'profil de rencontre': 4900,
            'application rencontre': 7300,
            'rencontre sérieuse': 5100,
            'site de rencontre gratuit': 9200,
            'conseils séduction': 3800,
            'rencontre amicale': 2900,
            'club libertin': 1800,
            'échangisme débutant': 1200,
            'relation ouverte': 2100,
            'polyamour guide': 1500,
            'dating coach': 2800,
            'love coach': 2200
        }
    
    def analyze_content_relevance(self, topic: str, target_category: str = None) -> Dict[str, Any]:
        """Analyse la pertinence d'un sujet pour le site de rencontre"""
        
        analysis = {
            'topic': topic,
            'relevance_score': 0,
            'category_match': {},
            'seo_potential': 0,
            'trend_alignment': 0,
            'seasonal_relevance': 0,
            'audience_appeal': {},
            'recommendations': [],
            'suggested_angle': '',
            'estimated_traffic': 0
        }
        
        topic_lower = topic.lower()
        
        # 1. Analyse par catégorie
        for category, data in self.dating_categories.items():
            match_score = 0
            for keyword in data['keywords']:
                if keyword in topic_lower:
                    match_score += 10
            
            # Bonus si c'est la catégorie cible
            if target_category and category == target_category:
                match_score *= 1.5
            
            analysis['category_match'][category] = {
                'score': match_score,
                'weight': data['weight'],
                'weighted_score': match_score * data['weight']
            }
            
            analysis['relevance_score'] += match_score * data['weight']
        
        # 2. Potentiel SEO
        seo_score = 0
        matching_keywords = []
        for keyword, volume in self.seo_keywords.items():
            if any(word in topic_lower for word in keyword.split()):
                seo_score += volume / 1000  # Normaliser
                matching_keywords.append((keyword, volume))
                analysis['estimated_traffic'] += volume * 0.1  # 10% du volume estimé
        
        analysis['seo_potential'] = min(seo_score, 100)
        analysis['matching_keywords'] = matching_keywords
        
        # 3. Alignement avec les tendances
        trend_score = 0
        for trend in self.trending_topics:
            if any(word in topic_lower for word in trend['topic'].split()):
                trend_score = max(trend_score, trend['trend_score'])
        
        analysis['trend_alignment'] = trend_score
        
        # 4. Pertinence saisonnière
        current_month = datetime.now().month
        seasonal_keywords = self.seasonal_trends.get(current_month, [])
        seasonal_score = 0
        for keyword in seasonal_keywords:
            if keyword in topic_lower:
                seasonal_score += 20
        
        analysis['seasonal_relevance'] = min(seasonal_score, 100)
        
        # 5. Attrait par segment d'audience
        for segment, data in self.audience_segments.items():
            appeal_score = 0
            for interest in data['interests']:
                if interest in topic_lower:
                    appeal_score += 15
            
            analysis['audience_appeal'][segment] = {
                'score': appeal_score,
                'weight': data['weight'],
                'weighted_score': appeal_score * data['weight']
            }
        
        # 6. Score final
        final_score = (
            analysis['relevance_score'] * 0.3 +
            analysis['seo_potential'] * 0.25 +
            analysis['trend_alignment'] * 0.2 +
            analysis['seasonal_relevance'] * 0.15 +
            sum(seg['weighted_score'] for seg in analysis['audience_appeal'].values()) * 0.1
        )
        
        analysis['final_score'] = min(final_score, 100)
        
        # 7. Recommandations
        analysis['recommendations'] = self._generate_recommendations(analysis)
        analysis['suggested_angle'] = self._suggest_content_angle(topic, analysis)
        
        return analysis
    
    def _generate_recommendations(self, analysis: Dict) -> List[str]:
        """Génère des recommandations basées sur l'analyse"""
        recommendations = []
        
        if analysis['final_score'] < 30:
            recommendations.append("⚠️ Sujet peu pertinent pour votre audience")
            recommendations.append("💡 Considérez un angle plus spécifique aux rencontres")
        
        if analysis['seo_potential'] < 20:
            recommendations.append("📈 Potentiel SEO faible - ajoutez des mots-clés populaires")
        
        if analysis['trend_alignment'] < 50:
            recommendations.append("🔥 Sujet peu tendance - vérifiez les sujets actuels")
        
        if analysis['seasonal_relevance'] > 60:
            recommendations.append("🌟 Excellent timing saisonnier !")
        
        # Recommandations par catégorie
        best_category = max(analysis['category_match'].items(), key=lambda x: x[1]['weighted_score'])
        if best_category[1]['weighted_score'] > 15:
            recommendations.append(f"🎯 Angle {best_category[0]} recommandé")
        
        return recommendations
    
    def _suggest_content_angle(self, topic: str, analysis: Dict) -> str:
        """Suggère un angle de contenu optimisé"""
        best_category = max(analysis['category_match'].items(), key=lambda x: x[1]['weighted_score'])
        category_name = best_category[0]
        
        angles = {
            'amical': f"Guide pratique : {topic} pour créer des amitiés durables",
            'amour': f"Conseils experts : {topic} pour trouver l'amour véritable",
            'libertin': f"Découverte lifestyle : {topic} dans le monde libertin"
        }
        
        return angles.get(category_name, f"Guide complet : tout savoir sur {topic}")
    
    def suggest_trending_articles(self, count: int = 10, category: str = None) -> List[Dict]:
        """Suggère les articles les plus pertinents à créer basés sur TOUS les mots-clés"""

        suggestions = []

        # Importer les modèles Django pour accéder à la base de données
        try:
            from .models_keywords import SEOKeyword, TrendingTopic, SeasonalKeyword

            # Récupérer TOUS les mots-clés de la base de données
            all_topics = []

            # 1. MOTS-CLÉS SEO DE LA BASE DE DONNÉES
            seo_keywords = SEOKeyword.objects.filter(is_active=True)
            if category and category != 'all':
                seo_keywords = seo_keywords.filter(category=category)

            # Générer des sujets variés à partir des mots-clés
            for keyword_obj in seo_keywords:
                keyword = keyword_obj.keyword
                cat = keyword_obj.category

                # Générer plusieurs variations de sujets par mot-clé
                topic_templates = self._get_topic_templates(cat)

                for template in topic_templates:
                    topic = template.format(keyword=keyword)
                    all_topics.append((topic, cat, keyword_obj.weight))

            # 2. SUJETS TENDANCE DE LA BASE DE DONNÉES
            trending_topics = TrendingTopic.objects.filter(is_active=True)
            if category and category != 'all':
                trending_topics = trending_topics.filter(category=category)

            for trend in trending_topics:
                all_topics.append((trend.topic, trend.category, trend.trend_score / 20))  # Convertir en poids

            # 3. SUJETS SAISONNIERS
            current_month = datetime.now().month
            seasonal_keywords = SeasonalKeyword.objects.filter(month=current_month, is_active=True)

            for seasonal in seasonal_keywords:
                seasonal_topic = f"{seasonal.keyword} : guide complet 2024"
                all_topics.append((seasonal_topic, 'saisonnier', seasonal.weight))

            # 4. MÉLANGER ET DIVERSIFIER
            # Mélanger la liste pour éviter la répétition
            random.shuffle(all_topics)

            # Limiter à un nombre raisonnable pour l'analyse (performance)
            max_topics = min(len(all_topics), count * 3)  # 3x plus que demandé pour avoir du choix
            selected_topics = all_topics[:max_topics]

        except ImportError:
            # Fallback vers l'ancien système si problème d'import
            print("⚠️ Fallback vers sujets prédéfinis")
            selected_topics = self._get_fallback_topics(category)

        # Analyser chaque sujet sélectionné
        for topic_data in selected_topics:
            if len(topic_data) == 3:
                topic, source_category, weight = topic_data
            else:
                topic, source_category = topic_data
                weight = 1.0

            analysis = self.analyze_content_relevance(topic, category)

            # Booster le score selon le poids du mot-clé
            boosted_score = analysis['final_score'] * (1 + (weight - 1) * 0.1)  # +10% par point de poids
            boosted_score = min(boosted_score, 100)  # Plafonner à 100

            suggestions.append({
                'topic': topic,
                'score': boosted_score,
                'category': source_category,
                'seo_potential': analysis['seo_potential'],
                'trend_score': analysis['trend_alignment'],
                'estimated_traffic': analysis['estimated_traffic'] * weight,  # Booster le trafic
                'suggested_angle': analysis['suggested_angle'],
                'recommendations': analysis['recommendations'][:3],  # Top 3
                'keyword_weight': weight
            })

        # Trier par score et retourner le top avec diversité
        suggestions.sort(key=lambda x: x['score'], reverse=True)

        # Assurer la diversité des catégories
        diverse_suggestions = self._ensure_category_diversity(suggestions, count)

        return diverse_suggestions[:count]

    def _get_topic_templates(self, category: str) -> List[str]:
        """Retourne des templates de sujets variés selon la catégorie"""

        templates = {
            'amical': [
                "Comment {keyword} : guide complet 2024",
                "{keyword} : 10 conseils d'experts",
                "{keyword} après 30 ans : stratégies efficaces",
                "Réussir {keyword} : erreurs à éviter",
                "{keyword} en ligne vs réel : comparatif",
                "Applications pour {keyword} : top 2024",
                "{keyword} : témoignages et conseils",
                "Événements {keyword} : où participer",
                "{keyword} : psychologie et conseils"
            ],
            'amour': [
                "{keyword} : guide ultime 2024",
                "Réussir {keyword} : 15 conseils d'experts",
                "{keyword} : éviter les erreurs courantes",
                "{keyword} après une rupture : recommencer",
                "{keyword} en ligne : optimiser son profil",
                "{keyword} : psychologie de la séduction",
                "Premier rendez-vous {keyword} : réussir",
                "{keyword} : signes d'une relation sérieuse",
                "{keyword} : communication dans le couple",
                "{keyword} longue distance : maintenir l'amour"
            ],
            'libertin': [
                "{keyword} : guide du débutant 2024",
                "{keyword} : étiquette et règles",
                "Découvrir {keyword} : premiers pas",
                "{keyword} : communication dans le couple",
                "{keyword} : sécurité et respect",
                "Événements {keyword} : où commencer",
                "{keyword} : mythes et réalités",
                "{keyword} : témoignages de couples",
                "{keyword} : évolution du lifestyle"
            ],
            'general': [
                "{keyword} : révolution 2024",
                "{keyword} : tendances et innovations",
                "{keyword} : guide complet débutant",
                "{keyword} vs alternatives : comparatif",
                "{keyword} : avantages et inconvénients",
                "Futur de {keyword} : prédictions 2024",
                "{keyword} : statistiques et analyses",
                "{keyword} : conseils d'experts",
                "{keyword} : erreurs à éviter absolument"
            ]
        }

        return templates.get(category, templates['general'])

    def _get_fallback_topics(self, category: str) -> List[Tuple]:
        """Sujets de fallback si la base de données n'est pas accessible"""

        fallback = []

        # Sujets par catégorie (ancien système)
        for cat_name, cat_data in self.dating_categories.items():
            if not category or category == cat_name:
                for topic in cat_data['topics']:
                    fallback.append((topic, cat_name, 1.0))

        return fallback

    def _ensure_category_diversity(self, suggestions: List[Dict], count: int) -> List[Dict]:
        """Assure une diversité des catégories dans les suggestions"""

        if not suggestions:
            return suggestions

        # Grouper par catégorie
        by_category = {}
        for suggestion in suggestions:
            cat = suggestion['category']
            if cat not in by_category:
                by_category[cat] = []
            by_category[cat].append(suggestion)

        # Distribuer équitablement
        diverse_list = []
        categories = list(by_category.keys())
        max_per_category = max(1, count // len(categories))

        # Prendre le meilleur de chaque catégorie
        for category in categories:
            diverse_list.extend(by_category[category][:max_per_category])

        # Compléter avec les meilleurs restants
        remaining_slots = count - len(diverse_list)
        if remaining_slots > 0:
            used_topics = {s['topic'] for s in diverse_list}
            remaining = [s for s in suggestions if s['topic'] not in used_topics]
            diverse_list.extend(remaining[:remaining_slots])

        return diverse_list
