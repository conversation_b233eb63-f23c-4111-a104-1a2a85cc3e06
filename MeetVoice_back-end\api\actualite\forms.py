from django import forms
from .models import Actualite
from ckeditor.widgets import CKEditorWidget

class ActualiteForm(forms.ModelForm):
    """Formulaire pour créer et modifier des articles"""

    class Meta:
        model = Actualite
        fields = [
            'titre', 'theme', 'status',
            'contenu', 'petit_description', 'photo', 'tags',
            'redacteur', 'collaborateur', 'mis_en_avant'
        ]
        widgets = {
            'titre': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Titre de l\'article'
            }),
            'theme': forms.Select(attrs={'class': 'form-control'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'contenu': CKEditorWidget(attrs={'class': 'form-control'}),
            'petit_description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Description courte de l\'article'
            }),
            'photo': forms.FileInput(attrs={'class': 'form-control'}),
            'tags': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Tags séparés par des virgules'
            }),
            'redacteur': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom du rédacteur'
            }),
            'collaborateur': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'Collaborateurs (optionnel)'
            }),
            'mis_en_avant': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Rendre certains champs obligatoires
        self.fields['titre'].required = True
        self.fields['theme'].required = True
        self.fields['contenu'].required = True

        # Ajouter des classes CSS pour le styling
        for field_name, field in self.fields.items():
            if field_name != 'mis_en_avant':
                field.widget.attrs.update({'class': field.widget.attrs.get('class', '') + ' mb-3'})

    def clean_tags(self):
        """Nettoie et valide les tags"""
        tags = self.cleaned_data.get('tags', '')
        if tags:
            # Nettoie les tags et supprime les doublons
            tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
            return ', '.join(list(dict.fromkeys(tag_list)))  # Supprime les doublons
        return tags