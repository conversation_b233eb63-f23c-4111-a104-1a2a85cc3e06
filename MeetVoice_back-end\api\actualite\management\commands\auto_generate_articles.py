"""
Commande Django pour la génération automatique d'articles avec images
Usage: python manage.py auto_generate_articles --count=1 --author=admin
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import sys
import os

# Ajouter le répertoire parent au path pour importer le générateur
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from generate_article_with_image import ArticleWithImageGenerator

User = get_user_model()

class Command(BaseCommand):
    help = 'Génère automatiquement des articles avec images selon les thèmes prédéfinis'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=1,
            help='Nombre d\'articles à générer (défaut: 1)'
        )
        parser.add_argument(
            '--author',
            type=str,
            default='admin',
            help='Nom d\'utilisateur de l\'auteur (défaut: admin)'
        )
        parser.add_argument(
            '--publish',
            action='store_true',
            help='Publier les articles directement (sinon restent en brouillon)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Simulation sans création réelle d\'articles'
        )

    def handle(self, *args, **options):
        count = options['count']
        author = options['author']
        publish = options['publish']
        dry_run = options['dry_run']

        self.stdout.write(
            self.style.SUCCESS(f'🤖 Génération automatique de {count} article(s)')
        )

        if dry_run:
            self.stdout.write(
                self.style.WARNING('🧪 Mode simulation - Aucun article ne sera créé')
            )

        try:
            # Vérifier que l'auteur existe
            try:
                user = User.objects.get(username=author)
                self.stdout.write(f'👤 Auteur: {user.username}')
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f'⚠️  Utilisateur {author} non trouvé, création automatique...')
                )
                user = User.objects.create_user(
                    username=author,
                    email=f'{author}@meetvoice.com',
                    first_name='Générateur',
                    last_name='Automatique'
                )
                self.stdout.write(f'✅ Utilisateur créé: {user.username}')

            if dry_run:
                self.stdout.write('🎲 Thèmes qui seraient utilisés:')
                generator = ArticleWithImageGenerator()
                import random
                for i in range(count):
                    theme = random.choice(generator.themes_articles)
                    self.stdout.write(f'   {i+1}. {theme}')
                return

            # Initialiser le générateur
            generator = ArticleWithImageGenerator()
            
            # Générer les articles
            articles_crees = generator.generate_multiple_articles(count, author)
            
            # Publier si demandé
            if publish and articles_crees:
                for article in articles_crees:
                    article.status = 'published'
                    article.save()
                self.stdout.write(f'📢 {len(articles_crees)} article(s) publié(s) automatiquement')
            
            # Résumé
            self.stdout.write(
                self.style.SUCCESS(f'✅ Génération terminée!')
            )
            
            for i, article in enumerate(articles_crees, 1):
                self.stdout.write(f'   📝 Article {i}: {article.titre[:50]}...')
                self.stdout.write(f'      🏷️  Thème: {article.get_theme_display()}')
                self.stdout.write(f'      📊 Statut: {article.get_status_display()}')
                self.stdout.write(f'      🖼️ Image: {"Oui" if article.photo else "Non"}')
                self.stdout.write(f'      🔗 URL: http://127.0.0.1:8000/actualite/detail/{article.id}/')
            
            if articles_crees:
                self.stdout.write(f'🏢 Back-office: http://127.0.0.1:8000/backoffice/articles/')
            
            # Statistiques
            if len(articles_crees) != count:
                self.stdout.write(
                    self.style.WARNING(f'⚠️  {count - len(articles_crees)} article(s) ont échoué')
                )

        except Exception as e:
            raise CommandError(f'Erreur lors de la génération automatique: {e}')
