from django.core.management.base import BaseCommand
from actualite.models import Actualite
import html

class Command(BaseCommand):
    help = 'Corrige l\'echappement HTML dans les articles'

    def handle(self, *args, **options):
        self.stdout.write("Correction echappement HTML...")
        
        # Corriger l'article 34 specifiquement
        try:
            article = Actualite.objects.get(id=34)
            self.stdout.write(f"Article 34: {article.titre}")
            
            original = article.contenu
            self.stdout.write(f"Contenu original (100 chars): {original[:100]}")
            
            # Corriger
            fixed = html.unescape(original)
            fixed = fixed.replace('```html', '')
            fixed = fixed.replace('```', '')
            fixed = fixed.strip()
            
            if fixed != original:
                article.contenu = fixed
                article.save()
                self.stdout.write(self.style.SUCCESS("Article 34 corrige!"))
                self.stdout.write(f"Nouveau contenu (100 chars): {fixed[:100]}")
            else:
                self.stdout.write("Pas de changement necessaire pour article 34")
                
        except Actualite.DoesNotExist:
            self.stdout.write(self.style.ERROR("Article 34 non trouve"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Erreur article 34: {e}"))
        
        # Corriger tous les articles recents
        articles = Actualite.objects.all().order_by('-id')[:10]
        fixed_count = 0
        
        for article in articles:
            original = article.contenu
            
            if '```html' in original or '```' in original or '&quot;' in original:
                fixed = html.unescape(original)
                fixed = fixed.replace('```html', '')
                fixed = fixed.replace('```', '')
                fixed = fixed.strip()
                
                if fixed != original:
                    article.contenu = fixed
                    article.save()
                    fixed_count += 1
                    self.stdout.write(f"Article {article.id} corrige")
        
        self.stdout.write(self.style.SUCCESS(f"{fixed_count} articles corriges au total"))
        self.stdout.write("Testez: http://127.0.0.1:8000/actualite/detail/34/")
