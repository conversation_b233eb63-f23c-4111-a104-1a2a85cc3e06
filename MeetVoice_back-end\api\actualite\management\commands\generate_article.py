"""
Commande Django pour générer des articles avec l'API Gemini
Usage: python manage.py generate_article "sujet" --author=username
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
import sys
import os

# Ajouter le répertoire parent au path pour importer le générateur
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from generate_article import GeminiArticleGenerator

User = get_user_model()

class Command(BaseCommand):
    help = 'Génère un article automatiquement avec l\'API Gemini'

    def add_arguments(self, parser):
        parser.add_argument(
            'sujet',
            type=str,
            help='Sujet de l\'article à générer'
        )
        parser.add_argument(
            '--author',
            type=str,
            default='admin',
            help='Nom d\'utilisateur de l\'auteur (défaut: admin)'
        )
        parser.add_argument(
            '--publish',
            action='store_true',
            help='Publier l\'article directement (sinon reste en brouillon)'
        )
        parser.add_argument(
            '--theme',
            type=str,
            help='Forcer un thème spécifique'
        )

    def handle(self, *args, **options):
        sujet = options['sujet']
        author = options['author']
        publish = options['publish']
        force_theme = options.get('theme')

        self.stdout.write(
            self.style.SUCCESS(f'🤖 Génération d\'un article sur: {sujet}')
        )

        try:
            # Vérifier que l'auteur existe
            try:
                User.objects.get(username=author)
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f'⚠️  Utilisateur {author} non trouvé, création automatique...')
                )

            # Initialiser le générateur
            generator = GeminiArticleGenerator()
            
            # Générer l'article
            article = generator.generate_article(sujet, author)
            
            # Forcer le thème si spécifié
            if force_theme:
                from actualite.models import THEME_CHOICES
                themes = dict(THEME_CHOICES)
                if force_theme in themes:
                    article.theme = force_theme
                    article.save()
                    self.stdout.write(f'🏷️  Thème forcé: {themes[force_theme]}')
                else:
                    self.stdout.write(
                        self.style.WARNING(f'⚠️  Thème {force_theme} non valide, thème auto conservé')
                    )
            
            # Publier si demandé
            if publish:
                article.status = 'published'
                article.save()
                self.stdout.write('📢 Article publié automatiquement')
            
            self.stdout.write(
                self.style.SUCCESS(f'✅ Article créé avec succès!')
            )
            self.stdout.write(f'   📝 Titre: {article.titre}')
            self.stdout.write(f'   🏷️  Thème: {article.get_theme_display()}')
            self.stdout.write(f'   📊 Statut: {article.get_status_display()}')
            self.stdout.write(f'   🔗 URL: http://127.0.0.1:8000/actualite/detail/{article.id}/')
            self.stdout.write(f'   🏢 Back-office: http://127.0.0.1:8000/backoffice/articles/')

        except Exception as e:
            raise CommandError(f'Erreur lors de la génération: {e}')
