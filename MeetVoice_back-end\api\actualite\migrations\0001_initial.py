# Generated by Django 5.2.3 on 2025-06-17 02:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Actualite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('titre', models.CharField(max_length=200)),
                ('contenu', models.TextField()),
                ('date_publication', models.DateTimeField(auto_now_add=True)),
                ('categorie', models.CharField(choices=[('recruteur', 'Recruteur'), ('candidats', 'candidats')], max_length=50)),
                ('theme', models.CharField(choices=[('<PERSON><PERSON><PERSON><PERSON> son embauche', '<PERSON><PERSON><PERSON><PERSON> son embauche'), ('Décryptage', 'Décryptage'), ('Success stories', 'Success stories'), ('Tendances RH', 'Tendances RH'), ("J'aime l'IA", "J'aime l'IA"), ('Recruter autrement', 'Recruter autrement')], default=None, max_length=50)),
                ('access_count', models.IntegerField(default=0)),
                ('mis_en_avant', models.BooleanField(default=False)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='actualites_photos/')),
                ('redacteur', models.CharField(blank=True, max_length=200, null=True)),
                ('collaborateur', models.TextField(blank=True, null=True)),
                ('petit_description', models.CharField(blank=True, max_length=200, null=True)),
                ('auteur', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
