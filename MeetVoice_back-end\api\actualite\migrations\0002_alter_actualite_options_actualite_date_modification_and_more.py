# Generated by Django 5.2.3 on 2025-06-20 17:55

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('actualite', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='actualite',
            options={'ordering': ['-date_publication'], 'verbose_name': 'Article', 'verbose_name_plural': 'Articles'},
        ),
        migrations.AddField(
            model_name='actualite',
            name='date_modification',
            field=models.DateTimeField(auto_now=True, verbose_name='Date de modification'),
        ),
        migrations.AddField(
            model_name='actualite',
            name='sous_titre',
            field=models.CharField(blank=True, max_length=300, null=True, verbose_name='Sous-titre'),
        ),
        migrations.AddField(
            model_name='actualite',
            name='status',
            field=models.CharField(choices=[('draft', 'Brouillon'), ('published', 'Publié'), ('archived', 'Archivé')], default='draft', max_length=20, verbose_name='Statut'),
        ),
        migrations.AddField(
            model_name='actualite',
            name='tags',
            field=models.CharField(blank=True, help_text='Tags séparés par des virgules', max_length=500, null=True, verbose_name='Tags'),
        ),
        migrations.AlterField(
            model_name='actualite',
            name='access_count',
            field=models.IntegerField(default=0, verbose_name='Nombre de vues'),
        ),
        migrations.AlterField(
            model_name='actualite',
            name='auteur',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Auteur'),
        ),
        migrations.AlterField(
            model_name='actualite',
            name='categorie',
            field=models.CharField(choices=[('recruteur', 'Recruteur'), ('candidats', 'Candidats')], max_length=50, verbose_name='Catégorie'),
        ),
        migrations.AlterField(
            model_name='actualite',
            name='collaborateur',
            field=models.TextField(blank=True, null=True, verbose_name='Collaborateurs'),
        ),
        migrations.AlterField(
            model_name='actualite',
            name='contenu',
            field=models.TextField(verbose_name='Contenu'),
        ),
        migrations.AlterField(
            model_name='actualite',
            name='date_publication',
            field=models.DateTimeField(auto_now_add=True, verbose_name='Date de publication'),
        ),
        migrations.AlterField(
            model_name='actualite',
            name='mis_en_avant',
            field=models.BooleanField(default=False, verbose_name='Mis en avant'),
        ),
        migrations.AlterField(
            model_name='actualite',
            name='petit_description',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Description courte'),
        ),
        migrations.AlterField(
            model_name='actualite',
            name='photo',
            field=models.ImageField(blank=True, null=True, upload_to='actualites_photos/', verbose_name='Photo'),
        ),
        migrations.AlterField(
            model_name='actualite',
            name='redacteur',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Rédacteur'),
        ),
        migrations.AlterField(
            model_name='actualite',
            name='theme',
            field=models.CharField(choices=[('Réussir son embauche', 'Réussir son embauche'), ('Décryptage', 'Décryptage'), ('Success stories', 'Success stories'), ('Tendances RH', 'Tendances RH'), ("J'aime l'IA", "J'aime l'IA"), ('Recruter autrement', 'Recruter autrement')], default=None, max_length=50, verbose_name='Thème'),
        ),
        migrations.AlterField(
            model_name='actualite',
            name='titre',
            field=models.CharField(max_length=200, verbose_name='Titre'),
        ),
    ]
