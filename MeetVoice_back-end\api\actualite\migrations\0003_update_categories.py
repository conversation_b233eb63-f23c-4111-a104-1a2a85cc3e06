# Generated migration to update categories

from django.db import migrations

def update_categories_forward(apps, schema_editor):
    """Mise à jour des catégories existantes"""
    Actualite = apps.get_model('actualite', 'Actualite')
    
    # Mapping des anciennes vers les nouvelles catégories
    category_mapping = {
        'recruteur': 'films',
        'candidats': 'tendance'
    }
    
    # Mapping des anciens vers les nouveaux thèmes
    theme_mapping = {
        'Réussir son embauche': 'Découverte',
        'Décryptage': 'Analyse',
        'Success stories': 'Interview',
        'Tendances RH': 'Tendances',
        "J'aime l'IA": 'Actualité',
        'Recruter autrement': 'Critique'
    }
    
    # Mettre à jour les catégories
    for old_cat, new_cat in category_mapping.items():
        Actualite.objects.filter(categorie=old_cat).update(categorie=new_cat)
    
    # Mettre à jour les thèmes
    for old_theme, new_theme in theme_mapping.items():
        Actualite.objects.filter(theme=old_theme).update(theme=new_theme)

def update_categories_reverse(apps, schema_editor):
    """Retour en arrière des catégories"""
    Actualite = apps.get_model('actualite', 'Actualite')
    
    # Mapping inverse pour le retour en arrière
    category_mapping = {
        'films': 'recruteur',
        'tendance': 'candidats',
        'podcasts': 'recruteur',
        'lifestyle': 'candidats',
        'psychologie': 'candidats'
    }
    
    theme_mapping = {
        'Découverte': 'Réussir son embauche',
        'Analyse': 'Décryptage',
        'Interview': 'Success stories',
        'Tendances': 'Tendances RH',
        'Actualité': "J'aime l'IA",
        'Critique': 'Recruter autrement',
        'Bien-être': 'Décryptage',
        'Développement personnel': 'Réussir son embauche'
    }
    
    # Retour en arrière des catégories
    for new_cat, old_cat in category_mapping.items():
        Actualite.objects.filter(categorie=new_cat).update(categorie=old_cat)
    
    # Retour en arrière des thèmes
    for new_theme, old_theme in theme_mapping.items():
        Actualite.objects.filter(theme=new_theme).update(theme=old_theme)

class Migration(migrations.Migration):

    dependencies = [
        ('actualite', '0002_alter_actualite_options_actualite_date_modification_and_more'),
    ]

    operations = [
        migrations.RunPython(update_categories_forward, update_categories_reverse),
    ]
