# Generated by Django 5.2.3 on 2025-06-28 02:21

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('actualite', '0008_remove_image_url_field'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContentSuggestionConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Nom de la configuration')),
                ('relevance_weight', models.FloatField(default=0.3, help_text='Poids de la pertinence par catégorie (0.0-1.0)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)], verbose_name='Poids pertinence')),
                ('seo_weight', models.FloatField(default=0.25, help_text='Poids du potentiel SEO (0.0-1.0)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)], verbose_name='Poids SEO')),
                ('trend_weight', models.FloatField(default=0.2, help_text='Poids des tendances actuelles (0.0-1.0)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)], verbose_name='Poids tendance')),
                ('seasonal_weight', models.FloatField(default=0.15, help_text='Poids de la saisonnalité (0.0-1.0)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)], verbose_name='Poids saisonnier')),
                ('audience_weight', models.FloatField(default=0.1, help_text="Poids de l'attrait par audience (0.0-1.0)", validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)], verbose_name='Poids audience')),
                ('amical_category_weight', models.FloatField(default=0.3, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)], verbose_name='Poids catégorie amical')),
                ('amour_category_weight', models.FloatField(default=0.5, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)], verbose_name='Poids catégorie amour')),
                ('libertin_category_weight', models.FloatField(default=0.2, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)], verbose_name='Poids catégorie libertin')),
                ('is_active', models.BooleanField(default=True, verbose_name='Configuration active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Configuration suggestions',
                'verbose_name_plural': 'Configurations suggestions',
                'ordering': ['-is_active', 'name'],
            },
        ),
        migrations.CreateModel(
            name='TrendingTopic',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('topic', models.CharField(help_text="Sujet ou thème d'actualité", max_length=300, verbose_name='Sujet tendance')),
                ('trend_score', models.IntegerField(default=50, help_text='Score de popularité (0-100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='Score de tendance')),
                ('category', models.CharField(choices=[('amical', 'Amical'), ('amour', 'Amour'), ('libertin', 'Libertin'), ('general', 'Général')], default='general', max_length=20, verbose_name='Catégorie')),
                ('description', models.TextField(blank=True, help_text='Description du sujet tendance', verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Actif')),
                ('start_date', models.DateField(blank=True, help_text='Date de début de la tendance', null=True, verbose_name='Date de début')),
                ('end_date', models.DateField(blank=True, help_text='Date de fin de la tendance (optionnel)', null=True, verbose_name='Date de fin')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Sujet tendance',
                'verbose_name_plural': 'Sujets tendance',
                'ordering': ['-trend_score', 'topic'],
            },
        ),
        migrations.CreateModel(
            name='SeasonalKeyword',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('keyword', models.CharField(max_length=200, verbose_name='Mot-clé saisonnier')),
                ('month', models.IntegerField(choices=[(1, 'Janvier'), (2, 'Février'), (3, 'Mars'), (4, 'Avril'), (5, 'Mai'), (6, 'Juin'), (7, 'Juillet'), (8, 'Août'), (9, 'Septembre'), (10, 'Octobre'), (11, 'Novembre'), (12, 'Décembre')], verbose_name='Mois')),
                ('weight', models.FloatField(default=1.0, validators=[django.core.validators.MinValueValidator(0.1), django.core.validators.MaxValueValidator(5.0)], verbose_name='Poids saisonnier')),
                ('is_active', models.BooleanField(default=True, verbose_name='Actif')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Mot-clé saisonnier',
                'verbose_name_plural': 'Mots-clés saisonniers',
                'ordering': ['month', 'keyword'],
                'unique_together': {('keyword', 'month')},
            },
        ),
        migrations.CreateModel(
            name='SEOKeyword',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('keyword', models.CharField(help_text='Mot-clé ou expression de recherche', max_length=200, verbose_name='Mot-clé')),
                ('category', models.CharField(choices=[('amical', 'Amical'), ('amour', 'Amour'), ('libertin', 'Libertin'), ('general', 'Général')], default='general', help_text='Catégorie de rencontre associée', max_length=20, verbose_name='Catégorie')),
                ('search_volume', models.IntegerField(default=1000, help_text='Volume de recherche mensuel estimé', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100000)], verbose_name='Volume de recherche')),
                ('weight', models.FloatField(default=1.0, help_text='Importance du mot-clé (0.1 = faible, 10.0 = très important)', validators=[django.core.validators.MinValueValidator(0.1), django.core.validators.MaxValueValidator(10.0)], verbose_name='Poids')),
                ('is_active', models.BooleanField(default=True, help_text="Utiliser ce mot-clé dans l'analyse", verbose_name='Actif')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Mot-clé SEO',
                'verbose_name_plural': 'Mots-clés SEO',
                'ordering': ['-weight', 'category', 'keyword'],
                'unique_together': {('keyword', 'category')},
            },
        ),
    ]
