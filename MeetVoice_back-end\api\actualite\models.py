from django.db import models
from django.conf import settings
from django.urls import reverse
from django.utils.text import slugify
import uuid


# Catégories supprimées

THEME_CHOICES = [
        ('Critique', 'Critique'),
        ('Actualité', 'Actualité'),
        ('Interview', 'Interview'),
        ('Découverte', 'Découverte'),
        ('Bien-être', 'Bien-être'),
        ('Développement personnel', 'Développement personnel'),
        ('Analyse', 'Analyse'),
        ('Tendances', 'Tendances'),
    ]

STATUS_CHOICES = [
        ('draft', 'Brouillon'),
        ('published', 'Publié'),
        ('archived', 'Archivé'),
    ]

class Actualite(models.Model):
    # Informations principales
    titre = models.CharField(max_length=200, verbose_name="Titre")
    slug = models.SlugField(max_length=250, unique=True, verbose_name="Slug",
                           help_text="URL conviviale générée automatiquement")
    auteur = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name="Auteur")
    contenu = models.TextField(verbose_name="Contenu")
    petit_description = models.CharField(max_length=300, blank=True, null=True, verbose_name="Description courte",
                                        help_text="Description optimisée SEO (recommandé: 150-300 caractères)")

    # Métadonnées
    date_publication = models.DateTimeField(auto_now_add=True, verbose_name="Date de publication")
    date_modification = models.DateTimeField(auto_now=True, verbose_name="Date de modification")
    theme = models.CharField(max_length=50, choices=THEME_CHOICES, default=None, verbose_name="Thème")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="Statut")
    tags = models.CharField(max_length=500, blank=True, null=True, verbose_name="Tags",
                           help_text="Tags séparés par des virgules")

    # Statistiques et mise en avant
    access_count = models.IntegerField(default=0, verbose_name="Nombre de vues")
    mis_en_avant = models.BooleanField(default=False, verbose_name="Mis en avant")

    # Médias et collaborateurs
    photo = models.ImageField(upload_to='actualites_photos/', blank=True, null=True, verbose_name="Photo")
    redacteur = models.CharField(max_length=200, blank=True, null=True, verbose_name="Rédacteur")
    collaborateur = models.TextField(blank=True, null=True, verbose_name="Collaborateurs")

    class Meta:
        verbose_name = "Article"
        verbose_name_plural = "Articles"
        ordering = ['-date_publication']

    def __str__(self):
        return self.titre

    def get_absolute_url(self):
        return reverse('article_detail', kwargs={'pk': self.pk})

    def delete(self, *args, **kwargs):
        """Supprime l'article et ses images associées"""
        # Supprimer l'image du champ photo
        if self.photo:
            try:
                from django.core.files.storage import default_storage
                if default_storage.exists(self.photo.name):
                    default_storage.delete(self.photo.name)
                    print(f"✅ Image photo supprimée: {self.photo.name}")
            except Exception as e:
                print(f"⚠️ Erreur suppression image photo: {e}")



        # Appeler la méthode delete() parente
        super().delete(*args, **kwargs)

    def increment_access_count(self):
        """Incrémente le compteur de vues"""
        self.access_count += 1
        self.save(update_fields=['access_count'])

    def get_tags_list(self):
        """Retourne la liste des tags"""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
        return []

    def generate_unique_slug(self):
        """Génère un slug unique basé sur le titre"""
        base_slug = slugify(self.titre)
        if not base_slug:
            # Si le titre ne peut pas être slugifié (caractères spéciaux), utiliser un UUID
            base_slug = f"article-{uuid.uuid4().hex[:8]}"

        slug = base_slug
        counter = 1

        # Vérifier l'unicité et ajouter un suffixe si nécessaire
        while Actualite.objects.filter(slug=slug).exclude(pk=self.pk).exists():
            slug = f"{base_slug}-{counter}"
            counter += 1

        return slug

    def save(self, *args, **kwargs):
        """Sauvegarde avec génération automatique du slug"""
        if not self.slug:
            self.slug = self.generate_unique_slug()
        super().save(*args, **kwargs)

    def get_optimized_image_html(self, size='medium'):
        """Génère le HTML optimisé pour l'image avec formats modernes"""
        if not self.photo:
            return ""

        # Extraire le nom de base du fichier
        import os
        base_name = os.path.splitext(os.path.basename(str(self.photo)))[0]

        # Construire les URLs pour différents formats
        base_url = f"/media/articles/{base_name}"

        # Générer la balise picture avec fallbacks (formats modernes uniquement)
        picture_html = f"""
        <picture>
            <source srcset="{base_url}_{size}.avif" type="image/avif">
            <source srcset="{base_url}_{size}.webp" type="image/webp">
            <img src="{base_url}.webp" alt="{self.titre}" loading="lazy"
                 style="width: 100%; height: auto; object-fit: cover;">
        </picture>
        """

        return picture_html

    def get_responsive_image_srcset(self):
        """Génère les attributs srcset pour les images responsives"""
        if not self.photo:
            return ""

        import os
        base_name = os.path.splitext(os.path.basename(str(self.photo)))[0]
        base_url = f"/media/articles/{base_name}"

        # Générer le srcset pour les formats modernes
        srcset_webp = f"{base_url}_small.webp 400w, {base_url}_medium.webp 800w, {base_url}_large.webp 1200w"
        srcset_avif = f"{base_url}_small.avif 400w, {base_url}_medium.avif 800w, {base_url}_large.avif 1200w"

        return {
            'srcset_avif': srcset_avif,
            'srcset_webp': srcset_webp,
            'fallback': f"{base_url}.webp",
            'sizes': "(max-width: 400px) 400px, (max-width: 800px) 800px, 1200px"
        }

    @property
    def is_published(self):
        """Vérifie si l'article est publié"""
        return self.status == 'published'

    @property
    def reading_time(self):
        """Estime le temps de lecture en minutes (250 mots/minute)"""
        word_count = len(self.contenu.split())
        return max(1, round(word_count / 250))


# Importer les modèles de mots-clés
from .models_keywords import SEOKeyword, TrendingTopic, SeasonalKeyword, ContentSuggestionConfig
