#!/usr/bin/env python
"""
Modèles pour la gestion des mots-clés SEO configurables
"""
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator

class SEOKeyword(models.Model):
    """Mots-clés SEO configurables pour l'analyse de pertinence"""
    
    CATEGORY_CHOICES = [
        ('amical', 'Amical'),
        ('amour', 'Amour'),
        ('libertin', '<PERSON>bertin'),
        ('general', 'Général'),
    ]
    
    keyword = models.CharField(
        max_length=200,
        verbose_name="Mot-clé",
        help_text="Mot-clé ou expression de recherche"
    )
    
    category = models.CharField(
        max_length=20,
        choices=CATEGORY_CHOICES,
        default='general',
        verbose_name="Catégorie",
        help_text="Catégorie de rencontre associée"
    )
    
    search_volume = models.IntegerField(
        default=1000,
        validators=[MinValueValidator(0), MaxValueValidator(100000)],
        verbose_name="Volume de recherche",
        help_text="Volume de recherche mensuel estimé"
    )
    
    weight = models.FloatField(
        default=1.0,
        validators=[MinValueValidator(0.1), MaxValueValidator(10.0)],
        verbose_name="Poids",
        help_text="Importance du mot-clé (0.1 = faible, 10.0 = très important)"
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name="Actif",
        help_text="Utiliser ce mot-clé dans l'analyse"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Mot-clé SEO"
        verbose_name_plural = "Mots-clés SEO"
        ordering = ['-weight', 'category', 'keyword']
        unique_together = ['keyword', 'category']
    
    def __str__(self):
        return f"{self.keyword} ({self.get_category_display()}) - {self.search_volume}/mois"


class TrendingTopic(models.Model):
    """Sujets tendance configurables"""
    
    topic = models.CharField(
        max_length=300,
        verbose_name="Sujet tendance",
        help_text="Sujet ou thème d'actualité"
    )
    
    trend_score = models.IntegerField(
        default=50,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name="Score de tendance",
        help_text="Score de popularité (0-100)"
    )
    
    category = models.CharField(
        max_length=20,
        choices=SEOKeyword.CATEGORY_CHOICES,
        default='general',
        verbose_name="Catégorie"
    )
    
    description = models.TextField(
        blank=True,
        verbose_name="Description",
        help_text="Description du sujet tendance"
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name="Actif"
    )
    
    start_date = models.DateField(
        null=True, blank=True,
        verbose_name="Date de début",
        help_text="Date de début de la tendance"
    )
    
    end_date = models.DateField(
        null=True, blank=True,
        verbose_name="Date de fin",
        help_text="Date de fin de la tendance (optionnel)"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Sujet tendance"
        verbose_name_plural = "Sujets tendance"
        ordering = ['-trend_score', 'topic']
    
    def __str__(self):
        return f"{self.topic} (Score: {self.trend_score})"


class SeasonalKeyword(models.Model):
    """Mots-clés saisonniers configurables"""
    
    MONTH_CHOICES = [
        (1, 'Janvier'), (2, 'Février'), (3, 'Mars'), (4, 'Avril'),
        (5, 'Mai'), (6, 'Juin'), (7, 'Juillet'), (8, 'Août'),
        (9, 'Septembre'), (10, 'Octobre'), (11, 'Novembre'), (12, 'Décembre'),
    ]
    
    keyword = models.CharField(
        max_length=200,
        verbose_name="Mot-clé saisonnier"
    )
    
    month = models.IntegerField(
        choices=MONTH_CHOICES,
        verbose_name="Mois"
    )
    
    weight = models.FloatField(
        default=1.0,
        validators=[MinValueValidator(0.1), MaxValueValidator(5.0)],
        verbose_name="Poids saisonnier"
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name="Actif"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Mot-clé saisonnier"
        verbose_name_plural = "Mots-clés saisonniers"
        ordering = ['month', 'keyword']
        unique_together = ['keyword', 'month']
    
    def __str__(self):
        return f"{self.keyword} ({self.get_month_display()})"


class ContentSuggestionConfig(models.Model):
    """Configuration globale pour les suggestions de contenu"""
    
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name="Nom de la configuration"
    )
    
    # Poids des différents critères d'analyse
    relevance_weight = models.FloatField(
        default=0.3,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name="Poids pertinence",
        help_text="Poids de la pertinence par catégorie (0.0-1.0)"
    )
    
    seo_weight = models.FloatField(
        default=0.25,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name="Poids SEO",
        help_text="Poids du potentiel SEO (0.0-1.0)"
    )
    
    trend_weight = models.FloatField(
        default=0.2,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name="Poids tendance",
        help_text="Poids des tendances actuelles (0.0-1.0)"
    )
    
    seasonal_weight = models.FloatField(
        default=0.15,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name="Poids saisonnier",
        help_text="Poids de la saisonnalité (0.0-1.0)"
    )
    
    audience_weight = models.FloatField(
        default=0.1,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name="Poids audience",
        help_text="Poids de l'attrait par audience (0.0-1.0)"
    )
    
    # Poids des catégories
    amical_category_weight = models.FloatField(
        default=0.3,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name="Poids catégorie amical"
    )
    
    amour_category_weight = models.FloatField(
        default=0.5,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name="Poids catégorie amour"
    )
    
    libertin_category_weight = models.FloatField(
        default=0.2,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name="Poids catégorie libertin"
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name="Configuration active"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Configuration suggestions"
        verbose_name_plural = "Configurations suggestions"
        ordering = ['-is_active', 'name']
    
    def __str__(self):
        return f"{self.name} {'(Active)' if self.is_active else '(Inactive)'}"
    
    def save(self, *args, **kwargs):
        # S'assurer qu'une seule configuration est active
        if self.is_active:
            ContentSuggestionConfig.objects.filter(is_active=True).update(is_active=False)
        super().save(*args, **kwargs)
    
    @classmethod
    def get_active_config(cls):
        """Récupère la configuration active"""
        return cls.objects.filter(is_active=True).first()
    
    def get_weights_sum(self):
        """Vérifie que la somme des poids fait 1.0"""
        return (self.relevance_weight + self.seo_weight + self.trend_weight + 
                self.seasonal_weight + self.audience_weight)
    
    def get_category_weights_sum(self):
        """Vérifie que la somme des poids de catégories fait 1.0"""
        return (self.amical_category_weight + self.amour_category_weight + 
                self.libertin_category_weight)
