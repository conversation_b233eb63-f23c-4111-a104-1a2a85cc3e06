#!/usr/bin/env python
"""
Décorateurs de sécurité pour les APIs d'actualité
"""
from functools import wraps
from django.http import JsonResponse
from django.core.cache import cache
from django.conf import settings
import time
from datetime import datetime, timedelta
import hashlib

def get_client_ip(request):
    """Récupère l'IP réelle du client"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

def rate_limit_by_ip(max_requests=10, window_minutes=5):
    """
    Limite le nombre de requêtes par IP
    
    Args:
        max_requests: Nombre maximum de requêtes
        window_minutes: Fenêtre de temps en minutes
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            ip = get_client_ip(request)
            cache_key = f"rate_limit_ip_{ip}_{view_func.__name__}"
            
            # Récupérer le compteur actuel
            current_requests = cache.get(cache_key, 0)
            
            if current_requests >= max_requests:
                return JsonResponse({
                    'error': f'Trop de requêtes. Limite: {max_requests} requêtes par {window_minutes} minutes.',
                    'retry_after': window_minutes * 60
                }, status=429)
            
            # Incrémenter le compteur
            cache.set(cache_key, current_requests + 1, window_minutes * 60)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

def require_authentication(allow_staff_only=True):
    """
    Exige une authentification utilisateur
    
    Args:
        allow_staff_only: Si True, seuls les staff peuvent accéder
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return JsonResponse({
                    'error': 'Authentification requise',
                    'login_url': '/login/'
                }, status=401)
            
            if allow_staff_only and not request.user.is_staff:
                return JsonResponse({
                    'error': 'Accès réservé aux administrateurs',
                    'required_permission': 'staff'
                }, status=403)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

def require_user_or_ip_whitelist(whitelist_ips=None):
    """
    Permet l'accès soit aux utilisateurs connectés, soit aux IPs en whitelist
    
    Args:
        whitelist_ips: Liste des IPs autorisées (ex: ['127.0.0.1', '*************'])
    """
    if whitelist_ips is None:
        whitelist_ips = ['127.0.0.1', '::1']  # localhost par défaut
    
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            ip = get_client_ip(request)
            
            # Vérifier si l'utilisateur est connecté
            if request.user.is_authenticated:
                return view_func(request, *args, **kwargs)
            
            # Vérifier si l'IP est en whitelist
            if ip in whitelist_ips:
                return view_func(request, *args, **kwargs)
            
            return JsonResponse({
                'error': 'Accès non autorisé. Connectez-vous ou utilisez une IP autorisée.',
                'client_ip': ip,
                'login_url': '/login/'
            }, status=403)
        return wrapper
    return decorator

def api_key_required(header_name='X-API-Key'):
    """
    Exige une clé API dans les headers
    
    Args:
        header_name: Nom du header contenant la clé API
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            api_key = request.META.get(f'HTTP_{header_name.upper().replace("-", "_")}')
            
            # Clés API autorisées (à configurer dans settings.py)
            valid_api_keys = getattr(settings, 'VALID_API_KEYS', [])
            
            if not api_key:
                return JsonResponse({
                    'error': f'Clé API requise dans le header {header_name}',
                    'example': f'{header_name}: your-api-key-here'
                }, status=401)
            
            if api_key not in valid_api_keys:
                return JsonResponse({
                    'error': 'Clé API invalide',
                    'provided_key': api_key[:8] + '...' if len(api_key) > 8 else api_key
                }, status=403)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

def log_api_usage(log_details=True):
    """
    Log l'utilisation de l'API
    
    Args:
        log_details: Si True, log les détails de la requête
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            start_time = time.time()
            ip = get_client_ip(request)
            user = request.user if request.user.is_authenticated else 'Anonymous'
            
            try:
                response = view_func(request, *args, **kwargs)
                status_code = response.status_code
                success = True
            except Exception as e:
                status_code = 500
                success = False
                response = JsonResponse({'error': str(e)}, status=500)
            
            end_time = time.time()
            duration = round((end_time - start_time) * 1000, 2)  # en ms
            
            if log_details:
                print(f"🌐 API {view_func.__name__} | "
                      f"👤 {user} | "
                      f"🌍 {ip} | "
                      f"📊 {status_code} | "
                      f"⏱️ {duration}ms | "
                      f"{'✅' if success else '❌'}")
            
            # Optionnel: Sauvegarder en base de données
            # APIUsageLog.objects.create(...)
            
            return response
        return wrapper
    return decorator

def combined_security(
    max_requests_per_ip=20,
    window_minutes=10,
    require_auth=False,
    staff_only=False,
    whitelist_ips=None,
    require_api_key=False,
    log_usage=True
):
    """
    Décorateur combiné avec plusieurs niveaux de sécurité
    
    Args:
        max_requests_per_ip: Limite de requêtes par IP
        window_minutes: Fenêtre de temps pour la limite
        require_auth: Exiger une authentification
        staff_only: Réserver aux staff uniquement
        whitelist_ips: IPs autorisées sans authentification
        require_api_key: Exiger une clé API
        log_usage: Logger l'utilisation
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 1. Rate limiting par IP
            if max_requests_per_ip:
                ip = get_client_ip(request)
                cache_key = f"rate_limit_ip_{ip}_{view_func.__name__}"
                current_requests = cache.get(cache_key, 0)
                
                if current_requests >= max_requests_per_ip:
                    return JsonResponse({
                        'error': f'Trop de requêtes. Limite: {max_requests_per_ip} requêtes par {window_minutes} minutes.',
                        'retry_after': window_minutes * 60,
                        'client_ip': ip
                    }, status=429)
                
                cache.set(cache_key, current_requests + 1, window_minutes * 60)
            
            # 2. Vérification clé API
            if require_api_key:
                api_key = request.META.get('HTTP_X_API_KEY')
                valid_api_keys = getattr(settings, 'VALID_API_KEYS', [])
                
                if not api_key or api_key not in valid_api_keys:
                    return JsonResponse({
                        'error': 'Clé API requise ou invalide',
                        'header_required': 'X-API-Key'
                    }, status=401)
            
            # 3. Authentification utilisateur ou IP whitelist
            if require_auth:
                if not request.user.is_authenticated:
                    # Vérifier whitelist IP si définie
                    if whitelist_ips:
                        ip = get_client_ip(request)
                        if ip not in whitelist_ips:
                            return JsonResponse({
                                'error': 'Authentification requise ou IP non autorisée',
                                'client_ip': ip,
                                'login_url': '/login/'
                            }, status=401)
                    else:
                        return JsonResponse({
                            'error': 'Authentification requise',
                            'login_url': '/login/'
                        }, status=401)
                
                if staff_only and not request.user.is_staff:
                    return JsonResponse({
                        'error': 'Accès réservé aux administrateurs'
                    }, status=403)
            
            # 4. Logging
            if log_usage:
                start_time = time.time()
                ip = get_client_ip(request)
                user = request.user.username if request.user.is_authenticated else 'Anonymous'
                
                try:
                    response = view_func(request, *args, **kwargs)
                    duration = round((time.time() - start_time) * 1000, 2)
                    
                    print(f"🌐 {view_func.__name__} | "
                          f"👤 {user} | "
                          f"🌍 {ip} | "
                          f"📊 {response.status_code} | "
                          f"⏱️ {duration}ms")
                    
                    return response
                except Exception as e:
                    duration = round((time.time() - start_time) * 1000, 2)
                    print(f"❌ {view_func.__name__} | "
                          f"👤 {user} | "
                          f"🌍 {ip} | "
                          f"💥 ERROR: {str(e)} | "
                          f"⏱️ {duration}ms")
                    raise
            else:
                return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator

# Configurations prédéfinies pour différents niveaux de sécurité

def public_api(max_requests=50, window_minutes=15):
    """API publique avec rate limiting seulement"""
    return combined_security(
        max_requests_per_ip=max_requests,
        window_minutes=window_minutes,
        require_auth=False,
        log_usage=True
    )

def user_api(max_requests=100, window_minutes=10):
    """API pour utilisateurs connectés"""
    return combined_security(
        max_requests_per_ip=max_requests,
        window_minutes=window_minutes,
        require_auth=True,
        staff_only=False,
        log_usage=True
    )

def admin_api(max_requests=200, window_minutes=5):
    """API pour administrateurs uniquement"""
    return combined_security(
        max_requests_per_ip=max_requests,
        window_minutes=window_minutes,
        require_auth=True,
        staff_only=True,
        log_usage=True
    )

def backoffice_api(whitelist_ips=None):
    """API pour back-office (admin ou IP locale)"""
    if whitelist_ips is None:
        whitelist_ips = ['127.0.0.1', '::1', '***********/24']  # localhost + réseau local
    
    return combined_security(
        max_requests_per_ip=500,  # Limite élevée pour le back-office
        window_minutes=5,
        require_auth=False,  # Pas obligatoire si IP autorisée
        staff_only=False,
        whitelist_ips=whitelist_ips,
        log_usage=True
    )
