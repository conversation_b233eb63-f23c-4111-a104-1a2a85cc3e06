from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Actualite

User = get_user_model()


class AuthorSerializer(serializers.ModelSerializer):
    """Serializer pour les informations de l'auteur"""
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'username', 'nom', 'prenom', 'full_name']

    def get_full_name(self, obj):
        """Retourne le nom complet de l'auteur"""
        if hasattr(obj, 'nom') and hasattr(obj, 'prenom') and obj.nom and obj.prenom:
            return f"{obj.prenom} {obj.nom}"
        elif hasattr(obj, 'first_name') and hasattr(obj, 'last_name') and obj.first_name and obj.last_name:
            return f"{obj.first_name} {obj.last_name}"
        return obj.username


class ActualiteListSerializer(serializers.ModelSerializer):
    """Serializer pour la liste des articles (vue simplifiée)"""
    auteur = AuthorSerializer(read_only=True)
    tags_list = serializers.SerializerMethodField()
    reading_time = serializers.ReadOnlyField()
    is_published = serializers.ReadOnlyField()
    
    class Meta:
        model = Actualite
        fields = [
            'id', 'titre', 'slug', 'petit_description', 'auteur',
            'date_publication', 'date_modification', 'theme',
            'status', 'access_count', 'mis_en_avant', 'photo', 'tags', 'tags_list',
            'reading_time', 'is_published'
        ]
    
    def get_tags_list(self, obj):
        return obj.get_tags_list()


class ActualiteDetailSerializer(serializers.ModelSerializer):
    """Serializer pour le détail d'un article (vue complète)"""
    auteur = AuthorSerializer(read_only=True)
    tags_list = serializers.SerializerMethodField()
    reading_time = serializers.ReadOnlyField()
    is_published = serializers.ReadOnlyField()
    
    class Meta:
        model = Actualite
        fields = [
            'id', 'titre', 'slug', 'contenu', 'petit_description',
            'auteur', 'date_publication', 'date_modification',
            'theme', 'status', 'access_count', 'mis_en_avant', 'photo',
            'redacteur', 'collaborateur', 'tags', 'tags_list', 'reading_time',
            'is_published'
        ]
    
    def get_tags_list(self, obj):
        return obj.get_tags_list()


class ActualiteCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer pour créer et modifier des articles"""
    
    class Meta:
        model = Actualite
        fields = [
            'titre', 'slug', 'contenu', 'petit_description',
            'theme', 'status', 'photo', 'redacteur',
            'collaborateur', 'tags', 'mis_en_avant'
        ]
        extra_kwargs = {
            'titre': {'required': True},
            'contenu': {'required': True},
            'theme': {'required': True},
        }
    
    def validate_tags(self, value):
        """Valide et nettoie les tags"""
        if value:
            # Nettoie les tags et supprime les doublons
            tag_list = [tag.strip() for tag in value.split(',') if tag.strip()]
            return ', '.join(list(dict.fromkeys(tag_list)))
        return value
    
    def create(self, validated_data):
        """Crée un nouvel article"""
        # L'auteur sera défini dans la vue
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        """Met à jour un article existant"""
        return super().update(instance, validated_data)


class ActualiteStatsSerializer(serializers.ModelSerializer):
    """Serializer pour les statistiques des articles"""
    auteur_name = serializers.SerializerMethodField()
    tags_list = serializers.SerializerMethodField()
    
    class Meta:
        model = Actualite
        fields = [
            'id', 'titre', 'auteur_name', 'date_publication',
            'access_count', 'status', 'mis_en_avant', 'tags_list'
        ]
    
    def get_auteur_name(self, obj):
        """Retourne le nom complet de l'auteur"""
        if hasattr(obj.auteur, 'nom') and hasattr(obj.auteur, 'prenom') and obj.auteur.nom and obj.auteur.prenom:
            return f"{obj.auteur.prenom} {obj.auteur.nom}"
        elif hasattr(obj.auteur, 'first_name') and hasattr(obj.auteur, 'last_name') and obj.auteur.first_name and obj.auteur.last_name:
            return f"{obj.auteur.first_name} {obj.auteur.last_name}"
        return obj.auteur.username
    
    def get_tags_list(self, obj):
        return obj.get_tags_list()
