/* Styles spécifiques pour la gestion des articles */

/* Badges de statut */
.status-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

.status-draft {
  background-color: #ffc107;
  color: #212529;
}

.status-published {
  background-color: #28a745;
  color: white;
}

.status-archived {
  background-color: #6c757d;
  color: white;
}

/* Dropdown de statut interactif */
.status-dropdown {
  border: none;
  background: transparent;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  min-width: 100px;
}

.status-dropdown:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.status-dropdown.status-draft {
  background-color: #ffc107;
  color: #212529;
}

.status-dropdown.status-published {
  background-color: #28a745;
  color: white;
}

.status-dropdown.status-archived {
  background-color: #6c757d;
  color: white;
}

/* États de chargement */
.status-updating {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.status-updating::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  margin: -6px 0 0 -6px;
  border: 2px solid transparent;
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Animation de succès */
.status-success {
  animation: statusSuccess 0.5s ease;
}

@keyframes statusSuccess {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Formulaire d'article */
.article-form {
  max-height: 80vh;
  overflow-y: auto;
}

.article-form .form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.article-form .form-control,
.article-form .form-select {
  border-radius: 0.5rem;
  border: 1px solid #ced4da;
  transition: all 0.2s ease;
}

.article-form .form-control:focus,
.article-form .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Actions de table */
.table-actions {
  white-space: nowrap;
}

.table-actions .btn-group .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

/* Étoile mise en avant */
.featured-star {
  color: #ffc107;
  font-size: 1.2rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Statistiques */
.stats-card {
  transition: transform 0.2s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.border-left-primary {
  border-left: 4px solid #007bff !important;
}

.border-left-success {
  border-left: 4px solid #28a745 !important;
}

.border-left-warning {
  border-left: 4px solid #ffc107 !important;
}

.border-left-info {
  border-left: 4px solid #17a2b8 !important;
}

/* Filtres */
.filters-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: none;
}

.filters-card .form-control,
.filters-card .form-select {
  background-color: white;
  border: 1px solid #ced4da;
}

/* Messages de notification */
.notification-message {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  min-width: 300px;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.notification-message.show {
  transform: translateX(0);
}

/* Modal personnalisée */
.modal-content {
  border-radius: 0.75rem;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 0.75rem 0.75rem 0 0;
  border-bottom: none;
}

.modal-header .btn-close {
  filter: invert(1);
}

/* Prévisualisation d'image */
.image-preview {
  max-width: 200px;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Progress bar pour génération IA */
.generation-progress {
  background: linear-gradient(45deg, #667eea, #764ba2);
  background-size: 200% 200%;
  animation: gradientShift 2s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Spinner personnalisé */
.custom-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(102, 126, 234, 0.3);
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Tags */
.tag-input {
  position: relative;
}

.tag-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ced4da;
  border-top: none;
  border-radius: 0 0 0.5rem 0.5rem;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
}

.tag-suggestion {
  padding: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.tag-suggestion:hover {
  background-color: #f8f9fa;
}

/* Responsive pour articles */
@media (max-width: 768px) {
  .table-actions .btn-group {
    flex-direction: column;
  }
  
  .table-actions .btn {
    margin-bottom: 0.25rem;
  }
  
  .article-form {
    max-height: 60vh;
  }
  
  .stats-card {
    margin-bottom: 1rem;
  }
}
