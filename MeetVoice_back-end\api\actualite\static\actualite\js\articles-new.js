// JavaScript pour la gestion des articles - Version corrigée
console.log("🚀 Nouveau fichier JavaScript chargé - articles-new.js");

// Fonction pour afficher les messages
function showMessage(message, type = "info") {
  console.log(`📢 Message: ${message} (${type})`);
  
  // Créer l'élément de message
  const messageDiv = document.createElement("div");
  messageDiv.className = `alert alert-${type === "error" ? "danger" : type === "success" ? "success" : "info"} alert-dismissible fade show`;
  messageDiv.style.position = "fixed";
  messageDiv.style.top = "20px";
  messageDiv.style.right = "20px";
  messageDiv.style.zIndex = "9999";
  messageDiv.style.minWidth = "300px";
  
  messageDiv.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;
  
  document.body.appendChild(messageDiv);
  
  // Supprimer automatiquement après 5 secondes
  setTimeout(() => {
    if (messageDiv.parentNode) {
      messageDiv.parentNode.removeChild(messageDiv);
    }
  }, 5000);
}

// Fonction pour générer un article avec IA
function generateArticle() {
  console.log("🤖 Génération d'article avec IA...");

  // Support pour les deux templates (actualite et backoffice)
  const subjectInput = document.getElementById("articleSubject") || document.getElementById("generateSubject");
  const themeSelect = document.getElementById("articleTheme") || document.getElementById("generateTheme");
  const autoPublishCheckbox = document.getElementById("autoPublish");
  const customImageInput = document.getElementById("customImagePrompt");
  const generateBtn = document.getElementById("generateBtn");
  const generateForm = document.getElementById("generateForm");
  const generateProgress = document.getElementById("generateProgress");
  
  if (!subjectInput || !themeSelect) {
    showMessage("Erreur: Éléments du formulaire non trouvés", "error");
    return;
  }
  
  const subject = subjectInput.value.trim();
  const theme = themeSelect.value;
  const autoPublish = autoPublishCheckbox ? autoPublishCheckbox.checked : false;
  const customImagePrompt = customImageInput ? customImageInput.value.trim() : "";
  
  if (!subject) {
    showMessage("Veuillez saisir un sujet d'article", "error");
    return;
  }
  
  if (!theme) {
    showMessage("Veuillez sélectionner un thème", "error");
    return;
  }
  
  console.log(`📝 Génération: "${subject}" - Thème: ${theme} - Auto-publier: ${autoPublish} - Image custom: ${customImagePrompt ? 'Oui' : 'Non'}`);
  
  // Désactiver le bouton et afficher le progress
  if (generateBtn) generateBtn.disabled = true;
  if (generateForm) generateForm.style.display = "none";
  if (generateProgress) generateProgress.classList.remove("d-none");
  
  // Récupérer le token CSRF
  const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
                   document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
  
  if (!csrfToken) {
    showMessage("Erreur: Token CSRF non trouvé", "error");
    return;
  }
  
  // Faire l'appel API vers l'app actualite - URL CORRIGÉE
  console.log("🔗 URL utilisée: /actualite/api/generate-article/");
  fetch("/actualite/api/generate-article/", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-CSRFToken": csrfToken,
    },
    body: JSON.stringify({
      sujet: subject,
      auto_publish: autoPublish,
      image_prompt: customImagePrompt,
    }),
  })
    .then((response) => {
      console.log(`📡 Réponse génération: ${response.status}`);
      
      if (response.ok) {
        return response.json();
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    })
    .then((data) => {
      console.log("📄 Article généré:", data);
      
      // Masquer le progress
      if (generateProgress) generateProgress.classList.add("d-none");
      
      if (data && data.success) {
        // Afficher le résultat
        const resultTitle = document.getElementById("resultTitle");
        const resultTheme = document.getElementById("resultTheme");
        const resultViewLink = document.getElementById("resultViewLink");
        const generateResult = document.getElementById("generateResult");
        
        if (resultTitle && data.article && data.article.title) {
          resultTitle.textContent = data.article.title;
        }
        if (resultTheme && data.article && data.article.theme) {
          resultTheme.textContent = data.article.theme;
        }
        if (resultViewLink && data.article && data.article.id) {
          resultViewLink.href = `/actualite/detail/${data.article.id}/`;
        }
        if (generateResult) {
          generateResult.classList.remove("d-none");
        }
        
        showMessage("Article généré avec succès !", "success");
        
        // Recharger la page après 3 secondes
        setTimeout(() => location.reload(), 3000);
      } else {
        // Réafficher le formulaire
        if (generateForm) generateForm.style.display = "block";
        if (generateBtn) generateBtn.disabled = false;
        
        showMessage(data?.error || "Erreur lors de la génération", "error");
      }
    })
    .catch((error) => {
      console.error("❌ Erreur génération:", error);
      
      // Réafficher le formulaire
      if (generateProgress) generateProgress.classList.add("d-none");
      if (generateForm) generateForm.style.display = "block";
      if (generateBtn) generateBtn.disabled = false;
      
      showMessage(`Erreur lors de la génération: ${error.message}`, "error");
    });
}

// Fonction pour mettre à jour le statut d'un article
function updateArticleStatus(selectElement) {
  const articleId = selectElement.dataset.articleId;
  const newStatus = selectElement.value;
  
  console.log(`🔄 Mise à jour statut article ${articleId}: ${newStatus}`);
  
  const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
                   document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
  
  if (!csrfToken) {
    showMessage("Erreur: Token CSRF non trouvé", "error");
    return;
  }
  
  fetch(`/actualite/api/update-status/${articleId}/`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-CSRFToken": csrfToken,
    },
    body: JSON.stringify({
      status: newStatus,
    }),
  })
    .then((response) => {
      if (response.ok) {
        return response.json();
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    })
    .then((data) => {
      if (data.success) {
        showMessage("Statut mis à jour avec succès", "success");
      } else {
        showMessage(data.error || "Erreur lors de la mise à jour", "error");
      }
    })
    .catch((error) => {
      console.error("❌ Erreur mise à jour statut:", error);
      showMessage(`Erreur: ${error.message}`, "error");
    });
}

// Initialisation au chargement de la page
document.addEventListener("DOMContentLoaded", function () {
  console.log("✅ JavaScript Articles Back-office chargé (NOUVEAU FICHIER)");
  
  // Initialiser les dropdowns de statut
  const statusDropdowns = document.querySelectorAll(".status-dropdown");
  statusDropdowns.forEach((dropdown) => {
    dropdown.addEventListener("change", function () {
      updateArticleStatus(this);
    });
  });
  
  console.log(`📊 ${statusDropdowns.length} dropdowns de statut initialisés`);
});
