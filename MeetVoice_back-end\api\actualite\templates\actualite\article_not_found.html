{% extends 'base.html' %}
{% load static %}

{% block title %}Article non trouvé - MeetVoice{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 text-center">
            <div class="error-page">
                <h1 class="display-1 text-muted">404</h1>
                <h2 class="mb-4">Article non trouvé</h2>
                
                {% if error %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ error }}
                    </div>
                {% else %}
                    <p class="lead text-muted mb-4">
                        Désolé, l'article que vous recherchez n'existe pas ou a été supprimé.
                    </p>
                {% endif %}
                
                <div class="mt-4">
                    <a href="/backoffice/articles/" class="btn btn-primary me-3">
                        <i class="fas fa-newspaper"></i>
                        Voir tous les articles
                    </a>
                    <a href="/" class="btn btn-outline-secondary">
                        <i class="fas fa-home"></i>
                        Retour à l'accueil
                    </a>
                </div>
                
                <div class="mt-5">
                    <h4>Articles populaires</h4>
                    <div class="row mt-3">
                        <!-- Ici on pourrait afficher quelques articles populaires -->
                        <div class="col-12">
                            <p class="text-muted">
                                <a href="{% url 'actualite:afficher_actualites' %}">
                                    Découvrez nos derniers articles
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    padding: 2rem 0;
}

.error-page .display-1 {
    font-size: 8rem;
    font-weight: 300;
    opacity: 0.3;
}

.error-page h2 {
    color: #333;
    font-weight: 600;
}

.error-page .lead {
    font-size: 1.1rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
}

.btn i {
    margin-right: 0.5rem;
}
</style>
{% endblock %}
