{% extends 'base.html' %}
{% load static %}

{% block page_title_main %}Gestion des Articles{% endblock %}
{% block page_title_breadcrumb %}Gestion des Articles{% endblock %}
{% block page_title_header %}Gestion des Articles{% endblock %}
{% block page_icon %}<i class="fas fa-newspaper me-2"></i>{% endblock %}

{% block extra_css %}
<meta name="csrf-token" content="{{ csrf_token }}">
<link rel="stylesheet" href="{% static 'actualite/css/articles.css' %}">
{% endblock %}

{% block content %}
<!-- Navigation et actions -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <a href="{% url 'actualite:afficher_actualites' %}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i>Retour aux articles publics
        </a>
    </div>
    <div class="btn-group">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#articleModal" onclick="openCreateModal()">
            <i class="fas fa-plus me-2"></i>Nouvel Article
        </button>
        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#generateModal">
            <i class="fas fa-robot me-2"></i>Générer avec IA
        </button>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Articles</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ articles.count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-newspaper fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Publiés</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ published_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Brouillons</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ draft_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-edit fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Vues Totales</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_views }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-eye fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filtres et recherche -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Recherche</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ request.GET.search }}" placeholder="Titre, contenu...">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Statut</label>
                        <select class="form-control" id="status" name="status">
                            <option value="">Tous</option>
                            <option value="draft" {% if request.GET.status == 'draft' %}selected{% endif %}>Brouillon</option>
                            <option value="published" {% if request.GET.status == 'published' %}selected{% endif %}>Publié</option>
                            <option value="archived" {% if request.GET.status == 'archived' %}selected{% endif %}>Archivé</option>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label for="featured" class="form-label">Mis en avant</label>
                        <select class="form-control" id="featured" name="featured">
                            <option value="">Tous</option>
                            <option value="1" {% if request.GET.featured == '1' %}selected{% endif %}>Oui</option>
                            <option value="0" {% if request.GET.featured == '0' %}selected{% endif %}>Non</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Filtrer
                        </button>
                        <a href="{% url 'actualite:articles_manage' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Liste des articles -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    Articles ({{ articles.count }} résultat{{ articles.count|pluralize }})
                </h6>
                <div class="btn-group">
                    <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#articleModal" onclick="openCreateModal()">
                        <i class="fas fa-plus"></i> Nouvel Article
                    </button>
                    <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#generateModal">
                        <i class="fas fa-robot"></i> Générer avec IA
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if articles %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Titre</th>
                                    <th>Auteur</th>
                                    <th>Statut</th>
                                    <th>Thème</th>
                                    <th>Date Publication</th>
                                    <th>Vues</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for article in articles %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if article.mis_en_avant %}
                                                <i class="fas fa-star featured-star me-2" title="Mis en avant"></i>
                                            {% endif %}
                                            <div>
                                                <strong>{{ article.titre|truncatechars:50 }}</strong>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if article.auteur.first_name and article.auteur.last_name %}
                                            {{ article.auteur.first_name }} {{ article.auteur.last_name }}
                                        {% else %}
                                            {{ article.auteur.username }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <select class="status-dropdown status-{{ article.status }}"
                                                data-article-id="{{ article.id }}"
                                                data-current-status="{{ article.status }}">
                                            <option value="draft" {% if article.status == 'draft' %}selected{% endif %}>Brouillon</option>
                                            <option value="published" {% if article.status == 'published' %}selected{% endif %}>Publié</option>
                                            <option value="archived" {% if article.status == 'archived' %}selected{% endif %}>Archivé</option>
                                        </select>
                                    </td>

                                    <td>{{ article.theme }}</td>
                                    <td>{{ article.date_publication|date:"d/m/Y H:i" }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ article.access_count }}</span>
                                    </td>
                                    <td class="table-actions">
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary"
                                                    onclick="editArticle({{ article.id }})"
                                                    title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-info"
                                                    onclick="viewArticle({{ article.id }})"
                                                    title="Voir">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-warning"
                                                    onclick="toggleFeatured({{ article.id }})"
                                                    title="Basculer mise en avant">
                                                <i class="fas fa-star"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger"
                                                    onclick="deleteArticle({{ article.id }}, '{{ article.titre|escapejs }}')"
                                                    title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-newspaper fa-3x text-gray-300 mb-3"></i>
                        <p class="text-muted">Aucun article trouvé</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#articleModal" onclick="openCreateModal()">
                            <i class="fas fa-plus"></i> Créer le premier article
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal pour créer/modifier un article -->
<div class="modal fade" id="articleModal" tabindex="-1" aria-labelledby="articleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="articleModalLabel">Nouvel Article</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="articleForm" method="POST" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="modal-body article-form">
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Informations principales -->
                            <div class="mb-3">
                                <label for="titre" class="form-label">Titre *</label>
                                <input type="text" class="form-control" id="titre" name="titre" required>
                            </div>

                            <div class="mb-3">
                                <label for="petit_description" class="form-label">Description courte</label>
                                <textarea class="form-control" id="petit_description" name="petit_description" rows="3"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="contenu" class="form-label">Contenu *</label>
                                <textarea class="form-control" id="contenu" name="contenu" rows="10" required></textarea>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Métadonnées -->
                            <div class="mb-3">
                                <label for="status" class="form-label">Statut *</label>
                                <select class="form-control" id="status" name="status" required>
                                    <option value="draft">Brouillon</option>
                                    <option value="published">Publié</option>
                                    <option value="archived">Archivé</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="theme" class="form-label">Thème *</label>
                                <select class="form-control" id="theme" name="theme" required>
                                    <option value="">Sélectionner...</option>
                                    <option value="Critique">Critique</option>
                                    <option value="Actualité">Actualité</option>
                                    <option value="Interview">Interview</option>
                                    <option value="Découverte">Découverte</option>
                                    <option value="Bien-être">Bien-être</option>
                                    <option value="Développement personnel">Développement personnel</option>
                                    <option value="Analyse">Analyse</option>
                                    <option value="Tendances">Tendances</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="tags" class="form-label">Tags</label>
                                <input type="text" class="form-control" id="tags" name="tags"
                                       placeholder="Séparés par des virgules">
                                <small class="form-text text-muted">Ex: recrutement, IA, tendances</small>
                            </div>

                            <div class="mb-3">
                                <label for="photo" class="form-label">Photo</label>
                                <input type="file" class="form-control" id="photo" name="photo" accept="image/*">
                                <div id="currentPhoto" class="mt-2" style="display: none;">
                                    <img id="photoPreview" src="" alt="Photo actuelle" class="img-thumbnail" style="max-width: 200px;">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="redacteur" class="form-label">Rédacteur</label>
                                <input type="text" class="form-control" id="redacteur" name="redacteur">
                            </div>

                            <div class="mb-3">
                                <label for="collaborateur" class="form-label">Collaborateurs</label>
                                <textarea class="form-control" id="collaborateur" name="collaborateur" rows="2"></textarea>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="mis_en_avant" name="mis_en_avant">
                                <label class="form-check-label" for="mis_en_avant">
                                    Mettre en avant
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal pour générer un article avec IA -->
<div class="modal fade" id="generateModal" tabindex="-1" aria-labelledby="generateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="generateModalLabel">
                    <i class="fas fa-robot me-2"></i>Générer un article avec IA
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    L'IA Gemini va générer automatiquement un article complet basé sur le sujet que vous spécifiez.
                </div>

                <form id="generateForm">
                    <div class="mb-3">
                        <label for="generateSubject" class="form-label">Sujet de l'article *</label>
                        <input type="text" class="form-control" id="generateSubject" name="sujet" required
                               placeholder="Ex: Intelligence Artificielle, Développement personnel, Tendances 2024...">
                        <div class="form-text">Soyez spécifique pour obtenir un meilleur résultat</div>
                    </div>

                    <div class="mb-3">
                        <label for="generateTheme" class="form-label">Thème *</label>
                        <select class="form-control" id="generateTheme" name="theme" required>
                            <option value="">Sélectionner...</option>
                            <option value="Critique">Critique</option>
                            <option value="Actualité">Actualité</option>
                            <option value="Interview">Interview</option>
                            <option value="Découverte">Découverte</option>
                            <option value="Bien-être">Bien-être</option>
                            <option value="Développement personnel">Développement personnel</option>
                            <option value="Analyse">Analyse</option>
                            <option value="Tendances">Tendances</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="customImagePrompt" class="form-label">Prompt d'image personnalisé (optionnel)</label>
                        <textarea class="form-control" id="customImagePrompt" name="image_prompt" rows="3"
                                  placeholder="Ex: professional woman talking on phone, modern office, high quality..."></textarea>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Si laissé vide, l'image sera générée automatiquement basée sur le titre de l'article
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoPublish" name="auto_publish">
                            <label class="form-check-label" for="autoPublish">
                                Publier automatiquement (sinon reste en brouillon)
                            </label>
                        </div>
                    </div>
                </form>

                <div id="generateProgress" class="d-none">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Génération en cours...</span>
                        </div>
                        <p class="mt-2">Génération en cours... Cela peut prendre quelques secondes.</p>
                    </div>
                </div>

                <div id="generateResult" class="d-none">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>Article généré avec succès !</h6>
                        <p class="mb-2"><strong>Titre:</strong> <span id="resultTitle"></span></p>
                        <p class="mb-2"><strong>Thème:</strong> <span id="resultTheme"></span></p>
                        <p class="mb-0">
                            <a href="#" id="resultViewLink" target="_blank" class="btn btn-sm btn-outline-primary me-2">
                                <i class="fas fa-eye"></i> Voir l'article
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="fas fa-refresh"></i> Actualiser la liste
                            </button>
                        </p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-success" id="generateBtn" onclick="generateArticle()">
                    <i class="fas fa-robot me-2"></i>Générer l'article
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script src="{% static 'actualite/js/articles-new.js' %}"></script>
{% endblock %}
