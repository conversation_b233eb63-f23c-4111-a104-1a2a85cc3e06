<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ actualite.titre }} - Détail Article</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .article-header {
            background: linear-gradient(135deg, #2a1d34 0%, #4a3458 100%);
            color: white;
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
        }
        .article-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        .article-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }
        .article-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        .back-button {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }
        .back-button:hover {
            color: white;
            background-color: rgba(255,255,255,0.1);
            border-color: rgba(255,255,255,0.5);
        }
        .article-meta {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-top: -3rem;
            position: relative;
            z-index: 3;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .meta-group {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
        }
        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.95rem;
        }
        .meta-item i {
            width: 20px;
            text-align: center;
        }
        .badge-custom {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.85rem;
        }
        .badge-draft {
            background-color: #6c757d;
            color: white;
        }
        .badge-published {
            background-color: #198754;
            color: white;
        }
        .badge-archived {
            background-color: #fd7e14;
            color: white;
        }

        .article-content {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .article-text {
            line-height: 1.8;
            font-size: 1.1rem;
            color: #333;
        }
        .article-text h1, .article-text h2, .article-text h3, .article-text h4 {
            color: #2a1d34;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        .article-text p {
            margin-bottom: 1.5rem;
        }
        .tags-section {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #eee;
        }
        .tag {
            background: rgba(66, 190, 229, 0.1);
            color: #42BEE5;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.85rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            display: inline-block;
        }

        .article-image {
            max-width: 100%;
            height: auto;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin: 0 auto;
            display: block;
        }

        .image-container {
            position: relative;
        }

        .image-actions {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .image-container:hover .image-actions {
            opacity: 1;
        }

        /* Styles pour les images modernes */
        .modern-image-container {
            position: relative;
            overflow: hidden;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin: 0 auto;
            max-width: 100%;
        }

        .modern-image {
            display: block;
            width: 100%;
        }

        .format-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            z-index: 10;
        }

        .format-badge .badge {
            font-size: 0.75rem;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            background: linear-gradient(45deg, #28a745, #20c997) !important;
            color: white;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .format-badge .badge i {
            margin-right: 0.4rem;
        }

        /* Animation de chargement pour les images */
        .article-image {
            transition: transform 0.3s ease, opacity 0.3s ease;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        .article-image[src] {
            background: none;
            animation: none;
        }

        .modern-image-container:hover .article-image {
            transform: scale(1.02);
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Placeholder pour articles sans image */
        .no-image-placeholder {
            padding: 3rem 2rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
        }

        .no-image-placeholder:hover {
            border-color: #6c757d;
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        }

        /* Badges colorés selon le format */
        .format-badge .badge.bg-warning {
            background: linear-gradient(45deg, #ffc107, #fd7e14) !important;
        }

        .format-badge .badge.bg-primary {
            background: linear-gradient(45deg, #0d6efd, #6610f2) !important;
        }

        .format-badge .badge.bg-info {
            background: linear-gradient(45deg, #0dcaf0, #20c997) !important;
        }

        /* Responsive pour mobile */
        @media (max-width: 576px) {
            .format-badge {
                top: 10px;
                right: 10px;
            }

            .format-badge .badge {
                font-size: 0.65rem;
                padding: 0.3rem 0.6rem;
            }

            .no-image-placeholder {
                padding: 2rem 1rem;
            }
        }

        /* Styles pour les articles suggérés */
        .article-suggestion-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            border-radius: 15px;
            overflow: hidden;
        }

        .article-suggestion-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
        }

        .article-suggestion-card .card-img-top {
            transition: transform 0.3s ease;
        }

        .article-suggestion-card:hover .card-img-top {
            transform: scale(1.05);
        }

        .article-suggestion-card .badge {
            font-size: 0.75rem;
        }

        .article-suggestion-card .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            line-height: 1.3;
            color: #333;
        }

        .article-suggestion-card .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .article-suggestion-card .btn:hover {
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header de l'article -->
        <div class="article-header">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        {% if request.GET.from == 'backoffice' %}
                            <a href="/backoffice/articles/" class="back-button mb-3 d-inline-block">
                                <i class="fas fa-arrow-left me-2"></i>Retour à la gestion
                            </a>
                        {% else %}
                            <a href="/actualite/afficher/" class="back-button mb-3 d-inline-block">
                                <i class="fas fa-arrow-left me-2"></i>Retour aux articles
                            </a>
                        {% endif %}
                        <h1 class="article-title">{{ actualite.titre }}</h1>
                        {% if actualite.petit_description %}
                            <p class="article-subtitle">{{ actualite.petit_description }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <!-- Barre de statut horizontale -->
            <div class="article-status-bar">
                <div class="d-flex flex-wrap align-items-center gap-3 p-3" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 15px; border: 1px solid #dee2e6;">
                    <!-- Statut principal -->
                    <span class="badge badge-custom badge-{{ actualite.status }} fs-6">
                        {{ actualite.get_status_display }}
                    </span>

                    <!-- Auteur -->
                    <div class="d-flex align-items-center">
                        <i class="fas fa-user text-primary me-2"></i>
                        <span class="fw-medium">
                            {% if actualite.auteur.nom and actualite.auteur.prenom %}
                                {{ actualite.auteur.prenom }} {{ actualite.auteur.nom }}
                            {% else %}
                                {{ actualite.auteur.username }}
                            {% endif %}
                        </span>
                    </div>

                    <!-- Date -->
                    <div class="d-flex align-items-center">
                        <i class="fas fa-calendar text-info me-2"></i>
                        <span>{{ actualite.date_publication|date:"d M Y" }}</span>
                    </div>

                    <!-- Vues -->
                    <div class="d-flex align-items-center">
                        <i class="fas fa-eye text-success me-2"></i>
                        <span>{{ actualite.access_count }} vue{% if actualite.access_count > 1 %}s{% endif %}</span>
                    </div>

                    <!-- Temps de lecture -->
                    <div class="d-flex align-items-center">
                        <i class="fas fa-clock text-warning me-2"></i>
                        <span>{{ actualite.reading_time }} min</span>
                    </div>

                    <!-- Thème -->
                    <div class="d-flex align-items-center">
                        <i class="fas fa-tag text-secondary me-2"></i>
                        <span>{{ actualite.theme }}</span>
                    </div>

                    <!-- Mis en avant -->
                    {% if actualite.mis_en_avant %}
                        <span class="badge" style="background-color: #ffc107; color: #212529;">
                            <i class="fas fa-star me-1"></i>Mis en avant
                        </span>
                    {% endif %}

                    <!-- Actions admin -->
                    {% if request.user.is_staff %}
                        <div class="ms-auto d-flex gap-2">
                            <a href="{% url 'actualite:edit_actualite' actualite.id %}" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-edit me-1"></i>Modifier
                            </a>
                            {% if request.GET.from == 'backoffice' %}
                                <span class="text-muted small">Modifié le {{ actualite.date_modification|date:"d/m/Y" }}</span>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Image de l'article - SIMPLE ET EFFICACE -->
            {% if actualite.photo %}
                <div class="text-center mb-4">
                    <img src="{{ actualite.photo.url }}" alt="{{ actualite.titre }}" class="article-image" id="articleImage">

                    <!-- Bouton pour régénérer l'image (visible seulement pour les staff) -->
                    {% if request.user.is_staff %}
                        <div class="image-actions mt-3">
                            <button class="btn btn-sm btn-outline-primary" onclick="showImageRegenerateModal()">
                                <i class="fas fa-redo me-1"></i>Régénérer l'image
                            </button>
                        </div>
                    {% endif %}
                </div>
            {% endif %}

            <!-- Contenu principal -->
            <div class="article-content">
                <div class="article-text">{{ actualite.contenu|safe }}</div>

                <!-- Tags -->
                {% if actualite.tags %}
                    <div class="tags-section">
                        <h5><i class="fas fa-tags me-2"></i>Tags</h5>
                        {% for tag in actualite.get_tags_list %}
                            <span class="tag">#{{ tag }}</span>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Collaborateurs -->
                {% if actualite.collaborateur %}
                    <div class="mt-4 p-3" style="background-color: #f8f9fa; border-radius: 10px;">
                        <h5><i class="fas fa-users me-2"></i>Collaborateurs</h5>
                        <p class="mb-0">{{ actualite.collaborateur }}</p>
                    </div>
                {% endif %}
            </div>

            <!-- Actions -->
            <div class="text-center my-5">
                {% if request.GET.from == 'backoffice' %}
                    <a href="/backoffice/articles/" class="back-button">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la gestion
                    </a>
                {% else %}
                    <a href="/actualite/afficher/" class="back-button">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Section Articles Similaires -->
    {% if articles_similaires %}
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h3 class="mb-4">
                    <i class="fas fa-newspaper me-2 text-primary"></i>
                    Articles similaires
                </h3>
                <div class="row">
                    {% for article_similaire in articles_similaires %}
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 shadow-sm article-suggestion-card">
                            {% if article_similaire.photo %}
                            <img src="{{ article_similaire.photo.url }}" class="card-img-top" alt="{{ article_similaire.titre }}" style="height: 200px; object-fit: cover;">
                            {% endif %}
                            <div class="card-body d-flex flex-column">
                                <span class="badge bg-primary mb-2 align-self-start">{{ article_similaire.theme }}</span>
                                <h5 class="card-title">{{ article_similaire.titre }}</h5>
                                {% if article_similaire.petit_description %}
                                <p class="card-text text-muted">{{ article_similaire.petit_description|truncatewords:15 }}</p>
                                {% endif %}
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ article_similaire.date_publication|date:"d/m/Y" }}
                                        </small>
                                        <small class="text-muted">
                                            <i class="fas fa-eye me-1"></i>
                                            {{ article_similaire.access_count }} vue{% if article_similaire.access_count > 1 %}s{% endif %}
                                        </small>
                                    </div>
                                    <a href="{% url 'actualite:detail_actualite_by_slug' article_similaire.slug %}" class="btn btn-outline-primary btn-sm w-100">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        Lire l'article
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Modal pour régénérer l'image -->
    <div class="modal fade" id="regenerateImageModal" tabindex="-1" aria-labelledby="regenerateImageModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="regenerateImageModalLabel">
                        <i class="fas fa-image me-2"></i>Régénérer l'image
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="imagePrompt" class="form-label">Nouveau prompt pour l'image</label>
                        <textarea class="form-control" id="imagePrompt" rows="3"
                                  placeholder="Ex: Une femme souriante dans un café moderne, lumière naturelle, style professionnel..."></textarea>
                        <div class="form-text">
                            Décrivez précisément l'image que vous souhaitez générer. Plus le prompt est détaillé, meilleur sera le résultat.
                        </div>
                    </div>

                    <!-- Aperçu de l'image actuelle -->
                    {% if actualite.photo %}
                    <div class="mb-3">
                        <label class="form-label">Image actuelle :</label>
                        <div class="text-center">
                            <img src="{{ actualite.photo.url }}" alt="Image actuelle" class="img-thumbnail" style="max-height: 150px;">
                        </div>
                    </div>
                    {% endif %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" id="regenerateImageBtn" onclick="regenerateImage()">
                        <i class="fas fa-magic me-1"></i>Générer nouvelle image
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de progression -->
    <div class="modal fade" id="regenerateProgressModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-body text-center p-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Génération en cours...</span>
                    </div>
                    <h6>Génération de la nouvelle image...</h6>
                    <p class="text-muted mb-0">Veuillez patienter quelques secondes</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Incrémenter le compteur de vues
        fetch(`/actualite/api/articles/{{ actualite.id }}/increment_views/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}',
            },
        }).catch(error => {
            console.log('Erreur lors de l\'incrémentation des vues:', error);
        });

        // Fonctions pour la régénération d'image
        function showImageRegenerateModal() {
            const modal = new bootstrap.Modal(document.getElementById('regenerateImageModal'));
            modal.show();
        }

        async function regenerateImage() {
            const promptInput = document.getElementById('imagePrompt');
            const prompt = promptInput.value.trim();

            if (!prompt) {
                alert('Veuillez saisir un prompt pour l\'image');
                return;
            }

            // Fermer le modal principal et afficher le modal de progression
            const mainModal = bootstrap.Modal.getInstance(document.getElementById('regenerateImageModal'));
            mainModal.hide();

            const progressModal = new bootstrap.Modal(document.getElementById('regenerateProgressModal'));
            progressModal.show();

            try {
                const response = await fetch(`/actualite/api/regenerate-image/{{ actualite.id }}/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token }}',
                    },
                    body: JSON.stringify({
                        prompt: prompt
                    })
                });

                const data = await response.json();

                // Fermer le modal de progression
                progressModal.hide();

                if (data.success) {
                    // Mettre à jour l'image sur la page
                    const articleImage = document.getElementById('articleImage');
                    if (articleImage) {
                        // Ajouter un timestamp pour éviter le cache
                        articleImage.src = data.new_photo_url + '?t=' + Date.now();
                    }

                    // Afficher un message de succès
                    showMessage('Image régénérée avec succès !', 'success');

                    // Vider le champ prompt
                    promptInput.value = '';

                } else {
                    showMessage('Erreur lors de la génération : ' + data.error, 'error');
                }

            } catch (error) {
                progressModal.hide();
                console.error('Erreur:', error);
                showMessage('Erreur technique lors de la génération', 'error');
            }
        }

        function showMessage(message, type) {
            // Créer une notification toast
            const toastContainer = document.getElementById('toastContainer') || createToastContainer();

            const toastId = 'toast-' + Date.now();
            const bgClass = type === 'success' ? 'bg-success' : 'bg-danger';

            const toastHTML = `
                <div id="${toastId}" class="toast ${bgClass} text-white" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                        ${message}
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHTML);

            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { delay: 4000 });
            toast.show();

            // Supprimer le toast après fermeture
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }

        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
            return container;
        }
    </script>
</body>
</html>