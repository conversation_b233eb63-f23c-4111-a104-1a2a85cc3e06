<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ actualite.titre }} - Détail Article</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .article-header {
            background: linear-gradient(135deg, #2A1D34 0%, #4a3458 100%);
            color: white;
            padding: 3rem 0;
        }
        .article-meta {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: -2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .article-content {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-top: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            line-height: 1.8;
        }
        .badge-custom {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        /* Nouvelles classes pour les catégories */
        .badge-films {
            background-color: #e74c3c;
            color: white;
        }
        .badge-tendance {
            background-color: #f39c12;
            color: white;
        }
        .badge-podcasts {
            background-color: #9b59b6;
            color: white;
        }
        .badge-lifestyle {
            background-color: #1abc9c;
            color: white;
        }
        .badge-psychologie {
            background-color: #3498db;
            color: white;
        }
        /* Classes pour les statuts */
        .badge-published {
            background-color: #28a745;
            color: white;
        }
        .badge-draft {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-archived {
            background-color: #6c757d;
            color: white;
        }
        .article-image {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            margin: 2rem 0;
        }
        .back-button {
            background: linear-gradient(45deg, #42BEE5, #D477EB);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            text-decoration: none;
            transition: transform 0.3s ease;
        }
        .back-button:hover {
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        .stats-section {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
            margin: 1rem 0;
        }
        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #6c757d;
        }
        .tags-section {
            margin-top: 2rem;
        }
        .tag {
            background: rgba(66, 190, 229, 0.1);
            color: #42BEE5;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.85rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header de l'article -->
        <div class="article-header">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <a href="/backoffice/articles/" class="back-button mb-3 d-inline-block" onclick="goBack(event)">
                            <i class="fas fa-arrow-left me-2"></i>Retour aux articles
                        </a>
                        <h1 class="display-4 mb-3">{{ actualite.titre }}</h1>
                        {% if actualite.sous_titre %}
                            <h2 class="h4 opacity-75">{{ actualite.sous_titre }}</h2>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <!-- Métadonnées de l'article -->
            <div class="article-meta">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex flex-wrap gap-2 mb-3">
                            <span class="badge badge-custom badge-{{ actualite.categorie }}">
                                {{ actualite.get_categorie_display }}
                            </span>
                            <span class="badge badge-custom badge-{{ actualite.status }}">
                                {{ actualite.get_status_display }}
                            </span>
                            {% if actualite.mis_en_avant %}
                                <span class="badge badge-custom" style="background-color: #ffc107; color: #212529;">
                                    <i class="fas fa-star me-1"></i>Mis en avant
                                </span>
                            {% endif %}
                        </div>
                        
                        <div class="stats-section">
                            <div class="stat-item">
                                <i class="fas fa-user"></i>
                                <span>
                                    {% if actualite.auteur.first_name and actualite.auteur.last_name %}
                                        {{ actualite.auteur.first_name }} {{ actualite.auteur.last_name }}
                                    {% else %}
                                        {{ actualite.auteur.username }}
                                    {% endif %}
                                </span>
                            </div>
                            
                            <div class="stat-item">
                                <i class="fas fa-calendar"></i>
                                <span>{{ actualite.date_publication|date:"d F Y à H:i" }}</span>
                            </div>
                            
                            <div class="stat-item">
                                <i class="fas fa-eye"></i>
                                <span>{{ actualite.access_count }} vue{{ actualite.access_count|pluralize }}</span>
                            </div>
                            
                            <div class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span>{{ actualite.reading_time }} min de lecture</span>
                            </div>
                            
                            <div class="stat-item">
                                <i class="fas fa-tag"></i>
                                <span>{{ actualite.theme }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 text-md-end">
                        {% if actualite.redacteur %}
                            <div class="stat-item justify-content-md-end">
                                <i class="fas fa-pen"></i>
                                <span>Rédacteur: {{ actualite.redacteur }}</span>
                            </div>
                        {% endif %}
                        
                        {% if actualite.date_modification != actualite.date_publication %}
                            <div class="stat-item justify-content-md-end">
                                <i class="fas fa-edit"></i>
                                <span>Modifié le {{ actualite.date_modification|date:"d/m/Y" }}</span>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Description courte -->
            {% if actualite.petit_description %}
                <div class="article-content">
                    <div class="lead text-muted">
                        {{ actualite.petit_description }}
                    </div>
                </div>
            {% endif %}

            <!-- Image de l'article -->
            {% if actualite.photo %}
                <div class="text-center">
                    <img src="{{ actualite.photo.url }}" alt="{{ actualite.titre }}" class="article-image">
                </div>
            {% endif %}

            <!-- Contenu principal -->
            <div class="article-content">
                <div class="article-text">
                    {{ actualite.contenu|safe }}
                </div>

                <!-- Tags -->
                {% if actualite.tags %}
                    <div class="tags-section">
                        <h5><i class="fas fa-tags me-2"></i>Tags</h5>
                        {% for tag in actualite.get_tags_list %}
                            <span class="tag">#{{ tag }}</span>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Collaborateurs -->
                {% if actualite.collaborateur %}
                    <div class="mt-4 p-3" style="background-color: #f8f9fa; border-radius: 10px;">
                        <h5><i class="fas fa-users me-2"></i>Collaborateurs</h5>
                        <p class="mb-0">{{ actualite.collaborateur }}</p>
                    </div>
                {% endif %}
            </div>

            <!-- Actions -->
            <div class="text-center my-5">
                <a href="/backoffice/articles/" class="back-button" onclick="goBack(event)">
                    <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Fonction pour revenir en arrière intelligemment
        function goBack(event) {
            event.preventDefault();
            
            // Vérifier si on peut revenir en arrière dans l'historique
            if (window.history.length > 1 && document.referrer) {
                // Si on vient d'une autre page du même site, revenir en arrière
                if (document.referrer.includes(window.location.hostname)) {
                    window.history.back();
                    return;
                }
            }
            
            // Sinon, rediriger vers la liste des articles
            window.location.href = '/backoffice/articles/';
        }

        // Incrémenter le compteur de vues
        fetch(`/actualite/api/articles/{{ actualite.id }}/increment_views/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).catch(error => {
            console.log('Erreur lors de l\'incrémentation des vues:', error);
        });
    </script>
</body>
</html>
