{% extends 'backoffice/base.html' %}
{% load static %}

{% block title %}Modifier l'article - {{ actualite.titre }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'backoffice/css/articles.css' %}">
<style>
.edit-form {
    background: white;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    padding: 30px;
    margin: 20px 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    border: 2px solid #e3e6f0;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.btn-save {
    background: linear-gradient(45deg, #4e73df, #224abe);
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(78, 115, 223, 0.4);
}

.btn-cancel {
    background: #6c757d;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background: #5a6268;
    color: white;
    text-decoration: none;
}

.current-image {
    max-width: 300px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.status-badges {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.status-badge.draft {
    background: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.status-badge.published {
    background: #d4edda;
    color: #155724;
    border-color: #00b894;
}



.status-badge.active {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.image-preview {
    margin-top: 15px;
}

.regenerate-btn {
    background: #17a2b8;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    color: white;
    font-size: 12px;
    margin-top: 10px;
}
</style>
{% endblock %}

{% block backoffice_content %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-edit text-primary me-2"></i>
                Modifier l'article
            </h1>
            <p class="text-muted">{{ actualite.titre }}</p>
        </div>
        <div>
            <a href="{% url 'backoffice:articles' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour aux articles
            </a>
        </div>
    </div>

    <!-- Formulaire d'édition -->
    <div class="edit-form">
        <form method="post" enctype="multipart/form-data" id="editForm">
            {% csrf_token %}
            
            <div class="row">
                <!-- Colonne principale -->
                <div class="col-lg-8">
                    <!-- Titre -->
                    <div class="form-group">
                        <label for="id_titre" class="form-label">
                            <i class="fas fa-heading me-2"></i>Titre de l'article
                        </label>
                        <input type="text" 
                               class="form-control" 
                               id="id_titre" 
                               name="titre" 
                               value="{{ actualite.titre }}" 
                               required>
                    </div>

                    <!-- Contenu -->
                    <div class="form-group">
                        <label for="id_contenu" class="form-label">
                            <i class="fas fa-align-left me-2"></i>Contenu de l'article
                        </label>
                        <textarea class="form-control" 
                                  id="id_contenu" 
                                  name="contenu" 
                                  rows="15" 
                                  required>{{ actualite.contenu }}</textarea>
                    </div>

                    <!-- Thème -->
                    <div class="form-group">
                        <label for="id_theme" class="form-label">
                            <i class="fas fa-tag me-2"></i>Thème
                        </label>
                        <input type="text" 
                               class="form-control" 
                               id="id_theme" 
                               name="theme" 
                               value="{{ actualite.theme }}">
                    </div>
                </div>

                <!-- Colonne latérale -->
                <div class="col-lg-4">
                    <!-- Statut -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-flag me-2"></i>Statut de l'article
                        </label>
                        <div class="status-badges">
                            <div class="status-badge draft {% if actualite.status == 'draft' %}active{% endif %}"
                                 data-status="draft">
                                <i class="fas fa-edit me-1"></i>Brouillon
                            </div>
                            <div class="status-badge published {% if actualite.status == 'published' %}active{% endif %}"
                                 data-status="published">
                                <i class="fas fa-check me-1"></i>Publié
                            </div>
                        </div>
                        <input type="hidden" name="status" id="status_input" value="{{ actualite.status }}">
                    </div>

                    <!-- Image actuelle -->
                    {% if actualite.photo %}
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-image me-2"></i>Image actuelle
                        </label>
                        <div class="image-preview">
                            <img src="{{ actualite.photo.url }}" alt="{{ actualite.titre }}" class="current-image img-fluid">
                            <button type="button" class="btn regenerate-btn" onclick="regenerateImage()">
                                <i class="fas fa-sync me-1"></i>Régénérer l'image
                            </button>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Upload nouvelle image -->
                    <div class="form-group">
                        <label for="id_photo" class="form-label">
                            <i class="fas fa-upload me-2"></i>Nouvelle image (optionnel)
                        </label>
                        <input type="file" 
                               class="form-control" 
                               id="id_photo" 
                               name="photo" 
                               accept="image/*">
                        <small class="text-muted">Formats acceptés: JPG, PNG, WebP</small>
                    </div>

                    <!-- Mise en avant -->
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" 
                                   class="form-check-input" 
                                   id="id_mis_en_avant" 
                                   name="mis_en_avant" 
                                   {% if actualite.mis_en_avant %}checked{% endif %}>
                            <label class="form-check-label" for="id_mis_en_avant">
                                <i class="fas fa-star me-2"></i>Mettre en avant cet article
                            </label>
                        </div>
                    </div>

                    <!-- Auteur -->
                    <div class="form-group">
                        <label for="id_auteur" class="form-label">
                            <i class="fas fa-user me-2"></i>Auteur
                        </label>
                        <input type="text" 
                               class="form-control" 
                               id="id_auteur" 
                               name="auteur" 
                               value="{{ actualite.auteur }}" 
                               readonly>
                    </div>
                </div>
            </div>

            <!-- Boutons d'action -->
            <div class="form-group mt-4 pt-3 border-top">
                <div class="d-flex justify-content-between">
                    <a href="{% url 'backoffice:articles' %}" class="btn-cancel">
                        <i class="fas fa-times me-2"></i>Annuler
                    </a>
                    <button type="submit" class="btn-save">
                        <i class="fas fa-save me-2"></i>Sauvegarder les modifications
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Modal de régénération d'image -->
<div class="modal fade" id="regenerateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Régénérer l'image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="customPrompt" class="form-label">Prompt personnalisé (optionnel)</label>
                    <input type="text" class="form-control" id="customPrompt" 
                           placeholder="Ex: Une image moderne sur le thème de...">
                    <small class="text-muted">Laissez vide pour utiliser le titre de l'article</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="confirmRegenerate()">
                    <i class="fas fa-sync me-1"></i>Régénérer
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Gestion des statuts
document.querySelectorAll('.status-badge').forEach(badge => {
    badge.addEventListener('click', function() {
        // Retirer la classe active de tous les badges
        document.querySelectorAll('.status-badge').forEach(b => b.classList.remove('active'));
        
        // Ajouter la classe active au badge cliqué
        this.classList.add('active');
        
        // Mettre à jour l'input caché
        document.getElementById('status_input').value = this.dataset.status;
    });
});

// Fonction pour régénérer l'image
function regenerateImage() {
    const modal = new bootstrap.Modal(document.getElementById('regenerateModal'));
    modal.show();
}

function confirmRegenerate() {
    const customPrompt = document.getElementById('customPrompt').value;
    const articleId = {{ actualite.id }};

    if (!customPrompt.trim()) {
        alert('Veuillez saisir un prompt pour l\'image');
        return;
    }

    // Fermer le modal
    bootstrap.Modal.getInstance(document.getElementById('regenerateModal')).hide();

    // Afficher un loader sur le bouton de régénération
    const regenerateBtn = document.querySelector('.regenerate-btn');
    const originalText = regenerateBtn.innerHTML;
    regenerateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Génération...';
    regenerateBtn.disabled = true;
    
    // Faire la requête
    fetch(`/actualite/api/regenerate-image/${articleId}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        },
        body: JSON.stringify({
            prompt: customPrompt
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Recharger la page pour voir la nouvelle image
            location.reload();
        } else {
            alert('Erreur: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur technique lors de la régénération');
    })
    .finally(() => {
        regenerateBtn.innerHTML = originalText;
        regenerateBtn.disabled = false;
    });
}

// Validation du formulaire
document.getElementById('editForm').addEventListener('submit', function(e) {
    const titre = document.getElementById('id_titre').value.trim();
    const contenu = document.getElementById('id_contenu').value.trim();
    
    if (!titre || !contenu) {
        e.preventDefault();
        alert('Le titre et le contenu sont obligatoires');
        return false;
    }
    
    // Afficher un loader sur le bouton
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sauvegarde...';
    submitBtn.disabled = true;
});

console.log('✅ Page d\'édition chargée');
</script>
{% endblock %}
