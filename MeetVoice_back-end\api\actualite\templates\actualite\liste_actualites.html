<!-- @format -->

<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Articles - Rencontre Libertine</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <style>
      body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      }
      .header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        padding: 2rem 0;
        margin-bottom: 2rem;
      }
      .article-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
        margin-bottom: 2rem;
        overflow: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      .article-card:hover {
        transform: translateY(-5px);
      }
      .article-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
      }
      .article-content {
        padding: 1.5rem;
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .article-title {
        color: #2a1d34;
        font-weight: 600;
        margin-bottom: 1rem;
      }
      .article-meta {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 1rem;
      }
      .article-excerpt {
        color: #555;
        line-height: 1.6;
        flex: 1;
        margin-bottom: 1rem;
      }
      .btn-read-more {
        background: linear-gradient(45deg, #42bee5, #d477eb);
        border: none;
        color: white;
        padding: 0.5rem 1.5rem;
        border-radius: 25px;
        text-decoration: none;
        transition: transform 0.3s ease;
      }
      .btn-read-more:hover {
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
      }
      .badge-status {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
      }

      /* Hauteur uniforme des cartes */
      .row {
        display: flex;
        flex-wrap: wrap;
      }

      .col-md-6,
      .col-lg-4 {
        display: flex;
        margin-bottom: 2rem;
      }

      /* Suggestions d'articles */
      .suggestions-section {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 3rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      }

      .suggestion-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border-left: 4px solid #667eea;
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .suggestion-card:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      .score-badge {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 0.3rem 0.8rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: bold;
      }

      .category-badge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.2rem 0.6rem;
        border-radius: 15px;
        font-size: 0.75rem;
      }

      .suggestion-stats {
        display: flex;
        gap: 1rem;
        margin-top: 0.5rem;
      }

      .stat-item {
        font-size: 0.8rem;
        color: #6c757d;
      }

      .loading-spinner {
        display: none;
        text-align: center;
        padding: 2rem;
      }

      /* Responsive */
      @media (max-width: 768px) {
        .article-image {
          height: 150px;
        }
        .article-content {
          padding: 1rem;
        }
        .suggestions-section {
          padding: 1rem;
        }
        .suggestion-stats {
          flex-direction: column;
          gap: 0.5rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="container">
        <h1 class="text-white text-center">
          <i class="fas fa-newspaper me-3"></i>Articles
        </h1>
        <p class="text-white text-center opacity-75">
          Découvrez nos derniers articles
        </p>
      </div>
    </div>

    <div class="container">
      <!-- Note pour les administrateurs -->
      <div class="alert alert-info text-center mb-4">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Administrateurs :</strong> Les suggestions d'articles intelligentes et la gestion des mots-clés SEO sont disponibles dans le
        <a href="/backoffice/articles/" class="alert-link fw-bold">back-office</a>
      </div>

      <!-- Section Articles Existants -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h3 class="mb-0">
          <i class="fas fa-newspaper me-2"></i>
          Articles Existants
        </h3>
        <span class="badge bg-info">{{ actualites|length }} article{{ actualites|length|pluralize }}</span>
      </div>

      <div class="row">
        {% for article in actualites %}
        <div class="col-md-6 col-lg-4">
          <div class="article-card">
            {% if article.photo %}
            <img
              src="{{ article.photo.url }}"
              alt="{{ article.titre }}"
              class="article-image"
            />
            {% elif article.image_url %}
            <img
              src="{{ article.image_url }}"
              alt="{{ article.titre }}"
              class="article-image"
            />
            {% endif %}

            <div class="article-content">
              <div
                class="d-flex justify-content-between align-items-start mb-2"
              >
                <span class="badge badge-status bg-primary"
                  >{{ article.theme }}</span
                >
                <small class="text-muted"
                  >{{ article.date_publication|date:"d/m/Y" }}</small
                >
              </div>

              <h5 class="article-title">{{ article.titre }}</h5>

              <div class="article-meta">
                <i class="fas fa-user me-1"></i>
                {% if article.auteur.nom and article.auteur.prenom %}
                {{ article.auteur.prenom }} {{ article.auteur.nom }}
                {% else %} {{ article.auteur.username }} {% endif %}

                <span class="ms-3">
                  <i class="fas fa-eye me-1"></i>{{ article.access_count }}
                  vue{% if article.access_count > 1 %}s{% endif %}
                </span>
              </div>

              {% if article.petit_description %}
              <p class="article-excerpt">
                {{ article.petit_description|truncatewords:20 }}
              </p>
              {% endif %}

              <a
                href="{% url 'actualite:detail_actualite' article.id %}"
                class="btn-read-more"
              >
                Lire la suite <i class="fas fa-arrow-right ms-1"></i>
              </a>
            </div>
          </div>
        </div>
        {% empty %}
        <div class="col-12">
          <div class="text-center text-white">
            <i class="fas fa-newspaper fa-3x mb-3 opacity-50"></i>
            <h3>Aucun article disponible</h3>
            <p>Revenez bientôt pour découvrir nos nouveaux contenus !</p>
          </div>
        </div>
        {% endfor %}
      </div>

      <!-- Pagination si nécessaire -->
      {% if is_paginated %}
      <nav aria-label="Navigation des articles" class="mt-4">
        <ul class="pagination justify-content-center">
          {% if page_obj.has_previous %}
          <li class="page-item">
            <a
              class="page-link"
              href="?page={{ page_obj.previous_page_number }}"
              >Précédent</a
            >
          </li>
          {% endif %} {% for num in page_obj.paginator.page_range %}
          <li
            class="page-item {% if page_obj.number == num %}active{% endif %}"
          >
            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
          </li>
          {% endfor %} {% if page_obj.has_next %}
          <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.next_page_number }}"
              >Suivant</a
            >
          </li>
          {% endif %}
        </ul>
      </nav>
      {% endif %}

      <div class="text-center mt-4 mb-5">
        <a href="/" class="btn btn-outline-light">
          <i class="fas fa-home me-2"></i>Retour à l'accueil
        </a>
      </div>
    </div>

    <!-- Modal pour analyser un sujet spécifique -->
    <div class="modal fade" id="analyzeModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-search me-2"></i>
              Analyser la Pertinence d'un Sujet
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form id="analyzeForm">
              <div class="mb-3">
                <label for="topicInput" class="form-label">Sujet à analyser</label>
                <input type="text" class="form-control" id="topicInput"
                       placeholder="Ex: conseils pour premier rendez-vous">
              </div>
              <div class="mb-3">
                <label for="categorySelect" class="form-label">Catégorie cible (optionnel)</label>
                <select class="form-select" id="categorySelect">
                  <option value="">Toutes les catégories</option>
                  <option value="amical">Amical</option>
                  <option value="amour">Amour</option>
                  <option value="libertin">Libertin</option>
                </select>
              </div>
            </form>
            <div id="analysisResult" class="mt-4" style="display: none;">
              <!-- Résultat de l'analyse -->
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            <button type="button" class="btn btn-primary" onclick="analyzeTopic()">
              <i class="fas fa-search me-2"></i>Analyser
            </button>
            <button type="button" class="btn btn-success" id="generateBtn" style="display: none;" onclick="generateFromAnalysis()">
              <i class="fas fa-magic me-2"></i>Générer l'Article
            </button>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
      let currentCategory = 'all';
      let currentAnalysis = null;

      // Charger les suggestions au démarrage
      document.addEventListener('DOMContentLoaded', function() {
        loadSuggestions('all');
      });

      // Charger les suggestions d'articles
      async function loadSuggestions(category = 'all') {
        currentCategory = category;

        // Afficher le spinner
        document.getElementById('loadingSpinner').style.display = 'block';
        document.getElementById('suggestionsContainer').innerHTML = '';

        // Mettre à jour les boutons actifs
        document.querySelectorAll('.btn-group .btn').forEach(btn => {
          btn.classList.remove('active');
        });
        event?.target?.classList.add('active');

        try {
          const url = category === 'all'
            ? '/actualite/api/suggest-articles/?count=8'
            : `/actualite/api/suggest-articles/?count=8&category=${category}`;

          const response = await fetch(url);
          const data = await response.json();

          if (data.success) {
            displaySuggestions(data.suggestions);
          } else {
            showError('Erreur lors du chargement des suggestions');
          }
        } catch (error) {
          console.error('Erreur:', error);
          showError('Erreur de connexion');
        } finally {
          document.getElementById('loadingSpinner').style.display = 'none';
        }
      }

      // Afficher les suggestions
      function displaySuggestions(suggestions) {
        const container = document.getElementById('suggestionsContainer');

        if (suggestions.length === 0) {
          container.innerHTML = `
            <div class="text-center text-muted py-4">
              <i class="fas fa-search fa-2x mb-3"></i>
              <p>Aucune suggestion trouvée pour cette catégorie.</p>
            </div>
          `;
          return;
        }

        const html = suggestions.map(suggestion => `
          <div class="suggestion-card" onclick="selectSuggestion('${suggestion.topic.replace(/'/g, "\\'")}', '${suggestion.category}')">
            <div class="d-flex justify-content-between align-items-start mb-2">
              <h6 class="mb-0">${suggestion.topic}</h6>
              <div class="d-flex gap-2">
                <span class="score-badge">${suggestion.score.toFixed(0)}/100</span>
                <span class="category-badge">${suggestion.category}</span>
              </div>
            </div>

            <p class="text-muted mb-2 small">${suggestion.suggested_angle}</p>

            <div class="suggestion-stats">
              <div class="stat-item">
                <i class="fas fa-chart-line me-1"></i>
                SEO: ${suggestion.seo_potential.toFixed(0)}/100
              </div>
              <div class="stat-item">
                <i class="fas fa-fire me-1"></i>
                Tendance: ${suggestion.trend_score.toFixed(0)}/100
              </div>
              <div class="stat-item">
                <i class="fas fa-users me-1"></i>
                ${suggestion.estimated_traffic.toFixed(0)} visiteurs/mois
              </div>
            </div>

            ${suggestion.recommendations.length > 0 ? `
              <div class="mt-2">
                <small class="text-info">
                  <i class="fas fa-lightbulb me-1"></i>
                  ${suggestion.recommendations[0]}
                </small>
              </div>
            ` : ''}
          </div>
        `).join('');

        container.innerHTML = html;
      }

      // Sélectionner une suggestion
      function selectSuggestion(topic, category) {
        if (confirm(`Voulez-vous générer un article sur le sujet :\\n"${topic}" ?`)) {
          generateArticle(topic, category);
        }
      }

      // Rafraîchir les suggestions
      function refreshSuggestions() {
        loadSuggestions(currentCategory);
      }

      // Afficher le modal d'analyse
      function showAnalyzeModal() {
        const modal = new bootstrap.Modal(document.getElementById('analyzeModal'));
        modal.show();
        document.getElementById('topicInput').focus();
      }

      // Analyser un sujet spécifique
      async function analyzeTopic() {
        const topic = document.getElementById('topicInput').value.trim();
        const category = document.getElementById('categorySelect').value;

        if (!topic) {
          alert('Veuillez saisir un sujet à analyser');
          return;
        }

        try {
          const response = await fetch('/actualite/api/analyze-topic/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ topic, category })
          });

          const data = await response.json();

          if (data.success) {
            currentAnalysis = data;
            displayAnalysisResult(data);
          } else {
            showError('Erreur lors de l\'analyse');
          }
        } catch (error) {
          console.error('Erreur:', error);
          showError('Erreur de connexion');
        }
      }

      // Afficher le résultat de l'analyse
      function displayAnalysisResult(data) {
        const container = document.getElementById('analysisResult');
        const analysis = data.analysis;
        const verdict = data.verdict;

        const scoreColor = verdict.score_category === 'excellent' ? 'success' :
                          verdict.score_category === 'good' ? 'primary' :
                          verdict.score_category === 'average' ? 'warning' : 'danger';

        container.innerHTML = `
          <div class="card">
            <div class="card-header">
              <h6 class="mb-0">Résultat de l'Analyse</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <h6>Score Global</h6>
                  <div class="progress mb-3">
                    <div class="progress-bar bg-${scoreColor}" style="width: ${analysis.final_score}%">
                      ${analysis.final_score.toFixed(1)}/100
                    </div>
                  </div>

                  <h6>Détails</h6>
                  <ul class="list-unstyled">
                    <li><strong>SEO:</strong> ${analysis.seo_potential.toFixed(0)}/100</li>
                    <li><strong>Tendance:</strong> ${analysis.trend_alignment.toFixed(0)}/100</li>
                    <li><strong>Saisonnier:</strong> ${analysis.seasonal_relevance.toFixed(0)}/100</li>
                    <li><strong>Trafic estimé:</strong> ${analysis.estimated_traffic.toFixed(0)} visiteurs/mois</li>
                  </ul>
                </div>

                <div class="col-md-6">
                  <h6>Verdict</h6>
                  <div class="alert alert-${scoreColor}">
                    <strong>${verdict.recommended ? '✅ Recommandé' : '⚠️ Peu recommandé'}</strong><br>
                    Qualité: ${verdict.score_category}<br>
                    Priorité: ${verdict.priority}
                  </div>

                  ${analysis.recommendations.length > 0 ? `
                    <h6>Recommandations</h6>
                    <ul class="small">
                      ${analysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                  ` : ''}
                </div>
              </div>

              <div class="mt-3">
                <h6>Angle Suggéré</h6>
                <p class="text-muted">${analysis.suggested_angle}</p>
              </div>
            </div>
          </div>
        `;

        container.style.display = 'block';

        // Afficher le bouton de génération si recommandé
        const generateBtn = document.getElementById('generateBtn');
        if (verdict.recommended) {
          generateBtn.style.display = 'inline-block';
        } else {
          generateBtn.style.display = 'none';
        }
      }

      // Générer un article depuis l'analyse
      function generateFromAnalysis() {
        if (currentAnalysis) {
          const topic = document.getElementById('topicInput').value.trim();
          const category = document.getElementById('categorySelect').value;
          generateArticle(topic, category);
        }
      }

      // Générer un article
      async function generateArticle(topic, category) {
        if (confirm(`Génération d'article en cours...\\nSujet: ${topic}\\nCela peut prendre quelques minutes.`)) {
          // Rediriger vers la page de génération ou appeler l'API
          window.location.href = `/backoffice/articles/?generate=${encodeURIComponent(topic)}&category=${category}`;
        }
      }

      // Afficher une erreur
      function showError(message) {
        const container = document.getElementById('suggestionsContainer');
        container.innerHTML = `
          <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
          </div>
        `;
      }
    </script>
  </body>
</html>
