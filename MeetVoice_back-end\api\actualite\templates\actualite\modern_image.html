{% comment %}
Template pour afficher des images optimisées sans JPEG
Utilise uniquement les formats modernes AVIF et WebP
{% endcomment %}

{% load static %}

<!-- Image optimisée avec formats modernes uniquement -->
<div class="modern-image-container">
    <picture class="modern-image">
        <!-- Format AVIF (le plus moderne, -50% vs JPEG) -->
        <source 
            srcset="{{ article.photo|replace:'.webp','_small.avif' }} 400w,
                    {{ article.photo|replace:'.webp','_medium.avif' }} 800w,
                    {{ article.photo|replace:'.webp','_large.avif' }} 1200w"
            sizes="(max-width: 400px) 400px, (max-width: 800px) 800px, 1200px"
            type="image/avif">
        
        <!-- Format WebP (fallback moderne, -30% vs JPEG) -->
        <source 
            srcset="{{ article.photo|replace:'.webp','_small.webp' }} 400w,
                    {{ article.photo|replace:'.webp','_medium.webp' }} 800w,
                    {{ article.photo|replace:'.webp','_large.webp' }} 1200w"
            sizes="(max-width: 400px) 400px, (max-width: 800px) 800px, 1200px"
            type="image/webp">
        
        <!-- Image par défaut (WebP) -->
        <img 
            src="{{ article.photo }}" 
            alt="{{ article.titre }}"
            loading="lazy"
            decoding="async"
            class="img-fluid modern-img"
            style="width: 100%; height: auto; object-fit: cover;">
    </picture>
    
    <!-- Badge "Formats Modernes" -->
    <div class="format-badge">
        <span class="badge bg-success">
            <i class="fas fa-rocket"></i> AVIF + WebP
        </span>
    </div>
</div>

<style>
.modern-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modern-image {
    display: block;
    width: 100%;
}

.modern-img {
    transition: transform 0.3s ease;
    border-radius: 0.5rem;
}

.modern-img:hover {
    transform: scale(1.02);
}

.format-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
}

.format-badge .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    background: linear-gradient(45deg, #28a745, #20c997) !important;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.format-badge .badge i {
    margin-right: 0.25rem;
}

/* Animation de chargement */
.modern-img {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

.modern-img[src] {
    background: none;
    animation: none;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Responsive */
@media (max-width: 576px) {
    .format-badge {
        top: 5px;
        right: 5px;
    }
    
    .format-badge .badge {
        font-size: 0.65rem;
        padding: 0.2rem 0.4rem;
    }
}

/* Performance hints pour le navigateur */
.modern-image-container::before {
    content: '';
    display: block;
    width: 100%;
    height: 0;
    padding-bottom: 66.67%; /* Ratio 3:2 */
    background: #f8f9fa;
}

.modern-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
</style>

<!-- Preload des images critiques -->
{% if article.mis_en_avant %}
<link rel="preload" as="image" href="{{ article.photo }}" type="image/webp">
{% endif %}

<!-- Structured data pour le SEO -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "ImageObject",
    "url": "{{ request.build_absolute_uri }}{{ article.photo }}",
    "caption": "{{ article.titre }}",
    "encodingFormat": ["image/avif", "image/webp"],
    "width": "1200",
    "height": "800",
    "author": {
        "@type": "Person",
        "name": "{{ article.auteur.get_full_name|default:article.auteur.username }}"
    }
}
</script>
