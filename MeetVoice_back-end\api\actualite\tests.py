from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
import json

from .models import Actualite
from .serializers import (
    ActualiteListSerializer, ActualiteDetailSerializer,
    ActualiteCreateUpdateSerializer
)
from .forms import ActualiteForm

User = get_user_model()


class ActualiteModelTest(TestCase):
    """Tests pour le modèle Actualite"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_article(self):
        """Test de création d'un article"""
        article = Actualite.objects.create(
            titre='Test Article',
            sous_titre='Test Subtitle',
            contenu='Contenu de test',
            auteur=self.user,
            categorie='recruteur',
            theme='Décryptage',
            status='published',
            petit_description='Description test'
        )

        self.assertEqual(article.titre, 'Test Article')
        self.assertEqual(article.auteur, self.user)
        self.assertEqual(article.status, 'published')
        self.assertEqual(str(article), 'Test Article')

    def test_article_defaults(self):
        """Test des valeurs par défaut"""
        article = Actualite.objects.create(
            titre='Test Article',
            contenu='Contenu de test',
            auteur=self.user,
            categorie='recruteur',
            theme='Décryptage'
        )

        self.assertEqual(article.status, 'draft')
        self.assertEqual(article.access_count, 0)
        self.assertFalse(article.mis_en_avant)

    def test_increment_access_count(self):
        """Test d'incrémentation du compteur de vues"""
        article = Actualite.objects.create(
            titre='Test Article',
            contenu='Contenu de test',
            auteur=self.user,
            categorie='recruteur',
            theme='Décryptage'
        )

        initial_count = article.access_count
        article.increment_access_count()

        self.assertEqual(article.access_count, initial_count + 1)

    def test_get_tags_list(self):
        """Test de la méthode get_tags_list"""
        article = Actualite.objects.create(
            titre='Test Article',
            contenu='Contenu de test',
            auteur=self.user,
            categorie='recruteur',
            theme='Décryptage',
            tags='python, django, test'
        )

        tags = article.get_tags_list()
        self.assertEqual(tags, ['python', 'django', 'test'])

    def test_is_published_property(self):
        """Test de la propriété is_published"""
        article = Actualite.objects.create(
            titre='Test Article',
            contenu='Contenu de test',
            auteur=self.user,
            categorie='recruteur',
            theme='Décryptage',
            status='published'
        )

        self.assertTrue(article.is_published)

        article.status = 'draft'
        article.save()

        self.assertFalse(article.is_published)

    def test_reading_time_property(self):
        """Test de la propriété reading_time"""
        # Créer un contenu avec environ 500 mots (2 minutes de lecture)
        content = ' '.join(['mot'] * 500)

        article = Actualite.objects.create(
            titre='Test Article',
            contenu=content,
            auteur=self.user,
            categorie='recruteur',
            theme='Décryptage'
        )

        # 500 mots / 250 mots par minute = 2 minutes
        self.assertEqual(article.reading_time, 2)


class ActualiteFormTest(TestCase):
    """Tests pour le formulaire ActualiteForm"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_valid_form(self):
        """Test d'un formulaire valide"""
        form_data = {
            'titre': 'Test Article',
            'sous_titre': 'Test Subtitle',
            'contenu': 'Contenu de test',
            'categorie': 'recruteur',
            'theme': 'Décryptage',
            'status': 'published',
            'petit_description': 'Description test',
            'tags': 'python, django',
            'redacteur': 'Test Redacteur'
        }

        form = ActualiteForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_required_fields(self):
        """Test des champs obligatoires"""
        form = ActualiteForm(data={})
        self.assertFalse(form.is_valid())

        required_fields = ['titre', 'contenu', 'categorie', 'theme']
        for field in required_fields:
            self.assertIn(field, form.errors)

    def test_clean_tags(self):
        """Test de la méthode clean_tags"""
        form_data = {
            'titre': 'Test Article',
            'contenu': 'Contenu de test',
            'categorie': 'recruteur',
            'theme': 'Décryptage',
            'tags': 'python, django, python, test'  # Avec doublons
        }

        form = ActualiteForm(data=form_data)
        self.assertTrue(form.is_valid())

        # Les doublons doivent être supprimés
        cleaned_tags = form.cleaned_data['tags']
        self.assertEqual(cleaned_tags, 'python, django, test')


class ActualiteSerializerTest(TestCase):
    """Tests pour les serializers"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )

        self.article = Actualite.objects.create(
            titre='Test Article',
            sous_titre='Test Subtitle',
            contenu='Contenu de test',
            auteur=self.user,
            categorie='recruteur',
            theme='Décryptage',
            status='published',
            tags='python, django'
        )

    def test_list_serializer(self):
        """Test du serializer de liste"""
        serializer = ActualiteListSerializer(self.article)
        data = serializer.data

        self.assertEqual(data['titre'], 'Test Article')
        self.assertEqual(data['auteur']['full_name'], 'Test User')
        self.assertIn('tags_list', data)
        self.assertIn('reading_time', data)

    def test_detail_serializer(self):
        """Test du serializer de détail"""
        serializer = ActualiteDetailSerializer(self.article)
        data = serializer.data

        self.assertEqual(data['titre'], 'Test Article')
        self.assertEqual(data['contenu'], 'Contenu de test')
        self.assertIn('tags_list', data)

    def test_create_update_serializer(self):
        """Test du serializer de création/modification"""
        data = {
            'titre': 'Nouvel Article',
            'contenu': 'Nouveau contenu',
            'categorie': 'candidats',
            'theme': 'Success stories',
            'status': 'draft',
            'tags': 'nouveau, test'
        }

        serializer = ActualiteCreateUpdateSerializer(data=data)
        self.assertTrue(serializer.is_valid())

        # Test de la validation des tags
        validated_data = serializer.validated_data
        self.assertEqual(validated_data['tags'], 'nouveau, test')


class ActualiteAPITest(APITestCase):
    """Tests pour l'API REST des articles"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )

        self.staff_user = User.objects.create_user(
            username='staffuser',
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )

        self.article = Actualite.objects.create(
            titre='Test Article',
            contenu='Contenu de test',
            auteur=self.user,
            categorie='recruteur',
            theme='Décryptage',
            status='published'
        )

        self.draft_article = Actualite.objects.create(
            titre='Draft Article',
            contenu='Contenu brouillon',
            auteur=self.user,
            categorie='candidats',
            theme='Success stories',
            status='draft'
        )

        self.client = APIClient()

    def test_list_articles_anonymous(self):
        """Test de la liste des articles pour un utilisateur anonyme"""
        url = reverse('actualite-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Les utilisateurs anonymes ne voient que les articles publiés
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['titre'], 'Test Article')

    def test_list_articles_authenticated(self):
        """Test de la liste des articles pour un utilisateur authentifié"""
        self.client.force_authenticate(user=self.user)
        url = reverse('actualite-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Les utilisateurs authentifiés voient tous les articles
        self.assertEqual(len(response.data), 2)

    def test_retrieve_article(self):
        """Test de récupération d'un article spécifique"""
        url = reverse('actualite-detail', kwargs={'pk': self.article.pk})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['titre'], 'Test Article')

    def test_create_article_authenticated(self):
        """Test de création d'un article par un utilisateur authentifié"""
        self.client.force_authenticate(user=self.user)

        data = {
            'titre': 'Nouvel Article',
            'contenu': 'Nouveau contenu',
            'categorie': 'recruteur',
            'theme': 'Décryptage',
            'status': 'draft'
        }

        url = reverse('actualite-list')
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Actualite.objects.count(), 3)

        # Vérifier que l'auteur est automatiquement défini
        new_article = Actualite.objects.get(titre='Nouvel Article')
        self.assertEqual(new_article.auteur, self.user)

    def test_create_article_anonymous(self):
        """Test de création d'un article par un utilisateur anonyme"""
        data = {
            'titre': 'Nouvel Article',
            'contenu': 'Nouveau contenu',
            'categorie': 'recruteur',
            'theme': 'Décryptage'
        }

        url = reverse('actualite-list')
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_update_article_owner(self):
        """Test de modification d'un article par son propriétaire"""
        self.client.force_authenticate(user=self.user)

        data = {
            'titre': 'Article Modifié',
            'contenu': 'Contenu modifié',
            'categorie': 'candidats',
            'theme': 'Success stories'
        }

        url = reverse('actualite-detail', kwargs={'pk': self.article.pk})
        response = self.client.patch(url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.article.refresh_from_db()
        self.assertEqual(self.article.titre, 'Article Modifié')

    def test_delete_article_owner(self):
        """Test de suppression d'un article par son propriétaire"""
        self.client.force_authenticate(user=self.user)

        url = reverse('actualite-detail', kwargs={'pk': self.article.pk})
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Actualite.objects.count(), 1)

    def test_published_articles_endpoint(self):
        """Test de l'endpoint des articles publiés"""
        url = reverse('actualite-published')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['status'], 'published')

    def test_featured_articles_endpoint(self):
        """Test de l'endpoint des articles mis en avant"""
        # Mettre un article en avant
        self.article.mis_en_avant = True
        self.article.save()

        url = reverse('actualite-featured')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertTrue(response.data[0]['mis_en_avant'])

    def test_increment_views_endpoint(self):
        """Test de l'endpoint d'incrémentation des vues"""
        initial_count = self.article.access_count

        url = reverse('actualite-increment-views', kwargs={'pk': self.article.pk})
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['access_count'], initial_count + 1)

        self.article.refresh_from_db()
        self.assertEqual(self.article.access_count, initial_count + 1)

    def test_stats_endpoint_staff_only(self):
        """Test de l'endpoint des statistiques (staff seulement)"""
        # Test avec utilisateur normal
        self.client.force_authenticate(user=self.user)
        url = reverse('actualite-stats')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test avec utilisateur staff
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)

    def test_filter_by_category(self):
        """Test de filtrage par catégorie"""
        self.client.force_authenticate(user=self.user)

        url = reverse('actualite-list')
        response = self.client.get(url, {'categorie': 'recruteur'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['categorie'], 'recruteur')

    def test_search_articles(self):
        """Test de recherche dans les articles"""
        self.client.force_authenticate(user=self.user)

        url = reverse('actualite-list')
        response = self.client.get(url, {'search': 'Test'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_ordering_articles(self):
        """Test de tri des articles"""
        url = reverse('actualite-list')
        response = self.client.get(url, {'ordering': 'titre'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)


class ActualiteViewTest(TestCase):
    """Tests pour les vues Django traditionnelles"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.staff_user = User.objects.create_user(
            username='staffuser',
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )

        self.article = Actualite.objects.create(
            titre='Test Article',
            contenu='Contenu de test',
            auteur=self.staff_user,
            categorie='recruteur',
            theme='Décryptage',
            status='published'
        )

        self.client = Client()

    def test_articles_view_requires_staff(self):
        """Test que la vue articles nécessite les droits staff"""
        url = reverse('actualites')

        # Test sans authentification
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirection vers login

        # Test avec utilisateur normal
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirection

        # Test avec utilisateur staff
        self.client.login(username='staffuser', password='testpass123')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_create_article_post(self):
        """Test de création d'article via POST"""
        self.client.login(username='staffuser', password='testpass123')

        data = {
            'titre': 'Nouvel Article',
            'contenu': 'Nouveau contenu',
            'categorie': 'recruteur',
            'theme': 'Décryptage',
            'status': 'draft'
        }

        url = reverse('actualites')
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, 302)  # Redirection après création
        self.assertTrue(Actualite.objects.filter(titre='Nouvel Article').exists())

    def test_get_article_json(self):
        """Test de récupération d'article en JSON"""
        self.client.login(username='staffuser', password='testpass123')

        url = reverse('get_actualite', kwargs={'actualite_id': self.article.pk})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')

        data = json.loads(response.content)
        self.assertEqual(data['titre'], 'Test Article')

    def test_edit_article(self):
        """Test de modification d'article"""
        self.client.login(username='staffuser', password='testpass123')

        data = {
            'titre': 'Article Modifié',
            'contenu': 'Contenu modifié',
            'categorie': 'candidats',
            'theme': 'Success stories',
            'status': 'published'
        }

        url = reverse('edit_actualite', kwargs={'actualite_id': self.article.pk})
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, 302)  # Redirection après modification

        self.article.refresh_from_db()
        self.assertEqual(self.article.titre, 'Article Modifié')
