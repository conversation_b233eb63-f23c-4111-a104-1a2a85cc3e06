#!/usr/bin/env python
"""
Correcteur orthographique et grammatical pour les articles
"""
import re
import unicodedata
from typing import Dict, List, Tuple

class FrenchTextCorrector:
    """Correcteur de texte français pour améliorer la qualité des articles"""
    
    def __init__(self):
        self.common_errors = self._load_common_errors()
        self.grammar_rules = self._load_grammar_rules()
        self.accent_corrections = self._load_accent_corrections()
        
    def _load_common_errors(self) -> Dict[str, str]:
        """Dictionnaire des erreurs courantes et leurs corrections"""
        return {
            # Erreurs d'orthographe courantes
            'dévelopement': 'développement',
            'déveloper': 'développer',
            'dévelopé': 'développé',
            'dévelopée': 'développée',
            'dévelopés': 'développés',
            'dévelopées': 'développées',
            'dévelopant': 'développant',
            'dévelopements': 'développements',

            # Autres erreurs courantes
            'apartir': 'à partir',
            'apartement': 'appartement',
            'apartements': 'appartements',
            'apartient': 'appartient',
            'apartiennent': 'appartiennent',
            'apartenu': 'appartenu',
            'apartenue': 'appartenue',
            'apartenus': 'appartenus',
            'apartenues': 'appartenues',
            'apartenant': 'appartenant',
            'apartenance': 'appartenance',

            # Erreurs de conjugaison
            'aller': 'aller',
            'allé': 'allé',
            'allée': 'allée',
            'allés': 'allés',
            'allées': 'allées',
            'allant': 'allant',

            # Erreurs de vocabulaire technique
            'dévelopeur': 'développeur',
            'dévelopeuse': 'développeuse',
            'dévelopeurs': 'développeurs',
            'dévelopeuses': 'développeuses',
            
            # Erreurs d'accents
            'créé': 'créé',
            'créée': 'créée',
            'créés': 'créés',
            'créées': 'créées',
            'évenement': 'événement',
            'évenements': 'événements',
            'élément': 'élément',
            'éléments': 'éléments',
            'différent': 'différent',
            'différente': 'différente',
            'différents': 'différents',
            'différentes': 'différentes',
            'intéressant': 'intéressant',
            'intéressante': 'intéressante',
            'intéressants': 'intéressants',
            'intéressantes': 'intéressantes',
            
            # Erreurs de conjugaison
            'à été': 'a été',
            'à faire': 'à faire',
            'à partir': 'à partir',
            'à travers': 'à travers',
            'à cause': 'à cause',
            'à propos': 'à propos',
            
            # Erreurs de ponctuation
            ' ,': ',',
            ' .': '.',
            ' !': '!',
            ' ?': '?',
            ' :': ':',
            ' ;': ';',
            
            # Erreurs de majuscules
            'internet': 'Internet',
            'google': 'Google',
            'facebook': 'Facebook',
            'youtube': 'YouTube',
            'linkedin': 'LinkedIn',
            'twitter': 'Twitter',
            
            # Erreurs techniques courantes
            'site web': 'site web',
            'site-web': 'site web',
            'email': 'e-mail',
            'emails': 'e-mails',
            'online': 'en ligne',
            'offline': 'hors ligne',
            'smartphone': 'smartphone',
            'smartphones': 'smartphones',
        }
    
    def _load_grammar_rules(self) -> List[Tuple[str, str]]:
        """Règles grammaticales de base"""
        return [
            # Accord des participes passés
            (r'\b(avoir|être)\s+([\w]+é)\b', self._check_participle_agreement),
            
            # Espaces avant ponctuation
            (r'\s+([,.!?;:])', r'\1'),
            (r'([,.!?;:])\s*([,.!?;:])', r'\1 \2'),
            
            # Espaces multiples
            (r'\s{2,}', ' '),
            
            # Majuscules après ponctuation
            (r'([.!?])\s+([a-z])', lambda m: m.group(1) + ' ' + m.group(2).upper()),
            
            # Apostrophes
            (r"\s+'", "'"),
            (r"'\s+", "'"),
            
            # Guillemets français
            (r'"([^"]+)"', r'« \1 »'),
        ]
    
    def _load_accent_corrections(self) -> Dict[str, str]:
        """Corrections d'accents spécifiques"""
        return {
            'a': 'à',  # dans certains contextes
            'ou': 'où',  # dans certains contextes
            'la': 'là',  # dans certains contextes
            'des': 'dès',  # dans certains contextes
        }
    
    def _check_participle_agreement(self, match) -> str:
        """Vérifie l'accord des participes passés (règle simplifiée)"""
        auxiliary = match.group(1)
        participle = match.group(2)
        
        # Règle simplifiée : avec être, accord souvent nécessaire
        if auxiliary.lower() == 'être':
            # Ici on pourrait ajouter une logique plus complexe
            pass
        
        return match.group(0)  # Retourner tel quel pour l'instant
    
    def correct_text(self, text: str) -> str:
        """Corrige le texte complet"""
        if not text:
            return text
        
        corrected = text
        
        # 1. Corrections des erreurs courantes
        corrected = self._fix_common_errors(corrected)
        
        # 2. Corrections grammaticales
        corrected = self._apply_grammar_rules(corrected)
        
        # 3. Corrections de ponctuation
        corrected = self._fix_punctuation(corrected)
        
        # 4. Corrections de mise en forme
        corrected = self._fix_formatting(corrected)
        
        # 5. Normalisation Unicode
        corrected = self._normalize_unicode(corrected)
        
        return corrected
    
    def _fix_common_errors(self, text: str) -> str:
        """Corrige les erreurs courantes"""
        corrected = text
        
        for error, correction in self.common_errors.items():
            # Correction sensible à la casse
            corrected = re.sub(r'\b' + re.escape(error) + r'\b', correction, corrected, flags=re.IGNORECASE)
            
            # Correction avec majuscule en début de phrase
            error_capitalized = error.capitalize()
            correction_capitalized = correction.capitalize()
            corrected = re.sub(r'\b' + re.escape(error_capitalized) + r'\b', correction_capitalized, corrected)
        
        return corrected
    
    def _apply_grammar_rules(self, text: str) -> str:
        """Applique les règles grammaticales"""
        corrected = text
        
        for pattern, replacement in self.grammar_rules:
            if callable(replacement):
                corrected = re.sub(pattern, replacement, corrected)
            else:
                corrected = re.sub(pattern, replacement, corrected)
        
        return corrected
    
    def _fix_punctuation(self, text: str) -> str:
        """Corrige la ponctuation"""
        corrected = text
        
        # Espaces avant ponctuation forte (français)
        corrected = re.sub(r'\s*([!?:;])', r' \1', corrected)
        corrected = re.sub(r'\s*([,.])(?!\d)', r'\1', corrected)  # Pas d'espace avant virgule/point sauf nombres
        
        # Espaces après ponctuation
        corrected = re.sub(r'([,.!?:;])(?=[a-zA-ZÀ-ÿ])', r'\1 ', corrected)
        
        # Guillemets français
        corrected = re.sub(r'"([^"]*)"', r'« \1 »', corrected)
        
        # Apostrophes typographiques
        corrected = re.sub(r"'", "'", corrected)
        
        return corrected
    
    def _fix_formatting(self, text: str) -> str:
        """Corrige la mise en forme"""
        corrected = text
        
        # Espaces multiples
        corrected = re.sub(r'\s{2,}', ' ', corrected)
        
        # Espaces en début/fin de ligne
        corrected = re.sub(r'^\s+|\s+$', '', corrected, flags=re.MULTILINE)
        
        # Lignes vides multiples
        corrected = re.sub(r'\n{3,}', '\n\n', corrected)
        
        # Majuscule après point
        corrected = re.sub(r'(\. )([a-z])', lambda m: m.group(1) + m.group(2).upper(), corrected)
        
        # Majuscule en début de phrase
        if corrected and corrected[0].islower():
            corrected = corrected[0].upper() + corrected[1:]
        
        return corrected
    
    def _normalize_unicode(self, text: str) -> str:
        """Normalise les caractères Unicode"""
        # Normalisation NFC (forme canonique composée)
        normalized = unicodedata.normalize('NFC', text)
        
        # Correction des caractères similaires
        replacements = {
            ''': "'",  # Apostrophe courbe -> droite
            ''': "'",
            '"': '"',  # Guillemets courbes -> droits
            '"': '"',
            '–': '-',  # Tiret en -> trait d'union
            '—': '-',  # Tiret em -> trait d'union
            '…': '...',  # Points de suspension
        }
        
        for old, new in replacements.items():
            normalized = normalized.replace(old, new)
        
        return normalized
    
    def analyze_text_quality(self, text: str) -> Dict[str, any]:
        """Analyse la qualité du texte"""
        if not text:
            return {'score': 0, 'issues': ['Texte vide']}
        
        issues = []
        score = 100
        
        # Vérifier les erreurs courantes
        for error in self.common_errors.keys():
            if re.search(r'\b' + re.escape(error) + r'\b', text, re.IGNORECASE):
                issues.append(f"Erreur d'orthographe détectée: '{error}'")
                score -= 5
        
        # Vérifier la ponctuation
        if re.search(r'\s+[,.!?;:]', text):
            issues.append("Espaces incorrects avant ponctuation")
            score -= 3
        
        if re.search(r'[,.!?;:](?=[a-zA-ZÀ-ÿ])', text):
            issues.append("Espaces manquants après ponctuation")
            score -= 3
        
        # Vérifier les majuscules
        sentences = re.split(r'[.!?]+', text)
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and sentence[0].islower():
                issues.append("Majuscule manquante en début de phrase")
                score -= 2
                break
        
        # Vérifier les espaces multiples
        if re.search(r'\s{2,}', text):
            issues.append("Espaces multiples détectés")
            score -= 2
        
        return {
            'score': max(0, score),
            'issues': issues,
            'word_count': len(text.split()),
            'character_count': len(text),
            'corrections_applied': self._count_corrections_applied(text)
        }

    def _count_corrections_applied(self, original_text: str) -> int:
        """Compte le nombre de corrections qui seraient appliquées"""
        corrections = 0

        # Compter les erreurs courantes
        for error in self.common_errors.keys():
            corrections += len(re.findall(r'\b' + re.escape(error) + r'\b', original_text, re.IGNORECASE))

        # Compter les problèmes de ponctuation
        corrections += len(re.findall(r'\s+[,.!?;:]', original_text))
        corrections += len(re.findall(r'[,.!?;:](?=[a-zA-ZÀ-ÿ])', original_text))

        # Compter les espaces multiples
        corrections += len(re.findall(r'\s{2,}', original_text))

        return corrections

    def get_correction_preview(self, text: str, max_examples: int = 5) -> Dict[str, any]:
        """Aperçu des corrections qui seraient appliquées"""
        if not text:
            return {'corrections': [], 'total_count': 0}

        corrections = []

        # Détecter les erreurs courantes
        for error, correction in list(self.common_errors.items())[:max_examples]:
            matches = re.finditer(r'\b' + re.escape(error) + r'\b', text, re.IGNORECASE)
            for match in matches:
                corrections.append({
                    'type': 'orthographe',
                    'original': match.group(0),
                    'correction': correction,
                    'position': match.start(),
                    'context': text[max(0, match.start()-20):match.end()+20]
                })
                if len(corrections) >= max_examples:
                    break
            if len(corrections) >= max_examples:
                break

        # Détecter les problèmes de ponctuation
        if len(corrections) < max_examples:
            punct_matches = re.finditer(r'\s+([,.!?;:])', text)
            for match in punct_matches:
                corrections.append({
                    'type': 'ponctuation',
                    'original': match.group(0),
                    'correction': match.group(1),
                    'position': match.start(),
                    'context': text[max(0, match.start()-20):match.end()+20]
                })
                if len(corrections) >= max_examples:
                    break

        total_count = self._count_corrections_applied(text)

        return {
            'corrections': corrections,
            'total_count': total_count,
            'preview_count': len(corrections)
        }
