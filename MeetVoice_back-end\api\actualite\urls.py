from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'actualite'

# Configuration du router pour l'API REST
router = DefaultRouter()
router.register(r'articles', views.ActualiteViewSet, basename='actualite')

urlpatterns = [
    # API REST endpoints
    path('api/', include(router.urls)),
    
    # Vues Django traditionnelles (pour le back-office)
    path('', views.Actualites, name='actualites'),
    path('edit/<int:actualite_id>/', views.edit_actualite, name='edit_actualite'),
    path('get/<int:actualite_id>/', views.get_actualite, name='get_actualite'),
    path('delete/<int:actualite_id>/', views.delete_actualite, name='delete_actualite'),
    path('highlight/<int:actualite_id>/', views.highlight_actualite, name='highlight_actualite'),
    path('detail/<int:actualite_id>/', views.detail_actualite, name='detail_actualite'),
    path('afficher/', views.afficher_actualites, name='afficher_actualites'),
    path('update-status/<int:actualite_id>/', views.update_status_actualite, name='update_status_actualite'),
    path('delete-commentaire/<int:commentaire_id>/', views.delete_commentaire, name='delete_commentaire'),

    # API pour la génération d'articles avec IA
    path('api/generate-article/', views.generate_article_api, name='generate_article_api'),

    # Vues CRUD pour la gestion des articles (ex-backoffice)
    path('manage/', views.articles_view, name='articles_manage'),
    path('api/update-status/<int:article_id>/', views.update_article_status_api, name='update_article_status_api'),

    # API pour régénérer l'image d'un article
    path('api/regenerate-image/<int:article_id>/', views.regenerate_image_api, name='regenerate_image_api'),

    # API pour récupérer un article par slug
    path('api/article/<slug:slug>/', views.get_article_by_slug, name='get_article_by_slug'),
    path('detail/<slug:slug>/', views.detail_actualite_by_slug, name='detail_actualite_by_slug'),

    # API pour les métriques de performance
    path('api/performance-metrics/', views.performance_metrics_api, name='performance_metrics_api'),

    # API pour suggestions d'articles pertinents
    path('api/suggest-articles/', views.suggest_articles_api, name='suggest_articles_api'),
    path('api/analyze-topic/', views.analyze_topic_relevance_api, name='analyze_topic_relevance_api'),
]
