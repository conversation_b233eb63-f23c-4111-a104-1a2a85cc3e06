import os
import json
from datetime import datetime
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from django.db.models import Q
from django.core.paginator import Paginator
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend

from .forms import ActualiteForm
from .models import Actualite
from .serializers import (
    ActualiteListSerializer, ActualiteDetailSerializer,
    ActualiteCreateUpdateSerializer, ActualiteStatsSerializer
)
from .services import GeminiArticleGenerator
from .content_analyzer import DatingContentAnalyzer

# Import des décorateurs de sécurité
from .security_decorators import (
    public_api, user_api, admin_api, backoffice_api,
    combined_security, get_client_ip
)
from commentaire.models import Commentaire


# ============================================================================
# API REST VIEWSETS POUR LES ARTICLES
# ============================================================================

class ActualiteViewSet(viewsets.ModelViewSet):
    """
    ViewSet complet pour la gestion des articles via API REST

    Endpoints disponibles:
    - GET /api/articles/ : Liste des articles
    - POST /api/articles/ : Créer un article
    - GET /api/articles/{id}/ : Détail d'un article
    - PUT/PATCH /api/articles/{id}/ : Modifier un article
    - DELETE /api/articles/{id}/ : Supprimer un article
    - GET /api/articles/published/ : Articles publiés seulement
    - GET /api/articles/featured/ : Articles mis en avant
    - POST /api/articles/{id}/increment_views/ : Incrémenter les vues
    """

    queryset = Actualite.objects.all()
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['theme', 'status', 'mis_en_avant', 'auteur']
    search_fields = ['titre', 'contenu', 'petit_description', 'tags']
    ordering_fields = ['date_publication', 'date_modification', 'access_count', 'titre']
    ordering = ['-date_publication']

    def get_serializer_class(self):
        """Retourne le serializer approprié selon l'action"""
        if self.action == 'list':
            return ActualiteListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return ActualiteCreateUpdateSerializer
        elif self.action == 'stats':
            return ActualiteStatsSerializer
        return ActualiteDetailSerializer

    def get_queryset(self):
        """Filtre les articles selon les permissions"""
        queryset = Actualite.objects.all()

        # Si l'utilisateur n'est pas authentifié, ne montrer que les articles publiés
        if not self.request.user.is_authenticated:
            queryset = queryset.filter(status='published')

        return queryset.select_related('auteur').order_by('-date_publication')

    def perform_create(self, serializer):
        """Définit l'auteur lors de la création"""
        serializer.save(auteur=self.request.user)

    @action(detail=False, methods=['get'])
    def published(self, request):
        """Retourne uniquement les articles publiés"""
        articles = self.get_queryset().filter(status='published')
        page = self.paginate_queryset(articles)
        if page is not None:
            serializer = ActualiteListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = ActualiteListSerializer(articles, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Retourne les articles mis en avant"""
        articles = self.get_queryset().filter(mis_en_avant=True, status='published')
        serializer = ActualiteListSerializer(articles, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[])
    def increment_views(self, request, pk=None):
        """Incrémente le compteur de vues d'un article (uniquement pour les vues publiques)"""
        # Vérifier que la requête ne vient pas du back-office
        referer = request.META.get('HTTP_REFERER', '')
        user_agent = request.META.get('HTTP_USER_AGENT', '')

        # Ne pas compter les vues si:
        # 1. La requête vient d'une page back-office
        # 2. L'utilisateur est un staff (administrateur)
        if '/backoffice/' in referer or (request.user.is_authenticated and request.user.is_staff):
            return Response({
                'access_count': self.get_object().access_count,
                'counted': False,
                'reason': 'Back-office or admin view excluded'
            })

        # Incrémenter uniquement pour les vues publiques
        article = self.get_object()
        article.increment_access_count()
        return Response({
            'access_count': article.access_count,
            'counted': True,
            'reason': 'Public view counted'
        })

    @action(detail=False, methods=['get'], url_path='by-slug/(?P<slug>[^/.]+)')
    def by_slug(self, request, slug=None):
        """Récupère un article par son slug"""
        try:
            article = get_object_or_404(Actualite, slug=slug)

            # Vérifier les permissions
            if article.status != 'published' and not request.user.is_staff:
                return Response({'error': 'Article non publié'}, status=status.HTTP_404_NOT_FOUND)

            # Incrémenter le compteur de vues pour les vues publiques
            referer = request.META.get('HTTP_REFERER', '')
            if '/backoffice/' not in referer and not (request.user.is_authenticated and request.user.is_staff):
                article.increment_access_count()

            serializer = ActualiteDetailSerializer(article, context={'request': request})
            return Response(serializer.data)

        except Exception as e:
            return Response({'error': f'Article non trouvé: {str(e)}'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Retourne les statistiques des articles"""
        if not request.user.is_staff:
            return Response({'error': 'Permission denied'}, status=status.HTTP_403_FORBIDDEN)

        articles = self.get_queryset()
        serializer = ActualiteStatsSerializer(articles, many=True, context={'request': request})
        return Response(serializer.data)


# ============================================================================
# VUES DJANGO TRADITIONNELLES (pour le back-office)
# ============================================================================

@login_required(login_url='/')
def Actualites(request):
    """Vue pour la gestion des articles dans le back-office"""
    if not request.user.is_authenticated or not request.user.is_staff:
        return redirect('/')

    if request.method == 'POST':
        actualite_form = ActualiteForm(request.POST, request.FILES)

        photo = request.FILES.get('photo')
        if photo:
            valid_extensions = ['.jpg', '.jpeg', '.png', '.webp']
            valid_mime_types = ['image/jpeg', 'image/png', 'image/webp']
            extension = os.path.splitext(photo.name)[1].lower()
            if extension not in valid_extensions or photo.content_type not in valid_mime_types:
                actualite_form.add_error('photo', 'Seuls les formats JPG, PNG et WebP sont acceptés.')

        if actualite_form.is_valid():
            actualite = actualite_form.save(commit=False)
            actualite.auteur = request.user
            actualite.save()
            return redirect('backoffice:articles')
    else:
        actualite_form = ActualiteForm()

    actualites = Actualite.objects.order_by('-date_publication')

    return render(request, 'actualite/liste_actualites.html', {
        'actualites': actualites,
        'actualite_form': actualite_form,
    })

@login_required(login_url='/')
def edit_actualite(request, actualite_id):
    """Vue pour modifier un article"""
    if not request.user.is_authenticated or not request.user.is_staff:
        return redirect('/')

    actualite = get_object_or_404(Actualite, id=actualite_id)

    if request.method == 'POST':
        # Récupérer les données du formulaire
        titre = request.POST.get('titre')
        contenu = request.POST.get('contenu')
        theme = request.POST.get('theme')
        status = request.POST.get('status')
        mis_en_avant = request.POST.get('mis_en_avant') == 'on'
        photo = request.FILES.get('photo')

        # Validation des fichiers photo
        if photo:
            valid_extensions = ['.jpg', '.jpeg', '.png', '.webp']
            valid_mime_types = ['image/jpeg', 'image/png', 'image/webp']
            extension = os.path.splitext(photo.name)[1].lower()
            if extension not in valid_extensions or photo.content_type not in valid_mime_types:
                from django.contrib import messages
                messages.error(request, 'Seuls les formats JPG, PNG et WebP sont acceptés.')
                return render(request, 'actualite/edit_actualite.html', {'actualite': actualite})

        # Mettre à jour l'article
        actualite.titre = titre
        actualite.contenu = contenu
        actualite.theme = theme
        actualite.status = status
        actualite.mis_en_avant = mis_en_avant

        if photo:
            actualite.photo = photo

        actualite.save()

        from django.contrib import messages
        messages.success(request, f'Article "{titre}" modifié avec succès!')
        return redirect('backoffice:articles')

    # GET request - afficher le formulaire
    return render(request, 'actualite/edit_actualite.html', {
        'actualite': actualite
    })



@login_required(login_url='/')
def get_actualite(request, actualite_id):
    """API pour récupérer les données d'un article"""
    if not request.user.is_authenticated or not request.user.is_staff:
        return redirect('/')

    actualite = get_object_or_404(Actualite, id=actualite_id)
    data = {
        'titre': actualite.titre,
        'contenu': actualite.contenu,
        'theme': actualite.theme,
        'status': actualite.status,
        'redacteur': actualite.redacteur,
        'collaborateur': actualite.collaborateur,
        'tags': actualite.tags,
        'photo_url': actualite.photo.url if actualite.photo else '',
        'petit_description': actualite.petit_description,
        'mis_en_avant': actualite.mis_en_avant
    }
    return JsonResponse(data)

@csrf_exempt
def delete_actualite(request, actualite_id):
    # Vérifier l'authentification pour les requêtes AJAX
    if not request.user.is_authenticated or not request.user.is_staff:
        return JsonResponse({'error': 'Non autorisé'}, status=403)

    if request.method == 'POST':
        try:
            actualite = get_object_or_404(Actualite, id=actualite_id)

            # Supprimer l'image associée si elle existe
            if actualite.photo:
                try:
                    from django.core.files.storage import default_storage
                    if default_storage.exists(actualite.photo.name):
                        default_storage.delete(actualite.photo.name)
                        print(f"✅ Image photo supprimée: {actualite.photo.name}")
                except Exception as e:
                    print(f"⚠️ Erreur suppression image photo: {e}")



            # Supprimer l'article
            titre = actualite.titre
            actualite.delete()
            print(f"✅ Article supprimé: {titre}")

            return JsonResponse({'success': True, 'message': 'Article supprimé avec succès'})

        except Exception as e:
            print(f"❌ Erreur suppression: {e}")
            return JsonResponse({'error': f'Erreur lors de la suppression: {str(e)}'}, status=500)

    return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

@login_required(login_url='/')
def highlight_actualite(request, actualite_id):
    """Vue pour basculer la mise en avant d'un article"""
    if not request.user.is_authenticated or not request.user.is_staff:
        return JsonResponse({'error': 'Non autorisé'}, status=403)

    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    try:
        actualite = get_object_or_404(Actualite, id=actualite_id)

        # Si on met en avant, retirer la mise en avant des autres articles
        if not actualite.mis_en_avant:
            Actualite.objects.exclude(id=actualite_id).update(mis_en_avant=False)

        # Basculer la mise en avant
        actualite.mis_en_avant = not actualite.mis_en_avant
        actualite.save()

        status_text = "mis en avant" if actualite.mis_en_avant else "retiré de la mise en avant"

        return JsonResponse({
            'success': True,
            'message': f'Article "{actualite.titre}" {status_text} avec succès',
            'mis_en_avant': actualite.mis_en_avant
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Erreur lors de la mise à jour: {str(e)}'
        }, status=500)

@login_required(login_url='/')
def update_status_actualite(request, actualite_id):
    """Vue pour mettre à jour le statut d'un article"""
    if not request.user.is_authenticated or not request.user.is_staff:
        return JsonResponse({'error': 'Non autorisé'}, status=403)

    if request.method == 'POST':
        try:
            actualite = get_object_or_404(Actualite, id=actualite_id)
            new_status = request.POST.get('status')

            if new_status in ['draft', 'published', 'archived']:
                actualite.status = new_status
                actualite.save()
                return JsonResponse({
                    'success': True,
                    'message': f'Statut mis à jour vers {actualite.get_status_display()}',
                    'new_status': new_status
                })
            else:
                return JsonResponse({'error': 'Statut invalide'}, status=400)

        except Exception as e:
            return JsonResponse({'error': f'Erreur lors de la mise à jour: {str(e)}'}, status=500)

    return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

def afficher_actualites(request):
    """Vue publique pour afficher les articles"""
    # Afficher tous les articles pour les tests, mais en production on filtrerait par 'published'
    actualites = Actualite.objects.all().order_by('-date_publication')

    context = {
        'actualites': actualites,
    }

    # Pour les tests, retourner une réponse JSON simple
    from django.http import JsonResponse
    if request.GET.get('format') == 'json':
        articles_data = []
        for article in actualites:
            articles_data.append({
                'id': article.id,
                'titre': article.titre,
                'status': article.status
            })
        return JsonResponse({'articles': articles_data})

    # Utiliser le template de liste des actualités
    return render(request, 'actualite/liste_actualites.html', context)

@login_required(login_url='/')
def delete_commentaire(request, commentaire_id):
    if not request.user.is_authenticated or not request.user.is_superuser:
        return redirect('/')
    commentaire = get_object_or_404(Commentaire, id=commentaire_id)
    if request.method == 'POST':
        commentaire.delete()
    return redirect('moderation')


@login_required(login_url='/')
def detail_actualite(request, actualite_id):
    """Vue pour afficher le détail d'un article"""
    actualite = get_object_or_404(Actualite, id=actualite_id)
    return render(request, 'actualite/detail_actualite.html', {'actualite': actualite})


# ============================================================================
# VUES POUR LA GÉNÉRATION D'ARTICLES AVEC IA
# ============================================================================

@csrf_exempt
def generate_article_api(request):
    """API pour générer un article avec Gemini"""

    print("🚀 === DÉBUT GÉNÉRATION ARTICLE ===")
    print(f"👤 Utilisateur: {request.user}")
    print(f"🔐 Authentifié: {request.user.is_authenticated}")
    print(f"👨‍💼 Staff: {request.user.is_staff}")
    print(f"📡 Méthode: {request.method}")

    # Vérification de l'authentification
    if not request.user.is_authenticated:
        print("❌ Utilisateur non authentifié")
        return JsonResponse({'error': 'Authentification requise'}, status=401)

    if not request.user.is_staff:
        print("❌ Utilisateur non staff")
        return JsonResponse({'error': 'Permissions insuffisantes'}, status=403)

    if request.method != 'POST':
        print("❌ Méthode non POST")
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    try:
        # Import du générateur
        from .services import GeminiArticleGenerator

        # Debug: vérifier si le générateur est disponible
        print(f"🔍 GeminiArticleGenerator importé avec succès")

        # Debug: afficher le contenu de la requête
        print(f"📡 Requête reçue: {request.body}")

        data = json.loads(request.body)
        sujet = data.get('sujet', '').strip()
        auto_publish = data.get('auto_publish', False)
        custom_image_prompt = data.get('image_prompt', '').strip()

        print(f"📝 Sujet extrait: '{sujet}'")
        print(f"📤 Auto-publier: {auto_publish}")
        print(f"🎨 Prompt image custom: '{custom_image_prompt}'" if custom_image_prompt else "🎨 Pas de prompt image custom")

        if not sujet:
            return JsonResponse({'error': 'Sujet requis'}, status=400)

        # Générer l'article
        print(f"🤖 Génération de l'article...")
        try:
            generator = GeminiArticleGenerator()
        except ValueError as e:
            print(f"❌ Erreur configuration: {e}")
            return JsonResponse({
                'error': 'Configuration manquante: Clé API Gemini non configurée. Veuillez contacter l\'administrateur.'
            }, status=500)

        article = generator.generate_article(
            sujet=sujet,
            auteur_username=request.user.username,
            custom_image_prompt=custom_image_prompt,
            auto_publish=auto_publish
        )

        print(f"✅ Article généré: {article.titre}")

        return JsonResponse({
            'success': True,
            'article': {
                'id': article.id,
                'titre': article.titre,
                'slug': article.slug,
                'theme': article.get_theme_display(),
                'status': article.get_status_display(),
                'tags': article.tags or '',
                'tags_list': article.get_tags_list(),
                'petit_description': article.petit_description or '',
                'contenu': article.contenu or '',
                'url': f'/actualite/detail/{article.slug}/',
                'url_by_id': f'/actualite/detail/{article.id}/',
                'api_url': f'/actualite/api/article/{article.slug}/',
                'edit_url': f'/backoffice/articles/'
            }
        })

    except json.JSONDecodeError as e:
        print(f"❌ Erreur JSON: {e}")
        return JsonResponse({'error': 'JSON invalide'}, status=400)
    except Exception as e:
        print(f"❌ Erreur génération: {e}")
        import traceback
        traceback.print_exc()
        return JsonResponse({'error': str(e)}, status=500)


# ============================================================================
# VUE CRUD POUR LA GESTION DES ARTICLES (ex-backoffice)
# ============================================================================

@login_required(login_url='/')
def articles_view(request):
    """Redirection vers le nouveau back-office unifié"""
    if not request.user.is_authenticated or not request.user.is_staff:
        return redirect('/')

    # Rediriger vers le nouveau back-office
    return redirect('backoffice:articles')


@csrf_exempt
@login_required(login_url='/')
def update_article_status_api(request, article_id):
    """AJAX endpoint to update article status"""
    if not request.user.is_authenticated or not request.user.is_staff:
        return JsonResponse({'error': 'Non autorisé'}, status=403)

    if request.method == 'POST':
        try:
            article = get_object_or_404(Actualite, id=article_id)

            # Handle both JSON and form data
            if request.content_type == 'application/json':
                data = json.loads(request.body)
                new_status = data.get('status')
            else:
                new_status = request.POST.get('status')

            # Validate status
            valid_statuses = ['draft', 'published', 'archived']
            if new_status not in valid_statuses:
                return JsonResponse({'error': 'Statut invalide'}, status=400)

            # Update status
            article.status = new_status
            article.save()

            return JsonResponse({
                'success': True,
                'message': f'Statut mis à jour vers "{article.get_status_display()}"',
                'new_status': new_status,
                'status_display': article.get_status_display()
            })

        except Exception as e:
            return JsonResponse({'error': f'Erreur: {str(e)}'}, status=500)

    return JsonResponse({'error': 'Méthode non autorisée'}, status=405)


@csrf_exempt
@login_required(login_url='/')
def regenerate_image_api(request, article_id):
    """API pour régénérer l'image d'un article avec un prompt personnalisé"""
    if not request.user.is_authenticated or not request.user.is_staff:
        return JsonResponse({'error': 'Non autorisé'}, status=403)

    if request.method == 'POST':
        try:
            article = get_object_or_404(Actualite, id=article_id)

            data = json.loads(request.body)
            custom_prompt = data.get('prompt', '').strip()

            if not custom_prompt:
                return JsonResponse({'error': 'Prompt requis'}, status=400)

            # Importer le générateur
            from .services import GeminiArticleGenerator
            generator = GeminiArticleGenerator()

            # Générer nouvelle image avec le prompt personnalisé
            new_image_result = generator._generate_article_image(
                article.titre,
                custom_prompt=custom_prompt
            )

            if new_image_result:
                # Supprimer l'ancienne image (photo uploadée)
                if article.photo:
                    try:
                        import os
                        from django.conf import settings

                        # Construire le chemin complet du fichier
                        file_path = os.path.join(settings.MEDIA_ROOT, str(article.photo))

                        if os.path.exists(file_path):
                            os.remove(file_path)
                            print(f"✅ Ancienne image photo supprimée: {article.photo}")
                        else:
                            print(f"ℹ️ Ancienne image photo déjà supprimée: {article.photo}")
                    except Exception as e:
                        print(f"⚠️ Erreur suppression ancienne image photo: {e}")

                # Stocker l'image dans le champ photo uniquement
                if new_image_result.startswith('articles/'):
                    # Image locale AVIF - stocker dans photo
                    article.photo = new_image_result
                elif new_image_result.startswith('/media/'):
                    # Convertir le chemin en nom de fichier relatif
                    filename = new_image_result.replace('/media/', '')
                    article.photo = filename
                else:
                    # URL externe - stocker dans photo aussi
                    article.photo = new_image_result

                article.save()

                return JsonResponse({
                    'success': True,
                    'message': 'Image régénérée avec succès',
                    'new_photo_url': f"/media/{article.photo}",
                    'prompt_used': custom_prompt
                })
            else:
                return JsonResponse({
                    'success': False,
                    'error': 'Impossible de générer l\'image'
                }, status=500)

        except json.JSONDecodeError:
            return JsonResponse({'error': 'JSON invalide'}, status=400)
        except Exception as e:
            return JsonResponse({'error': f'Erreur: {str(e)}'}, status=500)

    return JsonResponse({'error': 'Méthode non autorisée'}, status=405)


# ============================================================================
# VUES POUR GESTION DES SLUGS
# ============================================================================

def get_article_by_slug(request, slug):
    """API pour récupérer un article par son slug"""
    try:
        article = get_object_or_404(Actualite, slug=slug)

        # Incrémenter le compteur de vues
        article.increment_access_count()

        # Utiliser le serializer détaillé
        from .serializers import ActualiteDetailSerializer
        serializer = ActualiteDetailSerializer(article)

        return JsonResponse({
            'success': True,
            'article': serializer.data
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Article non trouvé: {str(e)}'
        }, status=404)


def detail_actualite_by_slug(request, slug):
    """Vue détail d'un article par slug (pour le front-end)"""
    try:
        article = get_object_or_404(Actualite, slug=slug)

        # Incrémenter le compteur de vues
        article.increment_access_count()

        # Récupérer les commentaires
        try:
            from commentaire.models import Commentaire
            commentaires = Commentaire.objects.filter(
                article=article,
                is_approved=True
            ).order_by('-date_creation')
        except Exception as e:
            print(f"⚠️ Erreur commentaires: {e}")
            commentaires = []

        # Récupérer des articles similaires (même thème, excluant l'article actuel)
        articles_similaires = Actualite.objects.filter(
            theme=article.theme,
            status='published'
        ).exclude(id=article.id).order_by('-date_publication')[:3]

        context = {
            'actualite': article,  # Le template attend 'actualite'
            'article': article,    # Garder aussi 'article' pour compatibilité
            'commentaires': commentaires,
            'articles_similaires': articles_similaires,
        }

        return render(request, 'actualite/detail_actualite.html', context)

    except Exception as e:
        return render(request, 'actualite/article_not_found.html', {
            'error': f'Article non trouvé: {str(e)}'
        }, status=404)


def performance_metrics_api(request):
    """API pour récupérer les métriques de performance du site"""
    try:
        from django.db import connection
        from django.core.cache import cache
        import time

        start_time = time.time()

        # Métriques de base de données
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM actualite_actualite WHERE status = 'published'")
            published_articles = cursor.fetchone()[0]

            cursor.execute("SELECT AVG(access_count) FROM actualite_actualite WHERE status = 'published'")
            avg_views = cursor.fetchone()[0] or 0

        # Métriques de performance
        db_query_time = time.time() - start_time

        # Vérifier le cache
        cache_test_key = 'performance_test'
        cache_start = time.time()
        cache.set(cache_test_key, 'test', 1)
        cache.get(cache_test_key)
        cache_time = time.time() - cache_start

        metrics = {
            'database': {
                'published_articles': published_articles,
                'average_views': round(avg_views, 2),
                'query_time_ms': round(db_query_time * 1000, 2)
            },
            'cache': {
                'response_time_ms': round(cache_time * 1000, 2),
                'status': 'operational'
            },
            'images': {
                'formats_supported': ['AVIF'],
                'formats_deprecated': ['JPEG', 'PNG', 'WebP'],
                'optimization': 'avif_only',
                'compression': {
                    'avif_quality': 80,
                    'size_reduction': '50-60% vs JPEG'
                },
                'performance': 'ultimate'
            },
            'seo': {
                'slugs_enabled': True,
                'meta_tags': True,
                'structured_data': True
            },
            'timestamp': time.time()
        }

        return JsonResponse({
            'success': True,
            'metrics': metrics,
            'recommendations': [
                '🏆 AVIF UNIQUEMENT - Format ultime du web !',
                '🚀 Performance maximale: -60% vs JPEG',
                '⚡ Qualité supérieure avec compression optimale',
                '🎯 Simplicité: 1 seul format, 1 seule image',
                '💾 Économie maximale de bande passante',
                '🔮 Future-proof: Le format de demain'
            ]
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Erreur lors de la récupération des métriques: {str(e)}'
        }, status=500)


# ============================================================================
# API POUR ANALYSE DE PERTINENCE ET SUGGESTIONS D'ARTICLES
# ============================================================================

@csrf_exempt
@backoffice_api(whitelist_ips=['127.0.0.1', '::1', '***********/24'])
def suggest_articles_api(request):
    """API pour suggérer des articles pertinents pour un site de rencontre"""

    if request.method == 'GET':
        try:
            # Paramètres de la requête
            category = request.GET.get('category', None)  # amical, amour, libertin
            count = int(request.GET.get('count', 10))

            # Initialiser l'analyseur
            analyzer = DatingContentAnalyzer()

            # Obtenir les suggestions
            suggestions = analyzer.suggest_trending_articles(count=count, category=category)

            return JsonResponse({
                'success': True,
                'suggestions': suggestions,
                'total_count': len(suggestions),
                'category_filter': category,
                'analysis_date': datetime.now().isoformat()
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'Erreur lors de la génération des suggestions: {str(e)}'
            }, status=500)

    elif request.method == 'POST':
        try:
            # Analyser un sujet spécifique
            data = json.loads(request.body)
            topic = data.get('topic', '').strip()
            target_category = data.get('category', None)

            if not topic:
                return JsonResponse({'error': 'Sujet requis'}, status=400)

            # Initialiser l'analyseur
            analyzer = DatingContentAnalyzer()

            # Analyser la pertinence
            analysis = analyzer.analyze_content_relevance(topic, target_category)

            return JsonResponse({
                'success': True,
                'analysis': analysis,
                'recommendations': {
                    'should_create': analysis['final_score'] > 50,
                    'priority': 'high' if analysis['final_score'] > 75 else 'medium' if analysis['final_score'] > 50 else 'low',
                    'best_timing': 'now' if analysis['seasonal_relevance'] > 60 else 'later'
                }
            })

        except json.JSONDecodeError:
            return JsonResponse({'error': 'JSON invalide'}, status=400)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'Erreur lors de l\'analyse: {str(e)}'
            }, status=500)

    return JsonResponse({'error': 'Méthode non autorisée'}, status=405)


@csrf_exempt
@backoffice_api(whitelist_ips=['127.0.0.1', '::1', '***********/24'])
def analyze_topic_relevance_api(request):
    """API pour analyser la pertinence d'un sujet spécifique"""

    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    try:
        data = json.loads(request.body)
        topic = data.get('topic', '').strip()
        category = data.get('category', None)

        if not topic:
            return JsonResponse({'error': 'Sujet requis'}, status=400)

        # Initialiser l'analyseur
        analyzer = DatingContentAnalyzer()

        # Analyser la pertinence
        analysis = analyzer.analyze_content_relevance(topic, category)

        # Obtenir un aperçu des corrections
        preview = analyzer.get_correction_preview(topic) if hasattr(analyzer, 'get_correction_preview') else None

        return JsonResponse({
            'success': True,
            'topic': topic,
            'analysis': analysis,
            'verdict': {
                'recommended': analysis['final_score'] > 60,
                'score_category': (
                    'excellent' if analysis['final_score'] > 80 else
                    'good' if analysis['final_score'] > 60 else
                    'average' if analysis['final_score'] > 40 else
                    'poor'
                ),
                'main_strengths': [
                    f"Score SEO: {analysis['seo_potential']:.1f}/100",
                    f"Tendance: {analysis['trend_alignment']:.1f}/100",
                    f"Saisonnier: {analysis['seasonal_relevance']:.1f}/100"
                ],
                'estimated_monthly_traffic': analysis['estimated_traffic']
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'JSON invalide'}, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Erreur lors de l\'analyse: {str(e)}'
        }, status=500)