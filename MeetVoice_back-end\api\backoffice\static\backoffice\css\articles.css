/* Styles spécifiques pour la gestion des articles */

/* Badges de statut */
.status-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

.status-draft {
  background-color: #ffc107;
  color: #212529;
}

.status-published {
  background-color: #28a745;
  color: white;
}

.status-archived {
  background-color: #6c757d;
  color: white;
}

/* Dropdown de statut interactif */
.status-dropdown {
  border: none;
  background: transparent;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  min-width: 100px;
}

.status-dropdown:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.status-dropdown.status-draft {
  background-color: #ffc107;
  color: #212529;
}

.status-dropdown.status-published {
  background-color: #28a745;
  color: white;
}

.status-dropdown.status-archived {
  background-color: #6c757d;
  color: white;
}

/* États de chargement */
.status-updating {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.status-updating::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  margin: -6px 0 0 -6px;
  border: 2px solid transparent;
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Animation de succès */
.status-success {
  animation: statusSuccess 0.5s ease;
}

@keyframes statusSuccess {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Formulaire d'article */
.article-form {
  max-height: 80vh;
  overflow-y: auto;
}

.article-form .form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.article-form .form-control,
.article-form .form-select {
  border-radius: 0.5rem;
  border: 1px solid #ced4da;
  transition: all 0.2s ease;
}

.article-form .form-control:focus,
.article-form .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Actions de table */
.table-actions {
  white-space: nowrap;
}

.table-actions .btn-group .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

/* Étoile mise en avant */
.featured-star {
  color: #ffc107;
  font-size: 1.2rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Statistiques */
.stats-card {
  transition: transform 0.2s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.border-left-primary {
  border-left: 4px solid #007bff !important;
}

.border-left-success {
  border-left: 4px solid #28a745 !important;
}

.border-left-warning {
  border-left: 4px solid #ffc107 !important;
}

.border-left-info {
  border-left: 4px solid #17a2b8 !important;
}

/* Filtres */
.filters-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: none;
}

.filters-card .form-control,
.filters-card .form-select {
  background-color: white;
  border: 1px solid #ced4da;
}

/* Messages de notification */
.notification-message {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  min-width: 300px;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.notification-message.show {
  transform: translateX(0);
}

/* Modal personnalisée */
.modal-content {
  border-radius: 0.75rem;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 0.75rem 0.75rem 0 0;
  border-bottom: none;
}

.modal-header .btn-close {
  filter: invert(1);
}

/* Prévisualisation d'image */
.image-preview {
  max-width: 200px;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Progress bar pour génération IA */
.generation-progress {
  background: linear-gradient(45deg, #667eea, #764ba2);
  background-size: 200% 200%;
  animation: gradientShift 2s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Spinner personnalisé */
.custom-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(102, 126, 234, 0.3);
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Tags */
.tag-input {
  position: relative;
}

.tag-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ced4da;
  border-top: none;
  border-radius: 0 0 0.5rem 0.5rem;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
}

.tag-suggestion {
  padding: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.tag-suggestion:hover {
  background-color: #f8f9fa;
}

/* Responsive pour articles */
@media (max-width: 768px) {
  .table-actions .btn-group {
    flex-direction: column;
  }
  
  .table-actions .btn {
    margin-bottom: 0.25rem;
  }
  
  .article-form {
    max-height: 60vh;
  }
  
  .stats-card {
    margin-bottom: 1rem;
  }
}

/* ============================================================================
   STYLES POUR LES SUGGESTIONS D'ARTICLES IA
   ============================================================================ */

/* Section suggestions */
.suggestions-section {
  background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
  border-radius: 0.75rem;
  border-left: 4px solid #ffc107;
}

.suggestion-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 0.75rem;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.suggestion-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.suggestion-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.suggestion-card:hover::before {
  transform: scaleY(1);
}

.suggestion-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
  line-height: 1.4;
}

.suggestion-angle {
  color: #6c757d;
  font-size: 0.85rem;
  margin-bottom: 0.75rem;
  font-style: italic;
}

.suggestion-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  color: #495057;
}

.stat-item i {
  margin-right: 0.25rem;
  width: 12px;
  text-align: center;
}

.suggestion-badges {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.75rem;
}

.score-badge {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 0.25rem 0.6rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.score-badge.score-excellent {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.score-badge.score-good {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.score-badge.score-average {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: #212529;
}

.score-badge.score-poor {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.category-badge {
  padding: 0.2rem 0.5rem;
  border-radius: 0.75rem;
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.category-amical {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.category-amour {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.category-libertin {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.category-tendance {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.category-saisonnier {
  background-color: #e2e3e5;
  color: #383d41;
  border: 1px solid #d6d8db;
}

.suggestion-recommendation {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: #e7f3ff;
  border-left: 3px solid #007bff;
  border-radius: 0 0.25rem 0.25rem 0;
  font-size: 0.8rem;
  color: #004085;
}

/* Boutons de filtre suggestions */
.suggestions-filters .btn-group .btn {
  font-size: 0.8rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.suggestions-filters .btn-outline-primary.active {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.suggestions-filters .btn-outline-success.active {
  background-color: #28a745;
  border-color: #28a745;
  color: white;
}

.suggestions-filters .btn-outline-danger.active {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
}

.suggestions-filters .btn-outline-warning.active {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

/* Loading spinner pour suggestions */
.suggestions-loading {
  text-align: center;
  padding: 2rem;
}

.suggestions-loading .spinner-border {
  width: 2rem;
  height: 2rem;
  border-width: 0.2em;
}

/* Message vide */
.suggestions-empty {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
}

.suggestions-empty i {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Responsive pour suggestions */
@media (max-width: 768px) {
  .suggestion-stats {
    flex-direction: column;
    gap: 0.5rem;
  }

  .suggestion-badges {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .suggestions-filters .btn-group {
    flex-wrap: wrap;
  }

  .suggestions-filters .btn {
    margin-bottom: 0.25rem;
  }
}

/* Animation d'apparition */
.suggestion-card {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
