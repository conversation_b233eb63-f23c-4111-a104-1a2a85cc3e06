/* Styles de base pour le back-office */

/* Variables CSS */
:root {
  --sidebar-bg-start: #2a1d34;
  --sidebar-bg-end: #3a2a44;
  --header-bg-start: #667eea;
  --header-bg-end: #764ba2;
  --sidebar-width: 250px;
  --navbar-height: 70px;
  --border-radius: 1rem;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 15px rgba(0, 0, 0, 0.15);
  --transition-smooth: all 0.3s ease;
}

/* Layout principal */
body {
  background-color: #f8f9fa;
}

.backoffice-container {
  margin-top: 0;
  min-height: calc(100vh - var(--navbar-height));
}

/* Sidebar */
.sidebar {
  background: linear-gradient(135deg, var(--sidebar-bg-start) 0%, var(--sidebar-bg-end) 100%);
  min-height: calc(100vh - var(--navbar-height));
  height: calc(100vh - var(--navbar-height));
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
  position: fixed;
  top: var(--navbar-height);
  left: 0;
  width: var(--sidebar-width);
  z-index: 1000;
  overflow-y: auto;
}

.sidebar .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: 0.8rem 1.2rem;
  margin: 0.2rem 0.5rem;
  border-radius: 0.5rem;
  transition: var(--transition-smooth);
  font-weight: 500;
}

.sidebar .nav-link:hover {
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.sidebar .nav-link.active {
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-light);
}

.sidebar .nav-link i {
  width: 20px;
  text-align: center;
}

/* Contenu principal */
.main-content {
  background-color: #ffffff;
  border-radius: var(--border-radius) 0 0 var(--border-radius);
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.05);
  padding: 2rem;
  min-height: calc(100vh - var(--navbar-height));
  margin-left: var(--sidebar-width);
  width: calc(100% - var(--sidebar-width));
}

/* En-tête de page */
.page-header {
  background: linear-gradient(135deg, var(--header-bg-start) 0%, var(--header-bg-end) 100%);
  color: white;
  padding: 1.5rem 2rem;
  border-radius: var(--border-radius);
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.page-header h1 {
  margin: 0;
  font-weight: 600;
}

/* Breadcrumb */
.breadcrumb-nav {
  background-color: transparent;
  padding: 0;
  margin-bottom: 1rem;
}

.breadcrumb-nav .breadcrumb-item a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
}

.breadcrumb-nav .breadcrumb-item.active {
  color: rgba(255, 255, 255, 1);
}

/* Cartes et composants */
.card {
  border: none;
  box-shadow: var(--shadow-light);
  border-radius: 0.75rem;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  border-radius: 0.75rem 0.75rem 0 0 !important;
}

/* Boutons */
.btn {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: var(--transition-smooth);
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

/* Tables */
.table {
  border-radius: 0.5rem;
  overflow: hidden;
}

.table thead th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    position: relative;
    width: 100%;
    border-radius: 0;
    margin-bottom: 1rem;
    top: auto;
    left: auto;
  }

  .main-content {
    border-radius: 0;
    padding: 1rem;
    margin-left: 0;
    width: 100%;
  }

  .backoffice-container {
    margin-top: var(--navbar-height);
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Utilitaires */
.shadow-custom {
  box-shadow: var(--shadow-medium);
}

.border-radius-custom {
  border-radius: var(--border-radius);
}

.transition-smooth {
  transition: var(--transition-smooth);
}
