/**
 * JavaScript pour la gestion des articles dans le back-office
 */

// Fonction pour changer le statut d'un article
function updateArticleStatus(articleId, newStatus) {
    console.log(`🔄 Mise à jour statut article ${articleId} vers ${newStatus}`);
    
    // Récupérer le token CSRF
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
                     document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                     getCookie('csrftoken');
    
    if (!csrfToken) {
        console.error('❌ Token CSRF non trouvé');
        alert('Erreur: Token CSRF manquant');
        return;
    }
    
    // Faire la requête AJAX
    fetch(`/actualite/api/update-status/${articleId}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
        },
        body: JSON.stringify({
            status: newStatus
        })
    })
    .then(response => {
        console.log(`📡 Réponse: ${response.status}`);
        return response.json();
    })
    .then(data => {
        if (data.success) {
            console.log('✅ Statut mis à jour avec succès');
            
            // Mettre à jour l'affichage du badge de statut
            updateStatusBadge(articleId, newStatus, data.status_display);
            
            // Afficher un message de succès
            showMessage(data.message, 'success');
            
            // Optionnel: recharger la page pour mettre à jour les statistiques
            setTimeout(() => {
                window.location.reload();
            }, 1000);
            
        } else {
            console.error('❌ Erreur:', data.error);
            showMessage('Erreur: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('❌ Erreur réseau:', error);
        showMessage('Erreur technique lors de la mise à jour', 'error');
    });
}

// Fonction pour mettre à jour le badge de statut dans le tableau
function updateStatusBadge(articleId, newStatus, statusDisplay) {
    const badge = document.querySelector(`[data-article-id="${articleId}"] .badge`);
    if (badge) {
        // Supprimer les anciennes classes de statut
        badge.classList.remove('badge-draft', 'badge-published', 'badge-archived');
        
        // Ajouter la nouvelle classe
        badge.classList.add(`badge-${newStatus}`);
        
        // Mettre à jour le texte
        badge.textContent = statusDisplay;
    }
}

// Fonction pour afficher des messages
function showMessage(message, type) {
    // Créer une notification toast
    const toastContainer = document.getElementById('toastContainer') || createToastContainer();
    
    const toastId = 'toast-' + Date.now();
    const bgClass = type === 'success' ? 'bg-success' : 'bg-danger';
    const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';
    
    const toastHTML = `
        <div id="${toastId}" class="toast ${bgClass} text-white" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-body">
                <i class="fas fa-${icon} me-2"></i>
                ${message}
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHTML);
    
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
    toast.show();
    
    // Supprimer le toast après fermeture
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

// Fonction pour créer le conteneur de toasts
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toastContainer';
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

// Fonction pour récupérer le cookie CSRF
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Fonction pour supprimer un article (avec confirmation)
function deleteArticle(articleId, articleTitle) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer l'article "${articleTitle}" ?\n\nCette action est irréversible.`)) {
        console.log(`🗑️ Suppression article ${articleId}`);
        
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || getCookie('csrftoken');
        
        fetch(`/actualite/delete/${articleId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken,
            }
        })
        .then(response => {
            if (response.ok) {
                showMessage('Article supprimé avec succès', 'success');
                // Supprimer la ligne du tableau
                const row = document.querySelector(`[data-article-id="${articleId}"]`);
                if (row) {
                    row.remove();
                }
                // Recharger après un délai
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showMessage('Erreur lors de la suppression', 'error');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showMessage('Erreur technique lors de la suppression', 'error');
        });
    }
}

// Fonction pour mettre en avant un article
function toggleHighlight(articleId) {
    console.log(`⭐ Toggle highlight article ${articleId}`);

    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                     document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                     getCookie('csrftoken');

    console.log('🔑 Token CSRF:', csrfToken ? 'Trouvé' : 'Non trouvé');

    if (!csrfToken) {
        console.error('❌ Token CSRF manquant');
        showMessage('Erreur: Token CSRF manquant', 'error');
        return;
    }

    console.log(`📡 Envoi requête vers: /actualite/highlight/${articleId}/`);

    fetch(`/actualite/highlight/${articleId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': csrfToken,
            'Content-Type': 'application/json',
        }
    })
    .then(response => {
        console.log(`📡 Réponse reçue: ${response.status}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('📊 Données reçues:', data);
        if (data.success) {
            showMessage(data.message, 'success');
            // Recharger pour mettre à jour l'affichage
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showMessage('Erreur: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('❌ Erreur complète:', error);
        showMessage('Erreur technique: ' + error.message, 'error');
    });
}

// Fonction pour modifier un article
function editArticle(articleId) {
    console.log(`✏️ Modification article ${articleId}`);

    // Rediriger vers la page d'édition
    window.location.href = `/actualite/edit/${articleId}/`;
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 Page articles back-office chargée');
    
    // Ajouter les event listeners pour les boutons d'action
    document.querySelectorAll('[data-action]').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const action = this.dataset.action;
            const articleId = this.dataset.articleId;
            const articleTitle = this.dataset.articleTitle;
            
            switch(action) {
                case 'edit':
                    editArticle(articleId);
                    break;
                case 'delete':
                    deleteArticle(articleId, articleTitle);
                    break;
                case 'highlight':
                    toggleHighlight(articleId);
                    break;
                default:
                    console.log(`Action non reconnue: ${action}`);
            }
        });
    });
    
    // Event listeners pour les boutons de statut
    document.querySelectorAll('[data-status-action]').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const articleId = this.dataset.articleId;
            const newStatus = this.dataset.statusAction;
            
            updateArticleStatus(articleId, newStatus);
        });
    });
});

console.log('✅ Script articles.js chargé');

// ============================================================================
// FONCTIONS POUR LES SUGGESTIONS D'ARTICLES IA
// ============================================================================

let currentCategory = 'all';
let currentAnalysis = null;

// Charger les suggestions au démarrage
document.addEventListener('DOMContentLoaded', function() {
    // Charger les suggestions après un petit délai pour laisser la page se charger
    setTimeout(() => {
        loadSuggestions('all');
    }, 500);
});

// Charger les suggestions d'articles
async function loadSuggestions(category = 'all') {
    currentCategory = category;

    console.log(`🎯 Chargement suggestions catégorie: ${category}`);

    // Afficher le spinner
    const spinner = document.getElementById('loadingSpinner');
    const container = document.getElementById('suggestionsContainer');
    const actions = document.getElementById('suggestionsActions');

    if (spinner) spinner.style.display = 'block';
    if (container) container.innerHTML = '';
    if (actions) actions.style.display = 'none';

    // Mettre à jour les boutons actifs
    document.querySelectorAll('.suggestions-filters .btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Marquer le bouton actif
    const activeBtn = document.querySelector(`[onclick="loadSuggestions('${category}')"]`);
    if (activeBtn) activeBtn.classList.add('active');

    try {
        const url = category === 'all'
            ? '/actualite/api/suggest-articles/?count=6'
            : `/actualite/api/suggest-articles/?count=6&category=${category}`;

        console.log(`📡 Requête vers: ${url}`);

        const response = await fetch(url);
        const data = await response.json();

        console.log('📊 Données reçues:', data);

        if (data.success) {
            displaySuggestions(data.suggestions);
            if (actions) actions.style.display = 'block';
        } else {
            showSuggestionsError('Erreur lors du chargement des suggestions');
        }
    } catch (error) {
        console.error('❌ Erreur:', error);
        showSuggestionsError('Erreur de connexion');
    } finally {
        if (spinner) spinner.style.display = 'none';
    }
}

// Afficher les suggestions
function displaySuggestions(suggestions) {
    const container = document.getElementById('suggestionsContainer');

    if (!container) {
        console.error('❌ Conteneur suggestions non trouvé');
        return;
    }

    if (suggestions.length === 0) {
        container.innerHTML = `
            <div class="suggestions-empty">
                <i class="fas fa-search fa-2x mb-3"></i>
                <p>Aucune suggestion trouvée pour cette catégorie.</p>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshSuggestions()">
                    <i class="fas fa-sync-alt me-1"></i>Actualiser
                </button>
            </div>
        `;
        return;
    }

    const html = suggestions.map((suggestion, index) => {
        const scoreClass = getScoreClass(suggestion.score);
        const categoryClass = getCategoryClass(suggestion.category);

        return `
            <div class="suggestion-card" onclick="selectSuggestion('${escapeHtml(suggestion.topic)}', '${suggestion.category}')" style="animation-delay: ${index * 0.1}s">
                <div class="suggestion-title">${escapeHtml(suggestion.topic)}</div>

                <div class="suggestion-angle">${escapeHtml(suggestion.suggested_angle)}</div>

                <div class="suggestion-stats">
                    <div class="stat-item">
                        <i class="fas fa-chart-line text-primary"></i>
                        SEO: ${suggestion.seo_potential.toFixed(0)}/100
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-fire text-danger"></i>
                        Tendance: ${suggestion.trend_score.toFixed(0)}/100
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-users text-info"></i>
                        ${suggestion.estimated_traffic.toFixed(0)} visiteurs/mois
                    </div>
                </div>

                <div class="suggestion-badges">
                    <span class="score-badge ${scoreClass}">${suggestion.score.toFixed(0)}/100</span>
                    <span class="category-badge ${categoryClass}">${suggestion.category}</span>
                </div>

                ${suggestion.recommendations.length > 0 ? `
                    <div class="suggestion-recommendation">
                        <i class="fas fa-lightbulb me-1"></i>
                        ${escapeHtml(suggestion.recommendations[0])}
                    </div>
                ` : ''}
            </div>
        `;
    }).join('');

    container.innerHTML = html;
    console.log(`✅ ${suggestions.length} suggestions affichées`);
}

// Obtenir la classe CSS selon le score
function getScoreClass(score) {
    if (score >= 80) return 'score-excellent';
    if (score >= 65) return 'score-good';
    if (score >= 45) return 'score-average';
    return 'score-poor';
}

// Obtenir la classe CSS selon la catégorie
function getCategoryClass(category) {
    const classes = {
        'amical': 'category-amical',
        'amour': 'category-amour',
        'libertin': 'category-libertin',
        'tendance': 'category-tendance',
        'saisonnier': 'category-saisonnier'
    };
    return classes[category] || 'category-tendance';
}

// Échapper le HTML pour éviter les injections
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Sélectionner une suggestion
function selectSuggestion(topic, category) {
    console.log(`🎯 Suggestion sélectionnée: ${topic} (${category})`);

    const message = `Voulez-vous générer un article sur le sujet :\n\n"${topic}"\n\nCatégorie: ${category}\n\nLa génération peut prendre quelques minutes.`;

    if (confirm(message)) {
        generateArticleFromSuggestion(topic, category);
    }
}

// Générer un article depuis une suggestion
async function generateArticleFromSuggestion(topic, category) {
    console.log(`🤖 Génération article: ${topic}`);

    // Afficher un message de progression
    showMessage('Génération de l\'article en cours...', 'info');

    try {
        const csrfToken = getCookie('csrftoken');

        const response = await fetch('/actualite/api/generate-article/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken,
            },
            body: JSON.stringify({
                sujet: topic,
                theme: category,
                auto_publish: false,
                image_prompt: `professional ${topic} illustration, modern, clean`
            })
        });

        const data = await response.json();

        if (data.success) {
            showMessage('Article généré avec succès !', 'success');

            // Recharger la page après un délai
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showMessage('Erreur lors de la génération: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('❌ Erreur génération:', error);
        showMessage('Erreur technique lors de la génération', 'error');
    }
}

// Rafraîchir les suggestions
function refreshSuggestions() {
    console.log('🔄 Actualisation des suggestions');
    loadSuggestions(currentCategory);
}

// Afficher le modal d'analyse
function showAnalyzeModal() {
    console.log('🔍 Ouverture modal analyse');

    // Créer le modal s'il n'existe pas
    if (!document.getElementById('analyzeModal')) {
        createAnalyzeModal();
    }

    const modal = new bootstrap.Modal(document.getElementById('analyzeModal'));
    modal.show();

    // Focus sur le champ de saisie
    setTimeout(() => {
        const input = document.getElementById('topicInput');
        if (input) input.focus();
    }, 500);
}

// Créer le modal d'analyse
function createAnalyzeModal() {
    const modalHtml = `
        <div class="modal fade" id="analyzeModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-search me-2"></i>
                            Analyser la Pertinence d'un Sujet
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="analyzeForm">
                            <div class="mb-3">
                                <label for="topicInput" class="form-label">Sujet à analyser</label>
                                <input type="text" class="form-control" id="topicInput"
                                       placeholder="Ex: conseils pour premier rendez-vous">
                            </div>
                            <div class="mb-3">
                                <label for="categorySelect" class="form-label">Catégorie cible (optionnel)</label>
                                <select class="form-select" id="categorySelect">
                                    <option value="">Toutes les catégories</option>
                                    <option value="amical">Amical</option>
                                    <option value="amour">Amour</option>
                                    <option value="libertin">Libertin</option>
                                </select>
                            </div>
                        </form>
                        <div id="analysisResult" class="mt-4" style="display: none;">
                            <!-- Résultat de l'analyse -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                        <button type="button" class="btn btn-primary" onclick="analyzeTopic()">
                            <i class="fas fa-search me-2"></i>Analyser
                        </button>
                        <button type="button" class="btn btn-success" id="generateBtn" style="display: none;" onclick="generateFromAnalysis()">
                            <i class="fas fa-magic me-2"></i>Générer l'Article
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

// Analyser un sujet spécifique
async function analyzeTopic() {
    const topic = document.getElementById('topicInput')?.value.trim();
    const category = document.getElementById('categorySelect')?.value;

    if (!topic) {
        alert('Veuillez saisir un sujet à analyser');
        return;
    }

    console.log(`🔍 Analyse sujet: ${topic} (${category})`);

    try {
        const response = await fetch('/actualite/api/analyze-topic/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ topic, category })
        });

        const data = await response.json();

        if (data.success) {
            currentAnalysis = data;
            displayAnalysisResult(data);
        } else {
            showMessage('Erreur lors de l\'analyse: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('❌ Erreur analyse:', error);
        showMessage('Erreur de connexion', 'error');
    }
}

// Afficher le résultat de l'analyse
function displayAnalysisResult(data) {
    const container = document.getElementById('analysisResult');
    const analysis = data.analysis;
    const verdict = data.verdict;

    if (!container) return;

    const scoreColor = verdict.score_category === 'excellent' ? 'success' :
                      verdict.score_category === 'good' ? 'primary' :
                      verdict.score_category === 'average' ? 'warning' : 'danger';

    container.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Résultat de l'Analyse</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Score Global</h6>
                        <div class="progress mb-3">
                            <div class="progress-bar bg-${scoreColor}" style="width: ${analysis.final_score}%">
                                ${analysis.final_score.toFixed(1)}/100
                            </div>
                        </div>

                        <h6>Détails</h6>
                        <ul class="list-unstyled">
                            <li><strong>SEO:</strong> ${analysis.seo_potential.toFixed(0)}/100</li>
                            <li><strong>Tendance:</strong> ${analysis.trend_alignment.toFixed(0)}/100</li>
                            <li><strong>Saisonnier:</strong> ${analysis.seasonal_relevance.toFixed(0)}/100</li>
                            <li><strong>Trafic estimé:</strong> ${analysis.estimated_traffic.toFixed(0)} visiteurs/mois</li>
                        </ul>
                    </div>

                    <div class="col-md-6">
                        <h6>Verdict</h6>
                        <div class="alert alert-${scoreColor}">
                            <strong>${verdict.recommended ? '✅ Recommandé' : '⚠️ Peu recommandé'}</strong><br>
                            Qualité: ${verdict.score_category}<br>
                            Priorité: ${verdict.priority || 'N/A'}
                        </div>

                        ${analysis.recommendations.length > 0 ? `
                            <h6>Recommandations</h6>
                            <ul class="small">
                                ${analysis.recommendations.map(rec => `<li>${escapeHtml(rec)}</li>`).join('')}
                            </ul>
                        ` : ''}
                    </div>
                </div>

                <div class="mt-3">
                    <h6>Angle Suggéré</h6>
                    <p class="text-muted">${escapeHtml(analysis.suggested_angle)}</p>
                </div>
            </div>
        </div>
    `;

    container.style.display = 'block';

    // Afficher le bouton de génération si recommandé
    const generateBtn = document.getElementById('generateBtn');
    if (generateBtn) {
        generateBtn.style.display = verdict.recommended ? 'inline-block' : 'none';
    }
}

// Générer un article depuis l'analyse
function generateFromAnalysis() {
    if (currentAnalysis) {
        const topic = document.getElementById('topicInput')?.value.trim();
        const category = document.getElementById('categorySelect')?.value;

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('analyzeModal'));
        if (modal) modal.hide();

        // Générer l'article
        generateArticleFromSuggestion(topic, category);
    }
}

// Afficher une erreur pour les suggestions
function showSuggestionsError(message) {
    const container = document.getElementById('suggestionsContainer');
    if (container) {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
                <button class="btn btn-outline-danger btn-sm ms-2" onclick="refreshSuggestions()">
                    <i class="fas fa-sync-alt me-1"></i>Réessayer
                </button>
            </div>
        `;
    }
}

// Fonction pour afficher des messages avec type info
function showMessage(message, type) {
    // Utiliser la fonction existante mais ajouter le support pour 'info'
    const toastContainer = document.getElementById('toastContainer') || createToastContainer();

    const toastId = 'toast-' + Date.now();
    let bgClass, icon;

    switch(type) {
        case 'success':
            bgClass = 'bg-success';
            icon = 'check-circle';
            break;
        case 'error':
            bgClass = 'bg-danger';
            icon = 'exclamation-triangle';
            break;
        case 'info':
            bgClass = 'bg-info';
            icon = 'info-circle';
            break;
        default:
            bgClass = 'bg-secondary';
            icon = 'bell';
    }

    const toastHTML = `
        <div id="${toastId}" class="toast ${bgClass} text-white" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-body">
                <i class="fas fa-${icon} me-2"></i>
                ${message}
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHTML);

    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: type === 'info' ? 5000 : 3000 });
    toast.show();

    // Supprimer le toast après fermeture
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

console.log('✅ Fonctions suggestions d\'articles chargées');
