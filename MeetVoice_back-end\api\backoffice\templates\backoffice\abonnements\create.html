{% extends 'backoffice/base.html' %}
{% load static %}

{% block page_title_main %}<PERSON><PERSON>er un Abonnement{% endblock %}
{% block page_title_breadcrumb %}Nouveau{% endblock %}
{% block page_title_header %}Créer un Abonnement{% endblock %}
{% block page_icon %}<i class="fas fa-plus me-2"></i>{% endblock %}

{% block extra_css %}
<style>
.admin-form-section {
    background: white;
    border: 1px solid #dee2e6;
    margin-bottom: 1rem;
}

.admin-form-header {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

.admin-form-body {
    padding: 1rem;
}

.price-preview {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    padding: 0.75rem;
    text-align: center;
    font-weight: bold;
}

.required-field::after {
    content: " *";
    color: #dc3545;
}

.help-text {
    font-size: 0.875rem;
    color: #6c757d;
}
</style>
{% endblock %}

{% block page_actions %}
<div class="d-flex gap-2">
    <a href="{% url 'backoffice:abonnements_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
    </a>
    <button type="submit" form="abonnement-form" class="btn btn-primary">
        <i class="fas fa-save me-2"></i>Créer l'abonnement
    </button>
</div>
{% endblock %}

{% block backoffice_content %}
<form id="abonnement-form" method="post">
    {% csrf_token %}
    
    <!-- Informations de base -->
    <div class="form-section">
        <h5 class="form-section-header">
            <i class="fas fa-info-circle me-2"></i>Informations de base
        </h5>
        <div class="form-section-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="nom" class="form-label required-field">Nom du plan</label>
                        <input type="text" class="form-control" id="nom" name="nom" required
                               placeholder="Ex: Premium, Basic, VIP...">
                        <div class="form-text">Nom affiché sur la page de tarification</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="ordre_affichage" class="form-label">Ordre d'affichage</label>
                        <input type="number" class="form-control" id="ordre_affichage" name="ordre_affichage" 
                               value="0" min="0">
                        <div class="form-text">0 = premier plan affiché</div>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="description_courte" class="form-label">Description courte</label>
                <input type="text" class="form-control" id="description_courte" name="description_courte"
                       placeholder="Description courte du plan d'abonnement">
                <div class="form-text">Description affichée sous le nom du plan</div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">
                            Plan actif
                        </label>
                        <div class="form-text">Visible sur la page de tarification</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_popular" name="is_popular">
                        <label class="form-check-label" for="is_popular">
                            Plan populaire
                        </label>
                        <div class="form-text">Affiche un badge "Populaire"</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="entreprise" name="entreprise">
                        <label class="form-check-label" for="entreprise">
                            Plan entreprise
                        </label>
                        <div class="form-text">Plan destiné aux entreprises</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Tarification -->
    <div class="form-section">
        <h5 class="form-section-header">
            <i class="fas fa-euro-sign me-2"></i>Tarification
        </h5>
        <div class="form-section-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="prix_ttc" class="form-label">Prix TTC (€)</label>
                        <input type="number" class="form-control" id="prix_ttc" name="prix_ttc" 
                               step="0.01" min="0" onchange="calculatePrices()">
                        <div class="form-text">Prix toutes taxes comprises</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="prix_ht" class="form-label">Prix HT (€)</label>
                        <input type="number" class="form-control" id="prix_ht" name="prix_ht" 
                               step="0.01" min="0" onchange="calculatePrices()">
                        <div class="form-text">Prix hors taxes (calculé auto)</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="credits" class="form-label">Crédits inclus</label>
                        <input type="number" class="form-control" id="credits" name="credits" 
                               value="0" min="0">
                        <div class="form-text">Nombre de crédits du plan</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="interval" class="form-label required-field">Intervalle de facturation</label>
                        <select class="form-select" id="interval" name="interval" required onchange="updatePreview()">
                            {% for value, label in interval_choices %}
                            <option value="{{ value }}" {% if value == 'month' %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="interval_count" class="form-label">Nombre d'intervalles</label>
                        <input type="number" class="form-control" id="interval_count" name="interval_count" 
                               value="1" min="1" onchange="updatePreview()">
                        <div class="form-text">Ex: 3 pour "tous les 3 mois"</div>
                    </div>
                </div>
            </div>
            
            <!-- Aperçu du prix -->
            <div class="price-preview">
                <h6 class="mb-2">Aperçu du prix</h6>
                <div id="price-display" class="h4 text-primary">0€/mois</div>
                <small class="text-muted">Prix affiché aux utilisateurs</small>
            </div>
        </div>
    </div>
    
    <!-- Fonctionnalités -->
    <div class="form-section">
        <h5 class="form-section-header">
            <i class="fas fa-list me-2"></i>Fonctionnalités
        </h5>
        <div class="form-section-body">
            <div class="feature-input">
                <label for="features" class="form-label">Fonctionnalités du plan</label>
                <textarea class="form-control" id="features" name="features" rows="6"
                          placeholder="Une fonctionnalité par ligne&#10;Exemple:&#10;Accès aux profils premium&#10;Messages illimités&#10;Support prioritaire"></textarea>
                <div class="form-text">Entrez une fonctionnalité par ligne. Ces éléments seront affichés avec des puces sur la page de tarification.</div>
            </div>
            
            <!-- Aperçu des fonctionnalités -->
            <div class="mt-3">
                <h6>Aperçu des fonctionnalités :</h6>
                <ul id="features-preview" class="list-unstyled">
                    <li class="text-muted"><i class="fas fa-info-circle me-2"></i>Saisissez les fonctionnalités ci-dessus pour voir l'aperçu</li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Intégration Stripe -->
    <div class="form-section">
        <h5 class="form-section-header stripe-section">
            <i class="fab fa-stripe me-2"></i>Intégration Stripe
        </h5>
        <div class="form-section-body">
            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="create_stripe" name="create_stripe" checked>
                <label class="form-check-label" for="create_stripe">
                    <strong>Créer automatiquement sur Stripe</strong>
                </label>
                <div class="form-text">
                    Crée automatiquement le produit et le prix sur Stripe lors de la sauvegarde.
                    Recommandé pour une synchronisation immédiate.
                </div>
            </div>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Information :</strong> Si vous ne cochez pas cette option, vous pourrez synchroniser 
                l'abonnement avec Stripe plus tard depuis la liste des abonnements.
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
// Calcul automatique des prix
function calculatePrices() {
    const prixTTC = parseFloat(document.getElementById('prix_ttc').value) || 0;
    const prixHT = parseFloat(document.getElementById('prix_ht').value) || 0;
    
    if (prixTTC > 0 && prixHT === 0) {
        // Calculer HT depuis TTC (TVA 20%)
        document.getElementById('prix_ht').value = (prixTTC / 1.20).toFixed(2);
    } else if (prixHT > 0 && prixTTC === 0) {
        // Calculer TTC depuis HT
        document.getElementById('prix_ttc').value = (prixHT * 1.20).toFixed(2);
    }
    
    updatePreview();
}

// Mise à jour de l'aperçu du prix
function updatePreview() {
    const prixTTC = parseFloat(document.getElementById('prix_ttc').value) || 0;
    const interval = document.getElementById('interval').value;
    const intervalCount = parseInt(document.getElementById('interval_count').value) || 1;
    
    let intervalText = '';
    switch(interval) {
        case 'day':
            intervalText = intervalCount > 1 ? `${intervalCount} jours` : 'jour';
            break;
        case 'week':
            intervalText = intervalCount > 1 ? `${intervalCount} semaines` : 'semaine';
            break;
        case 'month':
            intervalText = intervalCount > 1 ? `${intervalCount} mois` : 'mois';
            break;
        case 'year':
            intervalText = intervalCount > 1 ? `${intervalCount} ans` : 'an';
            break;
    }
    
    document.getElementById('price-display').textContent = `${prixTTC.toFixed(2)}€/${intervalText}`;
}

// Aperçu des fonctionnalités
document.getElementById('features').addEventListener('input', function() {
    const features = this.value.split('\n').filter(f => f.trim());
    const preview = document.getElementById('features-preview');
    
    if (features.length === 0) {
        preview.innerHTML = '<li class="text-muted"><i class="fas fa-info-circle me-2"></i>Saisissez les fonctionnalités ci-dessus pour voir l\'aperçu</li>';
    } else {
        preview.innerHTML = features.map(feature => 
            `<li><i class="fas fa-check text-success me-2"></i>${feature.trim()}</li>`
        ).join('');
    }
});

// Validation du formulaire
document.getElementById('abonnement-form').addEventListener('submit', function(e) {
    const prixTTC = parseFloat(document.getElementById('prix_ttc').value) || 0;
    const prixHT = parseFloat(document.getElementById('prix_ht').value) || 0;
    const nom = document.getElementById('nom').value.trim();
    
    if (!nom) {
        e.preventDefault();
        alert('Le nom du plan est requis.');
        document.getElementById('nom').focus();
        return;
    }
    
    if (prixTTC <= 0 && prixHT <= 0) {
        e.preventDefault();
        alert('Vous devez saisir au moins le prix TTC ou le prix HT.');
        document.getElementById('prix_ttc').focus();
        return;
    }
});

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    updatePreview();
});
</script>
{% endblock %}
