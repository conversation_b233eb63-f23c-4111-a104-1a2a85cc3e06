{% extends 'backoffice/base.html' %}
{% load static %}

{% block page_title_main %}{{ abonnement.nom }} - Détails{% endblock %}
{% block page_title_breadcrumb %}{{ abonnement.nom }}{% endblock %}
{% block page_title_header %}{{ abonnement.nom }}{% endblock %}
{% block page_icon %}<i class="fas fa-tag me-2"></i>{% endblock %}

{% block extra_css %}
<style>
.info-card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.info-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem 0.5rem 0 0;
    margin: 0;
}

.info-card-body {
    padding: 1.5rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    border-radius: 0.375rem;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stripe-info {
    background: linear-gradient(135deg, #635bff 0%, #4f46e5 100%);
    color: white;
    border-radius: 0.375rem;
    padding: 1rem;
}

.feature-list {
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
}

.subscription-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: white;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}
</style>
{% endblock %}

{% block page_actions %}
<div class="d-flex gap-2">
    <a href="{% url 'backoffice:abonnements_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
    </a>
    <a href="{% url 'backoffice:abonnement_edit' abonnement.id %}" class="btn btn-primary">
        <i class="fas fa-edit me-2"></i>Modifier
    </a>
    {% if not abonnement.stripe_product_id %}
    <button class="btn btn-info" onclick="syncStripe()">
        <i class="fab fa-stripe me-2"></i>Sync Stripe
    </button>
    {% endif %}
    <button class="btn btn-warning" onclick="toggleStatus()">
        <i class="fas fa-{% if abonnement.is_active %}pause{% else %}play{% endif %} me-2"></i>
        {% if abonnement.is_active %}Désactiver{% else %}Activer{% endif %}
    </button>
    <a href="{% url 'backoffice:abonnement_delete' abonnement.id %}" class="btn btn-outline-danger">
        <i class="fas fa-trash me-2"></i>Supprimer
    </a>
</div>
{% endblock %}

{% block backoffice_content %}
<!-- Informations principales -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="info-card">
            <h5 class="info-card-header">
                <i class="fas fa-info-circle me-2"></i>Informations générales
            </h5>
            <div class="info-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Nom du plan</h6>
                        <p class="mb-3">{{ abonnement.nom }}</p>
                        
                        {% if abonnement.description_courte %}
                        <h6>Description</h6>
                        <p class="mb-3">{{ abonnement.description_courte }}</p>
                        {% endif %}
                        
                        <h6>Prix</h6>
                        <p class="mb-3">
                            <span class="h5 text-primary">{{ abonnement.prix_ttc }}€</span>
                            <small class="text-muted">TTC ({{ abonnement.prix_ht }}€ HT)</small><br>
                            <small class="text-muted">{{ abonnement.get_price_display }}</small>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6>Statut</h6>
                        <p class="mb-3">
                            {% if abonnement.is_active %}
                                <span class="badge bg-success">Actif</span>
                            {% else %}
                                <span class="badge bg-secondary">Inactif</span>
                            {% endif %}
                            {% if abonnement.is_popular %}
                                <span class="badge bg-warning">Populaire</span>
                            {% endif %}
                            {% if abonnement.entreprise %}
                                <span class="badge bg-info">Entreprise</span>
                            {% endif %}
                        </p>
                        
                        <h6>Crédits inclus</h6>
                        <p class="mb-3">{{ abonnement.credits }} crédits</p>
                        
                        <h6>Ordre d'affichage</h6>
                        <p class="mb-3">{{ abonnement.ordre_affichage }}</p>
                        
                        <h6>Dates</h6>
                        <p class="mb-0">
                            <small class="text-muted">
                                Créé le {{ abonnement.date_creation|date:"d/m/Y à H:i" }}<br>
                                Modifié le {{ abonnement.date_modification|date:"d/m/Y à H:i" }}
                            </small>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Statistiques -->
        <div class="info-card">
            <h5 class="info-card-header">
                <i class="fas fa-chart-bar me-2"></i>Statistiques
            </h5>
            <div class="info-card-body">
                <div class="stat-item mb-3">
                    <div class="stat-value text-primary">{{ total_souscriptions }}</div>
                    <div class="text-muted">Total souscriptions</div>
                </div>
                <div class="stat-item mb-3">
                    <div class="stat-value text-success">{{ souscriptions_actives }}</div>
                    <div class="text-muted">Souscriptions actives</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value text-info">{{ revenus_total }}€</div>
                    <div class="text-muted">Revenus total</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Fonctionnalités -->
{% if features %}
<div class="info-card">
    <h5 class="info-card-header">
        <i class="fas fa-list me-2"></i>Fonctionnalités
    </h5>
    <div class="info-card-body">
        <div class="feature-list">
            <ul class="list-unstyled mb-0">
                {% for feature in features %}
                <li class="mb-2">
                    <i class="fas fa-check text-success me-2"></i>{{ feature }}
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>
{% endif %}

<!-- Intégration Stripe -->
<div class="info-card">
    <h5 class="info-card-header">
        <i class="fab fa-stripe me-2"></i>Intégration Stripe
    </h5>
    <div class="info-card-body">
        {% if abonnement.stripe_product_id %}
        <div class="stripe-info">
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-box me-2"></i>Produit Stripe</h6>
                    <p class="mb-2">
                        <code>{{ abonnement.stripe_product_id }}</code>
                        <a href="https://dashboard.stripe.com/products/{{ abonnement.stripe_product_id }}" 
                           target="_blank" class="btn btn-sm btn-light ms-2">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </p>
                </div>
                <div class="col-md-6">
                    <h6><i class="fas fa-tag me-2"></i>Prix Stripe</h6>
                    <p class="mb-2">
                        <code>{{ abonnement.stripe_price_id }}</code>
                        <a href="https://dashboard.stripe.com/prices/{{ abonnement.stripe_price_id }}" 
                           target="_blank" class="btn btn-sm btn-light ms-2">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </p>
                </div>
            </div>
            <div class="mt-3">
                <button class="btn btn-light btn-sm" onclick="updateStripe()">
                    <i class="fas fa-sync me-2"></i>Mettre à jour sur Stripe
                </button>
            </div>
        </div>
        {% else %}
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Non synchronisé avec Stripe</strong><br>
            Cet abonnement n'a pas encore été synchronisé avec Stripe.
            <button class="btn btn-warning btn-sm ms-2" onclick="syncStripe()">
                <i class="fab fa-stripe me-2"></i>Synchroniser maintenant
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Dernières souscriptions -->
{% if derniers_abonnements %}
<div class="info-card">
    <h5 class="info-card-header">
        <i class="fas fa-users me-2"></i>Dernières souscriptions
    </h5>
    <div class="info-card-body">
        {% for abonnement_user in derniers_abonnements %}
        <div class="subscription-item">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>{{ abonnement_user.user.nom }} {{ abonnement_user.user.prenom }}</strong><br>
                    <small class="text-muted">{{ abonnement_user.user.email }}</small>
                </div>
                <div class="text-end">
                    <span class="status-badge badge 
                        {% if abonnement_user.statut == 'active' %}bg-success
                        {% elif abonnement_user.statut == 'cancelled' %}bg-danger
                        {% elif abonnement_user.statut == 'past_due' %}bg-warning
                        {% else %}bg-secondary{% endif %}">
                        {{ abonnement_user.get_statut_display }}
                    </span><br>
                    <small class="text-muted">{{ abonnement_user.date_creation|date:"d/m/Y" }}</small>
                </div>
            </div>
            {% if abonnement_user.credits_restants %}
            <div class="mt-2">
                <small class="text-muted">
                    <i class="fas fa-coins me-1"></i>{{ abonnement_user.credits_restants }} crédits restants
                </small>
            </div>
            {% endif %}
        </div>
        {% endfor %}
        
        {% if total_souscriptions > 10 %}
        <div class="text-center mt-3">
            <small class="text-muted">
                Affichage des 10 dernières souscriptions sur {{ total_souscriptions }} au total
            </small>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function toggleStatus() {
    if (!confirm('Changer le statut de cet abonnement ?')) {
        return;
    }
    
    fetch(`/backoffice/api/abonnements/{{ abonnement.id }}/toggle-status/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Erreur: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Une erreur est survenue.');
    });
}

function syncStripe() {
    if (!confirm('Synchroniser cet abonnement avec Stripe ?')) {
        return;
    }
    
    fetch(`/backoffice/api/abonnements/{{ abonnement.id }}/sync-stripe/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Erreur: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Une erreur est survenue.');
    });
}

function updateStripe() {
    if (!confirm('Mettre à jour le produit Stripe avec les données actuelles ?')) {
        return;
    }
    
    // Utiliser la même API que sync mais pour mise à jour
    syncStripe();
}
</script>
{% endblock %}
