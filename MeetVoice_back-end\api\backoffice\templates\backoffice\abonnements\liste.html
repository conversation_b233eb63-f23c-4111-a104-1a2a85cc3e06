{% extends 'backoffice/base.html' %}
{% load static %}

{% block page_title_main %}Gestion des Abonnements{% endblock %}
{% block page_title_breadcrumb %}Abonnements{% endblock %}
{% block page_title_header %}Gestion des Abonnements{% endblock %}
{% block page_icon %}<i class="fas fa-tags me-2"></i>{% endblock %}

{% block page_actions %}
<div class="d-flex gap-2">
    <a href="{% url 'backoffice:abonnement_create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Créer un Abonnement
    </a>
    <button class="btn btn-success" onclick="syncAllStripe()">
        <i class="fab fa-stripe me-2"></i>Synchroniser avec Stripe
    </button>
    <button class="btn btn-outline-info" onclick="refreshFromStripe()">
        <i class="fas fa-sync me-2"></i>Importer depuis Stripe
    </button>
    <button class="btn btn-outline-secondary" onclick="exportData()">
        <i class="fas fa-download me-2"></i>Exporter CSV
    </button>
</div>
{% endblock %}

{% block backoffice_content %}
<!-- Statistiques administratives -->
<div class="row mb-3">
    <div class="col-md-3">
        <div class="card border-0 bg-light">
            <div class="card-body text-center py-3">
                <h4 class="mb-1">{{ total_abonnements|default:0 }}</h4>
                <small class="text-muted">Total Plans</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 bg-light">
            <div class="card-body text-center py-3">
                <h4 class="mb-1 text-success">{{ abonnements_actifs|default:0 }}</h4>
                <small class="text-muted">Plans Actifs</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 bg-light">
            <div class="card-body text-center py-3">
                <h4 class="mb-1 text-info">{{ abonnements_avec_stripe|default:0 }}</h4>
                <small class="text-muted">Sync Stripe</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 bg-light">
            <div class="card-body text-center py-3">
                <h4 class="mb-1 text-warning">{{ utilisateurs_abonnes|default:0 }}</h4>
                <small class="text-muted">Utilisateurs Abonnés</small>
            </div>
        </div>
    </div>
</div>

<!-- Actions en lot -->
<div class="card mb-3" id="bulk-actions" style="display: none;">
    <div class="card-body py-2">
        <div class="row align-items-center">
            <div class="col-md-6">
                <span id="selected-count">0</span> plan(s) sélectionné(s)
            </div>
            <div class="col-md-6 text-end">
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-success" onclick="bulkAction('activate')">
                        <i class="fas fa-check"></i> Activer
                    </button>
                    <button class="btn btn-warning" onclick="bulkAction('deactivate')">
                        <i class="fas fa-times"></i> Désactiver
                    </button>
                    <button class="btn btn-info" onclick="bulkAction('sync_stripe')">
                        <i class="fab fa-stripe"></i> Sync Stripe
                    </button>
                    <button class="btn btn-danger" onclick="bulkAction('delete')" data-bs-toggle="modal" data-bs-target="#confirmDeleteModal">
                        <i class="fas fa-trash"></i> Supprimer
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Table administrative -->
{% if abonnements %}
<div class="card">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h6 class="mb-0">Plans d'Abonnement ({{ abonnements|length }})</h6>
        <div class="form-check">
            <input type="checkbox" class="form-check-input" id="select-all" onchange="toggleSelectAll()">
            <label class="form-check-label" for="select-all">Tout sélectionner</label>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-sm table-hover mb-0">
            <thead class="table-light">
                <tr>
                    <th width="30"></th>
                    <th>Plan</th>
                    <th>Prix</th>
                    <th>Intervalle</th>
                    <th>Crédits</th>
                    <th>Statut</th>
                    <th>Stripe</th>
                    <th>Ordre</th>
                    <th width="150">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for abonnement in abonnements %}
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input subscription-checkbox" 
                               value="{{ abonnement.id }}" onchange="updateBulkActions()">
                    </td>
                    <td>
                        <div>
                            <strong>{{ abonnement.nom }}</strong>
                            {% if abonnement.is_popular %}
                                <span class="badge bg-warning text-dark ms-1">Populaire</span>
                            {% endif %}
                        </div>
                        {% if abonnement.description_courte %}
                            <small class="text-muted">{{ abonnement.description_courte|truncatechars:50 }}</small>
                        {% endif %}
                    </td>
                    <td>
                        <strong>{{ abonnement.prix_ttc }}€</strong>
                        <br><small class="text-muted">{{ abonnement.prix_ht }}€ HT</small>
                    </td>
                    <td>
                        <span class="badge bg-secondary">{{ abonnement.get_interval_display_fr }}</span>
                    </td>
                    <td>
                        <span class="badge bg-info">{{ abonnement.credits }} crédits</span>
                    </td>
                    <td>
                        {% if abonnement.is_active %}
                            <span class="badge bg-success">Actif</span>
                        {% else %}
                            <span class="badge bg-danger">Inactif</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if abonnement.stripe_product_id %}
                            <span class="badge bg-primary">
                                <i class="fab fa-stripe"></i> Sync
                            </span>
                        {% else %}
                            <span class="text-muted">Non sync</span>
                        {% endif %}
                    </td>
                    <td>{{ abonnement.ordre_affichage }}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <a href="{% url 'backoffice:abonnement_detail' abonnement.id %}" 
                               class="btn btn-outline-primary btn-sm" title="Voir détails">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'backoffice:abonnement_edit' abonnement.id %}" 
                               class="btn btn-outline-secondary btn-sm" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </a>
                            {% if not abonnement.stripe_product_id %}
                            <button class="btn btn-outline-info btn-sm" 
                                    onclick="syncStripe({{ abonnement.id }})" 
                                    title="Synchroniser avec Stripe">
                                <i class="fab fa-stripe"></i>
                            </button>
                            {% endif %}
                            <button class="btn btn-outline-danger btn-sm" 
                                    onclick="deleteAbonnement({{ abonnement.id }})" 
                                    title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% else %}
<!-- État vide -->
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">Aucun plan d'abonnement configuré</h5>
        <p class="text-muted">Créez votre premier plan d'abonnement pour commencer à proposer des services payants.</p>
        <a href="{% url 'backoffice:abonnement_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Créer le premier plan
        </a>
    </div>
</div>
{% endif %}

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="confirmDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer ce(s) plan(s) d'abonnement ?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Attention :</strong> Cette action supprimera également le produit correspondant dans Stripe.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">Supprimer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Variables globales
let selectedIds = [];
let deleteTarget = null;

// Gestion des sélections
function toggleSelectAll() {
    const selectAll = document.getElementById('select-all');
    const checkboxes = document.querySelectorAll('.subscription-checkbox');
    
    checkboxes.forEach(cb => {
        cb.checked = selectAll.checked;
    });
    
    updateBulkActions();
}

function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.subscription-checkbox:checked');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');
    
    selectedIds = Array.from(checkboxes).map(cb => cb.value);
    selectedCount.textContent = selectedIds.length;
    bulkActions.style.display = selectedIds.length > 0 ? 'block' : 'none';
}

// Actions en lot
function bulkAction(action) {
    if (selectedIds.length === 0) {
        alert('Veuillez sélectionner au moins un plan.');
        return;
    }
    
    let confirmMessage = '';
    switch(action) {
        case 'activate':
            confirmMessage = `Activer ${selectedIds.length} plan(s) ?`;
            break;
        case 'deactivate':
            confirmMessage = `Désactiver ${selectedIds.length} plan(s) ?`;
            break;
        case 'sync_stripe':
            confirmMessage = `Synchroniser ${selectedIds.length} plan(s) avec Stripe ?`;
            break;
        case 'delete':
            deleteTarget = selectedIds;
            return; // Le modal s'occupera de la confirmation
    }
    
    if (!confirm(confirmMessage)) {
        return;
    }
    
    executeBulkAction(action, selectedIds);
}

function executeBulkAction(action, ids) {
    fetch('{% url "backoffice:abonnements_bulk_actions" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({
            action: action,
            abonnement_ids: ids
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message || 'Action effectuée avec succès');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('danger', data.message || 'Une erreur est survenue');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showAlert('danger', 'Une erreur technique est survenue');
    });
}

// Actions individuelles
function syncStripe(abonnementId) {
    if (!confirm('Synchroniser ce plan avec Stripe ?')) {
        return;
    }
    
    fetch(`/backoffice/api/abonnements/${abonnementId}/sync-stripe/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Plan synchronisé avec Stripe');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('danger', data.message || 'Erreur lors de la synchronisation');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showAlert('danger', 'Erreur technique lors de la synchronisation');
    });
}

function deleteAbonnement(abonnementId) {
    deleteTarget = [abonnementId];
    const modal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
    modal.show();
}

function confirmDelete() {
    if (!deleteTarget || deleteTarget.length === 0) {
        return;
    }
    
    executeBulkAction('delete', deleteTarget);
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('confirmDeleteModal'));
    modal.hide();
    deleteTarget = null;
}

// Actions globales
function syncAllStripe() {
    const allCheckboxes = document.querySelectorAll('.subscription-checkbox');
    const allIds = Array.from(allCheckboxes).map(cb => cb.value);
    
    if (allIds.length === 0) {
        alert('Aucun plan à synchroniser.');
        return;
    }
    
    if (!confirm(`Synchroniser tous les plans (${allIds.length}) avec Stripe ?`)) {
        return;
    }
    
    executeBulkAction('sync_stripe', allIds);
}

function refreshFromStripe() {
    if (!confirm('Importer les nouveaux produits depuis Stripe ?')) {
        return;
    }
    
    fetch('{% url "backoffice:import_from_stripe" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', `${data.imported_count} nouveau(x) plan(s) importé(s)`);
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('danger', data.message || 'Erreur lors de l\'importation');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showAlert('danger', 'Erreur technique lors de l\'importation');
    });
}

function exportData() {
    window.location.href = '{% url "backoffice:export_abonnements" %}';
}

// Utilitaires
function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
           document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '';
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-info-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.main-content');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
