{% extends 'backoffice/base.html' %}
{% load static %}

{% block page_title_main %}Gestion des Articles{% endblock %}
{% block page_title_breadcrumb %}Gestion des Articles{% endblock %}
{% block page_title_header %}Gestion des Articles{% endblock %}
{% block page_icon %}<i class="fas fa-newspaper me-2"></i>{% endblock %}

{% block extra_css %}
<meta name="csrf-token" content="{{ csrf_token }}">
<link rel="stylesheet" href="{% static 'backoffice/css/articles.css' %}">
<style>
/* Styles pour la gestion des mots-clés */
.keyword-item {
    transition: all 0.3s ease;
    border-left: 4px solid #007bff;
}

.keyword-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.keyword-item .progress {
    height: 4px;
}

.keyword-item .card-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: #2c3e50;
}

#keywordsSection {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.nav-tabs .nav-link {
    color: #6c757d;
    border: none;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-bottom-color: #dee2e6;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background-color: transparent;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    border: none;
    color: #212529;
    font-weight: 500;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
    color: #212529;
    transform: translateY(-1px);
}
</style>
{% endblock %}

{% block backoffice_content %}
<!-- Navigation et actions -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <a href="{% url 'actualite:afficher_actualites' %}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i>Retour aux articles publics
        </a>
    </div>
    <div class="btn-group">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#articleModal" onclick="openCreateModal()">
            <i class="fas fa-plus me-2"></i>Nouvel Article
        </button>
        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#generateModal">
            <i class="fas fa-robot me-2"></i>Générer avec IA
        </button>
        <button class="btn btn-warning" onclick="toggleKeywordsSection()">
            <i class="fas fa-tags me-2"></i>Mots-clés SEO
        </button>
    </div>
</div>

<!-- Section Mots-clés SEO (masquée par défaut) -->
<div id="keywordsSection" class="row mb-4" style="display: none;">
    <div class="col-12">
        <div class="card shadow border-left-warning">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-tags me-2"></i>Gestion des Mots-clés SEO
                </h6>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addKeywordModal">
                        <i class="fas fa-plus"></i> Ajouter
                    </button>
                    <button type="button" class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#importKeywordsModal">
                        <i class="fas fa-upload"></i> Import
                    </button>
                    <button type="button" class="btn btn-secondary btn-sm" onclick="exportKeywords()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Onglets pour les catégories de mots-clés -->
                <ul class="nav nav-tabs mb-3" id="keywordTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="amical-keywords-tab" data-bs-toggle="tab" data-bs-target="#amical-keywords" type="button" role="tab">
                            <i class="fas fa-users me-1"></i>Amical
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="amour-keywords-tab" data-bs-toggle="tab" data-bs-target="#amour-keywords" type="button" role="tab">
                            <i class="fas fa-heart me-1"></i>Amour
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="libertin-keywords-tab" data-bs-toggle="tab" data-bs-target="#libertin-keywords" type="button" role="tab">
                            <i class="fas fa-fire me-1"></i>Libertin
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="general-keywords-tab" data-bs-toggle="tab" data-bs-target="#general-keywords" type="button" role="tab">
                            <i class="fas fa-globe me-1"></i>Général
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="keywordTabsContent">
                    <!-- Contenu des onglets sera chargé dynamiquement -->
                    <div class="tab-pane fade show active" id="amical-keywords" role="tabpanel">
                        <div id="amical-keywords-content">
                            <div class="text-center py-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                                <p class="mt-2 text-muted">Chargement des mots-clés...</p>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane fade" id="amour-keywords" role="tabpanel">
                        <div id="amour-keywords-content">
                            <div class="text-center py-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                                <p class="mt-2 text-muted">Chargement des mots-clés...</p>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane fade" id="libertin-keywords" role="tabpanel">
                        <div id="libertin-keywords-content">
                            <div class="text-center py-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                                <p class="mt-2 text-muted">Chargement des mots-clés...</p>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane fade" id="general-keywords" role="tabpanel">
                        <div id="general-keywords-content">
                            <div class="text-center py-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                                <p class="mt-2 text-muted">Chargement des mots-clés...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Articles</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ articles.count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-newspaper fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Publiés</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ published_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Brouillons</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ draft_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-edit fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Vues Totales</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_views }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-eye fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Section Suggestions d'Articles IA -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow border-left-warning">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-lightbulb me-2"></i>Suggestions d'Articles Pertinents (IA)
                </h6>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="loadSuggestions('all')">
                        Tous
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="loadSuggestions('amical')">
                        Amical
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="loadSuggestions('amour')">
                        Amour
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="loadSuggestions('libertin')">
                        Libertin
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="refreshSuggestions()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="showAnalyzeModal()">
                        <i class="fas fa-search"></i> Analyser
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="loading-spinner text-center" id="loadingSpinner" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2 text-muted small">Analyse des tendances en cours...</p>
                </div>

                <div id="suggestionsContainer">
                    <!-- Les suggestions seront chargées ici via JavaScript -->
                </div>

                <div class="text-center mt-3" id="suggestionsActions" style="display: none;">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Cliquez sur une suggestion pour générer l'article automatiquement
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filtres et recherche -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Recherche</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ request.GET.search }}" placeholder="Titre, contenu...">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Statut</label>
                        <select class="form-control" id="status" name="status">
                            <option value="">Tous</option>
                            <option value="draft" {% if request.GET.status == 'draft' %}selected{% endif %}>Brouillon</option>
                            <option value="published" {% if request.GET.status == 'published' %}selected{% endif %}>Publié</option>
                            <option value="archived" {% if request.GET.status == 'archived' %}selected{% endif %}>Archivé</option>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label for="featured" class="form-label">Mis en avant</label>
                        <select class="form-control" id="featured" name="featured">
                            <option value="">Tous</option>
                            <option value="1" {% if request.GET.featured == '1' %}selected{% endif %}>Oui</option>
                            <option value="0" {% if request.GET.featured == '0' %}selected{% endif %}>Non</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Filtrer
                        </button>
                        <a href="{% url 'backoffice:articles' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Liste des articles -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    Articles ({{ articles.count }} résultat{{ articles.count|pluralize }})
                </h6>
                <div class="btn-group">
                    <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#articleModal" onclick="openCreateModal()">
                        <i class="fas fa-plus"></i> Nouvel Article
                    </button>
                    <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#generateModal">
                        <i class="fas fa-robot"></i> Générer avec IA
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if articles %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Titre</th>
                                    <th>Auteur</th>
                                    <th>Statut</th>
                                    <th>Thème</th>
                                    <th>Date Publication</th>
                                    <th>Vues</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for article in articles %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if article.mis_en_avant %}
                                                <i class="fas fa-star featured-star me-2" title="Mis en avant"></i>
                                            {% endif %}
                                            <div>
                                                <strong>{{ article.titre|truncatechars:50 }}</strong>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if article.auteur.first_name and article.auteur.last_name %}
                                            {{ article.auteur.first_name }} {{ article.auteur.last_name }}
                                        {% else %}
                                            {{ article.auteur.username }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <select class="status-dropdown status-{{ article.status }}"
                                                data-article-id="{{ article.id }}"
                                                data-current-status="{{ article.status }}">
                                            <option value="draft" {% if article.status == 'draft' %}selected{% endif %}>Brouillon</option>
                                            <option value="published" {% if article.status == 'published' %}selected{% endif %}>Publié</option>
                                            <option value="archived" {% if article.status == 'archived' %}selected{% endif %}>Archivé</option>
                                        </select>
                                    </td>

                                    <td>{{ article.theme }}</td>
                                    <td>{{ article.date_publication|date:"d/m/Y H:i" }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ article.access_count }}</span>
                                    </td>
                                    <td class="table-actions" data-article-id="{{ article.id }}">
                                        <div class="btn-group" role="group">
                                            <!-- Bouton de publication -->
                                            {% if article.status == 'draft' %}
                                                <button class="btn btn-sm btn-outline-success"
                                                        data-status-action="published"
                                                        data-article-id="{{ article.id }}"
                                                        title="Publier l'article">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            {% endif %}

                                            <!-- Bouton modifier -->
                                            <button class="btn btn-sm btn-outline-primary"
                                                    data-action="edit"
                                                    data-article-id="{{ article.id }}"
                                                    title="Modifier l'article">
                                                <i class="fas fa-edit"></i>
                                            </button>

                                            <!-- Bouton voir -->
                                            <a href="{% url 'actualite:detail_actualite' article.id %}?from=backoffice"
                                               class="btn btn-sm btn-outline-info"
                                               title="Voir l'article">
                                                <i class="fas fa-eye"></i>
                                            </a>

                                            <!-- Bouton mise en avant -->
                                            <button class="btn btn-sm {% if article.mis_en_avant %}btn-warning{% else %}btn-outline-warning{% endif %}"
                                                    data-action="highlight"
                                                    data-article-id="{{ article.id }}"
                                                    title="{% if article.mis_en_avant %}Retirer de la mise en avant{% else %}Mettre en avant{% endif %}">
                                                <i class="fas fa-star"></i>
                                            </button>

                                            <!-- Bouton supprimer -->
                                            <button class="btn btn-sm btn-outline-danger"
                                                    data-action="delete"
                                                    data-article-id="{{ article.id }}"
                                                    data-article-title="{{ article.titre|escapejs }}"
                                                    title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-newspaper fa-3x text-gray-300 mb-3"></i>
                        <p class="text-muted">Aucun article trouvé</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#articleModal" onclick="openCreateModal()">
                            <i class="fas fa-plus"></i> Créer le premier article
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal pour créer/modifier un article -->
<div class="modal fade" id="articleModal" tabindex="-1" aria-labelledby="articleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="articleModalLabel">Nouvel Article</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="articleForm" method="POST" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="modal-body article-form">
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Informations principales -->
                            <div class="mb-3">
                                <label for="titre" class="form-label">Titre *</label>
                                <input type="text" class="form-control" id="titre" name="titre" required>
                            </div>

                            <div class="mb-3">
                                <label for="petit_description" class="form-label">Description courte</label>
                                <textarea class="form-control" id="petit_description" name="petit_description" rows="3"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="contenu" class="form-label">Contenu *</label>
                                <textarea class="form-control" id="contenu" name="contenu" rows="10" required></textarea>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Métadonnées -->
                            <div class="mb-3">
                                <label for="status" class="form-label">Statut *</label>
                                <select class="form-control" id="status" name="status" required>
                                    <option value="draft">Brouillon</option>
                                    <option value="published">Publié</option>
                                    <option value="archived">Archivé</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="theme" class="form-label">Thème *</label>
                                <select class="form-control" id="theme" name="theme" required>
                                    <option value="">Sélectionner...</option>
                                    <option value="Critique">Critique</option>
                                    <option value="Actualité">Actualité</option>
                                    <option value="Interview">Interview</option>
                                    <option value="Découverte">Découverte</option>
                                    <option value="Bien-être">Bien-être</option>
                                    <option value="Développement personnel">Développement personnel</option>
                                    <option value="Analyse">Analyse</option>
                                    <option value="Tendances">Tendances</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="tags" class="form-label">Tags</label>
                                <input type="text" class="form-control" id="tags" name="tags"
                                       placeholder="Séparés par des virgules">
                                <small class="form-text text-muted">Ex: recrutement, IA, tendances</small>
                            </div>

                            <div class="mb-3">
                                <label for="photo" class="form-label">Photo</label>
                                <input type="file" class="form-control" id="photo" name="photo" accept="image/*">
                                <div id="currentPhoto" class="mt-2" style="display: none;">
                                    <img id="photoPreview" src="" alt="Photo actuelle" class="img-thumbnail" style="max-width: 200px;">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="redacteur" class="form-label">Rédacteur</label>
                                <input type="text" class="form-control" id="redacteur" name="redacteur">
                            </div>

                            <div class="mb-3">
                                <label for="collaborateur" class="form-label">Collaborateurs</label>
                                <textarea class="form-control" id="collaborateur" name="collaborateur" rows="2"></textarea>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="mis_en_avant" name="mis_en_avant">
                                <label class="form-check-label" for="mis_en_avant">
                                    Mettre en avant
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer l'article "<span id="deleteArticleTitle"></span>" ?</p>
                <p class="text-danger"><strong>Cette action est irréversible.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">
                    <i class="fas fa-trash"></i> Supprimer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour générer un article avec IA -->
<div class="modal fade" id="generateModal" tabindex="-1" aria-labelledby="generateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="generateModalLabel">
                    <i class="fas fa-robot me-2"></i>Générer un article avec IA
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    L'IA Gemini va générer automatiquement un article complet basé sur le sujet que vous spécifiez.
                </div>

                <form id="generateForm">
                    <div class="mb-3">
                        <label for="generateSubject" class="form-label">Sujet de l'article *</label>
                        <input type="text" class="form-control" id="generateSubject" name="sujet" required
                               placeholder="Ex: Intelligence Artificielle, Développement personnel, Tendances 2024...">
                        <div class="form-text">Soyez spécifique pour obtenir un meilleur résultat</div>
                    </div>

                    <div class="mb-3">
                        <label for="generateTheme" class="form-label">Thème *</label>
                        <select class="form-control" id="generateTheme" name="theme" required>
                            <option value="">Sélectionner...</option>
                            <option value="Critique">Critique</option>
                            <option value="Actualité">Actualité</option>
                            <option value="Interview">Interview</option>
                            <option value="Découverte">Découverte</option>
                            <option value="Bien-être">Bien-être</option>
                            <option value="Développement personnel">Développement personnel</option>
                            <option value="Analyse">Analyse</option>
                            <option value="Tendances">Tendances</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="customImagePrompt" class="form-label">Prompt d'image personnalisé (optionnel)</label>
                        <textarea class="form-control" id="customImagePrompt" name="image_prompt" rows="3"
                                  placeholder="Ex: professional woman talking on phone, modern office, high quality..."></textarea>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Si laissé vide, l'image sera générée automatiquement basée sur le titre de l'article
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoPublish" name="auto_publish">
                            <label class="form-check-label" for="autoPublish">
                                Publier automatiquement (sinon reste en brouillon)
                            </label>
                        </div>
                    </div>
                </form>

                <div id="generateProgress" class="d-none">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Génération en cours...</span>
                        </div>
                        <p class="mt-2">Génération en cours... Cela peut prendre quelques secondes.</p>
                    </div>
                </div>

                <div id="generateResult" class="d-none">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>Article généré avec succès !</h6>
                        <p class="mb-2"><strong>Titre:</strong> <span id="resultTitle"></span></p>
                        <p class="mb-2"><strong>Thème:</strong> <span id="resultTheme"></span></p>
                        <p class="mb-0">
                            <a href="#" id="resultViewLink" target="_blank" class="btn btn-sm btn-outline-primary me-2">
                                <i class="fas fa-eye"></i> Voir l'article
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="fas fa-refresh"></i> Actualiser la liste
                            </button>
                        </p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-success" id="generateBtn" onclick="generateArticle()">
                    <i class="fas fa-robot me-2"></i>Générer l'article
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour analyser un sujet spécifique -->
<div class="modal fade" id="analyzeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-search me-2"></i>Analyser la Pertinence d'un Sujet
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Analysez n'importe quel sujet</strong> pour connaître son potentiel SEO et sa pertinence pour votre site de rencontre.
                </div>

                <form id="analyzeForm">
                    <div class="mb-3">
                        <label for="topicInput" class="form-label">Sujet à analyser *</label>
                        <input type="text" class="form-control" id="topicInput" required
                               placeholder="Ex: conseils pour premier rendez-vous, comment draguer en ligne...">
                        <small class="form-text text-muted">Décrivez le sujet d'article que vous souhaitez analyser</small>
                    </div>

                    <div class="mb-3">
                        <label for="categorySelect" class="form-label">Catégorie cible (optionnel)</label>
                        <select class="form-select" id="categorySelect">
                            <option value="">🎯 Toutes les catégories</option>
                            <option value="amical">🤝 Amical - Rencontres amicales</option>
                            <option value="amour">💕 Amour - Relations amoureuses</option>
                            <option value="libertin">🔥 Libertin - Lifestyle libertin</option>
                        </select>
                        <small class="form-text text-muted">Spécifiez une catégorie pour une analyse plus précise</small>
                    </div>
                </form>

                <div id="analysisResult" class="mt-4" style="display: none;">
                    <!-- Résultat de l'analyse sera affiché ici -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" onclick="analyzeTopic()">
                    <i class="fas fa-search me-2"></i>Analyser le Sujet
                </button>
                <button type="button" class="btn btn-success" id="generateBtn" style="display: none;" onclick="generateFromAnalysis()">
                    <i class="fas fa-magic me-2"></i>Générer l'Article
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour ajouter un mot-clé -->
<div class="modal fade" id="addKeywordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Nouveau Mot-clé SEO
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addKeywordForm">
                    <div class="mb-3">
                        <label for="keywordText" class="form-label">Mot-clé *</label>
                        <input type="text" class="form-control" id="keywordText" required
                               placeholder="Ex: rencontre amoureuse, site de rencontre...">
                        <small class="form-text text-muted">Utilisé pour analyser la pertinence des suggestions d'articles</small>
                    </div>

                    <div class="mb-3">
                        <label for="keywordCategory" class="form-label">Catégorie *</label>
                        <select class="form-select" id="keywordCategory" required>
                            <option value="amical">🤝 Amical - Rencontres amicales</option>
                            <option value="amour">💕 Amour - Relations amoureuses</option>
                            <option value="libertin">🔥 Libertin - Lifestyle libertin</option>
                            <option value="general">🌐 Général - Mots-clés généraux</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="searchVolume" class="form-label">Volume de recherche</label>
                                <input type="number" class="form-control" id="searchVolume"
                                       value="1000" min="0" max="100000">
                                <small class="form-text text-muted">Recherches/mois estimées</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="keywordWeight" class="form-label">Poids d'importance</label>
                                <input type="number" class="form-control" id="keywordWeight"
                                       value="1.0" min="0.1" max="10.0" step="0.1">
                                <small class="form-text text-muted">1.0 = normal, 5.0 = très important</small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-success" onclick="addKeyword()">
                    <i class="fas fa-plus me-2"></i>Ajouter le Mot-clé
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour importer des mots-clés en masse -->
<div class="modal fade" id="importKeywordsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>Import en Masse de Mots-clés
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Format :</strong> Saisissez un mot-clé par ligne ou séparez-les par des virgules.
                    <br><strong>Exemple :</strong> rencontre sérieuse, site de rencontre, premier rendez-vous
                </div>

                <form id="importKeywordsForm">
                    <div class="mb-3">
                        <label for="importCategory" class="form-label">Catégorie pour tous les mots-clés *</label>
                        <select class="form-select" id="importCategory" required>
                            <option value="amical">🤝 Amical</option>
                            <option value="amour">💕 Amour</option>
                            <option value="libertin">🔥 Libertin</option>
                            <option value="general">🌐 Général</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="keywordsTextArea" class="form-label">Mots-clés à importer *</label>
                        <textarea class="form-control" id="keywordsTextArea" rows="8" required
                                  placeholder="rencontre sérieuse&#10;site de rencontre&#10;premier rendez-vous&#10;application de rencontre&#10;speed dating&#10;..."></textarea>
                        <small class="form-text text-muted">Un mot-clé par ligne ou séparés par des virgules</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="defaultVolume" class="form-label">Volume par défaut</label>
                                <input type="number" class="form-control" id="defaultVolume"
                                       value="1000" min="0" max="100000">
                                <small class="form-text text-muted">Appliqué à tous les mots-clés</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="defaultWeight" class="form-label">Poids par défaut</label>
                                <input type="number" class="form-control" id="defaultWeight"
                                       value="1.0" min="0.1" max="10.0" step="0.1">
                                <small class="form-text text-muted">Appliqué à tous les mots-clés</small>
                            </div>
                        </div>
                    </div>
                </form>

                <div id="importResult" class="mt-3" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-info" onclick="importKeywords()">
                    <i class="fas fa-upload me-2"></i>Importer les Mots-clés
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour modifier un mot-clé -->
<div class="modal fade" id="editKeywordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Modifier le Mot-clé
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editKeywordForm">
                    <input type="hidden" id="editKeywordId">

                    <div class="mb-3">
                        <label for="editKeywordText" class="form-label">Mot-clé *</label>
                        <input type="text" class="form-control" id="editKeywordText" required>
                    </div>

                    <div class="mb-3">
                        <label for="editKeywordCategory" class="form-label">Catégorie *</label>
                        <select class="form-select" id="editKeywordCategory" required>
                            <option value="amical">🤝 Amical</option>
                            <option value="amour">💕 Amour</option>
                            <option value="libertin">🔥 Libertin</option>
                            <option value="general">🌐 Général</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editSearchVolume" class="form-label">Volume de recherche</label>
                                <input type="number" class="form-control" id="editSearchVolume"
                                       min="0" max="100000">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editKeywordWeight" class="form-label">Poids d'importance</label>
                                <input type="number" class="form-control" id="editKeywordWeight"
                                       min="0.1" max="10.0" step="0.1">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editKeywordActive">
                            <label class="form-check-label" for="editKeywordActive">
                                Mot-clé actif (utilisé dans l'analyse)
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="updateKeyword()">
                    <i class="fas fa-save me-2"></i>Sauvegarder
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
{{ block.super }}
<script src="{% static 'backoffice/js/articles.js' %}"></script>
<script src="{% static 'actualite/js/articles-new.js' %}"></script>

<!-- JavaScript pour les suggestions d'articles -->
<script>
console.log('🚀 Chargement suggestions d\'articles...');

let currentCategory = 'all';
let currentAnalysis = null;

// Charger les suggestions au démarrage
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DOM chargé, initialisation suggestions...');
    setTimeout(() => {
        loadSuggestions('all');
    }, 1000); // Délai pour laisser la page se charger
});

// Charger les suggestions d'articles
async function loadSuggestions(category = 'all') {
    currentCategory = category;

    console.log(`🎯 Chargement suggestions: ${category}`);

    // Afficher le spinner
    const spinner = document.getElementById('loadingSpinner');
    const container = document.getElementById('suggestionsContainer');
    const actions = document.getElementById('suggestionsActions');

    if (spinner) {
        spinner.style.display = 'block';
        console.log('⏳ Spinner affiché');
    }
    if (container) container.innerHTML = '';
    if (actions) actions.style.display = 'none';

    // Mettre à jour les boutons actifs
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });

    try {
        const url = category === 'all'
            ? '/actualite/api/suggest-articles/?count=6'
            : `/actualite/api/suggest-articles/?count=6&category=${category}`;

        console.log(`📡 Requête vers: ${url}`);

        const response = await fetch(url);
        console.log(`📊 Status: ${response.status}`);

        if (response.ok) {
            const data = await response.json();
            console.log('📋 Données reçues:', data);

            if (data.success) {
                displaySuggestions(data.suggestions);
                if (actions) actions.style.display = 'block';
                console.log(`✅ ${data.suggestions.length} suggestions affichées`);
            } else {
                showSuggestionsError('Erreur dans la réponse API: ' + (data.error || 'Inconnue'));
            }
        } else {
            const errorText = await response.text();
            console.error('❌ Erreur HTTP:', response.status, errorText);
            showSuggestionsError(`Erreur HTTP ${response.status}`);
        }
    } catch (error) {
        console.error('❌ Erreur:', error);
        showSuggestionsError('Erreur de connexion: ' + error.message);
    } finally {
        if (spinner) {
            spinner.style.display = 'none';
            console.log('⏳ Spinner masqué');
        }
    }
}

// Afficher les suggestions
function displaySuggestions(suggestions) {
    const container = document.getElementById('suggestionsContainer');

    if (!container) {
        console.error('❌ Conteneur suggestions non trouvé');
        return;
    }

    if (suggestions.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-search fa-2x mb-3"></i>
                <p>Aucune suggestion trouvée pour cette catégorie.</p>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshSuggestions()">
                    <i class="fas fa-sync-alt me-1"></i>Actualiser
                </button>
            </div>
        `;
        return;
    }

    const html = suggestions.map((suggestion, index) => {
        const scoreClass = getScoreClass(suggestion.score);
        const categoryClass = getCategoryClass(suggestion.category);

        return `
            <div class="card mb-3 suggestion-card" onclick="selectSuggestion('${escapeHtml(suggestion.topic)}', '${suggestion.category}')" style="cursor: pointer; transition: all 0.3s ease;">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="card-title mb-0">${escapeHtml(suggestion.topic)}</h6>
                        <div class="d-flex gap-2">
                            <span class="badge ${scoreClass}">${suggestion.score.toFixed(0)}/100</span>
                            <span class="badge ${categoryClass}">${suggestion.category}</span>
                        </div>
                    </div>

                    <p class="card-text text-muted small">${escapeHtml(suggestion.suggested_angle)}</p>

                    <div class="d-flex gap-3 small text-muted">
                        <span><i class="fas fa-chart-line me-1 text-primary"></i>SEO: ${suggestion.seo_potential.toFixed(0)}/100</span>
                        <span><i class="fas fa-fire me-1 text-danger"></i>Tendance: ${suggestion.trend_score.toFixed(0)}/100</span>
                        <span><i class="fas fa-users me-1 text-info"></i>${suggestion.estimated_traffic.toFixed(0)} visiteurs/mois</span>
                    </div>

                    ${suggestion.recommendations.length > 0 ? `
                        <div class="mt-2">
                            <small class="text-info">
                                <i class="fas fa-lightbulb me-1"></i>
                                ${escapeHtml(suggestion.recommendations[0])}
                            </small>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = html;

    // Ajouter les effets hover
    container.querySelectorAll('.suggestion-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.1)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
}

// Obtenir la classe CSS selon le score
function getScoreClass(score) {
    if (score >= 80) return 'bg-success';
    if (score >= 65) return 'bg-primary';
    if (score >= 45) return 'bg-warning';
    return 'bg-danger';
}

// Obtenir la classe CSS selon la catégorie
function getCategoryClass(category) {
    const classes = {
        'amical': 'bg-success',
        'amour': 'bg-danger',
        'libertin': 'bg-warning',
        'tendance': 'bg-info',
        'saisonnier': 'bg-secondary'
    };
    return classes[category] || 'bg-info';
}

// Échapper le HTML pour éviter les injections
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Sélectionner une suggestion
function selectSuggestion(topic, category) {
    console.log(`🎯 Suggestion sélectionnée: ${topic} (${category})`);

    const message = `Voulez-vous générer un article sur le sujet :\n\n"${topic}"\n\nCatégorie: ${category}\n\nLa génération peut prendre quelques minutes.`;

    if (confirm(message)) {
        generateArticleFromSuggestion(topic, category);
    }
}

// Générer un article depuis une suggestion
async function generateArticleFromSuggestion(topic, category) {
    console.log(`🤖 Génération article: ${topic}`);

    // Afficher un message de progression
    showToast('Génération de l\'article en cours...', 'info');

    try {
        const csrfToken = getCookie('csrftoken') || document.querySelector('[name=csrfmiddlewaretoken]')?.value;

        const response = await fetch('/actualite/api/generate-article/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken,
            },
            body: JSON.stringify({
                sujet: topic,
                theme: category,
                auto_publish: false,
                image_prompt: `professional ${topic} illustration, modern, clean`
            })
        });

        const data = await response.json();

        if (data.success) {
            showToast('Article généré avec succès !', 'success');

            // Recharger la page après un délai
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showToast('Erreur lors de la génération: ' + (data.error || 'Inconnue'), 'error');
        }
    } catch (error) {
        console.error('❌ Erreur génération:', error);
        showToast('Erreur technique lors de la génération', 'error');
    }
}

// Rafraîchir les suggestions
function refreshSuggestions() {
    console.log('🔄 Actualisation des suggestions');
    loadSuggestions(currentCategory);
}

// Afficher une erreur pour les suggestions
function showSuggestionsError(message) {
    const container = document.getElementById('suggestionsContainer');
    if (container) {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
                <button class="btn btn-outline-danger btn-sm ms-2" onclick="refreshSuggestions()">
                    <i class="fas fa-sync-alt me-1"></i>Réessayer
                </button>
            </div>
        `;
    }
}

// ============================================================================
// FONCTIONS POUR L'ANALYSE DE SUJETS
// ============================================================================

// Afficher le modal d'analyse
function showAnalyzeModal() {
    console.log('🔍 Ouverture modal analyse');

    const modal = new bootstrap.Modal(document.getElementById('analyzeModal'));
    modal.show();

    // Focus sur le champ de saisie
    setTimeout(() => {
        const input = document.getElementById('topicInput');
        if (input) input.focus();
    }, 500);
}

// Analyser un sujet spécifique
async function analyzeTopic() {
    const topic = document.getElementById('topicInput')?.value.trim();
    const category = document.getElementById('categorySelect')?.value;

    if (!topic) {
        alert('Veuillez saisir un sujet à analyser');
        return;
    }

    console.log(`🔍 Analyse sujet: ${topic} (${category || 'toutes catégories'})`);

    // Afficher un spinner dans le résultat
    const resultDiv = document.getElementById('analysisResult');
    resultDiv.innerHTML = `
        <div class="text-center py-3">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Analyse en cours...</span>
            </div>
            <p class="mt-2 text-muted">Analyse de la pertinence en cours...</p>
        </div>
    `;
    resultDiv.style.display = 'block';

    try {
        const response = await fetch('/actualite/api/analyze-topic/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify({ topic, category })
        });

        const data = await response.json();

        if (data.success) {
            currentAnalysis = data;
            displayAnalysisResult(data);
            console.log(`✅ Analyse terminée: Score ${data.analysis.final_score.toFixed(1)}/100`);
        } else {
            showAnalysisError('Erreur lors de l\'analyse: ' + (data.error || 'Inconnue'));
        }
    } catch (error) {
        console.error('❌ Erreur analyse:', error);
        showAnalysisError('Erreur de connexion: ' + error.message);
    }
}

// Afficher le résultat de l'analyse
function displayAnalysisResult(data) {
    const container = document.getElementById('analysisResult');
    const analysis = data.analysis;
    const verdict = data.verdict;

    if (!container) return;

    const scoreColor = verdict.score_category === 'excellent' ? 'success' :
                      verdict.score_category === 'good' ? 'primary' :
                      verdict.score_category === 'average' ? 'warning' : 'danger';

    const scoreIcon = verdict.recommended ? 'check-circle' : 'exclamation-triangle';
    const scoreText = verdict.recommended ? 'Recommandé' : 'Peu recommandé';

    container.innerHTML = `
        <div class="card border-${scoreColor}">
            <div class="card-header bg-${scoreColor} text-white">
                <h6 class="mb-0">
                    <i class="fas fa-${scoreIcon} me-2"></i>
                    Résultat de l'Analyse
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Score Global</h6>
                        <div class="progress mb-3" style="height: 20px;">
                            <div class="progress-bar bg-${scoreColor}" style="width: ${analysis.final_score}%">
                                <strong>${analysis.final_score.toFixed(1)}/100</strong>
                            </div>
                        </div>

                        <h6>Détails des Scores</h6>
                        <ul class="list-unstyled">
                            <li class="mb-1">
                                <strong>📈 SEO:</strong>
                                <span class="badge bg-info">${analysis.seo_potential.toFixed(0)}/100</span>
                            </li>
                            <li class="mb-1">
                                <strong>🔥 Tendance:</strong>
                                <span class="badge bg-warning">${analysis.trend_alignment.toFixed(0)}/100</span>
                            </li>
                            <li class="mb-1">
                                <strong>📅 Saisonnier:</strong>
                                <span class="badge bg-secondary">${analysis.seasonal_relevance.toFixed(0)}/100</span>
                            </li>
                            <li class="mb-1">
                                <strong>👥 Trafic estimé:</strong>
                                <span class="badge bg-primary">${analysis.estimated_traffic.toFixed(0)} visiteurs/mois</span>
                            </li>
                        </ul>
                    </div>

                    <div class="col-md-6">
                        <h6>Verdict</h6>
                        <div class="alert alert-${scoreColor} mb-3">
                            <strong><i class="fas fa-${scoreIcon} me-1"></i>${scoreText}</strong><br>
                            <small>
                                Qualité: <strong>${verdict.score_category}</strong><br>
                                Priorité: <strong>${verdict.priority || 'N/A'}</strong>
                            </small>
                        </div>

                        ${analysis.recommendations.length > 0 ? `
                            <h6>💡 Recommandations</h6>
                            <ul class="small">
                                ${analysis.recommendations.map(rec => `<li>${escapeHtml(rec)}</li>`).join('')}
                            </ul>
                        ` : ''}
                    </div>
                </div>

                <div class="mt-3">
                    <h6>🎯 Angle Suggéré</h6>
                    <div class="alert alert-light">
                        <em>"${escapeHtml(analysis.suggested_angle)}"</em>
                    </div>
                </div>

                ${verdict.recommended ? `
                    <div class="text-center mt-3">
                        <div class="alert alert-success">
                            <i class="fas fa-thumbs-up me-2"></i>
                            <strong>Ce sujet est recommandé !</strong> Il a un bon potentiel pour votre site de rencontre.
                        </div>
                    </div>
                ` : `
                    <div class="text-center mt-3">
                        <div class="alert alert-warning">
                            <i class="fas fa-info-circle me-2"></i>
                            Ce sujet pourrait être amélioré. Consultez les recommandations ci-dessus.
                        </div>
                    </div>
                `}
            </div>
        </div>
    `;

    container.style.display = 'block';

    // Afficher le bouton de génération si recommandé
    const generateBtn = document.getElementById('generateBtn');
    if (generateBtn) {
        generateBtn.style.display = verdict.recommended ? 'inline-block' : 'none';
    }
}

// Générer un article depuis l'analyse
function generateFromAnalysis() {
    if (currentAnalysis) {
        const topic = document.getElementById('topicInput')?.value.trim();
        const category = document.getElementById('categorySelect')?.value || 'general';

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('analyzeModal'));
        if (modal) modal.hide();

        // Générer l'article
        generateArticleFromSuggestion(topic, category);
    }
}

// Afficher une erreur d'analyse
function showAnalysisError(message) {
    const container = document.getElementById('analysisResult');
    if (container) {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
                <button class="btn btn-outline-danger btn-sm ms-2" onclick="analyzeTopic()">
                    <i class="fas fa-sync-alt me-1"></i>Réessayer
                </button>
            </div>
        `;
        container.style.display = 'block';
    }
}

// Fonction pour afficher des toasts
function showToast(message, type = 'info') {
    // Créer un toast simple
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(toast);

    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}

// Fonction pour obtenir le cookie CSRF
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

console.log('✅ JavaScript suggestions d\'articles chargé');

// ============================================================================
// GESTION DES MOTS-CLÉS SEO
// ============================================================================

let keywordsVisible = false;
let currentKeywords = {};

// Afficher/masquer la section mots-clés
function toggleKeywordsSection() {
    const section = document.getElementById('keywordsSection');
    keywordsVisible = !keywordsVisible;

    if (keywordsVisible) {
        section.style.display = 'block';
        // Charger les mots-clés au premier affichage
        loadKeywordsByCategory('amical');
        console.log('📋 Section mots-clés affichée');
    } else {
        section.style.display = 'none';
        console.log('📋 Section mots-clés masquée');
    }
}

// Charger les mots-clés par catégorie
async function loadKeywordsByCategory(category) {
    console.log(`🔍 Chargement mots-clés: ${category}`);

    const contentDiv = document.getElementById(`${category}-keywords-content`);

    // Afficher le spinner
    contentDiv.innerHTML = `
        <div class="text-center py-3">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-2 text-muted">Chargement des mots-clés ${category}...</p>
        </div>
    `;

    try {
        const response = await fetch(`/backoffice/api/keywords/${category}/`);
        const data = await response.json();

        if (data.success) {
            displayKeywords(category, data.keywords);
            console.log(`✅ ${data.keywords.length} mots-clés ${category} chargés`);
        } else {
            throw new Error(data.error || 'Erreur inconnue');
        }

    } catch (error) {
        console.error('❌ Erreur chargement mots-clés:', error);
        contentDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Erreur lors du chargement des mots-clés: ${error.message}
                <button class="btn btn-outline-danger btn-sm ms-2" onclick="loadKeywordsByCategory('${category}')">
                    <i class="fas fa-sync-alt me-1"></i>Réessayer
                </button>
            </div>
        `;
    }
}

// Générer des mots-clés de démonstration
function generateMockKeywords(category) {
    const keywords = {
        'amical': [
            { id: 1, keyword: 'se faire des amis', volume: 2400, weight: 2.0, active: true },
            { id: 2, keyword: 'rencontre amicale', volume: 1800, weight: 1.5, active: true },
            { id: 3, keyword: 'activités entre amis', volume: 1200, weight: 1.0, active: true },
            { id: 4, keyword: 'sortir entre amis', volume: 900, weight: 1.2, active: true },
        ],
        'amour': [
            { id: 5, keyword: 'site de rencontre', volume: 8500, weight: 3.0, active: true },
            { id: 6, keyword: 'rencontre sérieuse', volume: 5200, weight: 2.5, active: true },
            { id: 7, keyword: 'premier rendez-vous', volume: 3100, weight: 2.0, active: true },
            { id: 8, keyword: 'application rencontre', volume: 4200, weight: 2.2, active: true },
            { id: 9, keyword: 'speed dating', volume: 1500, weight: 1.5, active: true },
        ],
        'libertin': [
            { id: 10, keyword: 'club libertin', volume: 1800, weight: 2.0, active: true },
            { id: 11, keyword: 'soirée libertine', volume: 1200, weight: 1.8, active: true },
            { id: 12, keyword: 'échangisme', volume: 2200, weight: 2.5, active: true },
        ],
        'general': [
            { id: 13, keyword: 'rencontre en ligne', volume: 6500, weight: 2.8, active: true },
            { id: 14, keyword: 'célibataire', volume: 4800, weight: 2.0, active: true },
            { id: 15, keyword: 'dating', volume: 3200, weight: 1.8, active: true },
        ]
    };

    return keywords[category] || [];
}

// Afficher les mots-clés
function displayKeywords(category, keywords) {
    const contentDiv = document.getElementById(`${category}-keywords-content`);
    currentKeywords[category] = keywords;

    if (keywords.length === 0) {
        contentDiv.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-tags fa-2x mb-3"></i>
                <p>Aucun mot-clé dans cette catégorie</p>
                <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addKeywordModal" onclick="setKeywordCategory('${category}')">
                    <i class="fas fa-plus me-2"></i>Ajouter le premier
                </button>
            </div>
        `;
        return;
    }

    const html = keywords.map(keyword => {
        const weightPercent = Math.min(keyword.weight * 20, 100); // Convertir en pourcentage
        const statusBadge = keyword.active ? 'bg-success' : 'bg-secondary';
        const statusText = keyword.active ? 'Actif' : 'Inactif';

        return `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card h-100 keyword-item" data-keyword-id="${keyword.id}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">${escapeHtml(keyword.keyword)}</h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editKeyword(${keyword.id}, '${category}')">
                                        <i class="fas fa-edit me-2"></i>Modifier
                                    </a></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteKeyword(${keyword.id}, '${category}')">
                                        <i class="fas fa-trash me-2"></i>Supprimer
                                    </a></li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-2">
                            <small class="text-muted">Volume: ${keyword.volume.toLocaleString()}/mois</small>
                            <div class="progress mt-1" style="height: 4px;">
                                <div class="progress-bar bg-primary" style="width: ${weightPercent}%"></div>
                            </div>
                            <small class="text-muted">Poids: ${keyword.weight}</small>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge ${statusBadge}">${statusText}</span>
                            <small class="text-muted">${getCategoryIcon(category)} ${getCategoryName(category)}</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    contentDiv.innerHTML = `<div class="row">${html}</div>`;
    console.log(`✅ ${keywords.length} mots-clés ${category} affichés`);
}

// Obtenir l'icône de la catégorie
function getCategoryIcon(category) {
    const icons = {
        'amical': '🤝',
        'amour': '💕',
        'libertin': '🔥',
        'general': '🌐'
    };
    return icons[category] || '📝';
}

// Obtenir le nom de la catégorie
function getCategoryName(category) {
    const names = {
        'amical': 'Amical',
        'amour': 'Amour',
        'libertin': 'Libertin',
        'general': 'Général'
    };
    return names[category] || category;
}

// Définir la catégorie dans le modal d'ajout
function setKeywordCategory(category) {
    document.getElementById('keywordCategory').value = category;
}

// Ajouter un nouveau mot-clé
async function addKeyword() {
    const keyword = document.getElementById('keywordText').value.trim();
    const category = document.getElementById('keywordCategory').value;
    const searchVolume = parseInt(document.getElementById('searchVolume').value);
    const weight = parseFloat(document.getElementById('keywordWeight').value);

    if (!keyword) {
        alert('Veuillez saisir un mot-clé');
        return;
    }

    console.log(`➕ Ajout mot-clé: ${keyword} (${category})`);

    try {
        const response = await fetch('/backoffice/api/keywords/add/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify({
                keyword: keyword,
                category: category,
                search_volume: searchVolume,
                weight: weight
            })
        });

        const data = await response.json();

        if (data.success) {
            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addKeywordModal'));
            modal.hide();

            // Réinitialiser le formulaire
            document.getElementById('addKeywordForm').reset();
            document.getElementById('searchVolume').value = '1000';
            document.getElementById('keywordWeight').value = '1.0';

            // Recharger la liste des mots-clés pour cette catégorie
            loadKeywordsByCategory(category);

            showToast(`Mot-clé "${keyword}" ajouté avec succès !`, 'success');
        } else {
            showToast('Erreur: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('❌ Erreur:', error);
        showToast('Erreur de connexion', 'error');
    }
}

// Modifier un mot-clé
function editKeyword(keywordId, category) {
    const keyword = currentKeywords[category]?.find(k => k.id === keywordId);
    if (!keyword) {
        console.error('❌ Mot-clé non trouvé:', keywordId);
        return;
    }

    // Remplir le formulaire de modification
    document.getElementById('editKeywordId').value = keywordId;
    document.getElementById('editKeywordText').value = keyword.keyword;
    document.getElementById('editKeywordCategory').value = category;
    document.getElementById('editSearchVolume').value = keyword.volume;
    document.getElementById('editKeywordWeight').value = keyword.weight;
    document.getElementById('editKeywordActive').checked = keyword.active;

    // Afficher le modal
    const modal = new bootstrap.Modal(document.getElementById('editKeywordModal'));
    modal.show();
}

// Mettre à jour un mot-clé
async function updateKeyword() {
    const keywordId = parseInt(document.getElementById('editKeywordId').value);
    const newKeyword = document.getElementById('editKeywordText').value.trim();
    const newCategory = document.getElementById('editKeywordCategory').value;
    const newVolume = parseInt(document.getElementById('editSearchVolume').value);
    const newWeight = parseFloat(document.getElementById('editKeywordWeight').value);
    const newActive = document.getElementById('editKeywordActive').checked;

    if (!newKeyword) {
        alert('Veuillez saisir un mot-clé');
        return;
    }

    console.log(`✏️ Modification mot-clé: ${keywordId}`);

    // Trouver et mettre à jour le mot-clé
    let found = false;
    for (const category in currentKeywords) {
        const keywordIndex = currentKeywords[category].findIndex(k => k.id === keywordId);
        if (keywordIndex !== -1) {
            currentKeywords[category][keywordIndex] = {
                id: keywordId,
                keyword: newKeyword,
                volume: newVolume,
                weight: newWeight,
                active: newActive
            };

            // Réafficher la liste
            displayKeywords(category, currentKeywords[category]);
            found = true;
            break;
        }
    }

    if (found) {
        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('editKeywordModal'));
        modal.hide();

        showToast(`Mot-clé "${newKeyword}" modifié avec succès !`, 'success');
    } else {
        showToast('Erreur: mot-clé non trouvé', 'error');
    }
}

// Supprimer un mot-clé
async function deleteKeyword(keywordId, category) {
    // Trouver le mot-clé dans la liste actuelle pour obtenir son nom
    const keyword = currentKeywords[category]?.find(k => k.id === keywordId);
    const keywordName = keyword ? keyword.keyword : 'ce mot-clé';

    if (!confirm(`Êtes-vous sûr de vouloir supprimer le mot-clé "${keywordName}" ?`)) {
        return;
    }

    console.log(`🗑️ Suppression mot-clé: ${keywordId}`);

    try {
        const response = await fetch(`/backoffice/api/keywords/${keywordId}/delete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
            }
        });

        const data = await response.json();

        if (data.success) {
            // Recharger la liste des mots-clés pour cette catégorie
            loadKeywordsByCategory(category);

            showToast(`Mot-clé "${keywordName}" supprimé`, 'success');
        } else {
            showToast('Erreur: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('❌ Erreur:', error);
        showToast('Erreur de connexion', 'error');
    }
}

// Importer des mots-clés en masse
async function importKeywords() {
    const category = document.getElementById('importCategory').value;
    const keywordsText = document.getElementById('keywordsTextArea').value.trim();
    const defaultVolume = parseInt(document.getElementById('defaultVolume').value);
    const defaultWeight = parseFloat(document.getElementById('defaultWeight').value);

    if (!keywordsText) {
        alert('Veuillez saisir des mots-clés à importer');
        return;
    }

    console.log(`📥 Import mots-clés: ${category}`);

    try {
        const response = await fetch('/backoffice/api/keywords/import/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify({
                keywords_text: keywordsText,
                category: category,
                default_volume: defaultVolume,
                default_weight: defaultWeight
            })
        });

        const data = await response.json();

        if (data.success) {
            // Afficher le résultat
            const resultDiv = document.getElementById('importResult');
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>Import terminé !</h6>
                    <p class="mb-0">
                        <strong>${data.created_count}</strong> mots-clés créés<br>
                        <strong>${data.skipped_count}</strong> mots-clés ignorés (déjà existants)<br>
                        <strong>${data.total_processed}</strong> mots-clés traités au total
                    </p>
                </div>
            `;
            resultDiv.style.display = 'block';

            // Réinitialiser le formulaire
            document.getElementById('importKeywordsForm').reset();
            document.getElementById('defaultVolume').value = '1000';
            document.getElementById('defaultWeight').value = '1.0';

            // Recharger la liste des mots-clés pour cette catégorie
            loadKeywordsByCategory(category);

            showToast(`${data.created_count} mots-clés importés avec succès !`, 'success');
        } else {
            showToast('Erreur: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('❌ Erreur:', error);
        showToast('Erreur de connexion', 'error');
    }
}

// Exporter les mots-clés
async function exportKeywords() {
    console.log('📤 Export mots-clés');

    try {
        const response = await fetch('/backoffice/api/keywords/export/');
        const data = await response.json();

        if (data.success) {
            if (data.keywords.length === 0) {
                alert('Aucun mot-clé à exporter');
                return;
            }

            // Créer le CSV
            const headers = ['Mot-clé', 'Catégorie', 'Volume', 'Poids'];
            let csvContent = headers.join(',') + '\n';

            for (const keyword of data.keywords) {
                const row = [
                    `"${keyword.keyword}"`,
                    `"${getCategoryName(keyword.category)}"`,
                    keyword.search_volume,
                    keyword.weight
                ].join(',');
                csvContent += row + '\n';
            }

            // Télécharger le fichier
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `mots-cles-seo-${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            showToast(`${data.total_count} mots-clés exportés`, 'success');
        } else {
            showToast('Erreur lors de l\'export: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('❌ Erreur:', error);
        showToast('Erreur de connexion', 'error');
    }
}

// Gestionnaire d'événements pour les onglets de mots-clés
document.addEventListener('DOMContentLoaded', function() {
    // Ajouter les gestionnaires pour les onglets de mots-clés
    const keywordTabs = document.querySelectorAll('#keywordTabs button[data-bs-toggle="tab"]');
    keywordTabs.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(event) {
            const targetId = event.target.getAttribute('data-bs-target');
            const category = targetId.replace('#', '').replace('-keywords', '');

            // Charger les mots-clés si pas encore chargés
            if (!currentKeywords[category]) {
                loadKeywordsByCategory(category);
            }
        });
    });
});

console.log('✅ Gestion mots-clés SEO chargée');
</script>
{% endblock %}
