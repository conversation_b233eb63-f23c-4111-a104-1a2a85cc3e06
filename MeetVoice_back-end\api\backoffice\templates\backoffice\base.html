﻿{% extends 'base.html' %}
{% load static %}

{% block title %}
{% block page_title_main %}Back Office - Meet Voice{% endblock %}
{% endblock %}

{% block head %}
<link rel="stylesheet" href="{% static 'backoffice/css/base.css' %}" />
{% block extra_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="container-fluid backoffice-container">
  <nav class="sidebar">
    <div class="position-sticky pt-4">
      <div class="text-center mb-4">
        <h5 class="text-white mb-1">
          <i class="fas fa-cogs me-2"></i>Back Office
        </h5>
        <small class="text-white-50">Administration</small>
      </div>
      <ul class="nav flex-column">
        <li class="nav-item">
          <a class="nav-link {% if 'dashboard' in request.path %}active{% endif %}" href="{% url 'backoffice:dashboard' %}">
            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link {% if 'abonnements' in request.path %}active{% endif %}" href="{% url 'backoffice:abonnements_list' %}">
            <i class="fas fa-tags me-2"></i>Gestion Abonnements
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link {% if 'traffic' in request.path %}active{% endif %}" href="{% url 'backoffice:traffic_manager' %}">
            <i class="fas fa-chart-line me-2"></i>Traffic Manager
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link {% if 'analytics' in request.path %}active{% endif %}" href="{% url 'backoffice:google_analytics' %}">
            <i class="fab fa-google me-2"></i>Google Analytics
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link {% if 'articles' in request.path %}active{% endif %}" href="{% url 'backoffice:articles' %}">
            <i class="fas fa-newspaper me-2"></i>Articles
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link {% if 'contact' in request.path %}active{% endif %}" href="/contact/">
            <i class="fas fa-envelope me-2"></i>Messages Contact
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link {% if 'moderation' in request.path %}active{% endif %}" href="{% url 'backoffice:moderation_posts' %}">
            <i class="fas fa-shield-alt me-2"></i>Modération Posts
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link {% if 'moderation' in request.path and 'events' in request.path %}active{% endif %}" href="{% url 'backoffice:moderation_events' %}">
            <i class="fas fa-calendar-check me-2"></i>Modération Événements
          </a>
        </li>
        <hr class="my-3" style="border-color: rgba(255, 255, 255, 0.2)" />
        <li class="nav-item">
          <a class="nav-link {% if 'documentation' in request.path %}active{% endif %}" href="{% url 'documentation:dashboard' %}">
            <i class="fas fa-book me-2"></i>Documentation
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link {% if 'newsletter' in request.path %}active{% endif %}" href="{% url 'newsletter:dashboard' %}">
            <i class="fas fa-envelope me-2"></i>Newsletter
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link {% if 'reseaux-social' in request.path %}active{% endif %}" href="{% url 'reseaux_social:liste' %}">
            <i class="fas fa-share-alt me-2"></i>Réseaux Sociaux
          </a>
        </li>
        <hr class="my-3" style="border-color: rgba(255, 255, 255, 0.2)" />
        <li class="nav-item">
          <a class="nav-link" href="{% url 'admin:index' %}">
            <i class="fas fa-tools me-2"></i>Admin Django
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="{% url 'index' %}">
            <i class="fas fa-home me-2"></i>Retour au site
          </a>
        </li>
      </ul>
    </div>
  </nav>
  <main class="main-content">
    <div class="page-header">
      <nav aria-label="breadcrumb" class="breadcrumb-nav">
        <ol class="breadcrumb">
          <li class="breadcrumb-item">
            <a href="{% url 'backoffice:dashboard' %}">
              <i class="fas fa-home me-1"></i>Back Office
            </a>
          </li>
          <li class="breadcrumb-item active" aria-current="page">
            {% block page_title_breadcrumb %}Dashboard{% endblock %}
          </li>
        </ol>
      </nav>
      <h1>
        {% block page_icon %}<i class="fas fa-tachometer-alt me-2"></i>{% endblock %}
        {% block page_title_header %}Dashboard{% endblock %}
      </h1>
      {% block page_description %}{% endblock %}
    </div>
    {% block page_actions %}{% endblock %}
    {% if messages %}
    {% for message in messages %}
    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
      <i class="fas fa-info-circle me-2"></i>
      {{ message }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endfor %}
    {% endif %}
    {% block backoffice_content %}{% endblock %}
  </main>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}