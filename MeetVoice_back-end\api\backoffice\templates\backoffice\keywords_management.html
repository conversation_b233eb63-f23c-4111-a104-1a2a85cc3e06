{% extends 'backoffice/base.html' %}
{% load static %}

{% block page_title_main %}Gestion des Mots-clés SEO{% endblock %}
{% block page_title_breadcrumb %}Mots-clés SEO{% endblock %}
{% block page_title_header %}Gestion des Mots-clés SEO{% endblock %}
{% block page_icon %}<i class="fas fa-tags me-2"></i>{% endblock %}

{% block extra_css %}
<style>
.keyword-card {
    transition: all 0.3s ease;
    border-left: 4px solid #007bff;
}

.keyword-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.keyword-badge {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
    border-radius: 1rem;
}

.weight-indicator {
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
    border-radius: 2px;
}

.config-slider {
    width: 100%;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.75rem;
}

.category-amical { border-left-color: #28a745; }
.category-amour { border-left-color: #dc3545; }
.category-libertin { border-left-color: #ffc107; }
.category-general { border-left-color: #6c757d; }
</style>
{% endblock %}

{% block backoffice_content %}
<!-- Navigation et actions -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <a href="{% url 'backoffice:articles' %}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i>Retour aux articles
        </a>
    </div>
    <div class="btn-group">
        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addKeywordModal">
            <i class="fas fa-plus me-2"></i>Nouveau Mot-clé
        </button>
        <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#importModal">
            <i class="fas fa-upload me-2"></i>Import en Masse
        </button>
        <button class="btn btn-warning" onclick="exportKeywords()">
            <i class="fas fa-download me-2"></i>Exporter
        </button>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#configModal">
            <i class="fas fa-cog me-2"></i>Configuration
        </button>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card shadow h-100 py-3">
            <div class="card-body text-center">
                <i class="fas fa-tags fa-2x mb-2"></i>
                <div class="h4 mb-0 font-weight-bold">{{ stats.total_keywords }}</div>
                <div class="small">Mots-clés Actifs</div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card shadow h-100 py-3">
            <div class="card-body text-center">
                <i class="fas fa-fire fa-2x mb-2"></i>
                <div class="h4 mb-0 font-weight-bold">{{ stats.total_trending }}</div>
                <div class="small">Sujets Tendance</div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card shadow h-100 py-3">
            <div class="card-body text-center">
                <i class="fas fa-calendar fa-2x mb-2"></i>
                <div class="h4 mb-0 font-weight-bold">{{ stats.total_seasonal }}</div>
                <div class="small">Mots-clés Saisonniers</div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card shadow h-100 py-3">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-2"></i>
                <div class="h4 mb-0 font-weight-bold">{{ stats.total_volume|floatformat:0 }}</div>
                <div class="small">Volume Total/Mois</div>
            </div>
        </div>
    </div>
</div>

<!-- Onglets pour les différentes catégories -->
<div class="card shadow mb-4">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" id="keywordTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="amical-tab" data-bs-toggle="tab" data-bs-target="#amical" type="button" role="tab">
                    <i class="fas fa-users me-2"></i>Amical ({{ keywords_amical.count }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="amour-tab" data-bs-toggle="tab" data-bs-target="#amour" type="button" role="tab">
                    <i class="fas fa-heart me-2"></i>Amour ({{ keywords_amour.count }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="libertin-tab" data-bs-toggle="tab" data-bs-target="#libertin" type="button" role="tab">
                    <i class="fas fa-fire me-2"></i>Libertin ({{ keywords_libertin.count }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                    <i class="fas fa-globe me-2"></i>Général ({{ keywords_general.count }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="trending-tab" data-bs-toggle="tab" data-bs-target="#trending" type="button" role="tab">
                    <i class="fas fa-trending-up me-2"></i>Tendances ({{ trending_topics.count }})
                </button>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <div class="tab-content" id="keywordTabsContent">
            <!-- Onglet Amical -->
            <div class="tab-pane fade show active" id="amical" role="tabpanel">
                <div class="row" id="keywords-amical">
                    {% for keyword in keywords_amical %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card keyword-card category-amical h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0">{{ keyword.keyword }}</h6>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="editKeyword({{ keyword.id }})">
                                                <i class="fas fa-edit me-2"></i>Modifier
                                            </a></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteKeyword({{ keyword.id }})">
                                                <i class="fas fa-trash me-2"></i>Supprimer
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="mb-2">
                                    <small class="text-muted">Volume: {{ keyword.search_volume }}/mois</small>
                                    <div class="weight-indicator mt-1" style="width: {{ keyword.weight|floatformat:0 }}0%"></div>
                                    <small class="text-muted">Poids: {{ keyword.weight }}</small>
                                </div>
                                
                                <span class="keyword-badge bg-success text-white">{{ keyword.get_category_display }}</span>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-tags fa-2x mb-3"></i>
                            <p>Aucun mot-clé dans cette catégorie</p>
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addKeywordModal" onclick="setCategory('amical')">
                                <i class="fas fa-plus me-2"></i>Ajouter le premier
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Onglet Amour -->
            <div class="tab-pane fade" id="amour" role="tabpanel">
                <div class="row" id="keywords-amour">
                    {% for keyword in keywords_amour %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card keyword-card category-amour h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0">{{ keyword.keyword }}</h6>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="editKeyword({{ keyword.id }})">
                                                <i class="fas fa-edit me-2"></i>Modifier
                                            </a></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteKeyword({{ keyword.id }})">
                                                <i class="fas fa-trash me-2"></i>Supprimer
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="mb-2">
                                    <small class="text-muted">Volume: {{ keyword.search_volume }}/mois</small>
                                    <div class="weight-indicator mt-1" style="width: {{ keyword.weight|floatformat:0 }}0%"></div>
                                    <small class="text-muted">Poids: {{ keyword.weight }}</small>
                                </div>
                                
                                <span class="keyword-badge bg-danger text-white">{{ keyword.get_category_display }}</span>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-heart fa-2x mb-3"></i>
                            <p>Aucun mot-clé dans cette catégorie</p>
                            <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#addKeywordModal" onclick="setCategory('amour')">
                                <i class="fas fa-plus me-2"></i>Ajouter le premier
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Onglet Libertin -->
            <div class="tab-pane fade" id="libertin" role="tabpanel">
                <div class="row" id="keywords-libertin">
                    {% for keyword in keywords_libertin %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card keyword-card category-libertin h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0">{{ keyword.keyword }}</h6>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="editKeyword({{ keyword.id }})">
                                                <i class="fas fa-edit me-2"></i>Modifier
                                            </a></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteKeyword({{ keyword.id }})">
                                                <i class="fas fa-trash me-2"></i>Supprimer
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="mb-2">
                                    <small class="text-muted">Volume: {{ keyword.search_volume }}/mois</small>
                                    <div class="weight-indicator mt-1" style="width: {{ keyword.weight|floatformat:0 }}0%"></div>
                                    <small class="text-muted">Poids: {{ keyword.weight }}</small>
                                </div>
                                
                                <span class="keyword-badge bg-warning text-dark">{{ keyword.get_category_display }}</span>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-fire fa-2x mb-3"></i>
                            <p>Aucun mot-clé dans cette catégorie</p>
                            <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#addKeywordModal" onclick="setCategory('libertin')">
                                <i class="fas fa-plus me-2"></i>Ajouter le premier
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Onglet Général -->
            <div class="tab-pane fade" id="general" role="tabpanel">
                <div class="row" id="keywords-general">
                    {% for keyword in keywords_general %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card keyword-card category-general h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0">{{ keyword.keyword }}</h6>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="editKeyword({{ keyword.id }})">
                                                <i class="fas fa-edit me-2"></i>Modifier
                                            </a></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteKeyword({{ keyword.id }})">
                                                <i class="fas fa-trash me-2"></i>Supprimer
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="mb-2">
                                    <small class="text-muted">Volume: {{ keyword.search_volume }}/mois</small>
                                    <div class="weight-indicator mt-1" style="width: {{ keyword.weight|floatformat:0 }}0%"></div>
                                    <small class="text-muted">Poids: {{ keyword.weight }}</small>
                                </div>
                                
                                <span class="keyword-badge bg-secondary text-white">{{ keyword.get_category_display }}</span>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-globe fa-2x mb-3"></i>
                            <p>Aucun mot-clé dans cette catégorie</p>
                            <button class="btn btn-secondary" data-bs-toggle="modal" data-bs-target="#addKeywordModal" onclick="setCategory('general')">
                                <i class="fas fa-plus me-2"></i>Ajouter le premier
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Onglet Tendances -->
            <div class="tab-pane fade" id="trending" role="tabpanel">
                <div class="row" id="trending-topics">
                    {% for topic in trending_topics %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card keyword-card h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0">{{ topic.topic }}</h6>
                                    <span class="badge bg-info">{{ topic.trend_score }}/100</span>
                                </div>
                                
                                {% if topic.description %}
                                <p class="card-text small text-muted">{{ topic.description|truncatechars:100 }}</p>
                                {% endif %}
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="keyword-badge bg-info text-white">{{ topic.get_category_display }}</span>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="editTrendingTopic({{ topic.id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deleteTrendingTopic({{ topic.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-trending-up fa-2x mb-3"></i>
                            <p>Aucun sujet tendance configuré</p>
                            <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#addTrendingModal">
                                <i class="fas fa-plus me-2"></i>Ajouter le premier
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour ajouter un mot-clé -->
<div class="modal fade" id="addKeywordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Nouveau Mot-clé
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addKeywordForm">
                    <div class="mb-3">
                        <label for="keywordText" class="form-label">Mot-clé *</label>
                        <input type="text" class="form-control" id="keywordText" required
                               placeholder="Ex: rencontre amoureuse">
                    </div>

                    <div class="mb-3">
                        <label for="keywordCategory" class="form-label">Catégorie *</label>
                        <select class="form-select" id="keywordCategory" required>
                            <option value="amical">Amical</option>
                            <option value="amour">Amour</option>
                            <option value="libertin">Libertin</option>
                            <option value="general">Général</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="searchVolume" class="form-label">Volume de recherche</label>
                                <input type="number" class="form-control" id="searchVolume"
                                       value="1000" min="0" max="100000">
                                <small class="form-text text-muted">Recherches/mois</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="keywordWeight" class="form-label">Poids</label>
                                <input type="number" class="form-control" id="keywordWeight"
                                       value="1.0" min="0.1" max="10.0" step="0.1">
                                <small class="form-text text-muted">Importance (0.1-10.0)</small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-success" onclick="addKeyword()">
                    <i class="fas fa-plus me-2"></i>Ajouter
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour importer en masse -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>Import en Masse
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Saisissez un mot-clé par ligne ou séparez-les par des virgules.
                </div>

                <form id="importForm">
                    <div class="mb-3">
                        <label for="importCategory" class="form-label">Catégorie *</label>
                        <select class="form-select" id="importCategory" required>
                            <option value="amical">Amical</option>
                            <option value="amour">Amour</option>
                            <option value="libertin">Libertin</option>
                            <option value="general">Général</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="keywordsText" class="form-label">Mots-clés *</label>
                        <textarea class="form-control" id="keywordsText" rows="8" required
                                  placeholder="rencontre sérieuse&#10;site de rencontre&#10;premier rendez-vous&#10;..."></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="defaultVolume" class="form-label">Volume par défaut</label>
                                <input type="number" class="form-control" id="defaultVolume"
                                       value="1000" min="0" max="100000">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="defaultWeight" class="form-label">Poids par défaut</label>
                                <input type="number" class="form-control" id="defaultWeight"
                                       value="1.0" min="0.1" max="10.0" step="0.1">
                            </div>
                        </div>
                    </div>
                </form>

                <div id="importResult" class="mt-3" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-info" onclick="importKeywords()">
                    <i class="fas fa-upload me-2"></i>Importer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de configuration -->
<div class="modal fade" id="configModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cog me-2"></i>Configuration de l'Analyse
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Ces paramètres affectent le calcul des scores de pertinence des suggestions.
                </div>

                <form id="configForm">
                    <h6>Poids des Critères d'Analyse</h6>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="relevanceWeight" class="form-label">
                                    Pertinence Catégorie: <span id="relevanceValue">{{ active_config.relevance_weight|default:0.3 }}</span>
                                </label>
                                <input type="range" class="config-slider" id="relevanceWeight"
                                       min="0" max="1" step="0.05" value="{{ active_config.relevance_weight|default:0.3 }}"
                                       oninput="updateSliderValue('relevance', this.value)">
                            </div>

                            <div class="mb-3">
                                <label for="seoWeight" class="form-label">
                                    Potentiel SEO: <span id="seoValue">{{ active_config.seo_weight|default:0.25 }}</span>
                                </label>
                                <input type="range" class="config-slider" id="seoWeight"
                                       min="0" max="1" step="0.05" value="{{ active_config.seo_weight|default:0.25 }}"
                                       oninput="updateSliderValue('seo', this.value)">
                            </div>

                            <div class="mb-3">
                                <label for="trendWeight" class="form-label">
                                    Tendances: <span id="trendValue">{{ active_config.trend_weight|default:0.2 }}</span>
                                </label>
                                <input type="range" class="config-slider" id="trendWeight"
                                       min="0" max="1" step="0.05" value="{{ active_config.trend_weight|default:0.2 }}"
                                       oninput="updateSliderValue('trend', this.value)">
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="seasonalWeight" class="form-label">
                                    Saisonnalité: <span id="seasonalValue">{{ active_config.seasonal_weight|default:0.15 }}</span>
                                </label>
                                <input type="range" class="config-slider" id="seasonalWeight"
                                       min="0" max="1" step="0.05" value="{{ active_config.seasonal_weight|default:0.15 }}"
                                       oninput="updateSliderValue('seasonal', this.value)">
                            </div>

                            <div class="mb-3">
                                <label for="audienceWeight" class="form-label">
                                    Audience: <span id="audienceValue">{{ active_config.audience_weight|default:0.1 }}</span>
                                </label>
                                <input type="range" class="config-slider" id="audienceWeight"
                                       min="0" max="1" step="0.05" value="{{ active_config.audience_weight|default:0.1 }}"
                                       oninput="updateSliderValue('audience', this.value)">
                            </div>

                            <div class="alert alert-info small">
                                <strong>Total:</strong> <span id="totalWeight">1.00</span><br>
                                <small>Doit être égal à 1.00</small>
                            </div>
                        </div>
                    </div>

                    <h6>Poids des Catégories</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="amicalWeight" class="form-label">
                                    Amical: <span id="amicalValue">{{ active_config.amical_category_weight|default:0.3 }}</span>
                                </label>
                                <input type="range" class="config-slider" id="amicalWeight"
                                       min="0" max="1" step="0.05" value="{{ active_config.amical_category_weight|default:0.3 }}"
                                       oninput="updateSliderValue('amical', this.value)">
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="amourWeight" class="form-label">
                                    Amour: <span id="amourValue">{{ active_config.amour_category_weight|default:0.5 }}</span>
                                </label>
                                <input type="range" class="config-slider" id="amourWeight"
                                       min="0" max="1" step="0.05" value="{{ active_config.amour_category_weight|default:0.5 }}"
                                       oninput="updateSliderValue('amour', this.value)">
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="libertinWeight" class="form-label">
                                    Libertin: <span id="libertinValue">{{ active_config.libertin_category_weight|default:0.2 }}</span>
                                </label>
                                <input type="range" class="config-slider" id="libertinWeight"
                                       min="0" max="1" step="0.05" value="{{ active_config.libertin_category_weight|default:0.2 }}"
                                       oninput="updateSliderValue('libertin', this.value)">
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info small">
                        <strong>Total Catégories:</strong> <span id="totalCategoryWeight">1.00</span><br>
                        <small>Doit être égal à 1.00</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="saveConfiguration()">
                    <i class="fas fa-save me-2"></i>Sauvegarder
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
console.log('🔧 Chargement gestion mots-clés...');

// Fonction pour définir la catégorie dans le modal d'ajout
function setCategory(category) {
    document.getElementById('keywordCategory').value = category;
}

// Ajouter un nouveau mot-clé
async function addKeyword() {
    const keyword = document.getElementById('keywordText').value.trim();
    const category = document.getElementById('keywordCategory').value;
    const searchVolume = parseInt(document.getElementById('searchVolume').value);
    const weight = parseFloat(document.getElementById('keywordWeight').value);

    if (!keyword) {
        alert('Veuillez saisir un mot-clé');
        return;
    }

    try {
        const response = await fetch('/backoffice/api/keywords/add/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify({
                keyword: keyword,
                category: category,
                search_volume: searchVolume,
                weight: weight
            })
        });

        const data = await response.json();

        if (data.success) {
            showToast('Mot-clé ajouté avec succès !', 'success');

            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addKeywordModal'));
            modal.hide();

            // Recharger la page
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showToast('Erreur: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('❌ Erreur:', error);
        showToast('Erreur de connexion', 'error');
    }
}

// Importer des mots-clés en masse
async function importKeywords() {
    const category = document.getElementById('importCategory').value;
    const keywordsText = document.getElementById('keywordsText').value.trim();
    const defaultVolume = parseInt(document.getElementById('defaultVolume').value);
    const defaultWeight = parseFloat(document.getElementById('defaultWeight').value);

    if (!keywordsText) {
        alert('Veuillez saisir des mots-clés');
        return;
    }

    try {
        const response = await fetch('/backoffice/api/keywords/import/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify({
                keywords_text: keywordsText,
                category: category,
                default_volume: defaultVolume,
                default_weight: defaultWeight
            })
        });

        const data = await response.json();

        if (data.success) {
            const resultDiv = document.getElementById('importResult');
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>Import terminé !</h6>
                    <p class="mb-0">
                        <strong>${data.created_count}</strong> mots-clés créés<br>
                        <strong>${data.skipped_count}</strong> mots-clés ignorés (déjà existants)<br>
                        <strong>${data.total_processed}</strong> mots-clés traités au total
                    </p>
                </div>
            `;
            resultDiv.style.display = 'block';

            // Recharger après un délai
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        } else {
            showToast('Erreur: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('❌ Erreur:', error);
        showToast('Erreur de connexion', 'error');
    }
}

// Exporter les mots-clés
async function exportKeywords() {
    try {
        const response = await fetch('/backoffice/api/keywords/export/');
        const data = await response.json();

        if (data.success) {
            // Créer un fichier CSV
            let csvContent = "Mot-clé,Catégorie,Volume,Poids\n";
            data.keywords.forEach(keyword => {
                csvContent += `"${keyword.keyword}","${keyword.category}",${keyword.search_volume},${keyword.weight}\n`;
            });

            // Télécharger le fichier
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'mots-cles-seo.csv';
            a.click();
            window.URL.revokeObjectURL(url);

            showToast(`${data.total_count} mots-clés exportés`, 'success');
        } else {
            showToast('Erreur lors de l\'export', 'error');
        }
    } catch (error) {
        console.error('❌ Erreur:', error);
        showToast('Erreur de connexion', 'error');
    }
}

// Mettre à jour les valeurs des sliders
function updateSliderValue(type, value) {
    document.getElementById(type + 'Value').textContent = parseFloat(value).toFixed(2);

    // Calculer les totaux
    updateTotals();
}

// Mettre à jour les totaux
function updateTotals() {
    const relevance = parseFloat(document.getElementById('relevanceWeight').value);
    const seo = parseFloat(document.getElementById('seoWeight').value);
    const trend = parseFloat(document.getElementById('trendWeight').value);
    const seasonal = parseFloat(document.getElementById('seasonalWeight').value);
    const audience = parseFloat(document.getElementById('audienceWeight').value);

    const total = relevance + seo + trend + seasonal + audience;
    document.getElementById('totalWeight').textContent = total.toFixed(2);

    const amical = parseFloat(document.getElementById('amicalWeight').value);
    const amour = parseFloat(document.getElementById('amourWeight').value);
    const libertin = parseFloat(document.getElementById('libertinWeight').value);

    const categoryTotal = amical + amour + libertin;
    document.getElementById('totalCategoryWeight').textContent = categoryTotal.toFixed(2);
}

// Sauvegarder la configuration
async function saveConfiguration() {
    const config = {
        relevance_weight: parseFloat(document.getElementById('relevanceWeight').value),
        seo_weight: parseFloat(document.getElementById('seoWeight').value),
        trend_weight: parseFloat(document.getElementById('trendWeight').value),
        seasonal_weight: parseFloat(document.getElementById('seasonalWeight').value),
        audience_weight: parseFloat(document.getElementById('audienceWeight').value),
        amical_category_weight: parseFloat(document.getElementById('amicalWeight').value),
        amour_category_weight: parseFloat(document.getElementById('amourWeight').value),
        libertin_category_weight: parseFloat(document.getElementById('libertinWeight').value),
    };

    // Vérifier que les totaux sont corrects
    const total = config.relevance_weight + config.seo_weight + config.trend_weight + config.seasonal_weight + config.audience_weight;
    const categoryTotal = config.amical_category_weight + config.amour_category_weight + config.libertin_category_weight;

    if (Math.abs(total - 1.0) > 0.01) {
        alert('Le total des poids des critères doit être égal à 1.00');
        return;
    }

    if (Math.abs(categoryTotal - 1.0) > 0.01) {
        alert('Le total des poids des catégories doit être égal à 1.00');
        return;
    }

    try {
        const response = await fetch('/backoffice/api/config/update/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify(config)
        });

        const data = await response.json();

        if (data.success) {
            showToast('Configuration sauvegardée !', 'success');

            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('configModal'));
            modal.hide();
        } else {
            showToast('Erreur: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('❌ Erreur:', error);
        showToast('Erreur de connexion', 'error');
    }
}

// Supprimer un mot-clé
async function deleteKeyword(keywordId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce mot-clé ?')) {
        return;
    }

    try {
        const response = await fetch(`/backoffice/api/keywords/${keywordId}/delete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
            }
        });

        const data = await response.json();

        if (data.success) {
            showToast('Mot-clé supprimé', 'success');
            window.location.reload();
        } else {
            showToast('Erreur: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('❌ Erreur:', error);
        showToast('Erreur de connexion', 'error');
    }
}

// Fonction pour afficher des toasts
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}

// Fonction pour obtenir le cookie CSRF
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Initialiser les totaux au chargement
document.addEventListener('DOMContentLoaded', function() {
    updateTotals();
});

console.log('✅ Gestion mots-clés chargée');
</script>
{% endblock %}
