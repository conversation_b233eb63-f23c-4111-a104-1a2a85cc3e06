{% extends 'backoffice/base.html' %}

{% block page_title_main %}Modération - Événements{% endblock %}
{% block page_title_breadcrumb %}Modération - Événements{% endblock %}
{% block page_title_header %}Modération - Événements{% endblock %}
{% block page_icon %}<i class="fas fa-calendar-check me-2"></i>{% endblock %}

{% block backoffice_content %}
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Événements à Modérer</h6>
            </div>
            <div class="card-body">
                {% if events %}
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Titre</th>
                                    <th>Créateur</th>
                                    <th>Type</th>
                                    <th>Date Événement</th>
                                    <th>Participants</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for event in events %}
                                <tr>
                                    <td>{{ event.title }}</td>
                                    <td>{{ event.creator.nom }} {{ event.creator.prenom }}</td>
                                    <td>{{ event.get_event_type_display }}</td>
                                    <td>{{ event.event_date|date:"d/m/Y" }} à {{ event.event_time|time:"H:i" }}</td>
                                    <td>{{ event.current_participants_count }}/{{ event.max_participants }}</td>
                                    <td>
                                        {% if event.is_approved %}
                                            <span class="badge bg-success">Approuvé</span>
                                        {% else %}
                                            <span class="badge bg-warning">En attente</span>
                                        {% endif %}
                                        {% if not event.is_active %}
                                            <span class="badge bg-danger">Inactif</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if not event.is_approved %}
                                            <button class="btn btn-sm btn-success">Approuver</button>
                                        {% endif %}
                                        <button class="btn btn-sm btn-danger">Supprimer</button>
                                        <button class="btn btn-sm btn-outline-primary">Voir Détails</button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">Aucun événement à modérer</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
