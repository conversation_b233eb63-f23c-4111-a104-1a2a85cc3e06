{% extends 'backoffice/base.html' %}

{% block page_title_main %}Traffic Manager{% endblock %}
{% block page_title_breadcrumb %}Traffic Manager{% endblock %}
{% block page_title_header %}Traffic Manager{% endblock %}
{% block page_icon %}<i class="fas fa-chart-line me-2"></i>{% endblock %}

{% block backoffice_content %}
<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Utilisateurs Actifs (24h)
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_users }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Visites Quotidiennes
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ daily_visits }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-eye fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Visites Hebdomadaires
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ weekly_visits }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Trafic en Temps Réel</h6>
            </div>
            <div class="card-body">
                <canvas id="trafficChart"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Pages Populaires</h6>
            </div>
            <div class="card-body">
                {% if popular_pages %}
                    <div class="list-group">
                        {% for page in popular_pages %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                {{ page.url }}
                                <span class="badge bg-primary rounded-pill">{{ page.visits }}</span>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">Aucune donnée de trafic disponible</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Graphique de trafic en temps réel
const trafficCtx = document.getElementById('trafficChart').getContext('2d');
const trafficChart = new Chart(trafficCtx, {
    type: 'line',
    data: {
        labels: [],
        datasets: [{
            label: 'Visiteurs Actifs',
            data: [],
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Mise à jour des données de trafic
function updateTrafficStats() {
    fetch('{% url "backoffice:api_traffic_stats" %}')
        .then(response => response.json())
        .then(data => {
            // Ajouter un point au graphique
            const now = new Date().toLocaleTimeString();
            trafficChart.data.labels.push(now);
            trafficChart.data.datasets[0].data.push(data.active_users);
            
            // Garder seulement les 20 derniers points
            if (trafficChart.data.labels.length > 20) {
                trafficChart.data.labels.shift();
                trafficChart.data.datasets[0].data.shift();
            }
            
            trafficChart.update();
        })
        .catch(error => console.error('Erreur:', error));
}

// Mettre à jour toutes les 10 secondes
setInterval(updateTrafficStats, 10000);
</script>
{% endblock %}
