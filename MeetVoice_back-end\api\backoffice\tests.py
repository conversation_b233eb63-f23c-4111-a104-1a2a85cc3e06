from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import date, time, timedelta
import json

from compte.models import Compte
from abonnement.models import Abonnement, Facture
from actualite.models import Actualite
from mur.models import Mur
from evenement.models import Event

User = get_user_model()

class BackofficeViewTest(TestCase):
    """Tests pour les vues du back-office"""

    def setUp(self):
        self.client = Client()
        # Créer un utilisateur admin
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            username='admin',
            password='adminpass123'
        )
        self.admin_user.is_staff = True
        self.admin_user.is_admin = True
        self.admin_user.is_superuser = True
        self.admin_user.save()

        # Créer un utilisateur normal
        self.normal_user = User.objects.create_user(
            email='<EMAIL>',
            username='user',
            password='userpass123'
        )

    def test_dashboard_requires_staff(self):
        """Test que le dashboard nécessite les droits staff"""
        response = self.client.get(reverse('backoffice:dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirection vers login

    def test_dashboard_view_for_staff(self):
        """Test de la vue dashboard pour un staff"""
        self.client.login(email='<EMAIL>', password='adminpass123')
        response = self.client.get(reverse('backoffice:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'total_users')

    def test_tarifs_view_for_staff(self):
        """Test de la vue tarifs pour un staff"""
        self.client.login(email='<EMAIL>', password='adminpass123')
        response = self.client.get(reverse('backoffice:tarifs'))
        self.assertEqual(response.status_code, 200)

    def test_traffic_manager_view_for_staff(self):
        """Test de la vue traffic manager pour un staff"""
        self.client.login(email='<EMAIL>', password='adminpass123')
        response = self.client.get(reverse('backoffice:traffic_manager'))
        self.assertEqual(response.status_code, 200)

    def test_articles_view_for_staff(self):
        """Test de la vue articles pour un staff"""
        self.client.login(email='<EMAIL>', password='adminpass123')
        response = self.client.get(reverse('backoffice:articles'))
        self.assertEqual(response.status_code, 200)

    def test_moderation_posts_view_for_staff(self):
        """Test de la vue modération posts pour un staff"""
        self.client.login(email='<EMAIL>', password='adminpass123')
        response = self.client.get(reverse('backoffice:moderation_posts'))
        self.assertEqual(response.status_code, 200)

    def test_moderation_events_view_for_staff(self):
        """Test de la vue modération événements pour un staff"""
        self.client.login(email='<EMAIL>', password='adminpass123')
        response = self.client.get(reverse('backoffice:moderation_events'))
        self.assertEqual(response.status_code, 200)

    def test_api_dashboard_stats(self):
        """Test de l'API des statistiques du dashboard"""
        self.client.login(email='<EMAIL>', password='adminpass123')
        response = self.client.get(reverse('backoffice:api_dashboard_stats'))
        self.assertEqual(response.status_code, 200)

        data = json.loads(response.content)
        self.assertIn('total_users', data)
        self.assertIn('new_users_today', data)
        self.assertIn('active_subscriptions', data)
        self.assertIn('revenue_today', data)

    def test_api_traffic_stats(self):
        """Test de l'API des statistiques de trafic"""
        self.client.login(email='<EMAIL>', password='adminpass123')
        response = self.client.get(reverse('backoffice:api_traffic_stats'))
        self.assertEqual(response.status_code, 200)

        data = json.loads(response.content)
        self.assertIn('active_users', data)
        self.assertIn('posts_today', data)
        self.assertIn('events_today', data)

    def test_normal_user_cannot_access_backoffice(self):
        """Test qu'un utilisateur normal ne peut pas accéder au back-office"""
        self.client.login(email='<EMAIL>', password='userpass123')
        response = self.client.get(reverse('backoffice:dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirection

class BackofficeIntegrationTest(TestCase):
    """Tests d'intégration pour le back-office"""

    def setUp(self):
        self.client = Client()
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            username='admin',
            password='adminpass123'
        )
        self.admin_user.is_staff = True
        self.admin_user.is_admin = True
        self.admin_user.is_superuser = True
        self.admin_user.save()

        # Créer des données de test
        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            username='user1',
            password='pass'
        )

        self.event = Event.objects.create(
            title='Test Event',
            description='Test description',
            creator=self.user1,
            max_participants=10,
            event_date=date.today() + timedelta(days=7),
            event_time=time(14, 30),
            location='Test Location',
            event_type='soiree'
        )

    def test_dashboard_shows_correct_stats(self):
        """Test que le dashboard affiche les bonnes statistiques"""
        self.client.login(email='<EMAIL>', password='adminpass123')
        response = self.client.get(reverse('backoffice:api_dashboard_stats'))

        data = json.loads(response.content)
        # Il devrait y avoir 2 utilisateurs (admin + user1)
        self.assertEqual(data['total_users'], 2)

    def test_traffic_stats_integration(self):
        """Test d'intégration des statistiques de trafic"""
        self.client.login(email='<EMAIL>', password='adminpass123')
        response = self.client.get(reverse('backoffice:api_traffic_stats'))

        data = json.loads(response.content)
        # Il devrait y avoir 1 événement créé aujourd'hui
        self.assertEqual(data['events_today'], 1)
