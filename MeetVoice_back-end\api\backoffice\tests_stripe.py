from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from unittest.mock import patch, MagicMock
import stripe

from abonnement.models import Abonnement, Facture

User = get_user_model()

class StripeIntegrationTest(TestCase):
    """Tests d'intégration pour l'API Stripe"""
    
    def setUp(self):
        self.client = Client()
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            username='admin',
            password='adminpass123'
        )
        self.admin_user.is_staff = True
        self.admin_user.is_superuser = True
        self.admin_user.save()
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='user',
            password='userpass123'
        )
        
        # Créer un abonnement de test
        self.abonnement = Abonnement.objects.create(
            nom='Test Premium',
            prix_ttc=29.99,
            prix_ht=24.99,
            credits=100,
            interval='month'
        )
    
    @patch('stripe.Customer.create')
    def test_create_stripe_customer(self, mock_customer_create):
        """Test de création d'un client Stripe"""
        # Mock de la réponse Stripe
        mock_customer_create.return_value = MagicMock(
            id='cus_test123',
            email='<EMAIL>'
        )
        
        # Simuler la création d'un client Stripe
        customer = stripe.Customer.create(
            email=self.user.email,
            name=self.user.username
        )
        
        self.assertEqual(customer.id, 'cus_test123')
        self.assertEqual(customer.email, '<EMAIL>')
        mock_customer_create.assert_called_once()
    
    @patch('stripe.Subscription.create')
    @patch('stripe.Customer.create')
    def test_create_stripe_subscription(self, mock_customer_create, mock_subscription_create):
        """Test de création d'un abonnement Stripe"""
        # Mock des réponses Stripe
        mock_customer_create.return_value = MagicMock(id='cus_test123')
        mock_subscription_create.return_value = MagicMock(
            id='sub_test123',
            status='active',
            current_period_start=1640995200,  # 1er janvier 2022
            current_period_end=1643673600     # 1er février 2022
        )
        
        # Simuler la création d'un abonnement
        customer = stripe.Customer.create(email=self.user.email)
        subscription = stripe.Subscription.create(
            customer=customer.id,
            items=[{'price': 'price_test123'}]
        )
        
        self.assertEqual(subscription.id, 'sub_test123')
        self.assertEqual(subscription.status, 'active')
        mock_subscription_create.assert_called_once()
    
    @patch('stripe.Subscription.retrieve')
    def test_retrieve_stripe_subscription(self, mock_subscription_retrieve):
        """Test de récupération d'un abonnement Stripe"""
        mock_subscription_retrieve.return_value = MagicMock(
            id='sub_test123',
            status='active',
            customer='cus_test123'
        )
        
        subscription = stripe.Subscription.retrieve('sub_test123')
        
        self.assertEqual(subscription.id, 'sub_test123')
        self.assertEqual(subscription.status, 'active')
        mock_subscription_retrieve.assert_called_once_with('sub_test123')
    
    @patch('stripe.Subscription.modify')
    def test_update_stripe_subscription(self, mock_subscription_modify):
        """Test de modification d'un abonnement Stripe"""
        mock_subscription_modify.return_value = MagicMock(
            id='sub_test123',
            status='active'
        )
        
        subscription = stripe.Subscription.modify(
            'sub_test123',
            metadata={'updated': 'true'}
        )
        
        self.assertEqual(subscription.id, 'sub_test123')
        mock_subscription_modify.assert_called_once()
    
    @patch('stripe.Subscription.cancel')
    def test_cancel_stripe_subscription(self, mock_subscription_cancel):
        """Test d'annulation d'un abonnement Stripe"""
        mock_subscription_cancel.return_value = MagicMock(
            id='sub_test123',
            status='canceled'
        )
        
        subscription = stripe.Subscription.cancel('sub_test123')
        
        self.assertEqual(subscription.status, 'canceled')
        mock_subscription_cancel.assert_called_once_with('sub_test123')
    
    def test_stripe_webhook_handling(self):
        """Test de gestion des webhooks Stripe"""
        # Simuler un webhook Stripe
        webhook_data = {
            'type': 'invoice.payment_succeeded',
            'data': {
                'object': {
                    'id': 'in_test123',
                    'customer': 'cus_test123',
                    'amount_paid': 2999,
                    'status': 'paid'
                }
            }
        }
        
        # Ici, on testerait la logique de traitement des webhooks
        # Par exemple, mettre à jour le statut de paiement d'une facture
        self.assertTrue(True)  # Placeholder pour le test réel
    
    def test_stripe_error_handling(self):
        """Test de gestion des erreurs Stripe"""
        with patch('stripe.Customer.create') as mock_create:
            # Simuler une erreur Stripe
            mock_create.side_effect = stripe.error.CardError(
                message="Your card was declined.",
                param="number",
                code="card_declined"
            )
            
            try:
                stripe.Customer.create(email='<EMAIL>')
                self.fail("Expected CardError was not raised")
            except stripe.error.CardError as e:
                self.assertEqual(e.code, 'card_declined')
    
    def test_subscription_price_calculation(self):
        """Test de calcul des prix d'abonnement"""
        # Test du calcul TTC/HT
        self.assertEqual(self.abonnement.prix_ttc, 29.99)
        self.assertEqual(self.abonnement.prix_ht, 24.99)
        
        # Test du calcul de la TVA (20%)
        tva_expected = round(self.abonnement.prix_ttc - self.abonnement.prix_ht, 2)
        self.assertEqual(tva_expected, 5.00)

class StripeBackofficeIntegrationTest(TestCase):
    """Tests d'intégration entre Stripe et le back-office"""
    
    def setUp(self):
        self.client = Client()
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            username='admin',
            password='adminpass123'
        )
        self.admin_user.is_staff = True
        self.admin_user.is_superuser = True
        self.admin_user.save()
    
    def test_tarifs_page_displays_stripe_data(self):
        """Test que la page tarifs affiche les données Stripe"""
        self.client.login(email='<EMAIL>', password='adminpass123')
        response = self.client.get(reverse('backoffice:tarifs'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Abonnements Disponibles')
        self.assertContains(response, 'Factures Récentes')
    
    @patch('stripe.Product.list')
    def test_sync_stripe_products(self, mock_product_list):
        """Test de synchronisation des produits Stripe"""
        mock_product_list.return_value = MagicMock(
            data=[
                MagicMock(
                    id='prod_test123',
                    name='Premium Plan',
                    active=True
                )
            ]
        )
        
        products = stripe.Product.list()
        self.assertEqual(len(products.data), 1)
        self.assertEqual(products.data[0].name, 'Premium Plan')
    
    def test_revenue_calculation(self):
        """Test de calcul des revenus"""
        # Créer des factures de test
        user = User.objects.create_user(
            email='<EMAIL>',
            username='customer',
            password='pass'
        )
        
        abonnement = Abonnement.objects.create(
            nom='Test Plan',
            prix_ttc=19.99,
            prix_ht=16.66,
            credits=50,
            interval='month'
        )
        
        # Note: Créer une facture nécessiterait d'adapter le modèle Facture
        # Pour ce test, nous simulons juste le calcul
        test_revenue = 19.99
        
        # Tester le calcul des revenus (simulation)
        self.assertEqual(test_revenue, 19.99)
