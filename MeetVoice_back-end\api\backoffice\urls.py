from django.urls import path
from . import views

app_name = 'backoffice'

urlpatterns = [
    # Vues principales du back-office
    path('dashboard/', views.dashboard_view, name='dashboard'),
    path('traffic-manager/', views.traffic_manager_view, name='traffic_manager'),
    path('google-analytics/', views.google_analytics_view, name='google_analytics'),
    path('articles/', views.articles_view, name='articles'),
    path('articles/update-status/<int:article_id>/', views.update_article_status, name='update_article_status'),
    path('moderation/posts/', views.moderation_posts_view, name='moderation_posts'),
    path('moderation/events/', views.moderation_events_view, name='moderation_events'),
    
    # Gestion des abonnements
    path('abonnements/', views.abonnements_list_view, name='abonnements_list'),
    path('abonnements/create/', views.abonnement_create_view, name='abonnement_create'),
    path('abonnements/<int:abonnement_id>/', views.abonnement_detail_view, name='abonnement_detail'),
    path('abonnements/<int:abonnement_id>/edit/', views.abonnement_edit_view, name='abonnement_edit'),
    path('abonnements/<int:abonnement_id>/delete/', views.abonnement_delete_view, name='abonnement_delete'),

    # API endpoints pour les données en temps réel
    path('api/dashboard-stats/', views.api_dashboard_stats, name='api_dashboard_stats'),
    path('api/traffic-stats/', views.api_traffic_stats, name='api_traffic_stats'),

    # API endpoints pour les abonnements
    path('api/abonnements/<int:abonnement_id>/toggle-status/', views.abonnement_toggle_status, name='abonnement_toggle_status'),
    path('api/abonnements/<int:abonnement_id>/sync-stripe/', views.abonnement_sync_stripe, name='abonnement_sync_stripe'),
    path('api/abonnements/bulk-actions/', views.abonnements_bulk_actions, name='abonnements_bulk_actions'),
    path('api/abonnements/import-from-stripe/', views.import_from_stripe, name='import_from_stripe'),
    path('api/abonnements/export/', views.export_abonnements, name='export_abonnements'),

    # APIs pour la gestion des mots-clés SEO
    path('api/keywords/<str:category>/', views.get_keywords_by_category, name='get_keywords_by_category'),
    path('api/keywords/add/', views.add_keyword_api, name='add_keyword_api'),
    path('api/keywords/<int:keyword_id>/update/', views.update_keyword_api, name='update_keyword_api'),
    path('api/keywords/<int:keyword_id>/delete/', views.delete_keyword_api, name='delete_keyword_api'),
    path('api/keywords/import/', views.import_keywords_api, name='import_keywords_api'),
    path('api/keywords/export/', views.export_keywords_api, name='export_keywords_api'),
]
