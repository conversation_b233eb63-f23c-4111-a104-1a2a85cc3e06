from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.db.models import Sum
from django.utils import timezone
from datetime import timedelta
from django.views.decorators.csrf import csrf_exempt
from django.contrib import messages
import json
import sys
import os

from compte.models import Compte
from abonnement.models import Abonnement, Facture, Description, AbonnementUtilisateur
from abonnement.services import StripeService
from actualite.models import Actualite
from actualite.models_keywords import SEOKeyword, TrendingTopic, SeasonalKeyword, ContentSuggestionConfig
from mur.models import Mur
from evenement.models import Event



@staff_member_required
def dashboard_view(request):
    """Vue principale du dashboard avec métriques en temps réel"""
    context = {
        'total_users': Compte.objects.count(),
        'active_subscriptions': Facture.objects.filter(payer=True).count(),
        'total_revenue': Facture.objects.filter(payer=True).aggregate(Sum('prix_total_ttc'))['prix_total_ttc__sum'] or 0,
        'recent_articles': Actualite.objects.order_by('-date_publication')[:5],
    }
    return render(request, 'backoffice/dashboard.html', context)

@staff_member_required
def traffic_manager_view(request):
    """Vue du gestionnaire de trafic en temps réel"""
    # Statistiques de base
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    
    context = {
        'daily_visits': 0,  # À implémenter avec un système de tracking
        'weekly_visits': 0,
        'popular_pages': [],
        'active_users': Compte.objects.filter(last_login__gte=timezone.now() - timedelta(hours=24)).count(),
    }
    return render(request, 'backoffice/traffic_manager.html', context)

@staff_member_required
def google_analytics_view(request):
    """Vue d'intégration Google Analytics"""
    context = {
        'analytics_data': {},  # À implémenter avec l'API Google Analytics
    }
    return render(request, 'backoffice/google_analytics.html', context)

@staff_member_required
def articles_view(request):
    """Vue CRUD pour les articles avec filtres et pagination"""
    from django.core.paginator import Paginator
    from django.db.models import Q, Sum

    # Récupération des paramètres de filtrage
    search = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    featured_filter = request.GET.get('featured', '')

    # Construction de la requête
    articles = Actualite.objects.select_related('auteur').order_by('-date_publication')

    # Application des filtres
    if search:
        articles = articles.filter(
            Q(titre__icontains=search) |
            Q(contenu__icontains=search) |
            Q(petit_description__icontains=search) |
            Q(tags__icontains=search)
        )

    if status_filter:
        articles = articles.filter(status=status_filter)

    if featured_filter:
        articles = articles.filter(mis_en_avant=featured_filter == '1')

    # Pagination
    paginator = Paginator(articles, 25)  # 25 articles par page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Calcul des statistiques
    all_articles = Actualite.objects.all()
    stats = {
        'published_count': all_articles.filter(status='published').count(),
        'draft_count': all_articles.filter(status='draft').count(),
        'archived_count': all_articles.filter(status='archived').count(),
        'total_views': all_articles.aggregate(Sum('access_count'))['access_count__sum'] or 0,
    }

    context = {
        'articles': page_obj,
        'page_obj': page_obj,
        'is_paginated': page_obj.has_other_pages(),
        'published_count': stats['published_count'],
        'draft_count': stats['draft_count'],
        'archived_count': stats['archived_count'],
        'total_views': stats['total_views'],
    }
    return render(request, 'backoffice/articles.html', context)

@csrf_exempt
@login_required(login_url='/')
def update_article_status(request, article_id):
    """AJAX endpoint to update article status"""
    if not request.user.is_authenticated or not request.user.is_staff:
        return JsonResponse({'error': 'Non autorisé'}, status=403)

    if request.method == 'POST':
        try:
            import json
            article = get_object_or_404(Actualite, id=article_id)

            # Handle both JSON and form data
            if request.content_type == 'application/json':
                data = json.loads(request.body)
                new_status = data.get('status')
            else:
                new_status = request.POST.get('status')

            # Validate status
            valid_statuses = ['draft', 'published', 'archived']
            if new_status not in valid_statuses:
                return JsonResponse({'error': 'Statut invalide'}, status=400)

            # Update status
            article.status = new_status
            article.save()

            return JsonResponse({
                'success': True,
                'message': f'Statut mis à jour vers "{article.get_status_display()}"',
                'new_status': new_status,
                'status_display': article.get_status_display()
            })

        except Exception as e:
            return JsonResponse({'error': f'Erreur: {str(e)}'}, status=500)

    return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

@staff_member_required
def moderation_posts_view(request):
    """Vue de modération des posts du mur"""
    posts = Mur.objects.order_by('-date_creation')
    context = {
        'posts': posts,
    }
    return render(request, 'backoffice/moderation_posts.html', context)

@staff_member_required
def moderation_events_view(request):
    """Vue de modération des événements"""
    events = Event.objects.order_by('-created_at')
    context = {
        'events': events,
    }
    return render(request, 'backoffice/moderation_events.html', context)

# API endpoints pour les données en temps réel
@staff_member_required
def api_dashboard_stats(request):
    """API pour les statistiques du dashboard"""
    stats = {
        'total_users': Compte.objects.count(),
        'new_users_today': Compte.objects.filter(created_at__date=timezone.now().date()).count(),
        'active_subscriptions': Facture.objects.filter(payer=True).count(),
        'revenue_today': Facture.objects.filter(
            date_paiement__date=timezone.now().date(),
            payer=True
        ).aggregate(Sum('prix_total_ttc'))['prix_total_ttc__sum'] or 0,
    }
    return JsonResponse(stats)


# ==========================================
# VUES CRUD POUR LA GESTION DES ABONNEMENTS
# ==========================================

@staff_member_required
def abonnements_list_view(request):
    """Vue pour lister tous les abonnements"""
    abonnements = Abonnement.objects.all().order_by('ordre_affichage', 'prix_ttc')

    context = {
        'abonnements': abonnements,
        'total_abonnements': abonnements.count(),
        'abonnements_actifs': abonnements.filter(is_active=True).count(),
        'abonnements_avec_stripe': abonnements.exclude(stripe_product_id__isnull=True).exclude(stripe_product_id='').count(),
        'utilisateurs_abonnes': AbonnementUtilisateur.objects.filter(statut='active').count(),
    }
    return render(request, 'backoffice/abonnements/liste.html', context)


@staff_member_required
def abonnement_detail_view(request, abonnement_id):
    """Vue pour afficher les détails d'un abonnement"""
    abonnement = get_object_or_404(Abonnement, id=abonnement_id)

    # Statistiques de l'abonnement
    abonnements_utilisateurs = AbonnementUtilisateur.objects.filter(abonnement=abonnement)
    factures = Facture.objects.filter(abonnement=abonnement)

    context = {
        'abonnement': abonnement,
        'features': abonnement.get_features_list(),
        'total_souscriptions': abonnements_utilisateurs.count(),
        'souscriptions_actives': abonnements_utilisateurs.filter(statut='active').count(),
        'revenus_total': factures.filter(payer=True).aggregate(Sum('prix_total_ttc'))['prix_total_ttc__sum'] or 0,
        'derniers_abonnements': abonnements_utilisateurs.order_by('-date_creation')[:10],
    }
    return render(request, 'backoffice/abonnements/detail.html', context)


@staff_member_required
def abonnement_create_view(request):
    """Vue pour créer un nouvel abonnement"""
    if request.method == 'POST':
        try:
            # Récupérer les données du formulaire
            nom = request.POST.get('nom')
            description_courte = request.POST.get('description_courte', '')
            prix_ttc = request.POST.get('prix_ttc')
            credits = request.POST.get('credits', 0)
            interval = request.POST.get('interval', 'month')
            interval_count = request.POST.get('interval_count', 1)
            is_popular = request.POST.get('is_popular') == 'on'
            is_active = request.POST.get('is_active') == 'on'
            ordre_affichage = request.POST.get('ordre_affichage', 0)
            entreprise = request.POST.get('entreprise') == 'on'

            # Récupérer les fonctionnalités
            features_raw = request.POST.get('features', '')
            features = [f.strip() for f in features_raw.split('\n') if f.strip()]

            # Créer l'abonnement
            abonnement = Abonnement.objects.create(
                nom=nom,
                description_courte=description_courte,
                prix_ttc=prix_ttc,
                credits=int(credits),
                interval=interval,
                interval_count=int(interval_count),
                is_popular=is_popular,
                is_active=is_active,
                ordre_affichage=int(ordre_affichage),
                entreprise=entreprise,
                features=features
            )

            # Créer automatiquement le produit et prix Stripe si demandé
            create_stripe = request.POST.get('create_stripe') == 'on'
            if create_stripe:
                product_id = StripeService.create_product(abonnement)
                if product_id:
                    price_id = StripeService.create_price(abonnement)
                    if price_id:
                        messages.success(request, f'Abonnement "{nom}" créé avec succès et synchronisé avec Stripe.')
                    else:
                        messages.warning(request, f'Abonnement "{nom}" créé mais erreur lors de la création du prix Stripe.')
                else:
                    messages.warning(request, f'Abonnement "{nom}" créé mais erreur lors de la création du produit Stripe.')
            else:
                messages.success(request, f'Abonnement "{nom}" créé avec succès.')

            return redirect('backoffice:abonnements_list')

        except Exception as e:
            messages.error(request, f'Erreur lors de la création de l\'abonnement: {str(e)}')

    context = {
        'interval_choices': Abonnement._meta.get_field('interval').choices,
    }
    return render(request, 'backoffice/abonnements/create.html', context)


@staff_member_required
def abonnement_edit_view(request, abonnement_id):
    """Vue pour modifier un abonnement existant"""
    abonnement = get_object_or_404(Abonnement, id=abonnement_id)

    if request.method == 'POST':
        try:
            # Mettre à jour les données
            abonnement.nom = request.POST.get('nom')
            abonnement.description_courte = request.POST.get('description_courte', '')
            abonnement.prix_ttc = request.POST.get('prix_ttc')
            abonnement.credits = int(request.POST.get('credits', 0))
            abonnement.interval = request.POST.get('interval', 'month')
            abonnement.interval_count = int(request.POST.get('interval_count', 1))
            abonnement.is_popular = request.POST.get('is_popular') == 'on'
            abonnement.is_active = request.POST.get('is_active') == 'on'
            abonnement.ordre_affichage = int(request.POST.get('ordre_affichage', 0))
            abonnement.entreprise = request.POST.get('entreprise') == 'on'

            # Mettre à jour les fonctionnalités
            features_raw = request.POST.get('features', '')
            abonnement.features = [f.strip() for f in features_raw.split('\n') if f.strip()]

            abonnement.save()

            # Mettre à jour Stripe si demandé
            update_stripe = request.POST.get('update_stripe') == 'on'
            if update_stripe and abonnement.stripe_product_id:
                success = StripeService.update_product(abonnement)
                if success:
                    messages.success(request, f'Abonnement "{abonnement.nom}" mis à jour avec succès et synchronisé avec Stripe.')
                else:
                    messages.warning(request, f'Abonnement "{abonnement.nom}" mis à jour mais erreur lors de la synchronisation Stripe.')
            else:
                messages.success(request, f'Abonnement "{abonnement.nom}" mis à jour avec succès.')

            return redirect('backoffice:abonnement_detail', abonnement_id=abonnement.id)

        except Exception as e:
            messages.error(request, f'Erreur lors de la modification de l\'abonnement: {str(e)}')

    context = {
        'abonnement': abonnement,
        'features_text': '\n'.join(abonnement.get_features_list()),
        'interval_choices': Abonnement._meta.get_field('interval').choices,
    }
    return render(request, 'backoffice/abonnements/edit.html', context)


@staff_member_required
def abonnement_delete_view(request, abonnement_id):
    """Vue pour supprimer un abonnement"""
    abonnement = get_object_or_404(Abonnement, id=abonnement_id)

    if request.method == 'POST':
        try:
            # Vérifier s'il y a des abonnements actifs
            abonnements_actifs = AbonnementUtilisateur.objects.filter(
                abonnement=abonnement,
                statut='active'
            ).count()

            if abonnements_actifs > 0:
                messages.error(request, f'Impossible de supprimer l\'abonnement "{abonnement.nom}". Il y a {abonnements_actifs} abonnement(s) actif(s).')
                return redirect('backoffice:abonnement_detail', abonnement_id=abonnement.id)

            nom_abonnement = abonnement.nom
            abonnement.delete()

            messages.success(request, f'Abonnement "{nom_abonnement}" supprimé avec succès.')
            return redirect('backoffice:abonnements_list')

        except Exception as e:
            messages.error(request, f'Erreur lors de la suppression de l\'abonnement: {str(e)}')
            return redirect('backoffice:abonnement_detail', abonnement_id=abonnement.id)

    context = {
        'abonnement': abonnement,
        'abonnements_actifs': AbonnementUtilisateur.objects.filter(
            abonnement=abonnement,
            statut='active'
        ).count(),
        'total_factures': Facture.objects.filter(abonnement=abonnement).count(),
    }
    return render(request, 'backoffice/abonnements/delete.html', context)


@staff_member_required
def abonnement_toggle_status(request, abonnement_id):
    """API pour activer/désactiver un abonnement"""
    if request.method == 'POST':
        try:
            abonnement = get_object_or_404(Abonnement, id=abonnement_id)
            abonnement.is_active = not abonnement.is_active
            abonnement.save()

            # Mettre à jour Stripe si nécessaire
            if abonnement.stripe_product_id:
                StripeService.update_product(abonnement)

            status = 'activé' if abonnement.is_active else 'désactivé'
            return JsonResponse({
                'success': True,
                'message': f'Abonnement "{abonnement.nom}" {status} avec succès.',
                'is_active': abonnement.is_active
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Erreur: {str(e)}'
            }, status=500)

    return JsonResponse({'success': False, 'message': 'Méthode non autorisée'}, status=405)


@staff_member_required
def abonnement_sync_stripe(request, abonnement_id):
    """API pour synchroniser un abonnement avec Stripe"""
    if request.method == 'POST':
        try:
            abonnement = get_object_or_404(Abonnement, id=abonnement_id)

            # Créer le produit Stripe s'il n'existe pas
            if not abonnement.stripe_product_id:
                product_id = StripeService.create_product(abonnement)
                if not product_id:
                    return JsonResponse({
                        'success': False,
                        'message': 'Erreur lors de la création du produit Stripe'
                    }, status=500)

            # Créer le prix Stripe s'il n'existe pas
            if not abonnement.stripe_price_id:
                price_id = StripeService.create_price(abonnement)
                if not price_id:
                    return JsonResponse({
                        'success': False,
                        'message': 'Erreur lors de la création du prix Stripe'
                    }, status=500)

            return JsonResponse({
                'success': True,
                'message': f'Abonnement "{abonnement.nom}" synchronisé avec Stripe avec succès.',
                'stripe_product_id': abonnement.stripe_product_id,
                'stripe_price_id': abonnement.stripe_price_id
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Erreur: {str(e)}'
            }, status=500)

    return JsonResponse({'success': False, 'message': 'Méthode non autorisée'}, status=405)


@staff_member_required
def abonnements_bulk_actions(request):
    """API pour les actions en lot sur les abonnements"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            action = data.get('action')
            abonnement_ids = data.get('abonnement_ids', [])

            if not action or not abonnement_ids:
                return JsonResponse({
                    'success': False,
                    'message': 'Action et IDs d\'abonnements requis'
                }, status=400)

            abonnements = Abonnement.objects.filter(id__in=abonnement_ids)

            if action == 'activate':
                abonnements.update(is_active=True)
                message = f'{abonnements.count()} abonnement(s) activé(s)'

            elif action == 'deactivate':
                abonnements.update(is_active=False)
                message = f'{abonnements.count()} abonnement(s) désactivé(s)'

            elif action == 'sync_stripe':
                synced_count = 0
                for abonnement in abonnements:
                    if not abonnement.stripe_product_id:
                        StripeService.create_product(abonnement)
                    if not abonnement.stripe_price_id:
                        StripeService.create_price(abonnement)
                    synced_count += 1
                message = f'{synced_count} abonnement(s) synchronisé(s) avec Stripe'

            elif action == 'delete':
                # Vérifier qu'aucun abonnement n'a d'utilisateurs actifs
                for abonnement in abonnements:
                    abonnements_actifs = AbonnementUtilisateur.objects.filter(
                        abonnement=abonnement,
                        is_active=True
                    ).count()
                    if abonnements_actifs > 0:
                        return JsonResponse({
                            'success': False,
                            'message': f'Impossible de supprimer "{abonnement.nom}". Il y a {abonnements_actifs} abonnement(s) actif(s).'
                        }, status=400)

                # Supprimer les abonnements et leurs produits Stripe
                deleted_count = 0
                for abonnement in abonnements:
                    if abonnement.stripe_product_id:
                        StripeService.delete_product(abonnement.stripe_product_id)
                    abonnement.delete()
                    deleted_count += 1
                message = f'{deleted_count} abonnement(s) supprimé(s)'

            else:
                return JsonResponse({
                    'success': False,
                    'message': 'Action non reconnue'
                }, status=400)

            return JsonResponse({
                'success': True,
                'message': message
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Erreur: {str(e)}'
            }, status=500)

    return JsonResponse({'success': False, 'message': 'Méthode non autorisée'}, status=405)


@staff_member_required
def import_from_stripe(request):
    """API pour importer les produits depuis Stripe"""
    if request.method == 'POST':
        try:
            imported_count = StripeService.import_products_from_stripe()
            return JsonResponse({
                'success': True,
                'message': f'{imported_count} nouveau(x) plan(s) importé(s) depuis Stripe',
                'imported_count': imported_count
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Erreur lors de l\'importation: {str(e)}'
            }, status=500)

    return JsonResponse({'success': False, 'message': 'Méthode non autorisée'}, status=405)


@staff_member_required
def export_abonnements(request):
    """Export CSV des abonnements"""
    import csv
    from django.http import HttpResponse

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="abonnements.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'ID', 'Nom', 'Description', 'Prix TTC', 'Prix HT', 'Crédits',
        'Intervalle', 'Populaire', 'Actif', 'Stripe Product ID', 'Stripe Price ID',
        'Ordre', 'Date création'
    ])

    for abonnement in Abonnement.objects.all():
        writer.writerow([
            abonnement.id,
            abonnement.nom,
            abonnement.description_courte,
            abonnement.prix_ttc,
            abonnement.prix_ht,
            abonnement.credits,
            abonnement.get_interval_display(),
            'Oui' if abonnement.is_popular else 'Non',
            'Oui' if abonnement.is_active else 'Non',
            abonnement.stripe_product_id or '',
            abonnement.stripe_price_id or '',
            abonnement.ordre_affichage,
            abonnement.date_creation.strftime('%Y-%m-%d %H:%M:%S') if hasattr(abonnement, 'date_creation') else ''
        ])

    return response


@staff_member_required
def api_traffic_stats(request):
    """API pour les statistiques de trafic"""
    stats = {
        'active_users': Compte.objects.filter(last_login__gte=timezone.now() - timedelta(hours=1)).count(),
        'posts_today': Mur.objects.filter(date_creation__date=timezone.now().date()).count(),
        'events_today': Event.objects.filter(created_at__date=timezone.now().date()).count(),
    }
    return JsonResponse(stats)


# ============================================================================
# APIS POUR LA GESTION DES MOTS-CLÉS SEO
# ============================================================================

@csrf_exempt
@staff_member_required
def get_keywords_by_category(request, category):
    """API pour récupérer les mots-clés par catégorie"""

    try:
        keywords = SEOKeyword.objects.filter(
            category=category,
            is_active=True
        ).order_by('-weight', 'keyword')

        keywords_data = []
        for keyword in keywords:
            keywords_data.append({
                'id': keyword.id,
                'keyword': keyword.keyword,
                'volume': keyword.search_volume,
                'weight': keyword.weight,
                'active': keyword.is_active,
                'category': keyword.category,
            })

        return JsonResponse({
            'success': True,
            'keywords': keywords_data,
            'count': len(keywords_data)
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
@staff_member_required
def add_keyword_api(request):
    """API pour ajouter un nouveau mot-clé"""

    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Méthode non autorisée'})

    try:
        data = json.loads(request.body)

        keyword = data.get('keyword', '').strip()
        category = data.get('category', 'general')
        search_volume = int(data.get('search_volume', 1000))
        weight = float(data.get('weight', 1.0))

        if not keyword:
            return JsonResponse({'success': False, 'error': 'Mot-clé requis'})

        # Vérifier si le mot-clé existe déjà
        if SEOKeyword.objects.filter(keyword=keyword, category=category).exists():
            return JsonResponse({'success': False, 'error': 'Ce mot-clé existe déjà dans cette catégorie'})

        # Créer le mot-clé
        seo_keyword = SEOKeyword.objects.create(
            keyword=keyword,
            category=category,
            search_volume=search_volume,
            weight=weight,
            is_active=True
        )

        return JsonResponse({
            'success': True,
            'keyword': {
                'id': seo_keyword.id,
                'keyword': seo_keyword.keyword,
                'category': seo_keyword.category,
                'volume': seo_keyword.search_volume,
                'weight': seo_keyword.weight,
                'active': seo_keyword.is_active,
            }
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
@staff_member_required
def update_keyword_api(request, keyword_id):
    """API pour modifier un mot-clé"""

    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Méthode non autorisée'})

    try:
        keyword = get_object_or_404(SEOKeyword, id=keyword_id)
        data = json.loads(request.body)

        # Mettre à jour les champs
        keyword.keyword = data.get('keyword', keyword.keyword).strip()
        keyword.category = data.get('category', keyword.category)
        keyword.search_volume = int(data.get('search_volume', keyword.search_volume))
        keyword.weight = float(data.get('weight', keyword.weight))
        keyword.is_active = data.get('is_active', keyword.is_active)

        keyword.save()

        return JsonResponse({
            'success': True,
            'keyword': {
                'id': keyword.id,
                'keyword': keyword.keyword,
                'category': keyword.category,
                'volume': keyword.search_volume,
                'weight': keyword.weight,
                'active': keyword.is_active,
            }
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
@staff_member_required
def delete_keyword_api(request, keyword_id):
    """API pour supprimer un mot-clé"""

    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Méthode non autorisée'})

    try:
        keyword = get_object_or_404(SEOKeyword, id=keyword_id)
        keyword_name = keyword.keyword
        keyword.delete()

        return JsonResponse({
            'success': True,
            'message': f'Mot-clé "{keyword_name}" supprimé'
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
@staff_member_required
def import_keywords_api(request):
    """API pour importer des mots-clés en masse"""

    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Méthode non autorisée'})

    try:
        data = json.loads(request.body)
        keywords_text = data.get('keywords_text', '').strip()
        category = data.get('category', 'general')
        default_volume = int(data.get('default_volume', 1000))
        default_weight = float(data.get('default_weight', 1.0))

        if not keywords_text:
            return JsonResponse({'success': False, 'error': 'Texte de mots-clés requis'})

        # Parser les mots-clés (un par ligne ou séparés par des virgules)
        keywords_list = []
        for line in keywords_text.split('\n'):
            line = line.strip()
            if line:
                # Séparer par virgules si présentes
                if ',' in line:
                    keywords_list.extend([k.strip() for k in line.split(',') if k.strip()])
                else:
                    keywords_list.append(line)

        created_count = 0
        skipped_count = 0

        for keyword_text in keywords_list:
            if not keyword_text:
                continue

            # Vérifier si le mot-clé existe déjà
            if SEOKeyword.objects.filter(keyword=keyword_text, category=category).exists():
                skipped_count += 1
                continue

            # Créer le mot-clé
            SEOKeyword.objects.create(
                keyword=keyword_text,
                category=category,
                search_volume=default_volume,
                weight=default_weight,
                is_active=True
            )
            created_count += 1

        return JsonResponse({
            'success': True,
            'created_count': created_count,
            'skipped_count': skipped_count,
            'total_processed': len(keywords_list)
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@staff_member_required
def export_keywords_api(request):
    """API pour exporter les mots-clés"""

    try:
        category = request.GET.get('category', 'all')

        if category == 'all':
            keywords = SEOKeyword.objects.filter(is_active=True)
        else:
            keywords = SEOKeyword.objects.filter(category=category, is_active=True)

        keywords_data = []
        for keyword in keywords:
            keywords_data.append({
                'keyword': keyword.keyword,
                'category': keyword.category,
                'search_volume': keyword.search_volume,
                'weight': keyword.weight,
            })

        return JsonResponse({
            'success': True,
            'keywords': keywords_data,
            'total_count': len(keywords_data)
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


