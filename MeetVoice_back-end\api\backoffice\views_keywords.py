#!/usr/bin/env python
"""
Vues pour la gestion des mots-clés SEO dans le back-office
"""
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db import models
import json

from actualite.models_keywords import SEOKeyword, TrendingTopic, SeasonalKeyword, ContentSuggestionConfig


@login_required
def keywords_management(request):
    """Page principale de gestion des mots-clés"""
    
    # Récupérer les mots-clés par catégorie
    keywords_amical = SEOKeyword.objects.filter(category='amical', is_active=True).order_by('-weight')
    keywords_amour = SEOKeyword.objects.filter(category='amour', is_active=True).order_by('-weight')
    keywords_libertin = SEOKeyword.objects.filter(category='libertin', is_active=True).order_by('-weight')
    keywords_general = SEOKeyword.objects.filter(category='general', is_active=True).order_by('-weight')
    
    # Récupérer les sujets tendance
    trending_topics = TrendingTopic.objects.filter(is_active=True).order_by('-trend_score')[:10]
    
    # Récupérer les mots-clés saisonniers
    seasonal_keywords = SeasonalKeyword.objects.filter(is_active=True).order_by('month', 'keyword')
    
    # Configuration active
    active_config = ContentSuggestionConfig.get_active_config()
    
    # Statistiques
    stats = {
        'total_keywords': SEOKeyword.objects.filter(is_active=True).count(),
        'total_trending': TrendingTopic.objects.filter(is_active=True).count(),
        'total_seasonal': SeasonalKeyword.objects.filter(is_active=True).count(),
        'total_volume': SEOKeyword.objects.filter(is_active=True).aggregate(
            total=models.Sum('search_volume'))['total'] or 0
    }
    
    context = {
        'keywords_amical': keywords_amical,
        'keywords_amour': keywords_amour,
        'keywords_libertin': keywords_libertin,
        'keywords_general': keywords_general,
        'trending_topics': trending_topics,
        'seasonal_keywords': seasonal_keywords,
        'active_config': active_config,
        'stats': stats,
    }
    
    return render(request, 'backoffice/keywords_management.html', context)


@csrf_exempt
@login_required
def add_keyword_api(request):
    """API pour ajouter un nouveau mot-clé"""
    
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Méthode non autorisée'})
    
    try:
        data = json.loads(request.body)
        
        keyword = data.get('keyword', '').strip()
        category = data.get('category', 'general')
        search_volume = int(data.get('search_volume', 1000))
        weight = float(data.get('weight', 1.0))
        
        if not keyword:
            return JsonResponse({'success': False, 'error': 'Mot-clé requis'})
        
        # Vérifier si le mot-clé existe déjà
        if SEOKeyword.objects.filter(keyword=keyword, category=category).exists():
            return JsonResponse({'success': False, 'error': 'Ce mot-clé existe déjà dans cette catégorie'})
        
        # Créer le mot-clé
        seo_keyword = SEOKeyword.objects.create(
            keyword=keyword,
            category=category,
            search_volume=search_volume,
            weight=weight,
            is_active=True
        )
        
        return JsonResponse({
            'success': True,
            'keyword': {
                'id': seo_keyword.id,
                'keyword': seo_keyword.keyword,
                'category': seo_keyword.category,
                'search_volume': seo_keyword.search_volume,
                'weight': seo_keyword.weight,
            }
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
@login_required
def update_keyword_api(request, keyword_id):
    """API pour modifier un mot-clé"""
    
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Méthode non autorisée'})
    
    try:
        keyword = get_object_or_404(SEOKeyword, id=keyword_id)
        data = json.loads(request.body)
        
        # Mettre à jour les champs
        keyword.keyword = data.get('keyword', keyword.keyword).strip()
        keyword.category = data.get('category', keyword.category)
        keyword.search_volume = int(data.get('search_volume', keyword.search_volume))
        keyword.weight = float(data.get('weight', keyword.weight))
        keyword.is_active = data.get('is_active', keyword.is_active)
        
        keyword.save()
        
        return JsonResponse({
            'success': True,
            'keyword': {
                'id': keyword.id,
                'keyword': keyword.keyword,
                'category': keyword.category,
                'search_volume': keyword.search_volume,
                'weight': keyword.weight,
                'is_active': keyword.is_active,
            }
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
@login_required
def delete_keyword_api(request, keyword_id):
    """API pour supprimer un mot-clé"""
    
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Méthode non autorisée'})
    
    try:
        keyword = get_object_or_404(SEOKeyword, id=keyword_id)
        keyword.delete()
        
        return JsonResponse({'success': True})
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
@login_required
def add_trending_topic_api(request):
    """API pour ajouter un sujet tendance"""
    
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Méthode non autorisée'})
    
    try:
        data = json.loads(request.body)
        
        topic = data.get('topic', '').strip()
        trend_score = int(data.get('trend_score', 50))
        category = data.get('category', 'general')
        description = data.get('description', '').strip()
        
        if not topic:
            return JsonResponse({'success': False, 'error': 'Sujet requis'})
        
        # Créer le sujet tendance
        trending_topic = TrendingTopic.objects.create(
            topic=topic,
            trend_score=trend_score,
            category=category,
            description=description,
            is_active=True
        )
        
        return JsonResponse({
            'success': True,
            'topic': {
                'id': trending_topic.id,
                'topic': trending_topic.topic,
                'trend_score': trending_topic.trend_score,
                'category': trending_topic.category,
                'description': trending_topic.description,
            }
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
@login_required
def update_config_api(request):
    """API pour mettre à jour la configuration"""
    
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Méthode non autorisée'})
    
    try:
        data = json.loads(request.body)
        
        # Récupérer ou créer la configuration
        config, created = ContentSuggestionConfig.objects.get_or_create(
            name='default',
            defaults={
                'relevance_weight': 0.3,
                'seo_weight': 0.25,
                'trend_weight': 0.2,
                'seasonal_weight': 0.15,
                'audience_weight': 0.1,
                'amical_category_weight': 0.3,
                'amour_category_weight': 0.5,
                'libertin_category_weight': 0.2,
                'is_active': True
            }
        )
        
        # Mettre à jour les poids
        config.relevance_weight = float(data.get('relevance_weight', config.relevance_weight))
        config.seo_weight = float(data.get('seo_weight', config.seo_weight))
        config.trend_weight = float(data.get('trend_weight', config.trend_weight))
        config.seasonal_weight = float(data.get('seasonal_weight', config.seasonal_weight))
        config.audience_weight = float(data.get('audience_weight', config.audience_weight))
        
        config.amical_category_weight = float(data.get('amical_category_weight', config.amical_category_weight))
        config.amour_category_weight = float(data.get('amour_category_weight', config.amour_category_weight))
        config.libertin_category_weight = float(data.get('libertin_category_weight', config.libertin_category_weight))
        
        config.save()
        
        return JsonResponse({
            'success': True,
            'config': {
                'relevance_weight': config.relevance_weight,
                'seo_weight': config.seo_weight,
                'trend_weight': config.trend_weight,
                'seasonal_weight': config.seasonal_weight,
                'audience_weight': config.audience_weight,
                'amical_category_weight': config.amical_category_weight,
                'amour_category_weight': config.amour_category_weight,
                'libertin_category_weight': config.libertin_category_weight,
                'weights_sum': config.get_weights_sum(),
                'category_weights_sum': config.get_category_weights_sum(),
            }
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
@login_required
def import_keywords_api(request):
    """API pour importer des mots-clés en masse"""
    
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Méthode non autorisée'})
    
    try:
        data = json.loads(request.body)
        keywords_text = data.get('keywords_text', '').strip()
        category = data.get('category', 'general')
        default_volume = int(data.get('default_volume', 1000))
        default_weight = float(data.get('default_weight', 1.0))
        
        if not keywords_text:
            return JsonResponse({'success': False, 'error': 'Texte de mots-clés requis'})
        
        # Parser les mots-clés (un par ligne ou séparés par des virgules)
        keywords_list = []
        for line in keywords_text.split('\n'):
            line = line.strip()
            if line:
                # Séparer par virgules si présentes
                if ',' in line:
                    keywords_list.extend([k.strip() for k in line.split(',') if k.strip()])
                else:
                    keywords_list.append(line)
        
        created_count = 0
        skipped_count = 0
        
        for keyword_text in keywords_list:
            if not keyword_text:
                continue
                
            # Vérifier si le mot-clé existe déjà
            if SEOKeyword.objects.filter(keyword=keyword_text, category=category).exists():
                skipped_count += 1
                continue
            
            # Créer le mot-clé
            SEOKeyword.objects.create(
                keyword=keyword_text,
                category=category,
                search_volume=default_volume,
                weight=default_weight,
                is_active=True
            )
            created_count += 1
        
        return JsonResponse({
            'success': True,
            'created_count': created_count,
            'skipped_count': skipped_count,
            'total_processed': len(keywords_list)
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def export_keywords_api(request):
    """API pour exporter les mots-clés"""
    
    try:
        category = request.GET.get('category', 'all')
        
        if category == 'all':
            keywords = SEOKeyword.objects.filter(is_active=True)
        else:
            keywords = SEOKeyword.objects.filter(category=category, is_active=True)
        
        keywords_data = []
        for keyword in keywords:
            keywords_data.append({
                'keyword': keyword.keyword,
                'category': keyword.category,
                'search_volume': keyword.search_volume,
                'weight': keyword.weight,
            })
        
        return JsonResponse({
            'success': True,
            'keywords': keywords_data,
            'total_count': len(keywords_data)
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})
