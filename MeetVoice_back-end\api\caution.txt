1 respecter le partern design de l'application des couleurs
2 respecter la charte graphique
3 ne pas changer les variables des models
4 respecter les bonnes pratiques de django
5 crée une app pour chaque nouvelle fonctionnalité
6 ce projet est un back office + api faire la différence entre les deux
7 crée une interface clair car les utilisateurs sont pas des codeurs
8 chaque app a son propre template et ses fichiers startic
9 ne pas toucher aux autres app pour corriger un probleme d'une app differente
10 faire des test unitaire pour chaque fonctionnalité

