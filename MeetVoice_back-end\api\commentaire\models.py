from django.db import models
from django.conf import settings

class Commentaire(models.Model):
    titre = models.CharField(max_length=200, blank=True, null=True, default=None)
    date_creation = models.DateTimeField(auto_now_add=True)
    commentaire = models.CharField(max_length=1200, blank=True, null=True, default=None)
    note = models.IntegerField(blank=True, null=True)
    creator = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='commentaires_crees', blank=True, null=True)
    cible = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='commentaires_recus', blank=True, null=True)
    article = models.ForeignKey('actualite.Actualite', on_delete=models.CASCADE, related_name='commentaires', null=True, blank=True)
    mur = models.ForeignKey('mur.Mur', on_delete=models.CASCADE, related_name='commentaires', null=True, blank=True)


    def __str__(self):
        return self.commentaire if self.commentaire else "Commentaire non défini"