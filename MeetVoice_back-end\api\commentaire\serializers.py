from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Commentaire

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    """Serializer pour les informations utilisateur"""
    
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['id', 'username', 'nom', 'prenom', 'full_name']
    
    def get_full_name(self, obj):
        """Retourne le nom complet de l'utilisateur"""
        if hasattr(obj, 'nom') and hasattr(obj, 'prenom') and obj.nom and obj.prenom:
            return f"{obj.prenom} {obj.nom}"
        elif hasattr(obj, 'first_name') and hasattr(obj, 'last_name') and obj.first_name and obj.last_name:
            return f"{obj.first_name} {obj.last_name}"
        return obj.username


class CommentaireSerializer(serializers.ModelSerializer):
    """Serializer pour les commentaires"""
    
    creator_name = serializers.SerializerMethodField()
    cible_name = serializers.SerializerMethodField()
    article_title = serializers.SerializerMethodField()
    mur_title = serializers.SerializerMethodField()
    
    class Meta:
        model = Commentaire
        fields = [
            'id',
            'titre',
            'date_creation',
            'commentaire',
            'note',
            'creator',
            'creator_name',
            'cible',
            'cible_name',
            'article',
            'article_title',
            'mur',
            'mur_title'
        ]
        read_only_fields = ['id', 'date_creation']
    
    def get_creator_name(self, obj):
        """Retourne le nom complet du créateur"""
        if obj.creator:
            if hasattr(obj.creator, 'nom') and hasattr(obj.creator, 'prenom') and obj.creator.nom and obj.creator.prenom:
                return f"{obj.creator.prenom} {obj.creator.nom}"
            elif hasattr(obj.creator, 'first_name') and hasattr(obj.creator, 'last_name') and obj.creator.first_name and obj.creator.last_name:
                return f"{obj.creator.first_name} {obj.creator.last_name}"
            return obj.creator.username
        return None
    
    def get_cible_name(self, obj):
        """Retourne le nom complet de la cible"""
        if obj.cible:
            if hasattr(obj.cible, 'nom') and hasattr(obj.cible, 'prenom') and obj.cible.nom and obj.cible.prenom:
                return f"{obj.cible.prenom} {obj.cible.nom}"
            elif hasattr(obj.cible, 'first_name') and hasattr(obj.cible, 'last_name') and obj.cible.first_name and obj.cible.last_name:
                return f"{obj.cible.first_name} {obj.cible.last_name}"
            return obj.cible.username
        return None
    
    def get_article_title(self, obj):
        """Retourne le titre de l'article"""
        return obj.article.titre if obj.article else None
    
    def get_mur_title(self, obj):
        """Retourne le titre du post du mur"""
        return obj.mur.titre if obj.mur else None


class CommentaireListSerializer(serializers.ModelSerializer):
    """Serializer simplifié pour la liste des commentaires"""
    
    creator_name = serializers.SerializerMethodField()
    cible_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Commentaire
        fields = [
            'id',
            'titre',
            'date_creation',
            'commentaire',
            'note',
            'creator_name',
            'cible_name'
        ]
    
    def get_creator_name(self, obj):
        """Retourne le nom complet du créateur"""
        if obj.creator:
            if hasattr(obj.creator, 'nom') and hasattr(obj.creator, 'prenom') and obj.creator.nom and obj.creator.prenom:
                return f"{obj.creator.prenom} {obj.creator.nom}"
            elif hasattr(obj.creator, 'first_name') and hasattr(obj.creator, 'last_name') and obj.creator.first_name and obj.creator.last_name:
                return f"{obj.creator.first_name} {obj.creator.last_name}"
            return obj.creator.username
        return None
    
    def get_cible_name(self, obj):
        """Retourne le nom complet de la cible"""
        if obj.cible:
            if hasattr(obj.cible, 'nom') and hasattr(obj.cible, 'prenom') and obj.cible.nom and obj.cible.prenom:
                return f"{obj.cible.prenom} {obj.cible.nom}"
            elif hasattr(obj.cible, 'first_name') and hasattr(obj.cible, 'last_name') and obj.cible.first_name and obj.cible.last_name:
                return f"{obj.cible.first_name} {obj.cible.last_name}"
            return obj.cible.username
        return None


class CommentaireCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création de commentaires"""
    
    class Meta:
        model = Commentaire
        fields = [
            'titre',
            'commentaire',
            'note',
            'cible',
            'article',
            'mur'
        ]
    
    def validate_commentaire(self, value):
        """Validation du commentaire"""
        if not value or len(value.strip()) < 5:
            raise serializers.ValidationError("Le commentaire doit contenir au moins 5 caractères.")
        if len(value) > 1200:
            raise serializers.ValidationError("Le commentaire ne peut pas dépasser 1200 caractères.")
        return value.strip()
    
    def validate_note(self, value):
        """Validation de la note"""
        if value is not None and (value < 1 or value > 5):
            raise serializers.ValidationError("La note doit être comprise entre 1 et 5.")
        return value
    
    def validate(self, data):
        """Validation globale"""
        # Vérifier qu'au moins une cible est définie (cible, article ou mur)
        if not data.get('cible') and not data.get('article') and not data.get('mur'):
            raise serializers.ValidationError("Le commentaire doit être associé à un utilisateur, un article ou un post du mur.")
        
        # Vérifier qu'une seule cible est définie
        targets = [data.get('cible'), data.get('article'), data.get('mur')]
        if sum(1 for target in targets if target is not None) > 1:
            raise serializers.ValidationError("Le commentaire ne peut être associé qu'à une seule cible.")
        
        return data


class CommentaireUpdateSerializer(serializers.ModelSerializer):
    """Serializer pour la mise à jour de commentaires"""
    
    class Meta:
        model = Commentaire
        fields = [
            'titre',
            'commentaire',
            'note'
        ]
    
    def validate_commentaire(self, value):
        """Validation du commentaire"""
        if not value or len(value.strip()) < 5:
            raise serializers.ValidationError("Le commentaire doit contenir au moins 5 caractères.")
        if len(value) > 1200:
            raise serializers.ValidationError("Le commentaire ne peut pas dépasser 1200 caractères.")
        return value.strip()
    
    def validate_note(self, value):
        """Validation de la note"""
        if value is not None and (value < 1 or value > 5):
            raise serializers.ValidationError("La note doit être comprise entre 1 et 5.")
        return value


class CommentaireStatsSerializer(serializers.ModelSerializer):
    """Serializer pour les statistiques des commentaires"""
    
    creator_name = serializers.SerializerMethodField()
    target_type = serializers.SerializerMethodField()
    target_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Commentaire
        fields = [
            'id',
            'creator_name',
            'target_type',
            'target_name',
            'note',
            'date_creation'
        ]
    
    def get_creator_name(self, obj):
        """Retourne le nom complet du créateur"""
        if obj.creator:
            if hasattr(obj.creator, 'nom') and hasattr(obj.creator, 'prenom') and obj.creator.nom and obj.creator.prenom:
                return f"{obj.creator.prenom} {obj.creator.nom}"
            elif hasattr(obj.creator, 'first_name') and hasattr(obj.creator, 'last_name') and obj.creator.first_name and obj.creator.last_name:
                return f"{obj.creator.first_name} {obj.creator.last_name}"
            return obj.creator.username
        return None
    
    def get_target_type(self, obj):
        """Retourne le type de cible"""
        if obj.cible:
            return "utilisateur"
        elif obj.article:
            return "article"
        elif obj.mur:
            return "mur"
        return "inconnu"
    
    def get_target_name(self, obj):
        """Retourne le nom de la cible"""
        if obj.cible:
            if hasattr(obj.cible, 'nom') and hasattr(obj.cible, 'prenom') and obj.cible.nom and obj.cible.prenom:
                return f"{obj.cible.prenom} {obj.cible.nom}"
            elif hasattr(obj.cible, 'first_name') and hasattr(obj.cible, 'last_name') and obj.cible.first_name and obj.cible.last_name:
                return f"{obj.cible.first_name} {obj.cible.last_name}"
            return obj.cible.username
        elif obj.article:
            return obj.article.titre
        elif obj.mur:
            return obj.mur.titre if obj.mur.titre else "Post sans titre"
        return None
