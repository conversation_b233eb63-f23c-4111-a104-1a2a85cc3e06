"""
Tests pour l'application commentaire
"""
import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status

from .models import Commentaire
from actualite.models import Actualite
from mur.models import Mur
from .serializers import CommentaireSerializer, CommentaireCreateSerializer

User = get_user_model()


class CommentaireModelTest(TestCase):
    """Tests pour le modèle Commentaire"""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        self.target_user = User.objects.create_user(
            email='<EMAIL>',
            username='targetuser',
            password='testpass123'
        )
        self.commentaire = Commentaire.objects.create(
            creator=self.user,
            cible=self.target_user,
            titre="Commentaire Test",
            commentaire="Contenu du commentaire de test",
            note=4
        )

    def test_commentaire_creation(self):
        """Test de création d'un commentaire"""
        self.assertEqual(self.commentaire.creator, self.user)
        self.assertEqual(self.commentaire.cible, self.target_user)
        self.assertEqual(self.commentaire.titre, "Commentaire Test")
        self.assertEqual(self.commentaire.commentaire, "Contenu du commentaire de test")
        self.assertEqual(self.commentaire.note, 4)

    def test_commentaire_str(self):
        """Test de la représentation string"""
        self.assertEqual(str(self.commentaire), "Contenu du commentaire de test")

    def test_commentaire_without_content(self):
        """Test d'un commentaire sans contenu"""
        comment_no_content = Commentaire.objects.create(
            creator=self.user,
            cible=self.target_user
        )
        self.assertEqual(str(comment_no_content), "Commentaire non défini")


class CommentaireAPITest(APITestCase):
    """Tests pour l'API des commentaires"""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        self.target_user = User.objects.create_user(
            email='<EMAIL>',
            username='targetuser',
            password='testpass123'
        )
        self.article = Actualite.objects.create(
            titre="Article Test",
            contenu="Contenu de l'article",
            auteur=self.user,
            theme="test"  # Champ obligatoire
        )
        self.mur_post = Mur.objects.create(
            user=self.user,
            titre="Post Test",
            text="Contenu du post"
        )
        self.commentaire = Commentaire.objects.create(
            creator=self.user,
            cible=self.target_user,
            titre="Commentaire Test",
            commentaire="Contenu du commentaire",
            note=4
        )

    def test_list_commentaires_anonymous(self):
        """Test de récupération des commentaires sans authentification"""
        url = '/commentaire/api/commentaires/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

    def test_create_commentaire_authentication_required(self):
        """Test que l'authentification est requise pour créer un commentaire"""
        url = '/commentaire/api/commentaires/'
        data = {
            'titre': 'Nouveau Commentaire',
            'commentaire': 'Contenu du nouveau commentaire',
            'cible': self.target_user.id,
            'note': 5
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_commentaire_on_user(self):
        """Test de création d'un commentaire sur un utilisateur"""
        self.client.force_authenticate(user=self.user)
        url = '/commentaire/api/commentaires/'
        data = {
            'titre': 'Nouveau Commentaire',
            'commentaire': 'Contenu du nouveau commentaire',
            'cible': self.target_user.id,
            'note': 5
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['titre'], 'Nouveau Commentaire')
        self.assertEqual(response.data['note'], 5)

    def test_create_commentaire_on_article(self):
        """Test de création d'un commentaire sur un article"""
        self.client.force_authenticate(user=self.user)
        url = '/commentaire/api/commentaires/'
        data = {
            'titre': 'Commentaire Article',
            'commentaire': 'Commentaire sur l\'article',
            'article': self.article.id
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['article_title'], self.article.titre)

    def test_create_commentaire_on_mur_post(self):
        """Test de création d'un commentaire sur un post du mur"""
        self.client.force_authenticate(user=self.user)
        url = '/commentaire/api/commentaires/'
        data = {
            'titre': 'Commentaire Post',
            'commentaire': 'Commentaire sur le post',
            'mur': self.mur_post.id
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['mur_title'], self.mur_post.titre)

    def test_create_commentaire_validation_single_target(self):
        """Test qu'un commentaire ne peut avoir qu'une seule cible"""
        self.client.force_authenticate(user=self.user)
        url = '/commentaire/api/commentaires/'
        data = {
            'titre': 'Commentaire Multiple',
            'commentaire': 'Commentaire avec plusieurs cibles',
            'cible': self.target_user.id,
            'article': self.article.id  # Deux cibles = erreur
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_commentaire_validation_no_target(self):
        """Test qu'un commentaire doit avoir au moins une cible"""
        self.client.force_authenticate(user=self.user)
        url = '/commentaire/api/commentaires/'
        data = {
            'titre': 'Commentaire Sans Cible',
            'commentaire': 'Commentaire sans cible'
            # Aucune cible = erreur
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_commentaire_validation_content_length(self):
        """Test de validation de la longueur du contenu"""
        self.client.force_authenticate(user=self.user)
        url = '/commentaire/api/commentaires/'
        data = {
            'titre': 'Test',
            'commentaire': 'Trop',  # Trop court
            'cible': self.target_user.id
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_commentaire_validation_note_range(self):
        """Test de validation de la plage de notes"""
        self.client.force_authenticate(user=self.user)
        url = '/commentaire/api/commentaires/'
        data = {
            'titre': 'Test Note',
            'commentaire': 'Commentaire avec note invalide',
            'cible': self.target_user.id,
            'note': 6  # Note invalide (> 5)
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_my_comments_endpoint(self):
        """Test de l'endpoint mes commentaires"""
        # Créer un commentaire d'un autre utilisateur
        other_user = User.objects.create_user(
            email='<EMAIL>',
            username='otheruser',
            password='testpass123'
        )
        Commentaire.objects.create(
            creator=other_user,
            cible=self.target_user,
            commentaire="Commentaire d'un autre"
        )

        self.client.force_authenticate(user=self.user)
        url = '/commentaire/api/commentaires/my_comments/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['creator'], self.user.id)

    def test_received_comments_endpoint(self):
        """Test de l'endpoint commentaires reçus"""
        self.client.force_authenticate(user=self.target_user)
        url = '/commentaire/api/commentaires/received_comments/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['cible'], self.target_user.id)

    def test_average_rating_endpoint(self):
        """Test de l'endpoint note moyenne"""
        # Créer un autre commentaire avec note
        Commentaire.objects.create(
            creator=self.user,
            cible=self.target_user,
            commentaire="Autre commentaire",
            note=2
        )

        url = f'/commentaire/api/commentaires/average_rating/?user_id={self.target_user.id}'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['average_rating'], 3.0)  # (4 + 2) / 2
        self.assertEqual(response.data['total_ratings'], 2)
