"""
Vues pour la gestion des commentaires
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.core.paginator import Paginator
from django.db.models import Q

# DRF imports
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend

from .models import Commentaire
from .serializers import (
    CommentaireSerializer, CommentaireListSerializer, CommentaireCreateSerializer,
    CommentaireUpdateSerializer, CommentaireStatsSerializer
)


# ============================================================================
# API REST VIEWSETS
# ============================================================================

class CommentaireViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion des commentaires via API REST"""

    queryset = Commentaire.objects.all()
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['creator', 'cible', 'article', 'mur', 'note']
    search_fields = ['titre', 'commentaire']
    ordering_fields = ['date_creation', 'note']
    ordering = ['-date_creation']

    def get_serializer_class(self):
        """Retourne le serializer approprié selon l'action"""
        if self.action == 'list':
            return CommentaireListSerializer
        elif self.action == 'create':
            return CommentaireCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return CommentaireUpdateSerializer
        elif self.action == 'stats':
            return CommentaireStatsSerializer
        return CommentaireSerializer

    def get_queryset(self):
        """Filtre les commentaires selon les permissions"""
        queryset = Commentaire.objects.all()

        # Filtrer par article si spécifié
        article_id = self.request.query_params.get('article_id')
        if article_id:
            queryset = queryset.filter(article_id=article_id)

        # Filtrer par post du mur si spécifié
        mur_id = self.request.query_params.get('mur_id')
        if mur_id:
            queryset = queryset.filter(mur_id=mur_id)

        # Filtrer par utilisateur cible si spécifié
        cible_id = self.request.query_params.get('cible_id')
        if cible_id:
            queryset = queryset.filter(cible_id=cible_id)

        return queryset

    def perform_create(self, serializer):
        """Définit le créateur lors de la création"""
        serializer.save(creator=self.request.user)

    def perform_update(self, serializer):
        """Vérifie les permissions lors de la mise à jour"""
        instance = self.get_object()

        # Seul l'auteur ou un staff peut modifier
        if instance.creator != self.request.user and not self.request.user.is_staff:
            raise PermissionError("Vous ne pouvez modifier que vos propres commentaires")

        serializer.save()

    def perform_destroy(self, instance):
        """Vérifie les permissions lors de la suppression"""
        # Seul l'auteur ou un staff peut supprimer
        if instance.creator != self.request.user and not self.request.user.is_staff:
            raise PermissionError("Vous ne pouvez supprimer que vos propres commentaires")

        instance.delete()

    @action(detail=False, methods=['get'])
    def my_comments(self, request):
        """Retourne les commentaires de l'utilisateur connecté"""
        if not request.user.is_authenticated:
            return Response(
                {'error': 'Authentification requise'},
                status=status.HTTP_401_UNAUTHORIZED
            )

        user_comments = self.get_queryset().filter(creator=request.user)
        serializer = self.get_serializer(user_comments, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def received_comments(self, request):
        """Retourne les commentaires reçus par l'utilisateur connecté"""
        if not request.user.is_authenticated:
            return Response(
                {'error': 'Authentification requise'},
                status=status.HTTP_401_UNAUTHORIZED
            )

        received_comments = self.get_queryset().filter(cible=request.user)
        serializer = self.get_serializer(received_comments, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def article_comments(self, request):
        """Retourne les commentaires d'un article spécifique"""
        article_id = request.query_params.get('article_id')
        if not article_id:
            return Response(
                {'error': 'ID de l\'article requis'},
                status=status.HTTP_400_BAD_REQUEST
            )

        article_comments = self.get_queryset().filter(article_id=article_id)
        serializer = self.get_serializer(article_comments, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def mur_comments(self, request):
        """Retourne les commentaires d'un post du mur spécifique"""
        mur_id = request.query_params.get('mur_id')
        if not mur_id:
            return Response(
                {'error': 'ID du post requis'},
                status=status.HTTP_400_BAD_REQUEST
            )

        mur_comments = self.get_queryset().filter(mur_id=mur_id)
        serializer = self.get_serializer(mur_comments, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def with_rating(self, request):
        """Retourne les commentaires avec une note"""
        rated_comments = self.get_queryset().filter(note__isnull=False)
        serializer = self.get_serializer(rated_comments, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Retourne les statistiques des commentaires"""
        comments = self.get_queryset()
        serializer = CommentaireStatsSerializer(comments, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def average_rating(self, request):
        """Retourne la note moyenne pour un utilisateur"""
        user_id = request.query_params.get('user_id')
        if not user_id:
            return Response(
                {'error': 'ID de l\'utilisateur requis'},
                status=status.HTTP_400_BAD_REQUEST
            )

        from django.db.models import Avg
        avg_rating = self.get_queryset().filter(
            cible_id=user_id,
            note__isnull=False
        ).aggregate(avg_rating=Avg('note'))

        return Response({
            'user_id': user_id,
            'average_rating': avg_rating['avg_rating'] or 0,
            'total_ratings': self.get_queryset().filter(
                cible_id=user_id,
                note__isnull=False
            ).count()
        })
