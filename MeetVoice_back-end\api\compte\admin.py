from django.contrib import admin
from .models import Compte


@admin.register(Compte)
class User(admin.ModelAdmin):
    list_display =('email','last_login','username','nom','prenom','is_admin','numberPhone', 'get_last_activity')
    list_filter=('last_login','created_at',)
    search_fields=('email','nom','prenom')
    
    def get_last_activity(self, obj):
        if obj.last_activity:
            return obj.last_activity.strftime('%Y-%m-%d %H:%M:%S')
        return "Aucune activité"
    
    get_last_activity.short_description = 'Dernière activité'


