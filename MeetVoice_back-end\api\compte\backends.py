"""
Backends d'authentification personnalisés pour MeetVoice
"""

from django.contrib.auth.backends import BaseBackend
from django.contrib.auth import get_user_model
from django.db.models import Q

User = get_user_model()


class EmailOrUsernameBackend(BaseBackend):
    """
    Backend d'authentification qui permet la connexion avec email OU username
    
    Fonctionnalités :
    - Authentification avec email
    - Authentification avec username (pseudo)
    - Support du modèle Compte personnalisé
    - Gestion des cas insensibles à la casse pour l'email
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        """
        Authentifie un utilisateur avec email ou username
        
        Args:
            request: Requête HTTP
            username: Email ou username de l'utilisateur
            password: Mot de passe
            **kwargs: Arguments supplémentaires
            
        Returns:
            User: Instance utilisateur si authentification réussie, None sinon
        """
        
        if username is None or password is None:
            return None
        
        try:
            # Essayer de trouver l'utilisateur par email OU username
            # Utilisation de Q pour une recherche OR
            user = User.objects.get(
                Q(email__iexact=username) | Q(username__iexact=username)
            )
            
            # Vérifier le mot de passe
            if user.check_password(password):
                return user
            else:
                return None
                
        except User.DoesNotExist:
            # Utilisateur non trouvé
            return None
        except User.MultipleObjectsReturned:
            # Plusieurs utilisateurs trouvés (ne devrait pas arriver avec unique=True)
            return None
    
    def get_user(self, user_id):
        """
        Récupère un utilisateur par son ID
        
        Args:
            user_id: ID de l'utilisateur
            
        Returns:
            User: Instance utilisateur ou None
        """
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None


class IPTrustedBackend(BaseBackend):
    """
    Backend d'authentification pour les IPs de confiance
    Utilisé par le middleware d'authentification automatique
    """
    
    def authenticate(self, request, user=None, **kwargs):
        """
        Authentifie un utilisateur pour le système d'IP de confiance
        
        Args:
            request: Requête HTTP
            user: Instance utilisateur à authentifier
            **kwargs: Arguments supplémentaires
            
        Returns:
            User: Instance utilisateur si valide, None sinon
        """
        
        if user is None:
            return None
        
        # Vérifier que l'utilisateur est actif
        if user.is_active:
            return user
        
        return None
    
    def get_user(self, user_id):
        """
        Récupère un utilisateur par son ID
        
        Args:
            user_id: ID de l'utilisateur
            
        Returns:
            User: Instance utilisateur ou None
        """
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None
