from compte.models import Sortie, Film, Musique, Caractere,  Hobie, Tendance, Langue
from django.http import HttpResponse
import django
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'meet_voice3.settings')
django.setup()

def sortie():
    data = [
        "Boite de nuit", "Restaurant", "Théatre", "Pub", "Cinéma", "Parc", "Concert",
        "Randonnée", "Galerie d'art", "Salle de jeux", "Escape game", "Cours de danse",
        "Dégustation de vin", "Mini-golf", "Karaoke", "Événement sportif", "Spa", "Karaoke"
    ]
    
    for activite in data:
        nouvelle_sortie = Sortie(sortie=activite)
        nouvelle_sortie.save()

def films():
    styles = [
        "Action", "Comédie", "Drame", "Science-fiction", "Horreur", "Animation", "Romance",
        "Fantasy", "Thriller", "Aventure", "Documentaire", "Mystère", "Crime"
    ]
    
    for style in styles:
        nouveau_film = Film(film=style)
        nouveau_film.save()

def enregistrer_styles_de_musique():
    styles = [
        "Rock", "Pop", "Hip-hop", "Rap", "Classique", "Jazz", "Électro", "R&B",
        "Country", "Reggae", "Blues", "Metal", "Soul", "Folk", "Punk", "Indie", "Techno"
    ]
    
    for style in styles:
        nouvelle_musique = Musique(musique=style)
        nouvelle_musique.save()

def enregistrer_caracteristiques_personne():
    caracteristiques = [
    "Sérieux", "Timide", "Gentil", "Aventurier", "Sportif", "Artiste", "Sociable",
    "Intelligent", "Cultivé", "Sensible", "Romantique", "Honnête", "Drôle", "Curieux",
    "Optimiste", "Calme", "Créatif", "Ambitieux", "Famille", "Indépendant", "Religieux",
    "Énergique", "Enthousiaste", "Intrépide", "Expressif", "Intrigant", "Réfléchi",
    "Détendu", "Visionnaire", "Original", "Audacieux", "Empathique", "Libre-penseur",
    "Charmant", "Prudent", "Perspicace", "Innovant", "Pragmatique", "Spontané", "Altruiste",
    "Fiable", "Méticuleux", "Perfectionniste", "Observateur", "Persévérant", "Autodidacte",
    "Créatif", "Sensible", "Charismatique", "Courageux", "Adaptable", "Patient", "Déterminé",
    "Diplomate", "Eloquent", "Ponctuel", "Organisé", "Altruiste", "Tolérant", "Optimiste"
]


    for caract in caracteristiques:
        nouvelle_caracteristique = Caractere(caractere=caract)
        nouvelle_caracteristique.save()

               
                
           

def enregistrer_hobbies_personne():
    hobbies=[
    "Escalade", "Ski", "Snowboard", "Patinage", "Surf", "Planche à voile", "Kitesurf",
    "Yoga", "Méditation", "Escrime", "Tir à l'arc", "Pilotage", "Plongée", "Saut en parachute",
    "Vélo de montagne", "Kayak", "Voile", "Planche à roulettes", "Sculpture", "Poterie",
    "Couture", "Tricot", "Modélisme", "Astronomie", "Aéromodélisme", "Photographie animalière",
    "Jardinage botanique", "Astronomie", "Aéromodélisme", "Photographie animalière", "Jardinage botanique",
    "Équitation", "Danse contemporaine", "Acrobaties", "Jonglerie", "Poterie", "Jeu de rôle",
    "Poker", "Échecs", "Backgammon", "Bridge", "Plongée sous-marine", "Canyoning",
    "Spéléologie", "Géocaching", "Jeux de cartes", "Astronomie", "Voyage culinaire", "Plongée libre",
    "Pilotage de drones", "Escalade en salle", "Paintball","Sport en salle",  "Photographie aérienne", "Spéléologie", "Escalade en montagne", "Karaté", "Tir à l'arc", "Patinage artistique",
    "Saut à l'élastique", "Windsurf", "Paddleboard", "Aérobie", "Crossfit", "Danse latine", "Aïkido", "Parapente",
    "Plongée avec tuba", "Escalade de glace", "Surf des neiges", "Badminton", "Pilates", "Tai-chi", "Capoeira",
    "Cyclisme extrême", "Musculation", "Kung-fu", "Salsa", "Hip-hop", "Jujitsu", "Planche à voile",
    "Gymnastique rythmique", "Kickboxing", "Course à pied", "Squash", "Escalade en salle", "Bowling", "Curling",
    "Cirque", "Danse orientale", "Haltérophilie", "Ultimate Frisbee", "Dodgeball", "Water polo", "Plongée technique",
    "Aviron", "Planche à roulettes électrique", "Pêche à la mouche", "VTT en descente", "Biathlon", "Slacklining",
    "Patinage de vitesse", "Jorkyball", "Canoë-kayak slalom", "Luge", "Rafting",
]


    for hobby in hobbies:
        nouveau_hobby = Hobie(hobie=hobby)
        nouveau_hobby.save()

def enregistrer_styles_vestimentaires():
    styles = [
        "Streetwear", "Bohème", "Classique", "Sportif", "Chic", "Décontracté", "Vintage",
        "Gothique", "Punk", "Hippie", "Minimaliste", "Rétro", "Formel", "Rock", "Urbain",
        "Ethnique", "Preppy", "Arty", "Casual", "Industriel", "Western",
        "Excentrique", "Surfeur", "Biker", "Business", "Glamour", "Élégant", "Grungy",
        "Rave", "SteamPunk", "Boho-chic", "Kawaii", "Rétro-futuriste", "Hipster", "Cyberpunk",
        "Japonais", "Grunge chic",
        "Nudiste", "Drag queen", "Cosplay",
        "Rocker", "Surfer", "Randonneur", "Garçonne", "Bomber", "Uniforme",
        "Skater", "Nautique", "Bardot", "Garçon manqué",  "Cybergoth", 
        "Androgyne", "Bibliothécaire", "Aviateur", "Néo-gothique", "Casual chic",
        "Surfwear", "Néoclassique", "Funky"        
    ]

    for style in styles:
        nouveau_style = Tendance(tendance=style)
        nouveau_style.save()

def langue(): 
    langues = [
    "Français", "Anglais", "Espagnol", "Mandarin (Chinois)", "Hindi", "Bengali", "Arabe", 
    "Portugais", "Russe", "Japonais", "Allemand", "Coréen", "Italien", 
    "Polonais", "Ukrainien", "Grec", "Suédois", "Finnois", "Norvégien", 
    "Danois", "Néerlandais", "Turc", "Tchèque", "Roumain", "Hongrois", 
    "Bulgare", "Serbe", "Croate", "Slovaque", "Slovène", "Lithuanien", 
    "Letton", "Estonien", "Biélorusse", "Moldave", "Albanais", "Macédonien", 
    "Monténégrin", "Bosniaque", "Catalan", "Basque", "Galicien", "Breton", 
    "Écossais", "Irlandais", "Gallois", "Luxembourgeois", "Maltais", "Islandais", 
    "Féroïen", "Géorgien", "Arménien", "Azerbaïdjanais", "Kazakh", "Ouzbek", 
    "Tatar", "Turkmène", "Kirghiz", "Ossète", "Abkhaze", "Tchétchène", 
    "Bachkir", "Tchouvache", "Mokcha", "Oudmourte", "Komi", "Mari", 
    "Erzya", "Moksha", "Oudmurt", "Tchouktche", "Evenki", "Nenets", 
    "Saami", "Finois de Carélie", "Mordve", "Khanty", "Mansis", "Nganasan", 
    "Néguidale", "Koryak", "Itelmène", "Aleoute", "Tlingit", "Haïda", 
    "Nuu-chah-nulth", "Kwak'wala", "Nuxálk", "Coast Salish", "Haida", "Nuuchahnulth", 
    "Kwakwaka'wakw", "Nuu-chah-nulth", "Salish de la Côte", "Haïda", "Ojibwé", "Cree", 
    "Oji-cree", "Innu-aimun", "Naskapi", "Atikamekw", "Wolastoqiyik", "Mi'kmaq", 
    "Beothuk", "Passamaquoddy", "Malécite", "Iñupiaq", "Yup'ik", "Central Alaskan Yup'ik", 
    "Siberian Yupik", "St. Lawrence Island Yupik", "Inuktitut", "Cree de l'Est", "Cree des Plaines", 
    "Cree des Bois", "Cree de l'Ouest", "Innu-aimun", "Naskapi", "Atikamekw", "Wolastoqiyik", 
    "Mi'kmaq", "Beothuk", "Passamaquoddy", "Malécite", "Iñupiaq", "Yup'ik", "Central Alaskan Yup'ik", 
    "Siberian Yupik", "St. Lawrence Island Yupik", "Inuktitut"
]
    for langue in langues:
        langue = Langue(langue=langue)
        langue.save()




def execta():
    langue()
    sortie()
    films()   
    enregistrer_styles_de_musique()  
    enregistrer_caracteristiques_personne()
    enregistrer_hobbies_personne()   
    enregistrer_styles_vestimentaires()

def exect(request):
    execta()
    return HttpResponse("Données créées avec succès !")

