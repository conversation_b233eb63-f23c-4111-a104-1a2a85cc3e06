"""
Middleware pour la connexion automatique par IP
"""

from django.contrib.auth import login, get_user_model
from django.utils.deprecation import MiddlewareMixin
from django.utils import timezone
from .models import TrustedIP, AutoLoginLog
import logging

User = get_user_model()

logger = logging.getLogger(__name__)

class AutoLoginByIPMiddleware(MiddlewareMixin):
    """
    Middleware révolutionnaire pour connexion automatique par IP
    
    Fonctionnement:
    1. Détecte l'IP du visiteur
    2. Vérifie si cette IP est dans les IPs de confiance
    3. Connecte automatiquement l'utilisateur si l'IP est reconnue
    4. Log toutes les tentatives pour audit
    """
    
    def process_request(self, request):
        """Traite chaque requête pour vérifier la connexion automatique"""
        
        # Ne pas traiter si l'utilisateur est déjà connecté
        if request.user.is_authenticated:
            return None
        
        # Récupérer l'IP du client
        client_ip = self.get_client_ip(request)
        if not client_ip:
            return None
        
        # Récupérer le User-Agent
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        try:
            # Chercher une IP de confiance active et non expirée
            trusted_ip = TrustedIP.objects.filter(
                ip_address=client_ip,
                is_active=True
            ).select_related('user').first()
            
            if trusted_ip and not trusted_ip.is_expired():
                # IP de confiance trouvée, connecter automatiquement
                user = trusted_ip.user
                
                # Vérifier que l'utilisateur est toujours actif
                if user.is_active:
                    # Connecter l'utilisateur
                    login(request, user, backend='django.contrib.auth.backends.ModelBackend')
                    
                    # Incrémenter le compteur d'utilisation
                    trusted_ip.increment_usage()
                    
                    # Logger le succès
                    AutoLoginLog.objects.create(
                        user=user,
                        ip_address=client_ip,
                        success=True,
                        reason="Connexion automatique par IP de confiance",
                        user_agent=user_agent
                    )
                    
                    logger.info(f"🚀 Connexion automatique réussie: {user.username} depuis {client_ip}")
                    
                else:
                    # Utilisateur inactif, désactiver l'IP de confiance
                    trusted_ip.is_active = False
                    trusted_ip.save()
                    
                    AutoLoginLog.objects.create(
                        user=user,
                        ip_address=client_ip,
                        success=False,
                        reason="Utilisateur inactif",
                        user_agent=user_agent
                    )
                    
                    logger.warning(f"❌ Connexion automatique échouée: utilisateur {user.username} inactif")
            
            else:
                # IP non reconnue ou expirée
                if trusted_ip and trusted_ip.is_expired():
                    logger.info(f"⏰ IP de confiance expirée pour {trusted_ip.user.username}: {client_ip}")
                
        except Exception as e:
            # Logger l'erreur sans interrompre la requête
            logger.error(f"❌ Erreur dans AutoLoginByIPMiddleware: {e}")
            
            # Logger l'échec
            AutoLoginLog.objects.create(
                user=None,
                ip_address=client_ip,
                success=False,
                reason=f"Erreur système: {str(e)}",
                user_agent=user_agent
            )
        
        return None
    
    def get_client_ip(self, request):
        """Récupère l'IP réelle du client en tenant compte des proxies"""
        
        # Vérifier les headers de proxy
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            # Prendre la première IP (client réel)
            ip = x_forwarded_for.split(',')[0].strip()
            return ip
        
        # Vérifier d'autres headers de proxy
        x_real_ip = request.META.get('HTTP_X_REAL_IP')
        if x_real_ip:
            return x_real_ip.strip()
        
        # IP directe
        remote_addr = request.META.get('REMOTE_ADDR')
        if remote_addr:
            return remote_addr.strip()
        
        return None


class TrustedIPManager:
    """Gestionnaire pour les IPs de confiance"""
    
    @staticmethod
    def add_trusted_ip(user, ip_address, device_name=None, user_agent=None, expires_days=30):
        """
        Ajoute une IP de confiance pour un utilisateur
        
        Args:
            user: Utilisateur Django
            ip_address: Adresse IP à ajouter
            device_name: Nom de l'appareil (optionnel)
            user_agent: User-Agent du navigateur (optionnel)
            expires_days: Nombre de jours avant expiration (None = jamais)
        """
        
        expires_at = None
        if expires_days:
            expires_at = timezone.now() + timezone.timedelta(days=expires_days)
        
        trusted_ip, created = TrustedIP.objects.get_or_create(
            user=user,
            ip_address=ip_address,
            defaults={
                'device_name': device_name,
                'user_agent': user_agent,
                'expires_at': expires_at,
                'is_active': True
            }
        )
        
        if not created:
            # Mettre à jour l'IP existante
            trusted_ip.device_name = device_name or trusted_ip.device_name
            trusted_ip.user_agent = user_agent or trusted_ip.user_agent
            trusted_ip.expires_at = expires_at
            trusted_ip.is_active = True
            trusted_ip.save()
        
        logger.info(f"✅ IP de confiance ajoutée: {user.username} - {ip_address}")
        return trusted_ip
    
    @staticmethod
    def remove_trusted_ip(user, ip_address):
        """Supprime une IP de confiance"""
        try:
            trusted_ip = TrustedIP.objects.get(user=user, ip_address=ip_address)
            trusted_ip.delete()
            logger.info(f"🗑️ IP de confiance supprimée: {user.username} - {ip_address}")
            return True
        except TrustedIP.DoesNotExist:
            logger.warning(f"❌ IP de confiance non trouvée: {user.username} - {ip_address}")
            return False
    
    @staticmethod
    def get_user_trusted_ips(user):
        """Récupère toutes les IPs de confiance d'un utilisateur"""
        return TrustedIP.objects.filter(user=user, is_active=True).order_by('-last_used')
    
    @staticmethod
    def cleanup_expired_ips():
        """Nettoie les IPs expirées"""
        expired_count = TrustedIP.objects.filter(
            expires_at__lt=timezone.now(),
            is_active=True
        ).update(is_active=False)
        
        logger.info(f"🧹 {expired_count} IPs expirées désactivées")
        return expired_count
