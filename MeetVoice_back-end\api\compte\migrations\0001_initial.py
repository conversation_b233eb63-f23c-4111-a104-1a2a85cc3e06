# Generated by Django 5.2.3 on 2025-06-13 06:21

import django.core.validators
import django.db.models.deletion
import django_resized.forms
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Caractere',
            fields=[
                ('auto_increment_id', models.AutoField(primary_key=True, serialize=False)),
                ('caractere', models.CharField(default=None, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='Cgu',
            fields=[
                ('auto_increment_id', models.AutoField(primary_key=True, serialize=False)),
                ('nom', models.CharField(default=None, max_length=30)),
                ('texte', models.CharField(default=None, max_length=1000)),
            ],
        ),
        migrations.CreateModel(
            name='Chartes',
            fields=[
                ('auto_increment_id', models.AutoField(primary_key=True, serialize=False)),
                ('nom', models.CharField(default=None, max_length=30)),
                ('texte', models.CharField(default=None, max_length=1000)),
            ],
        ),
        migrations.CreateModel(
            name='Film',
            fields=[
                ('auto_increment_id', models.AutoField(primary_key=True, serialize=False)),
                ('film', models.CharField(default=None, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='Hobie',
            fields=[
                ('auto_increment_id', models.AutoField(primary_key=True, serialize=False)),
                ('hobie', models.CharField(default=None, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='Langue',
            fields=[
                ('auto_increment_id', models.AutoField(primary_key=True, serialize=False)),
                ('langue', models.CharField(default=None, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='Musique',
            fields=[
                ('auto_increment_id', models.AutoField(primary_key=True, serialize=False)),
                ('musique', models.CharField(default=None, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='Photo',
            fields=[
                ('auto_increment_id', models.AutoField(primary_key=True, serialize=False)),
                ('photos', django_resized.forms.ResizedImageField(blank=True, crop=None, force_format='JPEG', keep_meta=True, null=True, quality=85, scale=None, size=[250, 350], upload_to='photo')),
            ],
        ),
        migrations.CreateModel(
            name='Sortie',
            fields=[
                ('auto_increment_id', models.AutoField(primary_key=True, serialize=False)),
                ('sortie', models.CharField(default=None, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='Tendance',
            fields=[
                ('auto_increment_id', models.AutoField(primary_key=True, serialize=False)),
                ('tendance', models.CharField(default=None, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='Compte',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('username', models.CharField(default='', max_length=250, unique=True)),
                ('nom', models.CharField(default='', max_length=250)),
                ('prenom', models.CharField(default='', max_length=250)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('date_joined', models.DateTimeField(auto_now_add=True)),
                ('last_login', models.DateTimeField(auto_now=True)),
                ('date_de_naissance', models.DateField(blank=True, default=None, null=True)),
                ('numberPhone', models.CharField(blank=True, default=None, max_length=10, null=True, unique=True, validators=[django.core.validators.RegexValidator(regex='^\\+?1?\\d{8,15}$')])),
                ('sexe', models.CharField(blank=True, choices=[('Homme', 'Homme'), ('Femme', 'Femme'), ('Homosexuelle', 'Homosexuelle'), ('Lesbienne', 'Lesbienne'), ('Bisexuelle', 'Bisexuelle'), ('Non Binaire', 'Non Binaire'), ('Transgenre', 'Transgenre'), ('Transsexeulle', 'Transsexeulle')], default=None, max_length=30, null=True)),
                ('taille', models.IntegerField(blank=True, default=None, null=True)),
                ('poids', models.CharField(blank=True, default=None, max_length=3, null=True)),
                ('ethnique', models.CharField(blank=True, choices=[('Caucasien', 'Caucasien'), ('Métisse', 'Métisse'), ('Arabe', 'Arabe'), ('Africaine', 'Africaine'), ('Indienne', 'indienne'), ('Latine', 'Latine'), ('Asiatique', 'Asiatique')], default=None, max_length=30, null=True)),
                ('religion', models.CharField(blank=True, choices=[('Athé', 'Athé'), ('Catholique', 'Cahtolique'), ('Musulman', 'Musulman'), ('Boudihsme', 'Boudihsme'), ('Indouihsme', 'Indouihsme'), ('Juive', 'Juive'), ('Protestante', 'Protestante'), ('Ortodoxe', 'Ortodoxe'), ('Agnotisme', 'Agnotisme')], default=None, max_length=30, null=True)),
                ('yeux', models.CharField(blank=True, choices=[('Noire', 'Noire'), ('Marron', 'Marron'), ('Bleu', 'Bleu'), ('Vert', 'Vert'), ('Noisette', 'Noisette')], default=None, max_length=30, null=True)),
                ('shilhouette', models.CharField(blank=True, choices=[('Normal', 'Normal'), ('Mince', 'Mince'), ('Athlétique', 'Athlétique'), ('Ronde', 'Ronde'), ('Forte', 'forte'), ('Handicapé', 'Handicapé'), ('Chaise Roulante', 'Chaise Roulante')], default=None, max_length=30, null=True)),
                ('description', models.FileField(blank=True, default=None, null=True, upload_to='Description')),
                ('metier', models.CharField(blank=True, choices=[('Santé et Médecine', [('Médecin', 'Médecin'), ('Infirmier', 'Infirmier'), ('Pharmacien', 'Pharmacien'), ('Vétérinaire', 'Vétérinaire'), ('Dentiste', 'Dentiste'), ('Cardiologue', 'Cardiologue'), ('Pédiatre', 'Pédiatre'), ('Néphrologue', 'Néphrologue'), ('Gynécologue-obstétricien', 'Gynécologue-obstétricien'), ('Neurologue', 'Neurologue'), ('Hématologue', 'Hématologue'), ('Anesthésiste-réanimateur', 'Anesthésiste-réanimateur')]), ('Éducation', [('Enseignant', 'Enseignant'), ('Chercheur', 'Chercheur'), ('Professeur de danse', 'Professeur de danse')]), ('Ingénierie et Technologie', [('Ingénieur', 'Ingénieur'), ('Informaticien', 'Informaticien'), ('Développeur Web', 'Développeur Web'), ('Ingénieur du son', 'Ingénieur du son'), ('Ingénieur civil', 'Ingénieur civil'), ('Ingénieur de données', 'Ingénieur de données'), ('Géomètre-topographe', 'Géomètre-topographe'), ('Consultant en cybersécurité', 'Consultant en cybersécurité')]), ('Droit', [('Avocat', 'Avocat'), ('Magistrat', 'Magistrat'), ('Juriste', 'Juriste')]), ('Art et Créativité', [('Artiste', 'Artiste'), ('Acteur', 'Acteur'), ('Designer', 'Designer'), ('Sculpteur', 'Sculpteur'), ('Cinéaste', 'Cinéaste'), ('Directeur artistique', 'Directeur artistique'), ('Art-thérapeute', 'Art-thérapeute')]), ('Restauration et Hôtellerie', [('Cuisinier', 'Cuisinier'), ('Chef pâtissier', 'Chef pâtissier'), ('Chef de rang', 'Chef de rang'), ('Chef de cuisine', 'Chef de cuisine'), ('Barman', 'Barman'), ('Chef de Rang', 'Chef de Rang')]), ('Police et Sécurité', [('Policier', 'Policier'), ('Gendarme', 'Gendarme'), ('Militaire', 'Militaire')]), ('Agriculture et Environnement', [('Agriculteur', 'Agriculteur'), ('Bûcheron', 'Bûcheron'), ('Océanographe marin', 'Océanographe marin'), ('Démographe', 'Démographe')]), ('Sciences et Recherche', [('Chercheur', 'Chercheur'), ('Chercheur en biologie', 'Chercheur en biologie'), ('Géophysicien', 'Géophysicien'), ('Hydrologue', 'Hydrologue'), ('Botaniste', 'Botaniste'), ('Ethnologue', 'Ethnologue'), ('Astrophysicien', 'Astrophysicien'), ('Paléontologue', 'Paléontologue'), ('Sismologue', 'Sismologue'), ('Gériatre', 'Gériatre'), ('Embryologiste', 'Embryologiste'), ('Mycologue', 'Mycologue'), ('Paléontologue', 'Paléontologue'), ('Géographe', 'Géographe')]), ('Bâtiment et Artisanat', [('Artisan', 'Artisan'), ('Plombier', 'Plombier'), ('Mécanicien', 'Mécanicien'), ('Menuisier', 'Menuisier'), ('Électricien', 'Électricien'), ('Architecte naval', 'Architecte naval'), ('Serrurier', 'Serrurier'), ('Forgeron', 'Forgeron'), ("Ferronnier d'art", "Ferronnier d'art"), ('Luthier', 'Luthier'), ('Maréchal-ferrant', 'Maréchal-ferrant'), ('Prothésiste ongulaire', 'Prothésiste ongulaire')]), ('Économie et Gestion', [('Entrepreneur', 'Entrepreneur'), ('Banquier', 'Banquier'), ('Responsable qualité', 'Responsable qualité'), ('Responsable des achats', 'Responsable des achats'), ('Négociateur immobilier', 'Négociateur immobilier'), ('Économiste', 'Économiste')]), ("Technologies de l'Information et de la Communication", [('Développeur Web', 'Développeur Web'), ('Concepteur de jeux vidéo', 'Concepteur de jeux vidéo'), ('Ingénieur du son', 'Ingénieur du son')]), ('Communication et Marketing', [('Journaliste', 'Journaliste'), ('Chargé de communication', 'Chargé de communication')]), ('Immobilier', [('Agent immobilier', 'Agent immobilier')]), ('Traduction et Interprétation', [('Traducteur', 'Traducteur'), ('Traducteur juridique', 'Traducteur juridique'), ('Traducteur-interprète', 'Traducteur-interprète')]), ('Criminologie', [('Criminologue', 'Criminologue')]), ('Urbanisme et Aménagement du Territoire', [('Urbaniste', 'Urbaniste'), ('Urbaniste paysagiste', 'Urbaniste paysagiste')]), ('Restauration et Gastronomie', [('Serveur', 'Serveur'), ('Boulanger', 'Boulanger'), ('Chef pâtissier', 'Chef pâtissier')]), ('Informatique et Sécurité', [('Cryptologue', 'Cryptologue')]), ('Musique et Création sonore', [('Orchestrateur', 'Orchestrateur')]), ('Transport et Aéronautique', [('Chauffeur', 'Chauffeur'), ("Pilote d'avion", "Pilote d'avion"), ('Skipper', 'Skipper')]), ('Design et Création graphique', [('Designer', 'Designer'), ('Designer industriel', 'Designer industriel'), ('Graphiste', 'Graphiste')]), ('Mode et Couture', [('Couturier', 'Couturier'), ('Modéliste', 'Modéliste')]), ('Psychologie et Thérapie', [('Psychologue', 'Psychologue'), ('Psychothérapeute', 'Psychothérapeute')]), ('Enseignement spécialisé et Réadaptation', [('Ergothérapeute', 'Ergothérapeute'), ('Physiothérapeute', 'Physiothérapeute'), ('Orthophoniste', 'Orthophoniste'), ('Orthoptiste', 'Orthoptiste')]), ('Finance', [('Banquier', 'Banquier'), ('Responsable qualité', 'Responsable qualité')]), ('Environnement', [('Hydraulicien', 'Hydraulicien'), ('Botaniste marin', 'Botaniste marin'), ('Toxicologue alimentaire', 'Toxicologue alimentaire'), ('Géomètre', 'Géomètre'), ('Horloger', 'Horloger'), ('Numismate', 'Numismate')]), ('Zoologie', [('Zoologiste', 'Zoologiste')])], default=None, max_length=150, null=True)),
                ('en_couple', models.BooleanField(default=False)),
                ('recherche', models.CharField(blank=True, choices=[('Amical', 'Amical'), ('Amour', 'Amour'), ('Libertin', 'Libertin')], default=None, max_length=30, null=True)),
                ('credit', models.IntegerField()),
                ('abonnement', models.CharField(blank=True, choices=[('Amical', 'Amical'), ('Amour', 'Amour'), ('Libertin', 'Libertin')], default=None, max_length=30, null=True)),
                ('chartre', models.BooleanField(default=True)),
                ('Cgu', models.BooleanField(default=True)),
                ('cookie', models.BooleanField(default=True)),
                ('is_member', models.BooleanField(default=False)),
                ('is_staff', models.BooleanField(default=False)),
                ('is_admin', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=False)),
                ('amis', models.ManyToManyField(blank=True, to='compte.compte')),
                ('caratere', models.ManyToManyField(default=None, related_name='cara', to='compte.caractere')),
                ('en_couple_avec', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='partenaire', to='compte.compte')),
                ('style_de_film', models.ManyToManyField(default=None, related_name='style_de_films', to='compte.film')),
                ('hobie', models.ManyToManyField(default=None, related_name='hobies', to='compte.hobie')),
                ('style_de_musique', models.ManyToManyField(default=None, related_name='style_de_musique', to='compte.musique')),
                ('photo', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='photo', to='compte.photo')),
                ('preference_de_sortie', models.ManyToManyField(default=None, related_name='preference_de_sortie', to='compte.sortie')),
                ('tendance', models.ManyToManyField(default=None, related_name='tendances', to='compte.tendance')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
