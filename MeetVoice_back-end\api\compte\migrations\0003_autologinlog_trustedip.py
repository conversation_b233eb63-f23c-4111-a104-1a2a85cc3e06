# Generated by Django 5.2.3 on 2025-06-23 06:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('compte', '0002_compte_audio_alter_compte_credit_alter_compte_sexe'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AutoLoginLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField()),
                ('success', models.BooleanField(default=True)),
                ('reason', models.CharField(blank=True, max_length=200, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Log Connexion Auto',
                'verbose_name_plural': 'Logs Connexions Auto',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='TrustedIP',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField(verbose_name='Adresse IP')),
                ('device_name', models.CharField(blank=True, max_length=200, null=True, verbose_name="Nom de l'appareil")),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='User Agent')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name="Date d'ajout")),
                ('last_used', models.DateTimeField(auto_now=True, verbose_name='Dernière utilisation')),
                ('is_active', models.BooleanField(default=True, verbose_name='Actif')),
                ('login_count', models.IntegerField(default=0, verbose_name='Nombre de connexions')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name="Date d'expiration")),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trusted_ips', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'IP de Confiance',
                'verbose_name_plural': 'IPs de Confiance',
                'ordering': ['-last_used'],
                'unique_together': {('user', 'ip_address')},
            },
        ),
    ]
