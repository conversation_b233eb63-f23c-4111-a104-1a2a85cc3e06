from django.db import models
from django.core.validators import RegexValidator
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager
from django_resized import ResizedImageField
from django.utils import timezone
import uuid


situation = [
    ('<PERSON><PERSON><PERSON><PERSON><PERSON>','Célibataire'),
    ('En couple','En couple'),
    ('Couple libre','Couple libre'),
    ('Je sais pas','Je sais pas'),
    ('<PERSON>vor<PERSON>','Divor<PERSON>'),
    ('Veuf(ve)','Veuf(ve)')
]

sexe = [
    ('Homme','Homme'),
    ('Femme','Femme'),
    ('Homosexuelle','Homosexuelle'),
    ('<PERSON><PERSON><PERSON>','<PERSON>bie<PERSON>'),
    ('Bisexuelle','Bisexuelle'),
    ('Non Binaire','Non Binaire'),
    ('Transgenre','Transgenre'),
]

metiers_par_categorie = [
    {"cat": "<PERSON>é et Médecine", "metier": [
        "Médecin", "Infirmier", "Pharmacien", "Vétérinaire", "Dentist<PERSON>",
        "Cardiologue", "Pédiatre", "Néphrologue", "Gynécologue-obstétricien", "Neurologue",
        "Hématologue", "Anesthésiste-réanimateur"
    ]},
    {"cat": "Éducation", "metier": ["Enseignant", "Chercheur", "Professeur de danse"]},
    {"cat": "Ingénierie et Technologie", "metier": [
        "Ingénieur", "Informaticien", "Développeur Web", "Ingénieur du son",
        "Ingénieur civil", "Ingénieur de données", "Géomètre-topographe", "Consultant en cybersécurité"
    ]},
    {"cat": "Droit", "metier": ["Avocat", "Magistrat", "Juriste"]},
    {"cat": "Art et Créativité", "metier": [
        "Artiste", "Acteur", "Designer", "Sculpteur", "Cinéaste", "Directeur artistique", "Art-thérapeute"
    ]},
    {"cat": "Restauration et Hôtellerie", "metier": [
        "Cuisinier", "Chef pâtissier", "Chef de rang", "Chef de cuisine", "Barman", "Chef de Rang"
    ]},
    {"cat": "Police et Sécurité", "metier": ["Policier", "Gendarme", "Militaire"]},
    {"cat": "Agriculture et Environnement", "metier": [
        "Agriculteur", "Bûcheron", "Océanographe marin", "Démographe"
    ]},
    {"cat": "Sciences et Recherche", "metier": [
        "Chercheur", "Chercheur en biologie", "Géophysicien", "Hydrologue", "Botaniste",
        "Ethnologue", "Astrophysicien", "Paléontologue", "Sismologue", "Gériatre", "Embryologiste",
        "Mycologue", "Paléontologue", "Géographe"
    ]},
    {"cat": "Bâtiment et Artisanat", "metier": [
        "Artisan", "Plombier", "Mécanicien", "Menuisier", "Électricien", "Architecte naval", "Serrurier",
        "Forgeron", "Ferronnier d'art", "Luthier", "Maréchal-ferrant", "Prothésiste ongulaire"
    ]},
    {"cat": "Économie et Gestion", "metier": [
        "Entrepreneur", "Banquier", "Responsable qualité", "Responsable des achats",
        "Négociateur immobilier", "Économiste"
    ]},
    {"cat": "Technologies de l'Information et de la Communication", "metier": [
        "Développeur Web", "Concepteur de jeux vidéo", "Ingénieur du son"
    ]},
    {"cat": "Communication et Marketing", "metier": ["Journaliste", "Chargé de communication"]},
    {"cat": "Immobilier", "metier": ["Agent immobilier"]},
    {"cat": "Traduction et Interprétation", "metier": ["Traducteur", "Traducteur juridique", "Traducteur-interprète"]},
    {"cat": "Criminologie", "metier": ["Criminologue"]},
    {"cat": "Urbanisme et Aménagement du Territoire", "metier": ["Urbaniste", "Urbaniste paysagiste"]},
    {"cat": "Restauration et Gastronomie", "metier": ["Serveur", "Boulanger", "Chef pâtissier"]},
    {"cat": "Informatique et Sécurité", "metier": ["Cryptologue"]},
    {"cat": "Musique et Création sonore", "metier": ["Orchestrateur"]},
    {"cat": "Transport et Aéronautique", "metier": ["Chauffeur", "Pilote d'avion", "Skipper"]},
    {"cat": "Design et Création graphique", "metier": ["Designer", "Designer industriel", "Graphiste"]},
    {"cat": "Mode et Couture", "metier": ["Couturier", "Modéliste"]},
    {"cat": "Psychologie et Thérapie", "metier": ["Psychologue", "Psychothérapeute"]},
    {"cat": "Enseignement spécialisé et Réadaptation", "metier": [
        "Ergothérapeute", "Physiothérapeute", "Orthophoniste", "Orthoptiste"
    ]},
    {"cat": "Finance", "metier": ["Banquier", "Responsable qualité"]},
    {"cat": "Environnement", "metier": [
        "Hydraulicien", "Botaniste marin", "Toxicologue alimentaire", "Géomètre", "Horloger", "Numismate"
    ]},
    {"cat": "Zoologie", "metier": ["Zoologiste"]}
]
choices_metier = [(cat['cat'], [(m, m) for m in cat['metier']]) for cat in metiers_par_categorie]

ethnique = [
    ('Caucasien','Caucasien'),
    ('Métisse','Métisse'),
    ('Arabe','Arabe'),
    ('Africaine','Africaine'),
    ('Indienne','indienne'),
    ('Latine','Latine'),
    ('Asiatique','Asiatique'),    
]

yeux = [
    ('Noire','Noire'),
    ('Marron','Marron'),
    ('Bleu','Bleu'),
    ('Vert','Vert'),
    ('Noisette','Noisette'),    
]

shilhouette = [
    ('Normal','Normal'),
    ('Mince','Mince'),
    ('Athlétique','Athlétique'),
    ('Ronde','Ronde'),
    ('Forte','forte'),
    ('Handicapé','Handicapé'),
    ('Chaise Roulante','Chaise Roulante')
]

rencontre = [
    ('Amical', 'Amical'),
    ('Amour','Amour'),
    ('Libertin','Libertin')
]

religion = [
    ('Athé','Athé'),
    ('Catholique','Cahtolique'),
    ('Musulman','Musulman'),
    ('Boudihsme','Boudihsme'),
    ('Indouihsme','Indouihsme'),
    ('Juive','Juive'),
    ('Protestante','Protestante'),
    ('Ortodoxe','Ortodoxe'),
    ('Agnotisme','Agnotisme'),
]

class Sortie(models.Model):
    auto_increment_id = models.AutoField(primary_key=True)
    sortie = models.CharField(max_length=50, default=None)
    def __str__(self):
        return self.sortie

class Film(models.Model):
    auto_increment_id = models.AutoField(primary_key=True)
    film = models.CharField(max_length=50, default=None)
    def __str__(self):
        return self.film
 
class Musique(models.Model):
    auto_increment_id = models.AutoField(primary_key=True)
    musique = models.CharField(max_length=50, default=None)
    def __str__(self):
        return self.musique
   
class Caractere(models.Model):
    auto_increment_id = models.AutoField(primary_key=True)
    caractere = models.CharField(max_length=50, default=None)
    def __str__(self):
        return self.caractere

class Hobie(models.Model):
    auto_increment_id = models.AutoField(primary_key=True)
    hobie = models.CharField(max_length=50, default=None)
    def __str__(self):
        return self.hobie

class Tendance(models.Model):
    auto_increment_id = models.AutoField(primary_key=True)
    tendance = models.CharField(max_length=50, default=None)
    def __str__(self):
        return self.tendance
 
class Photo(models.Model):
    auto_increment_id = models.AutoField(primary_key=True)
    photos = ResizedImageField(size=[250, 350], quality=85, upload_to='photo', null=True, blank=True)
 

class Langue(models.Model):
    auto_increment_id = models.AutoField(primary_key=True)
    langue = models.CharField(max_length=50, default=None)
    def __str__(self):
        return self.langue
 

class CompteManager(BaseUserManager):
    def _create_user(self, email=None, password=None, **extra_fields):
        if not email:
            raise ValueError(f"vous n'avez pas d'adresse e-mail valide")
        email = self.normalize_email(email) 
        user = self.model(email=email, password=password, **extra_fields)
        user.set_password(password)
        user.is_active = True   
        user.save(using=self._db)
        return user
    
    def create_user(self, email=None, password=None, **extra_fields):
        email = self.normalize_email(email) 
        user = self.model(email=email, password=password, **extra_fields)
        user.set_password(password)
        user.is_active = True   
        user.save(using=self._db)
        return user
    
            
    def create_superuser(self, email=None, password=None,  **extra_fields):
        email = self.normalize_email(email)
        user = self.create_user(email=email, password=password, **extra_fields)
        user.is_admin = True
        user.is_staff = True 
        user.is_active = True    
        user.save(using=self._db)
        return user

class Compte(AbstractBaseUser):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True)
    username = models.CharField(unique=True, max_length=250, default='', null=True, blank=True)
    nom = models.CharField(max_length=250, default='', null=True, blank=True)
    prenom = models.CharField(max_length=250, default='', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    date_joined = models.DateTimeField(auto_now_add=True)
    last_login = models.DateTimeField(auto_now=True)
    date_de_naissance = models.DateField(auto_now=False, null=True, blank=True, default=None)
    phone = RegexValidator(regex = r"^\+?1?\d{8,15}$")
    numberPhone = models.CharField(validators = [phone], max_length = 10, default=None, unique=True, null=True, blank=True) 
    sexe = models.CharField(max_length=30,choices=sexe, default=None, null=True, blank=True)
    taille = models.IntegerField(null=True, blank=True, default=None)
    poids = models.CharField(max_length=3, null=True, blank=True, default=None)
    ethnique = models.CharField(max_length=30,choices=ethnique, default=None, null=True, blank=True)
    religion = models.CharField(max_length=30,choices=religion, default=None, null=True, blank=True)
    yeux = models.CharField(max_length=30, choices=yeux, default=None, null=True, blank=True)   
    shilhouette = models.CharField(max_length=30, choices=shilhouette, default=None, null=True, blank=True) 
    description = models.FileField(upload_to='Description',null=True, blank=True, default=None) 
    preference_de_sortie = models.ManyToManyField(Sortie, related_name="preference_de_sortie", default=None)
    style_de_film = models.ManyToManyField(Film, related_name="style_de_films", default=None)
    style_de_musique = models.ManyToManyField(Musique, related_name="style_de_musique", default=None)
    caratere = models.ManyToManyField(Caractere, related_name="cara", default=None)
    metier = models.CharField(max_length=150,choices=choices_metier, default=None, null=True, blank=True)
    photo = models.ForeignKey(Photo,on_delete=models.CASCADE,related_name="photo", blank=True, null=True)
    amis = models.ManyToManyField('self', symmetrical=True, blank=True)
    tendance = models.ManyToManyField(Tendance, related_name='tendances', default=None)    
    hobie = models.ManyToManyField(Hobie, related_name='hobies', default=None)
    en_couple = models.BooleanField(default=False)
    en_couple_avec = models.OneToOneField('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='partenaire')
    recherche = models.CharField(max_length=30,choices=rencontre, default=None, null=True, blank=True)    
    credit = models.IntegerField(default=0)
    abonnement = models.CharField(max_length=30,choices=rencontre, default=None, null=True, blank=True) 
    chartre = models.BooleanField(default=True)
    Cgu = models.BooleanField(default=True)
    cookie = models.BooleanField(default=True)
    
    audio = models.FileField(upload_to='audios/', blank=True, null=True)
 
    is_member = models.BooleanField(default=False)
    is_staff =  models.BooleanField(default=False)
    is_admin = models.BooleanField(default=False)
    is_active = models.BooleanField(default=False)

    USERNAME_FIELD = "email"
    EMAIL_FIELD = 'email'
   
    objects = CompteManager()

    def __str__(self):       
        return self.nom + " "+ self.prenom +" " + self.email 

    def has_perm(self, perm, obj=None):
        return True

    def has_module_perms(self, app_label):
        return True       
    




# ============================================================================
# MODÈLES POUR CONNEXION AUTOMATIQUE PAR IP
# ============================================================================

class TrustedIP(models.Model):
    """Modèle pour stocker les IPs de confiance pour connexion automatique"""

    user = models.ForeignKey('Compte', on_delete=models.CASCADE, related_name='trusted_ips')
    ip_address = models.GenericIPAddressField(verbose_name="Adresse IP")
    device_name = models.CharField(max_length=200, blank=True, null=True, verbose_name="Nom de l'appareil")
    user_agent = models.TextField(blank=True, null=True, verbose_name="User Agent")

    # Métadonnées
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Date d'ajout")
    last_used = models.DateTimeField(auto_now=True, verbose_name="Dernière utilisation")
    is_active = models.BooleanField(default=True, verbose_name="Actif")

    # Sécurité
    login_count = models.IntegerField(default=0, verbose_name="Nombre de connexions")
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name="Date d'expiration")

    class Meta:
        verbose_name = "IP de Confiance"
        verbose_name_plural = "IPs de Confiance"
        unique_together = ['user', 'ip_address']
        ordering = ['-last_used']

    def __str__(self):
        return f"{self.user.username} - {self.ip_address}"

    def is_expired(self):
        """Vérifie si l'IP de confiance a expiré"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    def increment_usage(self):
        """Incrémente le compteur d'utilisation"""
        self.login_count += 1
        self.last_used = timezone.now()
        self.save()


class AutoLoginLog(models.Model):
    """Log des connexions automatiques pour audit"""

    user = models.ForeignKey('Compte', on_delete=models.CASCADE, null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    success = models.BooleanField(default=True)
    reason = models.CharField(max_length=200, blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Log Connexion Auto"
        verbose_name_plural = "Logs Connexions Auto"
        ordering = ['-timestamp']

    def __str__(self):
        status = "✅" if self.success else "❌"
        return f"{status} {self.user.username} - {self.ip_address} - {self.timestamp}"