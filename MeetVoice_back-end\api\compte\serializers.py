from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import <PERSON><PERSON>e, <PERSON><PERSON>ie, Film, Musique, Caractere, Hobie, Tendance, Photo, Langue

class SortieSerializer(serializers.ModelSerializer):
    class Meta:
        model = Sortie
        fields = '__all__'

class FilmSerializer(serializers.ModelSerializer):
    class Meta:
        model = Film
        fields = '__all__'

class MusiqueSerializer(serializers.ModelSerializer):
    class Meta:
        model = Musique
        fields = '__all__'

class CaractereSerializer(serializers.ModelSerializer):
    class Meta:
        model = Caractere
        fields = '__all__'

class HobieSerializer(serializers.ModelSerializer):
    class Meta:
        model = Hobie
        fields = '__all__'

class TendanceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tendance
        fields = '__all__'

class PhotoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Photo
        fields = '__all__'

class LangueSerializer(serializers.ModelSerializer):
    class Meta:
        model = Langue
        fields = '__all__'

class CompteSerializer(serializers.ModelSerializer):
    preference_de_sortie = SortieSerializer(many=True)
    style_de_films = FilmSerializer(many=True)
    style_de_musique = MusiqueSerializer(many=True)
    caratere = CaractereSerializer(many=True)
    hobie = HobieSerializer(many=True)
    tendance = TendanceSerializer(many=True)
    langue = LangueSerializer()
    photo = PhotoSerializer()

    class Meta:
        model = Compte
        fields = '__all__'


User = get_user_model()


class CompteRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer pour l'inscription - Seuls email et password sont obligatoires
    """
    password = serializers.CharField(write_only=True, min_length=6)
    password_confirm = serializers.CharField(write_only=True)

    class Meta:
        model = Compte
        fields = [
            'email',
            'password',
            'password_confirm',
            'username',
            'nom',
            'prenom'
        ]
        extra_kwargs = {
            'email': {'required': True},
            'password': {'write_only': True},
            'username': {'required': False},
            'nom': {'required': False},
            'prenom': {'required': False}
        }

    def validate_email(self, value):
        """Validation de l'email"""
        if Compte.objects.filter(email=value).exists():
            raise serializers.ValidationError("Un compte avec cet email existe déjà.")
        return value.lower()

    def validate_username(self, value):
        """Validation du username (optionnel)"""
        if value and Compte.objects.filter(username=value).exists():
            raise serializers.ValidationError("Ce nom d'utilisateur est déjà pris.")
        return value

    def validate(self, attrs):
        """Validation des mots de passe"""
        password = attrs.get('password')
        password_confirm = attrs.get('password_confirm')

        if password != password_confirm:
            raise serializers.ValidationError("Les mots de passe ne correspondent pas.")

        # Supprimer password_confirm des données validées
        attrs.pop('password_confirm', None)
        return attrs

    def create(self, validated_data):
        """Créer un nouveau compte"""
        password = validated_data.pop('password')
        email = validated_data.pop('email')

        # Générer un username automatique si pas fourni
        if not validated_data.get('username'):
            email_prefix = email.split('@')[0]
            base_username = email_prefix
            counter = 1

            while Compte.objects.filter(username=base_username).exists():
                base_username = f"{email_prefix}{counter}"
                counter += 1

            validated_data['username'] = base_username

        # Créer le compte
        compte = Compte.objects.create_user(
            email=email,
            password=password,
            **validated_data
        )

        return compte

