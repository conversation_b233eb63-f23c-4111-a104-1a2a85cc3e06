{% extends "base.html" %}
{% load static %}

{% block title %}Mon Profil{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg border-0">
                <div class="card-header text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-user me-2"></i>Mon Profil
                    </h4>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <div class="profile-avatar mb-3">
                                <i class="fas fa-user-circle fa-5x text-muted"></i>
                            </div>
                            <h5>{{ user.prenom }} {{ user.nom }}</h5>
                            <p class="text-muted">{{ user.email }}</p>
                        </div>
                        <div class="col-md-8">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>Informations du Profil
                            </h6>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>Nom d'utilisateur :</strong></div>
                                <div class="col-sm-8">{{ user.username }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>Email :</strong></div>
                                <div class="col-sm-8">{{ user.email }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>Nom :</strong></div>
                                <div class="col-sm-8">{{ user.nom|default:"Non renseigné" }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>Prénom :</strong></div>
                                <div class="col-sm-8">{{ user.prenom|default:"Non renseigné" }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>Membre depuis :</strong></div>
                                <div class="col-sm-8">{{ user.date_joined|date:"d/m/Y" }}</div>
                            </div>
                            {% if user.is_staff %}
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>Statut :</strong></div>
                                <div class="col-sm-8">
                                    <span class="badge bg-success">
                                        <i class="fas fa-crown me-1"></i>Administrateur
                                    </span>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <hr class="my-4">

                    <div class="text-center">
                        <button class="btn btn-primary me-2">
                            <i class="fas fa-edit me-2"></i>Modifier le Profil
                        </button>
                        <button class="btn btn-outline-secondary">
                            <i class="fas fa-key me-2"></i>Changer le Mot de Passe
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-radius: 1rem 1rem 0 0 !important;
    }

    .card {
        border-radius: 1rem;
    }

    .profile-avatar {
        margin-bottom: 1rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }
</style>
{% endblock %}
