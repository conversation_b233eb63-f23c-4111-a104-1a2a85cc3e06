from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from django.urls import path
from . import views
from .views import register_api
from compte.facker import exect
from django.urls import include

app_name = 'compte'



router = DefaultRouter()
router.register(r'compte', views.CompteViewSet)
router.register(r'photo', views.PhotoViewSet)
router.register(r'film', views.FilmViewSet)
router.register(r'musique', views.MusiqueViewSet)
router.register(r'caractere', views.CaractereViewSet)
router.register(r'hobie', views.HobieViewSet)
router.register(r'tendance', views.TendanceViewSet)
router.register(r'langue', views.LangueViewSet)


urlpatterns = [
    path('login/', TokenObtainPairView.as_view(), name='token_obtain'),
    path('refresh/', TokenRefreshView.as_view(),name='token_refresh'),
    path('signup/',include ('django.contrib.auth.urls')),
    path('test/',exect),

    # === ENDPOINTS DE VALIDATION ===
    path('check-email/', views.check_email_exists, name='check-email'),
    path('check-phone/', views.check_phone_exists, name='check-phone'),

    # === ENDPOINT D'INSCRIPTION PUBLIQUE ===
    path('register/', register_api, name='register'),
]

