from django.shortcuts import render, redirect
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.http import JsonResponse
from .models import Compte
from django.contrib.auth import logout, login as auth_login
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from rest_framework import viewsets
from .models import Compte, Sortie, Film, Musique, Caractere, Hobie, Tendance, Photo, Langue, TrustedIP, AutoLoginLog
from .serializers import CompteSerializer, SortieSerializer, FilmSerializer, MusiqueSerializer, CaractereSerializer, HobieSerializer, TendanceSerializer, PhotoSerializer, LangueSerializer, CompteRegistrationSerializer
from .middleware import TrustedIPManager
import json

class CompteViewSet(viewsets.ModelViewSet):
    queryset = Compte.objects.all()
    serializer_class = CompteSerializer 
      
    
class SortieViewSet(viewsets.ModelViewSet):
    queryset = Sortie.objects.all()
    serializer_class = SortieSerializer

class FilmViewSet(viewsets.ModelViewSet):
    queryset = Film.objects.all()
    serializer_class = FilmSerializer

class MusiqueViewSet(viewsets.ModelViewSet):
    queryset = Musique.objects.all()
    serializer_class = MusiqueSerializer

class CaractereViewSet(viewsets.ModelViewSet):
    queryset = Caractere.objects.all()
    serializer_class = CaractereSerializer

class HobieViewSet(viewsets.ModelViewSet):
    queryset = Hobie.objects.all()
    serializer_class = HobieSerializer

class TendanceViewSet(viewsets.ModelViewSet):
    queryset = Tendance.objects.all()
    serializer_class = TendanceSerializer

class PhotoViewSet(viewsets.ModelViewSet):
    queryset = Photo.objects.all()
    serializer_class = PhotoSerializer

class LangueViewSet(viewsets.ModelViewSet):
    queryset = Langue.objects.all()
    serializer_class = LangueSerializer



def login(request):
    return render(request, 'login.html')

def logout_user(request):
    logout(request)
    return redirect('index')


# ============================================================================
# VUES POUR CONNEXION AUTOMATIQUE PAR IP
# ============================================================================

def get_client_ip(request):
    """Récupère l'IP réelle du client"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
        return ip

    x_real_ip = request.META.get('HTTP_X_REAL_IP')
    if x_real_ip:
        return x_real_ip.strip()

    remote_addr = request.META.get('REMOTE_ADDR')
    if remote_addr:
        return remote_addr.strip()

    return None


@login_required
def trusted_ips_view(request):
    """Vue pour gérer les IPs de confiance de l'utilisateur"""

    # Récupérer les IPs de confiance de l'utilisateur
    trusted_ips = TrustedIPManager.get_user_trusted_ips(request.user)

    # Récupérer l'IP actuelle
    current_ip = get_client_ip(request)

    # Vérifier si l'IP actuelle est déjà de confiance
    is_current_ip_trusted = trusted_ips.filter(ip_address=current_ip).exists()

    context = {
        'trusted_ips': trusted_ips,
        'current_ip': current_ip,
        'is_current_ip_trusted': is_current_ip_trusted,
        'login_logs': AutoLoginLog.objects.filter(user=request.user).order_by('-timestamp')[:10]
    }

    return render(request, 'compte/trusted_ips.html', context)


@login_required
@require_POST
@csrf_exempt
def add_current_ip_to_trusted(request):
    """Ajoute l'IP actuelle aux IPs de confiance"""

    try:
        data = json.loads(request.body)
        device_name = data.get('device_name', 'Appareil inconnu')
        expires_days = data.get('expires_days', 30)

        current_ip = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')

        if not current_ip:
            return JsonResponse({'success': False, 'error': 'Impossible de déterminer votre IP'})

        # Ajouter l'IP de confiance
        trusted_ip = TrustedIPManager.add_trusted_ip(
            user=request.user,
            ip_address=current_ip,
            device_name=device_name,
            user_agent=user_agent,
            expires_days=expires_days
        )

        return JsonResponse({
            'success': True,
            'message': f'IP {current_ip} ajoutée aux IPs de confiance',
            'ip_address': current_ip,
            'device_name': device_name
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_POST
@csrf_exempt
def remove_trusted_ip(request):
    """Supprime une IP de confiance"""

    try:
        data = json.loads(request.body)
        ip_address = data.get('ip_address')

        if not ip_address:
            return JsonResponse({'success': False, 'error': 'IP address required'})

        success = TrustedIPManager.remove_trusted_ip(request.user, ip_address)

        if success:
            return JsonResponse({
                'success': True,
                'message': f'IP {ip_address} supprimée des IPs de confiance'
            })
        else:
            return JsonResponse({
                'success': False,
                'error': 'IP de confiance non trouvée'
            })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


def auto_login_demo(request):
    """Vue de démonstration du système de connexion automatique"""

    current_ip = get_client_ip(request)

    # Statistiques pour la démo
    stats = {
        'total_trusted_ips': TrustedIP.objects.filter(is_active=True).count(),
        'total_auto_logins': AutoLoginLog.objects.filter(success=True).count(),
        'recent_logins': AutoLoginLog.objects.filter(success=True).order_by('-timestamp')[:5]
    }

    context = {
        'current_ip': current_ip,
        'is_authenticated': request.user.is_authenticated,
        'user': request.user if request.user.is_authenticated else None,
        'stats': stats
    }

    return render(request, 'compte/auto_login_demo.html', context)


# === ENDPOINTS DE VALIDATION ===

@api_view(['GET'])
@permission_classes([AllowAny])
def check_email_exists(request):
    """
    Vérifie si un email existe déjà dans la base de données

    Usage: GET /compte/check-email/?email=<EMAIL>

    Returns:
        - 200: {"exists": true/false, "message": "..."}
        - 400: {"error": "Email parameter required"}
    """
    email = request.GET.get('email')

    if not email:
        return Response({
            'error': 'Email parameter required',
            'usage': 'GET /compte/check-email/?email=<EMAIL>'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Vérifier si l'email existe
    exists = Compte.objects.filter(email__iexact=email).exists()

    return Response({
        'exists': exists,
        'email': email,
        'message': 'Email already registered' if exists else 'Email available'
    }, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([AllowAny])
def check_phone_exists(request):
    """
    Vérifie si un numéro de téléphone existe déjà dans la base de données

    Usage: GET /compte/check-phone/?phone=0123456789

    Returns:
        - 200: {"exists": true/false, "message": "..."}
        - 400: {"error": "Phone parameter required"}
    """
    phone = request.GET.get('phone')

    if not phone:
        return Response({
            'error': 'Phone parameter required',
            'usage': 'GET /compte/check-phone/?phone=0123456789'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Vérifier si le téléphone existe
    exists = Compte.objects.filter(numberPhone__iexact=phone).exists()

    return Response({
        'exists': exists,
        'phone': phone,
        'message': 'Phone number already registered' if exists else 'Phone number available'
    }, status=status.HTTP_200_OK)


@csrf_exempt
@require_POST
def register_api(request):
    """
    API d'inscription publique - Seuls email et password sont obligatoires
    ACCÈS PUBLIC SANS TOKEN

    POST Body:
        {
            "email": "<EMAIL>",
            "password": "motdepasse123",
            "password_confirm": "motdepasse123",
            "username": "pseudo_optionnel",  // Optionnel
            "nom": "Nom optionnel",          // Optionnel
            "prenom": "Prénom optionnel"     // Optionnel
        }

    Returns:
        201: {
            "success": true,
            "message": "Compte créé avec succès",
            "user": {
                "id": "uuid",
                "email": "<EMAIL>",
                "username": "pseudo_généré"
            }
        }
    """
    try:
        # Parser le JSON
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'JSON invalide',
                'usage': 'POST avec {"email": "...", "password": "...", "password_confirm": "..."}'
            }, status=400)

        serializer = CompteRegistrationSerializer(data=data)

        if serializer.is_valid():
            # Créer le compte
            compte = serializer.save()

            return JsonResponse({
                'success': True,
                'message': 'Compte créé avec succès',
                'user': {
                    'id': str(compte.id),
                    'email': compte.email,
                    'username': compte.username,
                    'nom': compte.nom or '',
                    'prenom': compte.prenom or ''
                },
                'access': 'PUBLIC - No token required'
            }, status=201)

        else:
            return JsonResponse({
                'success': False,
                'errors': serializer.errors,
                'message': 'Données invalides'
            }, status=400)

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Erreur interne: {str(e)}'
        }, status=500)