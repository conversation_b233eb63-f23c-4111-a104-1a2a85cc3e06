from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import Contact


@admin.register(Contact)
class ContactAdmin(admin.ModelAdmin):
    """Administration des messages de contact"""

    list_display = [
        'nom', 'email', 'objet', 'statut_badge', 'priorite_badge',
        'date_creation', 'traite_par', 'actions_rapides'
    ]

    list_filter = [
        'statut', 'priorite', 'date_creation', 'date_traitement'
    ]

    search_fields = [
        'nom', 'email', 'objet', 'contexte', 'traite_par'
    ]

    readonly_fields = [
        'date_creation', 'date_modification', 'ip_address', 'user_agent'
    ]

    fieldsets = (
        ('Informations de contact', {
            'fields': ('nom', 'email', 'telephone')
        }),
        ('Message', {
            'fields': ('objet', 'contexte')
        }),
        ('Traitement', {
            'fields': ('statut', 'priorite', 'reponse_admin', 'traite_par', 'date_traitement')
        }),
        ('Métadonnées', {
            'fields': ('date_creation', 'date_modification', 'ip_address', 'user_agent'),
            'classes': ('collapse',)
        })
    )

    ordering = ['-date_creation']

    actions = ['marquer_en_cours', 'marquer_resolu', 'marquer_urgent']

    def statut_badge(self, obj):
        """Badge coloré pour le statut"""
        colors = {
            'nouveau': '#dc3545',      # Rouge
            'en_cours': '#ffc107',     # Jaune
            'resolu': '#28a745',       # Vert
            'ferme': '#6c757d'         # Gris
        }
        color = colors.get(obj.statut, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; border-radius: 3px; font-size: 11px;">{}</span>',
            color, obj.get_statut_display()
        )
    statut_badge.short_description = 'Statut'

    def priorite_badge(self, obj):
        """Badge coloré pour la priorité"""
        colors = {
            'basse': '#17a2b8',        # Bleu clair
            'normale': '#6c757d',      # Gris
            'haute': '#fd7e14',        # Orange
            'urgente': '#dc3545'       # Rouge
        }
        color = colors.get(obj.priorite, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; border-radius: 3px; font-size: 11px;">{}</span>',
            color, obj.get_priorite_display()
        )
    priorite_badge.short_description = 'Priorité'

    def actions_rapides(self, obj):
        """Boutons d'actions rapides"""
        if obj.statut == 'nouveau':
            return format_html(
                '<a class="button" href="{}">Traiter</a>',
                reverse('admin:contact_contact_change', args=[obj.pk])
            )
        elif obj.statut == 'en_cours':
            return format_html(
                '<a class="button" href="{}">Résoudre</a>',
                reverse('admin:contact_contact_change', args=[obj.pk])
            )
        return '-'
    actions_rapides.short_description = 'Actions'

    def marquer_en_cours(self, request, queryset):
        """Action pour marquer les messages comme en cours"""
        updated = 0
        for contact in queryset:
            if contact.statut == 'nouveau':
                contact.marquer_en_cours(traite_par=request.user.username)
                updated += 1

        self.message_user(
            request,
            f'{updated} message(s) marqué(s) comme en cours de traitement.'
        )
    marquer_en_cours.short_description = "Marquer comme en cours"

    def marquer_resolu(self, request, queryset):
        """Action pour marquer les messages comme résolus"""
        updated = 0
        for contact in queryset:
            if contact.statut in ['nouveau', 'en_cours']:
                contact.marquer_resolu(traite_par=request.user.username)
                updated += 1

        self.message_user(
            request,
            f'{updated} message(s) marqué(s) comme résolu(s).'
        )
    marquer_resolu.short_description = "Marquer comme résolu"

    def marquer_urgent(self, request, queryset):
        """Action pour marquer les messages comme urgents"""
        updated = queryset.update(priorite='urgente')
        self.message_user(
            request,
            f'{updated} message(s) marqué(s) comme urgent(s).'
        )
    marquer_urgent.short_description = "Marquer comme urgent"

    def get_queryset(self, request):
        """Optimiser les requêtes"""
        return super().get_queryset(request).select_related()
