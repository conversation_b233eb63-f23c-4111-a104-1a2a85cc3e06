# Generated by Django 5.2.3 on 2025-07-01 06:35

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Contact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100, verbose_name='Nom complet')),
                ('email', models.EmailField(max_length=254, verbose_name='Adresse email')),
                ('telephone', models.CharField(blank=True, max_length=20, null=True, verbose_name='Téléphone')),
                ('objet', models.Char<PERSON>ield(max_length=200, verbose_name='Objet du message')),
                ('contexte', models.TextField(verbose_name='Message/Contexte')),
                ('statut', models.CharField(choices=[('nouveau', 'Nouveau'), ('en_cours', 'En cours de traitement'), ('resolu', 'Résolu'), ('ferme', 'Fermé')], default='nouveau', max_length=20, verbose_name='Statut')),
                ('priorite', models.CharField(choices=[('basse', 'Basse'), ('normale', 'Normale'), ('haute', 'Haute'), ('urgente', 'Urgente')], default='normale', max_length=20, verbose_name='Priorité')),
                ('date_creation', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('date_modification', models.DateTimeField(auto_now=True, verbose_name='Dernière modification')),
                ('date_traitement', models.DateTimeField(blank=True, null=True, verbose_name='Date de traitement')),
                ('reponse_admin', models.TextField(blank=True, null=True, verbose_name="Réponse de l'équipe")),
                ('traite_par', models.CharField(blank=True, max_length=100, null=True, verbose_name='Traité par')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='Adresse IP')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='User Agent')),
            ],
            options={
                'verbose_name': 'Message de contact',
                'verbose_name_plural': 'Messages de contact',
                'ordering': ['-date_creation'],
            },
        ),
    ]
