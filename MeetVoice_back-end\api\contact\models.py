from django.db import models
from django.utils import timezone


class Contact(models.Model):
    """Modèle pour les messages de contact des utilisateurs"""

    STATUT_CHOICES = [
        ('nouveau', 'Nouveau'),
        ('en_cours', 'En cours de traitement'),
        ('resolu', 'Résolu'),
        ('ferme', 'Fermé'),
    ]

    PRIORITE_CHOICES = [
        ('basse', 'Basse'),
        ('normale', 'Normale'),
        ('haute', 'Haute'),
        ('urgente', 'Urgente'),
    ]

    # Informations de contact
    nom = models.CharField(max_length=100, verbose_name="Nom complet")
    email = models.EmailField(verbose_name="Adresse email")
    telephone = models.CharField(max_length=20, blank=True, null=True, verbose_name="Téléphone")

    # Contenu du message
    objet = models.CharField(max_length=200, verbose_name="Objet du message")
    contexte = models.TextField(verbose_name="Message/Contexte")

    # Métadonnées
    statut = models.CharField(
        max_length=20,
        choices=STATUT_CHOICES,
        default='nouveau',
        verbose_name="Statut"
    )
    priorite = models.CharField(
        max_length=20,
        choices=PRIORITE_CHOICES,
        default='normale',
        verbose_name="Priorité"
    )

    # Dates
    date_creation = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")
    date_modification = models.DateTimeField(auto_now=True, verbose_name="Dernière modification")
    date_traitement = models.DateTimeField(blank=True, null=True, verbose_name="Date de traitement")

    # Réponse admin
    reponse_admin = models.TextField(blank=True, null=True, verbose_name="Réponse de l'équipe")
    traite_par = models.CharField(max_length=100, blank=True, null=True, verbose_name="Traité par")

    # Informations techniques
    ip_address = models.GenericIPAddressField(blank=True, null=True, verbose_name="Adresse IP")
    user_agent = models.TextField(blank=True, null=True, verbose_name="User Agent")

    class Meta:
        ordering = ['-date_creation']
        verbose_name = "Message de contact"
        verbose_name_plural = "Messages de contact"

    def __str__(self):
        return f"{self.nom} - {self.objet} ({self.get_statut_display()})"

    def marquer_en_cours(self, traite_par=None):
        """Marquer le message comme en cours de traitement"""
        self.statut = 'en_cours'
        self.date_traitement = timezone.now()
        if traite_par:
            self.traite_par = traite_par
        self.save()

    def marquer_resolu(self, reponse=None, traite_par=None):
        """Marquer le message comme résolu"""
        self.statut = 'resolu'
        self.date_traitement = timezone.now()
        if reponse:
            self.reponse_admin = reponse
        if traite_par:
            self.traite_par = traite_par
        self.save()

    def fermer(self):
        """Fermer le message"""
        self.statut = 'ferme'
        self.save()

    @property
    def est_nouveau(self):
        return self.statut == 'nouveau'

    @property
    def est_urgent(self):
        return self.priorite == 'urgente'
