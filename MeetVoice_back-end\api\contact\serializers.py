"""
Serializers pour l'application Contact
"""
from rest_framework import serializers
from .models import Contact


class ContactCreateSerializer(serializers.ModelSerializer):
    """Serializer pour créer un nouveau message de contact"""
    
    class Meta:
        model = Contact
        fields = [
            'nom',
            'email', 
            'telephone',
            'objet',
            'contexte'
        ]
        
    def validate_contexte(self, value):
        """Valider que le contexte n'est pas trop court"""
        if len(value.strip()) < 10:
            raise serializers.ValidationError("Le message doit contenir au moins 10 caractères.")
        return value
    
    def validate_objet(self, value):
        """Valider l'objet du message"""
        if len(value.strip()) < 3:
            raise serializers.ValidationError("L'objet doit contenir au moins 3 caractères.")
        return value


class ContactListSerializer(serializers.ModelSerializer):
    """Serializer pour lister les messages de contact"""
    
    statut_display = serializers.CharField(source='get_statut_display', read_only=True)
    priorite_display = serializers.Char<PERSON>ield(source='get_priorite_display', read_only=True)
    est_nouveau = serializers.BooleanField(read_only=True)
    est_urgent = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Contact
        fields = [
            'id',
            'nom',
            'email',
            'telephone',
            'objet',
            'contexte',
            'statut',
            'statut_display',
            'priorite',
            'priorite_display',
            'date_creation',
            'date_modification',
            'date_traitement',
            'traite_par',
            'est_nouveau',
            'est_urgent'
        ]


class ContactDetailSerializer(serializers.ModelSerializer):
    """Serializer détaillé pour un message de contact"""
    
    statut_display = serializers.CharField(source='get_statut_display', read_only=True)
    priorite_display = serializers.CharField(source='get_priorite_display', read_only=True)
    est_nouveau = serializers.BooleanField(read_only=True)
    est_urgent = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Contact
        fields = [
            'id',
            'nom',
            'email',
            'telephone',
            'objet',
            'contexte',
            'statut',
            'statut_display',
            'priorite',
            'priorite_display',
            'date_creation',
            'date_modification',
            'date_traitement',
            'reponse_admin',
            'traite_par',
            'ip_address',
            'user_agent',
            'est_nouveau',
            'est_urgent'
        ]


class ContactUpdateSerializer(serializers.ModelSerializer):
    """Serializer pour mettre à jour un message de contact (admin)"""
    
    class Meta:
        model = Contact
        fields = [
            'statut',
            'priorite',
            'reponse_admin',
            'traite_par'
        ]


class ContactStatsSerializer(serializers.Serializer):
    """Serializer pour les statistiques des messages de contact"""
    
    total_messages = serializers.IntegerField()
    nouveaux_messages = serializers.IntegerField()
    messages_en_cours = serializers.IntegerField()
    messages_resolus = serializers.IntegerField()
    messages_urgents = serializers.IntegerField()
    messages_aujourd_hui = serializers.IntegerField()
    messages_cette_semaine = serializers.IntegerField()
    taux_resolution = serializers.FloatField()


class ContactPublicCreateSerializer(serializers.ModelSerializer):
    """Serializer public pour créer un message de contact (sans authentification)"""
    
    class Meta:
        model = Contact
        fields = [
            'nom',
            'email',
            'telephone',
            'objet',
            'contexte'
        ]
        
    def validate_email(self, value):
        """Validation basique de l'email"""
        if '@' not in value or '.' not in value:
            raise serializers.ValidationError("Veuillez entrer une adresse email valide.")
        return value.lower()
    
    def validate_nom(self, value):
        """Validation du nom"""
        if len(value.strip()) < 2:
            raise serializers.ValidationError("Le nom doit contenir au moins 2 caractères.")
        return value.strip()
    
    def create(self, validated_data):
        """Créer un nouveau message avec des valeurs par défaut"""
        # Ajouter des métadonnées depuis la requête si disponible
        request = self.context.get('request')
        if request:
            validated_data['ip_address'] = self._get_client_ip(request)
            validated_data['user_agent'] = request.META.get('HTTP_USER_AGENT', '')
        
        return super().create(validated_data)
    
    def _get_client_ip(self, request):
        """Obtenir l'IP du client"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
