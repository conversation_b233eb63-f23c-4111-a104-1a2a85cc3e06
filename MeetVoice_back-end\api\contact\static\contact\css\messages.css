/* Styles pour la page des messages de contact - Design unifié */

/* Couleurs du thème MeetVoice */
:root {
    --primary-color: #2a1d34;
    --secondary-color: #4e385f;
    --accent-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

/* Le style du titre est maintenant dans le template directement */

/* Fond blanc pour le contenu */
.main-content {
    background-color: white !important;
}

.container-fluid {
    background-color: white !important;
}

/* Styles pour les cartes de statistiques */
.border-left-primary {
    border-left: 0.25rem solid var(--accent-color) !important;
}

.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}

.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}

.border-left-danger {
    border-left: 0.25rem solid var(--danger-color) !important;
}

/* Styles pour les badges de statut */
.bg-nouveau { background-color: var(--warning-color) !important; }
.bg-en_cours { background-color: var(--info-color) !important; }
.bg-resolu { background-color: var(--success-color) !important; }
.bg-ferme { background-color: var(--dark-color) !important; }

/* Styles pour les badges de priorité */
.bg-urgente { background-color: var(--danger-color) !important; }
.bg-haute { background-color: #fd7e14 !important; }
.bg-normale { background-color: var(--dark-color) !important; }
.bg-basse { background-color: var(--info-color) !important; }

/* Styles pour les cartes de messages */
.message-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.message-card:hover {
    transform: translateY(-2px);
}

.message-card .card {
    border: none;
    border-radius: 0.5rem;
}

.message-card .card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

.message-content {
    line-height: 1.6;
    color: #495057;
}

.message-text {
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Animations */
.message-card {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s ease forwards;
}

.message-card:nth-child(1) { animation-delay: 0.1s; }
.message-card:nth-child(2) { animation-delay: 0.2s; }
.message-card:nth-child(3) { animation-delay: 0.3s; }
.message-card:nth-child(4) { animation-delay: 0.4s; }
.message-card:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Styles pour les filtres */
#filters-section {
    transition: all 0.3s ease;
}

#filters-section.show {
    display: block !important;
}

/* Styles pour les boutons d'action */
.btn-group .btn {
    border-radius: 0.25rem;
    margin-right: 0.25rem;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* Styles pour les modals */
.modal-content {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    background-color: var(--primary-color);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Responsive */
@media (max-width: 768px) {
    .message-card .card-header .row {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .message-card .card-header .col-md-4 {
        margin-top: 0.5rem;
        text-align: left !important;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-bottom: 0.25rem;
        margin-right: 0;
    }
}

/* Anciens styles supprimés - remplacés par le design unifié Bootstrap */
