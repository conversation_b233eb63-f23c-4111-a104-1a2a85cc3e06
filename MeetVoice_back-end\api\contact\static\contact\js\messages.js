/**
 * JavaScript pour la page des messages de contact - Version unifiée
 */

// Variables globales
let currentMessageId = null;
let autoRefreshInterval = null;

document.addEventListener('DOMContentLoaded', function() {
    console.log('📧 Page des messages de contact chargée');

    // Initialiser les fonctionnalités
    initializeFilters();
    initializeSearch();
    initializeAutoRefresh();
    initializeAnimations();

    console.log('✅ JavaScript des messages de contact initialisé');
});

// Fonctions pour les actions sur les messages
window.toggleFilters = function() {
    const filtersSection = document.getElementById('filters-section');
    if (filtersSection.style.display === 'none') {
        filtersSection.style.display = 'block';
    } else {
        filtersSection.style.display = 'none';
    }
};

window.clearFilters = function() {
    document.getElementById('search-input').value = '';
    document.getElementById('status-filter').value = 'all';
    document.getElementById('priority-filter').value = 'all';
    document.getElementById('date-filter').value = 'all';
    applyFilters();
};

window.toggleFullMessage = function(messageId) {
    const messageText = document.getElementById(`message-text-${messageId}`);
    const messageFull = document.getElementById(`message-full-${messageId}`);

    if (messageText.style.display === 'none') {
        messageText.style.display = 'block';
        messageFull.style.display = 'none';
    } else {
        messageText.style.display = 'none';
        messageFull.style.display = 'block';
    }
};

window.updateStatus = function(messageId, newStatus) {
    const statusText = newStatus === 'en_cours' ? 'en cours' : 'résolu';

    if (confirm(`Marquer ce message comme ${statusText} ?`)) {
        // Appel AJAX pour mettre à jour le statut
        fetch(`/contact/api/${messageId}/marquer-${newStatus.replace('_', '-')}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Message marqué comme ${statusText}`, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification('Erreur lors de la mise à jour', 'error');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showNotification('Erreur de connexion', 'error');
        });
    }
};

window.openReplyModal = function(messageId, email, name, subject) {
    currentMessageId = messageId;

    // Remplir le modal
    document.getElementById('replyTo').value = email;
    document.getElementById('replySubject').value = `Re: ${subject}`;
    document.getElementById('replyMessage').value = `Bonjour ${name},\n\n`;

    // Ouvrir le modal
    const modal = new bootstrap.Modal(document.getElementById('replyModal'));
    modal.show();
};

window.sendReply = function() {
    if (!currentMessageId) {
        showNotification('Erreur: Aucun message sélectionné', 'error');
        return;
    }

    const subject = document.getElementById('replySubject').value;
    const message = document.getElementById('replyMessage').value;
    const markAsResolved = document.getElementById('markAsResolved').checked;

    if (!subject.trim() || !message.trim()) {
        showNotification('Veuillez remplir tous les champs', 'error');
        return;
    }

    // Désactiver le bouton d'envoi
    const sendButton = document.querySelector('#replyModal .btn-primary');
    sendButton.disabled = true;
    sendButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Envoi...';

    // Envoyer la réponse
    fetch('/contact/send-reply/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            message_id: currentMessageId,
            subject: subject,
            message: message,
            mark_as_resolved: markAsResolved
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Réponse envoyée avec succès !', 'success');

            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('replyModal'));
            modal.hide();

            // Recharger la page après un délai
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification(`Erreur: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showNotification('Erreur de connexion', 'error');
    })
    .finally(() => {
        // Réactiver le bouton
        sendButton.disabled = false;
        sendButton.innerHTML = '<i class="fas fa-paper-plane me-1"></i>Envoyer la réponse';
    });
};

// Fonction pour afficher les notifications
function showNotification(message, type = 'info') {
    // Créer une notification Bootstrap
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' : 'alert-info';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Fonction pour récupérer le token CSRF
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Auto-refresh intelligent
function initializeAutoRefresh() {
    // Auto-refresh toutes les 30 secondes seulement si la page est visible
    autoRefreshInterval = setInterval(() => {
        if (!document.hidden) {
            console.log('🔄 Auto-refresh de la page...');
            location.reload();
        }
    }, 30000);

    // Arrêter l'auto-refresh si la page n'est pas visible
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            clearInterval(autoRefreshInterval);
        } else {
            initializeAutoRefresh();
        }
    });
}
    
// Initialiser les animations
function initializeAnimations() {
    const messageCards = document.querySelectorAll('.message-card');
    messageCards.forEach((card, index) => {
        // Les animations CSS s'occupent de l'apparition
        // Ajouter des événements pour les interactions
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

// Gestion des filtres
function initializeFilters() {
    const statusFilter = document.getElementById('status-filter');
    const priorityFilter = document.getElementById('priority-filter');
    const dateFilter = document.getElementById('date-filter');

    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }
    if (priorityFilter) {
        priorityFilter.addEventListener('change', applyFilters);
    }
    if (dateFilter) {
        dateFilter.addEventListener('change', applyFilters);
    }
}

// Initialiser la recherche
function initializeSearch() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(applyFilters, 300));
    }
}

// Fonction debounce pour optimiser la recherche
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
    
// Appliquer les filtres
function applyFilters() {
    const searchTerm = document.getElementById('search-input')?.value.toLowerCase() || '';
    const statusFilter = document.getElementById('status-filter')?.value || 'all';
    const priorityFilter = document.getElementById('priority-filter')?.value || 'all';
    const dateFilter = document.getElementById('date-filter')?.value || 'all';

    const messageCards = document.querySelectorAll('.message-card');
    let visibleCount = 0;

    messageCards.forEach(card => {
        const messageId = card.dataset.messageId;
        const status = card.dataset.status;
        const priority = card.dataset.priority;

        // Recherche textuelle
        const text = card.textContent.toLowerCase();
        const matchesSearch = searchTerm === '' || text.includes(searchTerm);

        // Filtres
        const matchesStatus = statusFilter === 'all' || status === statusFilter;
        const matchesPriority = priorityFilter === 'all' || priority === priorityFilter;

        // Filtre de date (à implémenter selon les besoins)
        const matchesDate = dateFilter === 'all'; // Simplifié pour l'instant

        if (matchesSearch && matchesStatus && matchesPriority && matchesDate) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });

    console.log(`Filtres appliqués: ${visibleCount} messages visibles`);
}

// Raccourcis clavier
document.addEventListener('keydown', function(e) {
    // F5 ou Ctrl+R pour actualiser
    if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
        e.preventDefault();
        location.reload();
    }

    // Ctrl+F pour rechercher
    if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.focus();
        }
    }

    // Échap pour fermer les modals
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });
    }
});
    
// Fonctions pour les actions sur les messages
window.toggleFilters = function() {
    const filtersSection = document.getElementById('filters-section');
    if (filtersSection.style.display === 'none') {
        filtersSection.style.display = 'block';
    } else {
        filtersSection.style.display = 'none';
    }
};

window.clearFilters = function() {
    document.getElementById('search-input').value = '';
    document.getElementById('status-filter').value = 'all';
    document.getElementById('priority-filter').value = 'all';
    document.getElementById('date-filter').value = 'all';
    applyFilters();
};

window.toggleFullMessage = function(messageId) {
    const messageText = document.getElementById(`message-text-${messageId}`);
    const messageFull = document.getElementById(`message-full-${messageId}`);

    if (messageText.style.display === 'none') {
        messageText.style.display = 'block';
        messageFull.style.display = 'none';
    } else {
        messageText.style.display = 'none';
        messageFull.style.display = 'block';
    }
};

window.updateStatus = function(messageId, newStatus) {
    const statusText = newStatus === 'en_cours' ? 'en cours' : 'résolu';

    if (confirm(`Marquer ce message comme ${statusText} ?`)) {
        // Appel AJAX pour mettre à jour le statut
        fetch(`/contact/api/${messageId}/marquer-${newStatus.replace('_', '-')}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Message marqué comme ${statusText}`, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification('Erreur lors de la mise à jour', 'error');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showNotification('Erreur de connexion', 'error');
        });
    }
};
    
    // Ajouter des raccourcis clavier
    document.addEventListener('keydown', function(e) {
        // F5 ou Ctrl+R pour actualiser
        if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
            e.preventDefault();
            location.reload();
        }
        
        // Ctrl+F pour rechercher
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                searchInput.focus();
            }
        }
    });
    
    // Notification de nouveaux messages (si WebSocket disponible)
    if (typeof WebSocket !== 'undefined') {
        // TODO: Implémenter WebSocket pour les notifications en temps réel
        console.log('🔌 WebSocket disponible pour les notifications temps réel');
    }
    
    console.log('✅ JavaScript des messages de contact initialisé');
});
