{% extends 'backoffice/base.html' %}
{% load static %}

{% block page_title_main %}Messages{% endblock %}
{% block page_title_breadcrumb %}Contact{% endblock %}
{% block page_title_header %}Messages{% endblock %}
{% block page_icon %}<i class="fas fa-envelope me-2"></i>{% endblock %}

{% block extra_css %}
<style>
.page-header h1 { font-size: 1.5rem !important; }
.card { margin-bottom: 1rem; }
.badge { font-size: 0.75rem; }
</style>
{% endblock %}

{% block backoffice_content %}
<!-- Statistiques -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-primary">{{ total_messages }}</h5>
                <small>Total</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-warning">{{ nouveaux }}</h5>
                <small>Nouveaux</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-info">{{ en_cours }}</h5>
                <small>En cours</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-success">{{ resolus }}</h5>
                <small>Résolus</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-danger">{{ urgents }}</h5>
                <small>Urgents</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <button class="btn btn-primary w-100" onclick="location.reload()">
            <i class="fas fa-sync-alt"></i> Actualiser
        </button>
    </div>
</div>

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Messages
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_messages }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-envelope fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Nouveaux
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ nouveaux }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bell fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                En Cours
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ en_cours }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Résolus
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ resolus }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Urgents
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ urgents }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres et Recherche -->
    <div class="row mb-4" id="filters-section" style="display: none;">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-filter me-2"></i>Filtres
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="search-input" class="form-label">Rechercher</label>
                            <input type="text" class="form-control" id="search-input" placeholder="Nom, email, sujet...">
                        </div>
                        <div class="col-md-2">
                            <label for="status-filter" class="form-label">Statut</label>
                            <select class="form-select" id="status-filter">
                                <option value="all">Tous</option>
                                <option value="nouveau">Nouveaux</option>
                                <option value="en_cours">En cours</option>
                                <option value="resolu">Résolus</option>
                                <option value="ferme">Fermés</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="priority-filter" class="form-label">Priorité</label>
                            <select class="form-select" id="priority-filter">
                                <option value="all">Toutes</option>
                                <option value="urgente">Urgente</option>
                                <option value="haute">Haute</option>
                                <option value="normale">Normale</option>
                                <option value="basse">Basse</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date-filter" class="form-label">Période</label>
                            <select class="form-select" id="date-filter">
                                <option value="all">Toutes</option>
                                <option value="today">Aujourd'hui</option>
                                <option value="week">Cette semaine</option>
                                <option value="month">Ce mois</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                                <i class="fas fa-times me-1"></i>Effacer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des Messages -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>Liste des Messages
                    </h6>
                </div>
                <div class="card-body">
                    <div class="messages-grid">
        {% if contact_messages %}
            {% for message in contact_messages %}
            <div class="message-card mb-4" data-message-id="{{ message.id }}" data-status="{{ message.statut }}" data-priority="{{ message.priorite }}">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h6 class="mb-1 font-weight-bold">
                                    <i class="fas fa-user me-2 text-primary"></i>{{ message.nom }}
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-envelope me-1"></i>{{ message.email }}
                                    <span class="ms-3">
                                        <i class="fas fa-calendar me-1"></i>{{ message.date_creation|date:"d/m/Y à H:i" }}
                                    </span>
                                    {% if message.telephone %}
                                    <span class="ms-3">
                                        <i class="fas fa-phone me-1"></i>{{ message.telephone }}
                                    </span>
                                    {% endif %}
                                </small>
                            </div>
                            <div class="col-md-4 text-end">
                                {% if message.statut == 'nouveau' %}
                                    <span class="badge bg-warning text-dark me-2">
                                {% elif message.statut == 'en_cours' %}
                                    <span class="badge bg-info me-2">
                                {% elif message.statut == 'resolu' %}
                                    <span class="badge bg-success me-2">
                                {% else %}
                                    <span class="badge bg-secondary me-2">
                                {% endif %}
                                    {{ message.get_statut_display }}
                                </span>

                                {% if message.priorite == 'urgente' %}
                                    <span class="badge bg-danger">
                                {% elif message.priorite == 'haute' %}
                                    <span class="badge bg-warning text-dark">
                                {% elif message.priorite == 'normale' %}
                                    <span class="badge bg-secondary">
                                {% else %}
                                    <span class="badge bg-info">
                                {% endif %}
                                    {{ message.get_priorite_display }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <h6 class="card-title text-primary">
                            <i class="fas fa-tag me-2"></i>{{ message.objet }}
                        </h6>

                        <div class="message-content mb-3">
                            <div class="message-text" id="message-text-{{ message.id }}">
                                {% if message.contexte|length > 300 %}
                                    {{ message.contexte|slice:":300" }}...
                                    <button class="btn btn-link btn-sm p-0" onclick="toggleFullMessage({{ message.id }})">
                                        <small>Voir plus</small>
                                    </button>
                                {% else %}
                                    {{ message.contexte }}
                                {% endif %}
                            </div>
                            <div class="message-full d-none" id="message-full-{{ message.id }}">
                                {{ message.contexte }}
                                <button class="btn btn-link btn-sm p-0" onclick="toggleFullMessage({{ message.id }})">
                                    <small>Voir moins</small>
                                </button>
                            </div>
                        </div>

                        <div class="message-meta mb-3">
                            <small class="text-muted">
                                <span class="me-3">
                                    <i class="fas fa-hashtag me-1"></i>ID: {{ message.id }}
                                </span>
                                {% if message.traite_par %}
                                <span class="me-3">
                                    <i class="fas fa-user-tie me-1"></i>Traité par: {{ message.traite_par }}
                                </span>
                                {% endif %}
                                {% if message.ip_address %}
                                <span>
                                    <i class="fas fa-globe me-1"></i>IP: {{ message.ip_address }}
                                </span>
                                {% endif %}
                            </small>
                        </div>

                        <!-- Actions -->
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="btn-group" role="group">
                                {% if message.statut == 'nouveau' %}
                                <button class="btn btn-outline-info btn-sm" onclick="updateStatus({{ message.id }}, 'en_cours')">
                                    <i class="fas fa-play me-1"></i>Prendre en charge
                                </button>
                                {% endif %}

                                {% if message.statut == 'en_cours' %}
                                <button class="btn btn-outline-success btn-sm" onclick="updateStatus({{ message.id }}, 'resolu')">
                                    <i class="fas fa-check me-1"></i>Marquer résolu
                                </button>
                                {% endif %}

                                <button class="btn btn-outline-secondary btn-sm" onclick="togglePriority({{ message.id }}, '{{ message.priorite }}')">
                                    <i class="fas fa-flag me-1"></i>Priorité
                                </button>
                            </div>

                            <button class="btn btn-primary btn-sm" onclick="openReplyModal({{ message.id }}, '{{ message.email }}', '{{ message.nom }}', '{{ message.objet }}')">
                                <i class="fas fa-reply me-1"></i>Répondre
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-gray-600">Aucun message de contact</h5>
                <p class="text-muted">Les messages apparaîtront ici automatiquement.</p>
            </div>
        {% endif %}
                </div>
            </div>
        </div>
                </div>
            </div>
        </div>
    </div>
    </div>
  </main>
</div>

<!-- Modal de Réponse -->
<div class="modal fade" id="replyModal" tabindex="-1" aria-labelledby="replyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="replyModalLabel">
                    <i class="fas fa-reply me-2"></i>Répondre au message
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="replyForm">
                    <div class="mb-3">
                        <label for="replyTo" class="form-label">Destinataire</label>
                        <input type="email" class="form-control" id="replyTo" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="replySubject" class="form-label">Sujet</label>
                        <input type="text" class="form-control" id="replySubject">
                    </div>
                    <div class="mb-3">
                        <label for="replyMessage" class="form-label">Message</label>
                        <textarea class="form-control" id="replyMessage" rows="8" placeholder="Votre réponse..."></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="markAsResolved">
                            <label class="form-check-label" for="markAsResolved">
                                Marquer comme résolu après envoi
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Annuler
                </button>
                <button type="button" class="btn btn-primary" onclick="sendReply()">
                    <i class="fas fa-paper-plane me-1"></i>Envoyer la réponse
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Confirmation -->
<div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmModalLabel">Confirmation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="confirmModalBody">
                <!-- Contenu dynamique -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="confirmAction">Confirmer</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="{% static 'contact/js/messages.js' %}"></script>
{% endblock %}
