{% extends 'backoffice/base.html' %}
{% load static %}

{% block page_title_main %}Messages{% endblock %}
{% block page_title_breadcrumb %}Contact{% endblock %}
{% block page_title_header %}Messages{% endblock %}
{% block page_icon %}<i class="fas fa-envelope me-2"></i>{% endblock %}

{% block extra_css %}
<style>
.page-header h1 { font-size: 1.5rem !important; }
.card { margin-bottom: 1rem; }
.badge { font-size: 0.75rem; }

/* Styles pour les longs messages */
.message-content {
    max-height: none;
}

.message-content p {
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

/* Messages très longs */
#message-full-* {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    background-color: #f8f9fa;
    margin-bottom: 1rem;
}

/* Indicateur de longueur */
.text-muted .fas {
    color: #6c757d !important;
}
</style>
{% endblock %}

{% block backoffice_content %}
<!-- Statistiques -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-primary">{{ total_messages }}</h5>
                <small>Total</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-warning">{{ nouveaux }}</h5>
                <small>Nouveaux</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-info">{{ en_cours }}</h5>
                <small>En cours</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-success">{{ resolus }}</h5>
                <small>Résolus</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-danger">{{ urgents }}</h5>
                <small>Urgents</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <button class="btn btn-primary w-100" onclick="location.reload()">
            <i class="fas fa-sync-alt"></i> Actualiser
        </button>
    </div>
</div>

<!-- Messages -->
{% if contact_messages %}
    {% for message in contact_messages %}
    <div class="card mb-3">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <strong>{{ message.nom }}</strong> - {{ message.email }}
                <br><small class="text-muted">{{ message.date_creation|date:"d/m/Y H:i" }}</small>
            </div>
            <div>
                {% if message.statut == 'nouveau' %}
                    <span class="badge bg-warning">{{ message.get_statut_display }}</span>
                {% elif message.statut == 'en_cours' %}
                    <span class="badge bg-info">{{ message.get_statut_display }}</span>
                {% elif message.statut == 'resolu' %}
                    <span class="badge bg-success">{{ message.get_statut_display }}</span>
                {% else %}
                    <span class="badge bg-secondary">{{ message.get_statut_display }}</span>
                {% endif %}
                
                {% if message.priorite == 'urgente' %}
                    <span class="badge bg-danger">{{ message.get_priorite_display }}</span>
                {% elif message.priorite == 'haute' %}
                    <span class="badge bg-warning">{{ message.get_priorite_display }}</span>
                {% else %}
                    <span class="badge bg-secondary">{{ message.get_priorite_display }}</span>
                {% endif %}
            </div>
        </div>
        <div class="card-body">
            <h6 class="card-title">{{ message.objet }}</h6>

            <!-- Message avec troncature intelligente -->
            <div class="message-content">
                {% if message.contexte|length > 300 %}
                    <div id="message-short-{{ message.id }}">
                        <p class="card-text">{{ message.contexte|slice:":300" }}...</p>
                        <button class="btn btn-link btn-sm p-0" onclick="showFullMessage({{ message.id }})">
                            <i class="fas fa-chevron-down me-1"></i>Voir le message complet ({{ message.contexte|length }} caractères)
                        </button>
                    </div>
                    <div id="message-full-{{ message.id }}" style="display: none;">
                        <p class="card-text" style="white-space: pre-wrap;">{{ message.contexte }}</p>
                        <button class="btn btn-link btn-sm p-0" onclick="hideFullMessage({{ message.id }})">
                            <i class="fas fa-chevron-up me-1"></i>Réduire le message
                        </button>
                    </div>
                {% else %}
                    <p class="card-text" style="white-space: pre-wrap;">{{ message.contexte }}</p>
                {% endif %}
            </div>

            <!-- Informations supplémentaires -->
            {% if message.telephone %}
            <small class="text-muted d-block mb-2">
                <i class="fas fa-phone me-1"></i>Téléphone : {{ message.telephone }}
            </small>
            {% endif %}

            <small class="text-muted d-block mb-3">
                <i class="fas fa-info-circle me-1"></i>
                Message de {{ message.contexte|length }} caractères - ID: {{ message.id }}
                {% if message.ip_address %}
                    - IP: {{ message.ip_address }}
                {% endif %}
            </small>

            <div class="d-flex gap-2">
                {% if message.statut == 'nouveau' %}
                <button class="btn btn-sm btn-info" onclick="changerStatut({{ message.id }}, 'en_cours')">
                    <i class="fas fa-play me-1"></i>Prendre en charge
                </button>
                {% endif %}
                {% if message.statut == 'en_cours' %}
                <button class="btn btn-sm btn-success" onclick="changerStatut({{ message.id }}, 'resolu')">
                    <i class="fas fa-check me-1"></i>Marquer résolu
                </button>
                {% endif %}
                <button class="btn btn-sm btn-primary" onclick="repondreMessage('{{ message.email }}', '{{ message.nom }}', '{{ message.objet }}')">
                    <i class="fas fa-reply me-1"></i>Répondre
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="voirDetails({{ message.id }})">
                    <i class="fas fa-eye me-1"></i>Détails
                </button>
            </div>
        </div>
    </div>
    {% endfor %}
{% else %}
    <div class="text-center py-5">
        <h5>Aucun message</h5>
        <p class="text-muted">Les messages apparaîtront ici.</p>
    </div>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
// Gestion de l'affichage des messages
function showFullMessage(messageId) {
    document.getElementById('message-short-' + messageId).style.display = 'none';
    document.getElementById('message-full-' + messageId).style.display = 'block';
}

function hideFullMessage(messageId) {
    document.getElementById('message-short-' + messageId).style.display = 'block';
    document.getElementById('message-full-' + messageId).style.display = 'none';
}

// Actions sur les messages
function changerStatut(messageId, nouveauStatut) {
    if (confirm('Changer le statut de ce message ?')) {
        // Pour l'instant, on recharge la page
        // Plus tard on peut ajouter une vraie action AJAX
        alert('Statut changé vers: ' + nouveauStatut + ' (ID: ' + messageId + ')');
        location.reload();
    }
}

function repondreMessage(email, nom, objet) {
    // Ouvrir le client email par défaut
    const sujet = 'Re: ' + objet;
    const corps = 'Bonjour ' + nom + ',\n\n\n\nCordialement,\nÉquipe MeetVoice';
    const mailtoLink = 'mailto:' + email + '?subject=' + encodeURIComponent(sujet) + '&body=' + encodeURIComponent(corps);
    window.open(mailtoLink);
}

function voirDetails(messageId) {
    alert('Détails du message ID: ' + messageId + '\n\nFonctionnalité à développer si nécessaire.');
}

function toggleFilters() {
    // Pour plus tard si besoin de filtres
    alert('Filtres à développer si nécessaire');
}
</script>
{% endblock %}
