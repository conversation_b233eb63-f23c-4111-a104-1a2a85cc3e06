"""
URLs pour l'application Contact
"""
from django.urls import path
from . import views

app_name = 'contact'

urlpatterns = [
    # === BACK-OFFICE ===

    # Page de gestion des messages (back-office)
    path('', views.contact_messages_page, name='contact-messages-page'),

    # === API PUBLIQUE ===

    # Créer un message de contact (formulaire public du site)
    path('public/', views.contact_public, name='contact-public'),

    # === API PRIVÉE (pour développement/intégrations) ===

    # API REST complète (si nécessaire pour des intégrations)
    path('api/', views.ContactListCreateView.as_view(), name='contact-list-create'),
    path('api/<int:pk>/', views.ContactDetailView.as_view(), name='contact-detail'),
    path('api/stats/', views.contact_stats, name='contact-stats'),
    path('api/<int:pk>/marquer-en-cours/', views.marquer_en_cours, name='marquer-en-cours'),
    path('api/<int:pk>/marquer-resolu/', views.marquer_resolu, name='marquer-resolu'),
    path('send-reply/', views.send_reply, name='send-reply'),
]
