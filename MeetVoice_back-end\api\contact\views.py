"""
Vues pour l'application Contact - Version propre
"""
from django.shortcuts import render
from django.utils import timezone
from django.db.models import Count, Q
from datetime import datetime, timedelta
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.http import JsonResponse
import json

from .models import Contact
from .serializers import (
    ContactCreateSerializer,
    ContactListSerializer,
    ContactDetailSerializer,
    ContactUpdateSerializer,
    ContactStatsSerializer,
    ContactPublicCreateSerializer
)


class ContactListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des messages de contact"""

    queryset = Contact.objects.all()
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ContactCreateSerializer
        return ContactListSerializer

    def get_queryset(self):
        """Filtrer les messages selon les paramètres"""
        queryset = Contact.objects.all()

        # Filtres
        statut = self.request.query_params.get('statut')
        if statut:
            queryset = queryset.filter(statut=statut)

        priorite = self.request.query_params.get('priorite')
        if priorite:
            queryset = queryset.filter(priorite=priorite)

        # Recherche
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(nom__icontains=search) |
                Q(email__icontains=search) |
                Q(objet__icontains=search) |
                Q(contexte__icontains=search)
            )

        # Tri
        ordering = self.request.query_params.get('ordering', '-date_creation')
        queryset = queryset.order_by(ordering)

        return queryset


class ContactDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour récupérer, modifier ou supprimer un message de contact"""

    queryset = Contact.objects.all()
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return ContactUpdateSerializer
        return ContactDetailSerializer


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def contact_stats(request):
    """Statistiques des messages de contact"""
    
    # Statistiques générales
    total = Contact.objects.count()
    nouveau = Contact.objects.filter(statut='nouveau').count()
    en_cours = Contact.objects.filter(statut='en_cours').count()
    resolu = Contact.objects.filter(statut='resolu').count()
    ferme = Contact.objects.filter(statut='ferme').count()
    
    # Statistiques par priorité
    urgente = Contact.objects.filter(priorite='urgente').count()
    haute = Contact.objects.filter(priorite='haute').count()
    normale = Contact.objects.filter(priorite='normale').count()
    basse = Contact.objects.filter(priorite='basse').count()
    
    # Messages récents (7 derniers jours)
    date_limite = timezone.now() - timedelta(days=7)
    recents = Contact.objects.filter(date_creation__gte=date_limite).count()
    
    # Évolution par jour (7 derniers jours)
    evolution = []
    for i in range(7):
        date = timezone.now().date() - timedelta(days=i)
        count = Contact.objects.filter(date_creation__date=date).count()
        evolution.append({
            'date': date.strftime('%Y-%m-%d'),
            'count': count
        })
    
    stats = {
        'total': total,
        'par_statut': {
            'nouveau': nouveau,
            'en_cours': en_cours,
            'resolu': resolu,
            'ferme': ferme
        },
        'par_priorite': {
            'urgente': urgente,
            'haute': haute,
            'normale': normale,
            'basse': basse
        },
        'recents_7j': recents,
        'evolution_7j': evolution
    }
    
    return Response(stats)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def marquer_en_cours(request, pk):
    """Marquer un message comme en cours"""
    try:
        contact = Contact.objects.get(pk=pk)
        contact.statut = 'en_cours'
        contact.traite_par = request.user.username if hasattr(request.user, 'username') else 'Système'
        contact.save()
        
        serializer = ContactDetailSerializer(contact)
        return Response({
            'success': True,
            'message': 'Message marqué comme en cours',
            'contact': serializer.data
        })
    except Contact.DoesNotExist:
        return Response({
            'success': False,
            'error': 'Message non trouvé'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def marquer_resolu(request, pk):
    """Marquer un message comme résolu"""
    try:
        contact = Contact.objects.get(pk=pk)
        contact.statut = 'resolu'
        contact.traite_par = request.user.username if hasattr(request.user, 'username') else 'Système'
        contact.save()
        
        serializer = ContactDetailSerializer(contact)
        return Response({
            'success': True,
            'message': 'Message marqué comme résolu',
            'contact': serializer.data
        })
    except Contact.DoesNotExist:
        return Response({
            'success': False,
            'error': 'Message non trouvé'
        }, status=status.HTTP_404_NOT_FOUND)


@csrf_exempt
@require_http_methods(["POST"])
def contact_public(request):
    """API publique pour créer un message de contact (sans authentification)"""
    
    try:
        # Parser le JSON
        data = json.loads(request.body)
        
        # Ajouter l'IP du client
        data['ip_address'] = request.META.get('REMOTE_ADDR', '127.0.0.1')
        
        # Créer le serializer
        serializer = ContactPublicCreateSerializer(data=data)
        
        if serializer.is_valid():
            # Sauvegarder le message
            contact = serializer.save()
            
            return JsonResponse({
                'success': True,
                'message': 'Message de contact envoyé avec succès',
                'contact_id': contact.id,
                'data': {
                    'nom': contact.nom,
                    'email': contact.email,
                    'objet': contact.objet,
                    'date_creation': contact.date_creation.isoformat()
                }
            }, status=201)
        else:
            return JsonResponse({
                'success': False,
                'error': 'Données invalides',
                'errors': serializer.errors
            }, status=400)
            
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'JSON invalide'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Erreur serveur: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def contact_messages_page(request):
    """Page publique pour lire tous les messages de contact"""
    
    try:
        # Récupérer tous les messages de contact
        contact_messages = Contact.objects.all().order_by('-date_creation')

        # Calculer les statistiques
        total_messages = contact_messages.count()
        nouveaux = contact_messages.filter(statut='nouveau').count()
        en_cours = contact_messages.filter(statut='en_cours').count()
        resolus = contact_messages.filter(statut='resolu').count()
        urgents = contact_messages.filter(priorite='urgente').count()

        # Contexte pour le template
        context = {
            'contact_messages': contact_messages,
            'total_messages': total_messages,
            'nouveaux': nouveaux,
            'en_cours': en_cours,
            'resolus': resolus,
            'urgents': urgents,
        }
        
        return render(request, 'contact/messages_simple.html', context)
        
    except Exception as e:
        # En cas d'erreur, retourner une page d'erreur simple
        context = {
            'error_message': str(e),
            'messages': [],
            'total_messages': 0,
            'nouveaux': 0,
            'en_cours': 0,
            'resolus': 0,
            'urgents': 0,
        }
        return render(request, 'contact/messages_list.html', context, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def send_reply(request):
    """Envoyer une réponse à un message de contact"""

    try:
        data = json.loads(request.body)

        message_id = data.get('message_id')
        reply_subject = data.get('subject', '')
        reply_message = data.get('message', '')
        mark_as_resolved = data.get('mark_as_resolved', False)

        # Récupérer le message original
        try:
            original_message = Contact.objects.get(id=message_id)
        except Contact.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'Message non trouvé'
            }, status=404)

        # Simuler l'envoi d'email (à remplacer par un vrai service d'email)
        email_sent = send_email_reply(
            to_email=original_message.email,
            subject=reply_subject,
            message=reply_message,
            original_message=original_message
        )

        if email_sent:
            # Marquer comme résolu si demandé
            if mark_as_resolved:
                original_message.statut = 'resolu'
                original_message.traite_par = request.user.username if hasattr(request.user, 'username') else 'Système'
                original_message.save()

            return JsonResponse({
                'success': True,
                'message': 'Réponse envoyée avec succès',
                'email_sent': True,
                'marked_as_resolved': mark_as_resolved
            })
        else:
            return JsonResponse({
                'success': False,
                'error': 'Erreur lors de l\'envoi de l\'email'
            }, status=500)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'JSON invalide'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Erreur serveur: {str(e)}'
        }, status=500)


def send_email_reply(to_email, subject, message, original_message):
    """Envoyer un email de réponse (fonction simulée)"""

    try:
        from django.core.mail import send_mail
        from django.conf import settings

        # Construire le message complet
        full_message = f"""
Bonjour {original_message.nom},

{message}

---
Ceci est une réponse à votre message du {original_message.date_creation.strftime('%d/%m/%Y à %H:%M')} :
Sujet: {original_message.objet}

Cordialement,
L'équipe MeetVoice
        """

        # Envoyer l'email
        send_mail(
            subject=f"Re: {subject}",
            message=full_message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            recipient_list=[to_email],
            fail_silently=False,
        )

        return True

    except Exception as e:
        print(f"Erreur envoi email: {e}")
        # En mode développement, simuler l'envoi
        print(f"EMAIL SIMULÉ:")
        print(f"To: {to_email}")
        print(f"Subject: Re: {subject}")
        print(f"Message: {message}")
        return True  # Simuler le succès en développement
