{% extends 'base.html' %}

{% block page_title_main %}Dashboard{% endblock %}
{% block page_title_breadcrumb %}Dashboard{% endblock %}
{% block page_title_header %}Dashboard{% endblock %}
{% block page_icon %}<i class="fas fa-tachometer-alt me-2"></i>{% endblock %}

{% block content %}
<div class="row">
    <!-- Statistiques principales -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Utilisateurs Total
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_users }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Abonnements Actifs
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_subscriptions }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Revenus Total
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_revenue }}€</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-euro-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Articles Récents
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ recent_articles|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-newspaper fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Graphiques et tableaux -->
<div class="row">
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Statistiques en Temps Réel</h6>
            </div>
            <div class="card-body">
                <canvas id="realtimeChart"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Articles Récents</h6>
            </div>
            <div class="card-body">
                {% if recent_articles %}
                    <div class="list-group">
                        {% for article in recent_articles %}
                            <div class="list-group-item">
                                <h6 class="mb-1">{{ article.titre }}</h6>
                                <small class="text-muted">{{ article.date_publication|date:"d/m/Y H:i" }}</small>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">Aucun article récent</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Graphique en temps réel
const ctx = document.getElementById('realtimeChart').getContext('2d');
const realtimeChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: [],
        datasets: [{
            label: 'Utilisateurs Actifs',
            data: [],
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Mise à jour des données en temps réel
function updateStats() {
    fetch('{% url "dashboard:api_dashboard_stats" %}')
        .then(response => response.json())
        .then(data => {
            // Mettre à jour les statistiques
            document.querySelector('.h5:nth-of-type(1)').textContent = data.total_users;
            
            // Ajouter un point au graphique
            const now = new Date().toLocaleTimeString();
            realtimeChart.data.labels.push(now);
            realtimeChart.data.datasets[0].data.push(data.recent_signups);
            
            // Garder seulement les 10 derniers points
            if (realtimeChart.data.labels.length > 10) {
                realtimeChart.data.labels.shift();
                realtimeChart.data.datasets[0].data.shift();
            }
            
            realtimeChart.update();
        })
        .catch(error => console.error('Erreur:', error));
}

// Mettre à jour toutes les 30 secondes
setInterval(updateStats, 30000);
</script>
{% endblock %}
