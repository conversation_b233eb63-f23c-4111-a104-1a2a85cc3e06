from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required
from django.utils import timezone
from django.db.models import Sum
from datetime import timedelta
from django.http import JsonResponse

from compte.models import Compte
from abonnement.models import Facture
from actualite.models import Actualite
from evenement.models import Event


@staff_member_required
def dashboard_view(request):
    """Vue principale du dashboard avec métriques en temps réel"""
    context = {
        'total_users': Compte.objects.count(),
        'active_subscriptions': Facture.objects.filter(payer=True).count(),
        'total_revenue': Facture.objects.filter(payer=True).aggregate(Sum('prix_total_ttc'))['prix_total_ttc__sum'] or 0,
        'recent_articles': Actualite.objects.order_by('-date_publication')[:5],
    }
    return render(request, 'dashboard/dashboard.html', context)


@staff_member_required
def traffic_manager_view(request):
    """Vue du gestionnaire de trafic en temps réel"""
    # Statistiques de base
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)

    context = {
        'daily_visits': 0,  # À implémenter avec un système de tracking
        'weekly_visits': 0,
        'bounce_rate': 0,
        'active_users': Compte.objects.filter(last_login__gte=timezone.now() - timedelta(hours=24)).count(),
    }
    return render(request, 'dashboard/traffic_manager.html', context)


@staff_member_required
def google_analytics_view(request):
    """Vue d'intégration Google Analytics"""
    context = {
        'analytics_data': {},  # À implémenter avec l'API Google Analytics
    }
    return render(request, 'dashboard/google_analytics.html', context)


# API endpoints pour les données en temps réel
@staff_member_required
def api_dashboard_stats(request):
    """API pour les statistiques du dashboard"""
    stats = {
        'total_users': Compte.objects.count(),
        'active_subscriptions': Facture.objects.filter(payer=True).count(),
        'total_revenue': float(Facture.objects.filter(payer=True).aggregate(Sum('prix_total_ttc'))['prix_total_ttc__sum'] or 0),
        'recent_signups': Compte.objects.filter(date_joined__gte=timezone.now() - timedelta(days=7)).count(),
        'articles_count': Actualite.objects.count(),
        'published_articles': Actualite.objects.filter(status='published').count(),
    }
    return JsonResponse(stats)


@staff_member_required
def api_traffic_stats(request):
    """API pour les statistiques de trafic"""
    stats = {
        'active_users': Compte.objects.filter(last_login__gte=timezone.now() - timedelta(hours=1)).count(),
        'daily_users': Compte.objects.filter(last_login__gte=timezone.now() - timedelta(hours=24)).count(),
        'weekly_users': Compte.objects.filter(last_login__gte=timezone.now() - timedelta(days=7)).count(),
        'events_today': Event.objects.filter(created_at__date=timezone.now().date()).count(),
    }
    return JsonResponse(stats)
