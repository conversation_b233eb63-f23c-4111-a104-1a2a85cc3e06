from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import Document, DocumentCategory, DocumentView


@admin.register(DocumentCategory)
class DocumentCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'color_preview', 'icon_preview', 'documents_count', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {}
    
    def color_preview(self, obj):
        """Affiche un aperçu de la couleur"""
        return format_html(
            '<span style="background-color: {}; padding: 5px 10px; border-radius: 3px; color: white;">{}</span>',
            obj.color,
            obj.color
        )
    color_preview.short_description = 'Couleur'
    
    def icon_preview(self, obj):
        """Affiche un aperçu de l'icône"""
        return format_html('<i class="{}"></i> {}', obj.icon, obj.icon)
    icon_preview.short_description = 'Icône'
    
    def documents_count(self, obj):
        """Affiche le nombre de documents dans la catégorie"""
        count = obj.get_documents_count()
        if count > 0:
            url = reverse('admin:documentation_document_changelist') + f'?category__id__exact={obj.id}'
            return format_html('<a href="{}">{} document(s)</a>', url, count)
        return '0 document'
    documents_count.short_description = 'Documents'


@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'category', 'status_badge', 'priority_badge', 
        'author', 'view_count', 'created_at', 'updated_at'
    ]
    list_filter = ['status', 'priority', 'category', 'is_pinned', 'is_featured', 'created_at']
    search_fields = ['title', 'content', 'summary', 'tags']
    prepopulated_fields = {'slug': ('title',)}
    raw_id_fields = ['author', 'last_editor']
    readonly_fields = ['view_count', 'created_at', 'updated_at', 'published_at']
    
    fieldsets = (
        ('Informations principales', {
            'fields': ('title', 'slug', 'category', 'summary')
        }),
        ('Contenu', {
            'fields': ('content',),
            'classes': ('wide',)
        }),
        ('Métadonnées', {
            'fields': ('status', 'priority', 'tags', 'is_pinned', 'is_featured')
        }),
        ('Auteur et édition', {
            'fields': ('author', 'last_editor')
        }),
        ('Dates et statistiques', {
            'fields': ('created_at', 'updated_at', 'published_at', 'view_count'),
            'classes': ('collapse',)
        }),
    )
    
    def status_badge(self, obj):
        """Affiche le statut avec un badge coloré"""
        colors = {
            'draft': '#6c757d',
            'published': '#28a745',
            'archived': '#ffc107',
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; border-radius: 12px; font-size: 11px;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_badge.short_description = 'Statut'
    
    def priority_badge(self, obj):
        """Affiche la priorité avec un badge coloré"""
        colors = {
            'low': '#17a2b8',
            'normal': '#007bff',
            'high': '#ffc107',
            'urgent': '#dc3545',
        }
        color = colors.get(obj.priority, '#007bff')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; border-radius: 12px; font-size: 11px;">{}</span>',
            color,
            obj.get_priority_display()
        )
    priority_badge.short_description = 'Priorité'
    
    def save_model(self, request, obj, form, change):
        """Override pour définir l'auteur et le dernier éditeur"""
        if not change:  # Nouveau document
            obj.author = request.user
        obj.last_editor = request.user
        super().save_model(request, obj, form, change)
    
    actions = ['make_published', 'make_draft', 'make_archived']
    
    def make_published(self, request, queryset):
        """Action pour publier les documents sélectionnés"""
        updated = queryset.update(status='published')
        self.message_user(request, f'{updated} document(s) publié(s).')
    make_published.short_description = "Publier les documents sélectionnés"
    
    def make_draft(self, request, queryset):
        """Action pour mettre en brouillon les documents sélectionnés"""
        updated = queryset.update(status='draft')
        self.message_user(request, f'{updated} document(s) mis en brouillon.')
    make_draft.short_description = "Mettre en brouillon les documents sélectionnés"
    
    def make_archived(self, request, queryset):
        """Action pour archiver les documents sélectionnés"""
        updated = queryset.update(status='archived')
        self.message_user(request, f'{updated} document(s) archivé(s).')
    make_archived.short_description = "Archiver les documents sélectionnés"


@admin.register(DocumentView)
class DocumentViewAdmin(admin.ModelAdmin):
    list_display = ['document', 'user', 'viewed_at', 'ip_address']
    list_filter = ['viewed_at', 'document__category']
    search_fields = ['document__title', 'user__username']
    readonly_fields = ['viewed_at']
    raw_id_fields = ['document', 'user']
    
    def has_add_permission(self, request):
        """Empêche l'ajout manuel de vues"""
        return False
