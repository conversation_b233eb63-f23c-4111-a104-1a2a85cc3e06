from django import forms
from django.utils.text import slugify
from .models import Document, DocumentCategory


class DocumentForm(forms.ModelForm):
    """Formulaire pour créer/éditer un document"""
    
    class Meta:
        model = Document
        fields = [
            'title', 'slug', 'category', 'content', 'summary', 
            'status', 'priority', 'tags', 'is_pinned', 'is_featured'
        ]
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Titre du document...'
            }),
            'slug': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'url-du-document'
            }),
            'category': forms.Select(attrs={
                'class': 'form-select'
            }),
            'content': forms.Textarea(attrs={
                'class': 'form-control rich-editor',
                'rows': 20,
                'placeholder': 'Contenu du document...'
            }),
            'summary': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Résumé court du document (optionnel)...',
                'maxlength': 500
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-select'
            }),
            'tags': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'guide, formation, urgent (séparés par des virgules)'
            }),
            'is_pinned': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'is_featured': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Rendre le slug optionnel pour la création
        self.fields['slug'].required = False
        
        # Ajouter des classes CSS et des attributs
        for field_name, field in self.fields.items():
            if field_name not in ['is_pinned', 'is_featured']:
                field.widget.attrs.update({'class': field.widget.attrs.get('class', '') + ' mb-3'})
    
    def clean_slug(self):
        """Génère automatiquement le slug si non fourni"""
        slug = self.cleaned_data.get('slug')
        title = self.cleaned_data.get('title')
        
        if not slug and title:
            slug = slugify(title)
        
        # Vérifier l'unicité du slug
        if slug:
            existing = Document.objects.filter(slug=slug)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                counter = 1
                original_slug = slug
                while existing.filter(slug=slug).exists():
                    slug = f"{original_slug}-{counter}"
                    counter += 1
        
        return slug
    
    def clean_tags(self):
        """Nettoie et valide les tags"""
        tags = self.cleaned_data.get('tags', '')
        if tags:
            # Nettoyer les tags : supprimer espaces en trop, doublons
            tag_list = [tag.strip().lower() for tag in tags.split(',') if tag.strip()]
            tag_list = list(dict.fromkeys(tag_list))  # Supprimer doublons en gardant l'ordre
            return ', '.join(tag_list)
        return ''


class DocumentCategoryForm(forms.ModelForm):
    """Formulaire pour créer/éditer une catégorie"""
    
    class Meta:
        model = DocumentCategory
        fields = ['name', 'description', 'color', 'icon']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom de la catégorie...'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Description de la catégorie (optionnel)...'
            }),
            'color': forms.TextInput(attrs={
                'class': 'form-control',
                'type': 'color',
                'title': 'Choisir une couleur'
            }),
            'icon': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'fas fa-folder',
                'title': 'Classe FontAwesome (ex: fas fa-folder)'
            }),
        }


class DocumentSearchForm(forms.Form):
    """Formulaire de recherche de documents"""
    
    query = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Rechercher dans les documents...',
            'autocomplete': 'off'
        })
    )
    
    category = forms.ModelChoiceField(
        queryset=DocumentCategory.objects.all(),
        required=False,
        empty_label="Toutes les catégories",
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    status = forms.ChoiceField(
        choices=[('', 'Tous les statuts')] + Document.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    priority = forms.ChoiceField(
        choices=[('', 'Toutes les priorités')] + Document.PRIORITY_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    sort_by = forms.ChoiceField(
        choices=[
            ('-updated_at', 'Plus récent'),
            ('updated_at', 'Plus ancien'),
            ('title', 'Titre A-Z'),
            ('-title', 'Titre Z-A'),
            ('-view_count', 'Plus consulté'),
            ('-created_at', 'Date de création'),
        ],
        required=False,
        initial='-updated_at',
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
