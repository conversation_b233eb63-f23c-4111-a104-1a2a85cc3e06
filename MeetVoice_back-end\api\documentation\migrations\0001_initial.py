# Generated by Django 5.2.3 on 2025-06-22 07:30

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100, verbose_name='Nom de la catégorie')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('color', models.CharField(default='#007bff', help_text="Couleur d'affichage de la catégorie (ex: #007bff)", max_length=7, verbose_name='<PERSON>uleur (hex)')),
                ('icon', models.Char<PERSON>ield(default='fas fa-folder', help_text='Classe FontAwesome (ex: fas fa-folder)', max_length=50, verbose_name='Icône FontAwesome')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Créé le')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Modifié le')),
            ],
            options={
                'verbose_name': 'Catégorie de document',
                'verbose_name_plural': 'Catégories de documents',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='Titre du document')),
                ('slug', models.SlugField(max_length=200, unique=True, verbose_name='URL')),
                ('content', models.TextField(verbose_name='Contenu')),
                ('summary', models.TextField(blank=True, help_text='Résumé court du document (max 500 caractères)', max_length=500, verbose_name='Résumé')),
                ('status', models.CharField(choices=[('draft', 'Brouillon'), ('published', 'Publié'), ('archived', 'Archivé')], default='draft', max_length=20, verbose_name='Statut')),
                ('priority', models.CharField(choices=[('low', 'Basse'), ('normal', 'Normale'), ('high', 'Haute'), ('urgent', 'Urgente')], default='normal', max_length=20, verbose_name='Priorité')),
                ('tags', models.CharField(blank=True, help_text='Tags séparés par des virgules (ex: guide, formation, urgent)', max_length=500, verbose_name='Tags')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Créé le')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Modifié le')),
                ('published_at', models.DateTimeField(blank=True, null=True, verbose_name='Publié le')),
                ('view_count', models.PositiveIntegerField(default=0, verbose_name='Nombre de vues')),
                ('is_pinned', models.BooleanField(default=False, verbose_name='Épinglé')),
                ('is_featured', models.BooleanField(default=False, verbose_name='Mis en avant')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='authored_documents', to=settings.AUTH_USER_MODEL, verbose_name='Auteur')),
                ('last_editor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='edited_documents', to=settings.AUTH_USER_MODEL, verbose_name='Dernier éditeur')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='documents', to='documentation.documentcategory', verbose_name='Catégorie')),
            ],
            options={
                'verbose_name': 'Document',
                'verbose_name_plural': 'Documents',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='DocumentView',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('viewed_at', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='views', to='documentation.document')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Vue de document',
                'verbose_name_plural': 'Vues de documents',
            },
        ),
        migrations.AddIndex(
            model_name='document',
            index=models.Index(fields=['status', '-updated_at'], name='documentati_status_cc234d_idx'),
        ),
        migrations.AddIndex(
            model_name='document',
            index=models.Index(fields=['category', '-updated_at'], name='documentati_categor_2c3f84_idx'),
        ),
        migrations.AddIndex(
            model_name='document',
            index=models.Index(fields=['author', '-created_at'], name='documentati_author__f1803a_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='documentview',
            unique_together={('document', 'user')},
        ),
    ]
