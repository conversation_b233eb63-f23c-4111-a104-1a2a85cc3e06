from django.db import models
from django.conf import settings
from django.urls import reverse
from django.utils import timezone


class DocumentCategory(models.Model):
    """Catégories pour organiser les documents"""
    name = models.CharField(max_length=100, verbose_name="Nom de la catégorie")
    description = models.TextField(blank=True, verbose_name="Description")
    color = models.CharField(
        max_length=7, 
        default="#007bff", 
        verbose_name="Couleur (hex)",
        help_text="Couleur d'affichage de la catégorie (ex: #007bff)"
    )
    icon = models.CharField(
        max_length=50, 
        default="fas fa-folder", 
        verbose_name="Icône FontAwesome",
        help_text="Classe FontAwesome (ex: fas fa-folder)"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Créé le")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Modi<PERSON><PERSON> le")

    class Meta:
        verbose_name = "Catégorie de document"
        verbose_name_plural = "Catégories de documents"
        ordering = ['name']

    def __str__(self):
        return self.name

    def get_documents_count(self):
        """Retourne le nombre de documents dans cette catégorie"""
        return self.documents.count()


class Document(models.Model):
    """Modèle pour les documents de la base de connaissances interne"""
    
    STATUS_CHOICES = [
        ('draft', 'Brouillon'),
        ('published', 'Publié'),
        ('archived', 'Archivé'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', 'Basse'),
        ('normal', 'Normale'),
        ('high', 'Haute'),
        ('urgent', 'Urgente'),
    ]

    title = models.CharField(max_length=200, verbose_name="Titre du document")
    slug = models.SlugField(max_length=200, unique=True, verbose_name="URL")
    content = models.TextField(verbose_name="Contenu")
    summary = models.TextField(
        max_length=500, 
        blank=True, 
        verbose_name="Résumé",
        help_text="Résumé court du document (max 500 caractères)"
    )
    
    category = models.ForeignKey(
        DocumentCategory, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='documents',
        verbose_name="Catégorie"
    )
    
    status = models.CharField(
        max_length=20, 
        choices=STATUS_CHOICES, 
        default='draft',
        verbose_name="Statut"
    )
    
    priority = models.CharField(
        max_length=20, 
        choices=PRIORITY_CHOICES, 
        default='normal',
        verbose_name="Priorité"
    )
    
    tags = models.CharField(
        max_length=500, 
        blank=True, 
        verbose_name="Tags",
        help_text="Tags séparés par des virgules (ex: guide, formation, urgent)"
    )
    
    # Métadonnées
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='authored_documents',
        verbose_name="Auteur"
    )

    last_editor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='edited_documents',
        verbose_name="Dernier éditeur"
    )
    
    # Dates
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Créé le")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Modifié le")
    published_at = models.DateTimeField(null=True, blank=True, verbose_name="Publié le")
    
    # Statistiques
    view_count = models.PositiveIntegerField(default=0, verbose_name="Nombre de vues")
    
    # Permissions et accès
    is_pinned = models.BooleanField(default=False, verbose_name="Épinglé")
    is_featured = models.BooleanField(default=False, verbose_name="Mis en avant")
    
    class Meta:
        verbose_name = "Document"
        verbose_name_plural = "Documents"
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['status', '-updated_at']),
            models.Index(fields=['category', '-updated_at']),
            models.Index(fields=['author', '-created_at']),
        ]

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        """URL pour voir le document"""
        return reverse('documentation:document_detail', kwargs={'slug': self.slug})

    def get_edit_url(self):
        """URL pour éditer le document"""
        return reverse('documentation:document_edit', kwargs={'slug': self.slug})

    def get_delete_url(self):
        """URL pour supprimer le document"""
        return reverse('documentation:document_delete', kwargs={'slug': self.slug})

    def save(self, *args, **kwargs):
        """Override save pour gérer la date de publication"""
        if self.status == 'published' and not self.published_at:
            self.published_at = timezone.now()
        elif self.status != 'published':
            self.published_at = None
            
        super().save(*args, **kwargs)

    def increment_view_count(self):
        """Incrémenter le compteur de vues"""
        self.view_count += 1
        self.save(update_fields=['view_count'])

    def get_tags_list(self):
        """Retourne la liste des tags"""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
        return []

    def get_status_badge_class(self):
        """Retourne la classe CSS pour le badge de statut"""
        status_classes = {
            'draft': 'badge-secondary',
            'published': 'badge-success',
            'archived': 'badge-warning',
        }
        return status_classes.get(self.status, 'badge-secondary')

    def get_priority_badge_class(self):
        """Retourne la classe CSS pour le badge de priorité"""
        priority_classes = {
            'low': 'badge-info',
            'normal': 'badge-primary',
            'high': 'badge-warning',
            'urgent': 'badge-danger',
        }
        return priority_classes.get(self.priority, 'badge-primary')

    @property
    def is_published(self):
        """Vérifie si le document est publié"""
        return self.status == 'published'

    @property
    def reading_time(self):
        """Estime le temps de lecture en minutes (250 mots/minute)"""
        word_count = len(self.content.split())
        return max(1, round(word_count / 250))


class DocumentView(models.Model):
    """Modèle pour tracker les vues des documents"""
    document = models.ForeignKey(
        Document, 
        on_delete=models.CASCADE, 
        related_name='views'
    )
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    viewed_at = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    class Meta:
        verbose_name = "Vue de document"
        verbose_name_plural = "Vues de documents"
        unique_together = ['document', 'user']  # Un utilisateur = une vue par document

    def __str__(self):
        return f"{self.user.username} - {self.document.title}"
