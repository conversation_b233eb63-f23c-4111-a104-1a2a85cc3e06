from rest_framework import serializers
from django.contrib.auth.models import User
from .models import DocumentCategory, Document, DocumentView


class DocumentCategorySerializer(serializers.ModelSerializer):
    """Serializer pour les catégories de documents"""
    
    documents_count = serializers.SerializerMethodField()
    
    class Meta:
        model = DocumentCategory
        fields = [
            'id',
            'name',
            'description',
            'color',
            'icon',
            'is_active',
            'order',
            'documents_count',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_documents_count(self, obj):
        """Retourne le nombre de documents dans la catégorie"""
        return obj.get_documents_count()
    
    def validate_name(self, value):
        """Validation du nom de la catégorie"""
        if len(value.strip()) < 2:
            raise serializers.ValidationError("Le nom de la catégorie doit contenir au moins 2 caractères.")
        return value.strip()
    
    def validate_color(self, value):
        """Validation de la couleur (format hex)"""
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError("La couleur doit être au format hexadécimal (#RRGGBB).")
        return value


class DocumentCategoryListSerializer(serializers.ModelSerializer):
    """Serializer simplifié pour la liste des catégories"""
    
    documents_count = serializers.SerializerMethodField()
    
    class Meta:
        model = DocumentCategory
        fields = [
            'id',
            'name',
            'description',
            'color',
            'icon',
            'is_active',
            'documents_count'
        ]
    
    def get_documents_count(self, obj):
        """Retourne le nombre de documents dans la catégorie"""
        return obj.get_documents_count()


class AuthorSerializer(serializers.ModelSerializer):
    """Serializer pour les informations de l'auteur"""
    
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'full_name']
    
    def get_full_name(self, obj):
        """Retourne le nom complet de l'auteur"""
        if obj.first_name and obj.last_name:
            return f"{obj.first_name} {obj.last_name}"
        return obj.username


class DocumentSerializer(serializers.ModelSerializer):
    """Serializer pour les documents"""
    
    category_name = serializers.SerializerMethodField()
    author_name = serializers.SerializerMethodField()
    last_editor_name = serializers.SerializerMethodField()
    reading_time = serializers.ReadOnlyField()
    tags_list = serializers.SerializerMethodField()
    
    class Meta:
        model = Document
        fields = [
            'id',
            'title',
            'slug',
            'content',
            'summary',
            'category',
            'category_name',
            'author',
            'author_name',
            'last_editor',
            'last_editor_name',
            'status',
            'priority',
            'tags',
            'tags_list',
            'version',
            'is_pinned',
            'is_featured',
            'view_count',
            'reading_time',
            'created_at',
            'updated_at',
            'published_at'
        ]
        read_only_fields = [
            'id', 'slug', 'view_count', 'reading_time', 
            'created_at', 'updated_at', 'published_at'
        ]
    
    def get_category_name(self, obj):
        """Retourne le nom de la catégorie"""
        return obj.category.name if obj.category else None
    
    def get_author_name(self, obj):
        """Retourne le nom de l'auteur"""
        if obj.author:
            if obj.author.first_name and obj.author.last_name:
                return f"{obj.author.first_name} {obj.author.last_name}"
            return obj.author.username
        return None
    
    def get_last_editor_name(self, obj):
        """Retourne le nom du dernier éditeur"""
        if obj.last_editor:
            if obj.last_editor.first_name and obj.last_editor.last_name:
                return f"{obj.last_editor.first_name} {obj.last_editor.last_name}"
            return obj.last_editor.username
        return None
    
    def get_tags_list(self, obj):
        """Retourne la liste des tags"""
        if obj.tags:
            return [tag.strip() for tag in obj.tags.split(',') if tag.strip()]
        return []


class DocumentListSerializer(serializers.ModelSerializer):
    """Serializer simplifié pour la liste des documents"""
    
    category_name = serializers.SerializerMethodField()
    author_name = serializers.SerializerMethodField()
    reading_time = serializers.ReadOnlyField()
    
    class Meta:
        model = Document
        fields = [
            'id',
            'title',
            'slug',
            'summary',
            'category_name',
            'author_name',
            'status',
            'priority',
            'is_pinned',
            'is_featured',
            'view_count',
            'reading_time',
            'created_at',
            'updated_at'
        ]
    
    def get_category_name(self, obj):
        """Retourne le nom de la catégorie"""
        return obj.category.name if obj.category else None
    
    def get_author_name(self, obj):
        """Retourne le nom de l'auteur"""
        if obj.author:
            if obj.author.first_name and obj.author.last_name:
                return f"{obj.author.first_name} {obj.author.last_name}"
            return obj.author.username
        return None


class DocumentCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création de documents"""
    
    class Meta:
        model = Document
        fields = [
            'title',
            'content',
            'summary',
            'category',
            'status',
            'priority',
            'tags',
            'is_pinned',
            'is_featured'
        ]
    
    def validate_title(self, value):
        """Validation du titre"""
        if len(value.strip()) < 3:
            raise serializers.ValidationError("Le titre doit contenir au moins 3 caractères.")
        return value.strip()
    
    def validate_content(self, value):
        """Validation du contenu"""
        if len(value.strip()) < 10:
            raise serializers.ValidationError("Le contenu doit contenir au moins 10 caractères.")
        return value.strip()
    
    def validate_tags(self, value):
        """Validation et nettoyage des tags"""
        if value:
            # Nettoie les tags et supprime les doublons
            tag_list = [tag.strip() for tag in value.split(',') if tag.strip()]
            return ', '.join(list(dict.fromkeys(tag_list)))
        return value


class DocumentUpdateSerializer(serializers.ModelSerializer):
    """Serializer pour la mise à jour de documents"""
    
    class Meta:
        model = Document
        fields = [
            'title',
            'content',
            'summary',
            'category',
            'status',
            'priority',
            'tags',
            'is_pinned',
            'is_featured'
        ]
    
    def validate_title(self, value):
        """Validation du titre"""
        if len(value.strip()) < 3:
            raise serializers.ValidationError("Le titre doit contenir au moins 3 caractères.")
        return value.strip()
    
    def validate_content(self, value):
        """Validation du contenu"""
        if len(value.strip()) < 10:
            raise serializers.ValidationError("Le contenu doit contenir au moins 10 caractères.")
        return value.strip()


class DocumentViewSerializer(serializers.ModelSerializer):
    """Serializer pour les vues de documents"""
    
    document_title = serializers.SerializerMethodField()
    user_name = serializers.SerializerMethodField()
    
    class Meta:
        model = DocumentView
        fields = [
            'id',
            'document',
            'document_title',
            'user',
            'user_name',
            'viewed_at',
            'ip_address'
        ]
        read_only_fields = ['id', 'viewed_at']
    
    def get_document_title(self, obj):
        """Retourne le titre du document"""
        return obj.document.title
    
    def get_user_name(self, obj):
        """Retourne le nom de l'utilisateur"""
        if obj.user.first_name and obj.user.last_name:
            return f"{obj.user.first_name} {obj.user.last_name}"
        return obj.user.username


class DocumentStatsSerializer(serializers.ModelSerializer):
    """Serializer pour les statistiques des documents"""
    
    category_name = serializers.SerializerMethodField()
    author_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Document
        fields = [
            'id',
            'title',
            'category_name',
            'author_name',
            'status',
            'view_count',
            'is_pinned',
            'is_featured',
            'created_at',
            'updated_at'
        ]
    
    def get_category_name(self, obj):
        """Retourne le nom de la catégorie"""
        return obj.category.name if obj.category else None
    
    def get_author_name(self, obj):
        """Retourne le nom de l'auteur"""
        if obj.author:
            if obj.author.first_name and obj.author.last_name:
                return f"{obj.author.first_name} {obj.author.last_name}"
            return obj.author.username
        return None
