/* ===== NOTION-STYLE DOCUMENTATION ===== */
/* Version: 2024-12-19 - Cartes modernes + Barre de recherche en ligne */

/* Variables CSS pour cohérence */
:root {
    --notion-bg: #ffffff;
    --notion-sidebar-bg: #f7f6f3;
    --notion-text: #37352f;
    --notion-text-light: #787774;
    --notion-border: #e9e9e7;
    --notion-hover: #f1f1ef;
    --notion-blue: #2383e2;
    --notion-font: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol";
}

/* Reset et base */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--notion-font);
    background: var(--notion-bg);
    color: var(--notion-text);
    margin: 0;
    padding: 0;
    line-height: 1.5;
}

/* Layout principal */
.documentation-wrapper {
    display: flex;
    min-height: 100vh;
    background: var(--notion-bg);
    margin-top: 70px; /* Ajout pour éviter le chevauchement avec la navbar */
}

.documentation-sidebar {
    width: 280px;
    background: #2A1D34;
    border-right: 2px solid #000;
    padding: 30px 20px 20px 20px;
    position: fixed;
    top: 70px; /* Position sous la navbar */
    left: 0;
    height: calc(100vh - 70px); /* Ajustement pour la navbar */
    overflow-y: auto;
    z-index: 1000;
    box-sizing: border-box;
}

.documentation-content {
    flex: 1;
    margin-left: 280px;
    padding: 20px 32px; /* Réduction du padding top */
    min-height: calc(100vh - 70px);
    background: var(--notion-bg);
    max-width: calc(100vw - 280px);
}

/* Sidebar style original */
.sidebar-header {
    margin-bottom: 20px;
}

.sidebar-header h5 {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #4e385f;
}

.sidebar-header h5 i {
    margin-right: 8px;
}

.sidebar-nav .nav-link {
    color: rgba(255, 255, 255, 0.644);
    padding: 10px 15px;
    border-radius: 8px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
    font-size: 18px;
    font-family: roboto;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.sidebar-nav .nav-link:hover {
    background-color: #4e385f;
    color: #ffffff;
    text-decoration: none;
}

.sidebar-nav .nav-link.active {
    background-color: #4e385f;
    color: #ffffff;
}

.sidebar-nav .nav-link i {
    width: 20px;
    margin-right: 8px;
}

.sidebar-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #4e385f;
}

.sidebar-actions .btn-primary {
    background-color: #4e385f;
    border-color: #4e385f;
    color: #ffffff;
    margin-bottom: 10px;
    width: 100%;
}

.sidebar-actions .btn-primary:hover {
    background-color: #5d4370;
    border-color: #5d4370;
}

.sidebar-actions .btn {
    color: rgba(255, 255, 255, 0.644);
    border-color: #4e385f;
    width: 100%;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.sidebar-actions .btn:hover {
    background-color: #4e385f;
    border-color: #4e385f;
    color: #ffffff;
    text-decoration: none;
}

.sidebar-actions .btn-outline-primary {
    color: rgba(255, 255, 255, 0.644);
    border-color: #4e385f;
    background: transparent;
}

.sidebar-actions .btn-outline-primary:hover {
    background-color: #4e385f;
    border-color: #4e385f;
    color: #ffffff;
}

/* Page principale avec marges */
.notion-page {
    max-width: none;
    margin: 0 20px;
    padding: 20px 20px 30vh; /* Réduction du padding top */
    min-height: 100vh;
    width: calc(100% - 40px);
}

.notion-page-header {
    margin-bottom: 20px; /* Réduction de la marge */
}

.notion-page-title {
    font-size: 32px; /* Réduction de la taille */
    line-height: 1.2;
    font-weight: 700;
    color: var(--notion-text);
    margin: 0 0 6px 0; /* Réduction de la marge */
    word-break: break-word;
}

.notion-page-subtitle {
    font-size: 14px; /* Réduction de la taille */
    color: var(--notion-text-light);
    margin: 0;
    line-height: 1.5;
}

/* Dashboard style Notion */
.dashboard-header {
    margin-bottom: 32px;
}

.dashboard-header h2 {
    font-size: 40px;
    line-height: 1.2;
    font-weight: 700;
    color: var(--notion-text);
    margin: 0 0 8px 0;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 32px;
}

.stat-card {
    background: var(--notion-bg);
    border: 1px solid var(--notion-border);
    border-radius: 8px;
    padding: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.stat-card .stat-content h3 {
    margin: 0 0 4px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--notion-text);
}

.stat-card .stat-content p {
    margin: 0;
    color: var(--notion-text-light);
    font-size: 14px;
}

.stat-mini {
    text-align: center;
    padding: 12px;
    background: var(--notion-bg);
    border: 1px solid var(--notion-border);
    border-radius: 6px;
}

.stat-mini .stat-number {
    display: block;
    font-size: 20px;
    font-weight: 600;
    color: var(--notion-text);
}

.stat-mini .stat-label {
    display: block;
    color: var(--notion-text-light);
    font-size: 12px;
    margin-top: 4px;
}

/* Documents en cartes - 3 par rangée pour toute la largeur */
.documents-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-top: 20px;
}

.document-card {
    background: var(--notion-bg);
    border: 1px solid var(--notion-border);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-direction: column;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    min-height: 250px; /* Hauteur minimale pour plus d'espace */
}

.document-card:hover {
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.25);
    border-color: var(--notion-blue);
    color: inherit;
    text-decoration: none;
    transform: translateY(-6px);
}

.document-card:hover .document-card-body {
    background: linear-gradient(135deg, #fef0f0 0%, #fde8e8 100%);
}

.document-card-header {
    padding: 16px 16px 12px 16px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    background: #ffffff;
    flex-shrink: 0;
}

.document-header-left {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.document-icon {
    font-size: 18px;
    color: var(--notion-blue);
    flex-shrink: 0;
}

.document-category-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.document-category-badge {
    background: var(--notion-blue);
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.document-status-badge {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: var(--notion-text-light);
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--notion-text-light);
}

.status-dot.published {
    background: #00b894;
}

.status-dot.draft {
    background: #fdcb6e;
}

.status-dot.archived {
    background: #636e72;
}

.document-card-body {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #fef7f7 0%, #fdf2f2 100%);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    margin-top: auto;
}

.document-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--notion-text);
    line-height: 1.3;
    margin: 0 0 8px 0;
    word-break: break-word;
}

.document-summary {
    font-size: 13px;
    color: var(--notion-text-light);
    line-height: 1.4;
    margin: 0 0 12px 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    flex: 1;
}

.document-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 16px;
}

.document-tag {
    background: var(--notion-hover);
    color: var(--notion-text);
    padding: 2px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
}

.document-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 12px;
    border-top: 1px solid var(--notion-border);
    font-size: 12px;
    color: var(--notion-text-light);
}

.document-meta-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.document-meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.document-actions {
    display: flex;
    gap: 8px;
}

.document-action-btn {
    padding: 4px 8px;
    background: none;
    border: 1px solid var(--notion-border);
    border-radius: 4px;
    color: var(--notion-text-light);
    font-size: 11px;
    cursor: pointer;
    transition: all 0.15s ease;
}

.document-action-btn:hover {
    background: var(--notion-hover);
    color: var(--notion-text);
}

/* Liste alternative pour vue compacte */
.documents-list {
    background: var(--notion-bg);
    border: 1px solid var(--notion-border);
    border-radius: 8px;
    overflow: hidden;
}

.document-row {
    padding: 12px 16px;
    border-bottom: 1px solid var(--notion-border);
    display: flex;
    align-items: center;
    gap: 12px;
    transition: background-color 0.15s ease;
    text-decoration: none;
    color: inherit;
}

.document-row:last-child {
    border-bottom: none;
}

.document-row:hover {
    background: var(--notion-hover);
    color: inherit;
    text-decoration: none;
}

.document-row .document-icon {
    font-size: 16px;
    color: var(--notion-text-light);
    width: 20px;
    text-align: center;
}

.document-row .document-title {
    font-size: 14px;
    font-weight: 500;
    flex: 1;
    margin: 0;
}

.document-row .document-meta {
    font-size: 12px;
    color: var(--notion-text-light);
    white-space: nowrap;
}

/* Barre de recherche en une ligne */
.notion-search-bar {
    margin-bottom: 20px; /* Réduction de la marge */
    background: var(--notion-bg);
    border: 1px solid var(--notion-border);
    border-radius: 8px;
    padding: 12px; /* Réduction du padding */
}

.search-form {
    display: grid;
    grid-template-columns: 1fr auto auto auto auto;
    gap: 12px;
    align-items: center;
}

.search-input-group {
    position: relative;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--notion-text-light);
    font-size: 14px;
}

.search-input-group input {
    width: 100%;
    padding: 8px 12px 8px 36px;
    border: 1px solid var(--notion-border);
    border-radius: 6px;
    font-size: 14px;
    background: var(--notion-bg);
    color: var(--notion-text);
    transition: border-color 0.15s ease;
}

.search-input-group input:focus {
    outline: none;
    border-color: var(--notion-blue);
    box-shadow: 0 0 0 2px rgba(35, 131, 226, 0.1);
}

.search-form select {
    padding: 8px 12px;
    border: 1px solid var(--notion-border);
    border-radius: 6px;
    font-size: 14px;
    background: var(--notion-bg);
    color: var(--notion-text);
    min-width: 140px;
}

.search-btn {
    padding: 8px 16px;
    background: var(--notion-blue);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.15s ease;
    white-space: nowrap;
}

.search-btn:hover {
    background: #1a73d1;
}

/* État vide */
.no-documents {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    text-align: center;
}

.no-documents-content {
    max-width: 400px;
}

.no-documents-icon {
    font-size: 48px;
    color: var(--notion-text-light);
    margin-bottom: 16px;
}

.no-documents h3 {
    color: var(--notion-text);
    margin-bottom: 8px;
}

.no-documents p {
    color: var(--notion-text-light);
    margin-bottom: 24px;
}

/* Pagination style Notion */
.notion-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin-top: 32px;
    padding: 16px 0;
}

.pagination-btn {
    padding: 8px 16px;
    background: var(--notion-bg);
    border: 1px solid var(--notion-border);
    border-radius: 6px;
    color: var(--notion-text);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.15s ease;
}

.pagination-btn:hover {
    background: var(--notion-hover);
    color: var(--notion-text);
    text-decoration: none;
}

.pagination-info {
    font-size: 14px;
    color: var(--notion-text-light);
}

/* Responsive pour les cartes */
@media (max-width: 1200px) {
    .documents-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .documents-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .notion-page {
        padding: 20px 16px 30vh;
    }

    .document-card {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }

    .document-card:hover {
        box-shadow: 0 10px 28px rgba(0, 0, 0, 0.18);
        transform: translateY(-3px);
    }
}

/* Badges personnalisés */
.badge-secondary { background-color: #6c757d !important; }
.badge-success { background-color: #28a745 !important; }
.badge-warning { background-color: #ffc107 !important; color: #212529 !important; }
.badge-danger { background-color: #dc3545 !important; }
.badge-info { background-color: #17a2b8 !important; }
.badge-primary { background-color: #007bff !important; }

/* Formulaires */
.document-form .form-label {
    font-weight: 600;
    color: #495057;
}

.document-form .rich-editor {
    min-height: 400px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.category-form .color-preview {
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}

.category-form .icon-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.icon-suggestions .btn {
    margin: 2px;
}

/* Document detail avec marges */
.document-detail {
    max-width: none;
    margin: 0 20px;
    padding: 32px 20px 30vh;
    width: calc(100% - 40px);
}

.document-header {
    margin-bottom: 32px;
    border: none;
    background: none;
    padding: 0;
}

.document-title {
    font-size: 40px;
    line-height: 1.2;
    font-weight: 700;
    color: var(--notion-text);
    margin: 0 0 8px 0;
    word-break: break-word;
}

.document-summary {
    font-size: 16px;
    line-height: 1.5;
    color: var(--notion-text-light);
    margin: 0 0 16px 0;
}

.document-meta {
    font-size: 14px;
    color: var(--notion-text-light);
    border: none;
    padding: 0;
    margin-bottom: 32px;
}

/* Contenu Markdown style Notion */
.document-content {
    font-size: 16px;
    line-height: 1.6;
    color: var(--notion-text);
}

.document-content h1,
.document-content h2,
.document-content h3,
.document-content h4,
.document-content h5,
.document-content h6 {
    color: var(--notion-text);
    font-weight: 600;
    margin: 32px 0 8px 0;
    line-height: 1.3;
}

.document-content h1 { font-size: 32px; }
.document-content h2 { font-size: 24px; }
.document-content h3 { font-size: 20px; }
.document-content h4 { font-size: 18px; }
.document-content h5 { font-size: 16px; }
.document-content h6 { font-size: 16px; }

.document-content p {
    margin: 8px 0;
    line-height: 1.6;
}

.document-content ul,
.document-content ol {
    margin: 8px 0;
    padding-left: 24px;
}

.document-content li {
    margin: 2px 0;
    line-height: 1.6;
}

.document-content blockquote {
    border-left: 3px solid var(--notion-border);
    padding: 16px 0 16px 16px;
    margin: 16px 0;
    background: var(--notion-hover);
    border-radius: 0 4px 4px 0;
    font-style: normal;
    color: var(--notion-text);
}

.document-content code {
    background: rgba(135, 131, 120, 0.15);
    color: #eb5757;
    border-radius: 3px;
    font-size: 85%;
    padding: 0.2em 0.4em;
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
}

.document-content pre {
    background: var(--notion-hover);
    border: 1px solid var(--notion-border);
    border-radius: 6px;
    padding: 16px;
    overflow-x: auto;
    margin: 16px 0;
    font-size: 14px;
}

.document-content pre code {
    background: none;
    color: var(--notion-text);
    padding: 0;
    border-radius: 0;
}

/* Améliorations Markdown */
.markdown-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.markdown-content th,
.markdown-content td {
    border: 1px solid #dee2e6;
    padding: 12px 15px;
    text-align: left;
}

.markdown-content th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.markdown-content tr:nth-child(even) {
    background-color: #f8f9fa;
}

.markdown-content hr {
    border: none;
    border-top: 2px solid #e9ecef;
    margin: 30px 0;
}

.markdown-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin: 20px 0;
    display: block;
}

.markdown-content a {
    color: #007bff;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all 0.3s ease;
}

.markdown-content a:hover {
    border-bottom-color: #007bff;
    text-decoration: none;
}

/* Highlight.js pour la coloration syntaxique */
.hljs {
    background: #f8f9fa !important;
    color: #495057 !important;
    border-radius: 8px;
    padding: 15px;
}

/* Amélioration des listes */
.markdown-content ul li::marker {
    color: #007bff;
}

.markdown-content ol li::marker {
    color: #007bff;
    font-weight: 600;
}

/* Table des matières */
.table-of-contents {
    position: sticky;
    top: 20px;
}

.toc-list {
    list-style: none;
    padding: 0;
}

.toc-item {
    margin-bottom: 8px;
}

.toc-item a {
    color: #6c757d;
    padding: 5px 10px;
    display: block;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.toc-item a:hover {
    background-color: #f8f9fa;
    color: #007bff;
}

.toc-h1 { margin-left: 0; }
.toc-h2 { margin-left: 15px; }
.toc-h3 { margin-left: 30px; }
.toc-h4 { margin-left: 45px; }
.toc-h5 { margin-left: 60px; }
.toc-h6 { margin-left: 75px; }

/* Catégories */
.category-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.category-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.category-item {
    padding: 10px 0;
}

.color-preview {
    display: inline-block;
    border-radius: 50%;
}

/* Documents similaires */
.related-document h6 a {
    color: #495057;
    font-weight: 600;
}

.related-document h6 a:hover {
    color: #007bff;
}

/* Responsive */
@media (max-width: 992px) {
    .documentation-sidebar {
        width: 100%;
        position: relative;
        height: auto;
        border-right: none;
        border-bottom: 2px solid #000;
        z-index: 1001;
    }

    .documentation-content {
        margin-left: 0;
        width: 100%;
        padding: 15px;
    }
    
    .document-title {
        font-size: 2em;
    }
    
    .stat-card {
        margin-bottom: 15px;
    }
}

@media (max-width: 768px) {
    .document-detail .document-header {
        padding: 20px;
    }
    
    .document-title {
        font-size: 1.8em;
    }
    
    .document-summary {
        font-size: 1.1em;
    }
    
    .document-content {
        font-size: 1em;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.document-card,
.category-card,
.stat-card {
    animation: fadeIn 0.6s ease-out;
}

/* Scrollbar personnalisée */
.documentation-sidebar::-webkit-scrollbar {
    width: 6px;
}

.documentation-sidebar::-webkit-scrollbar-track {
    background: #1a1125;
    border-radius: 3px;
}

.documentation-sidebar::-webkit-scrollbar-thumb {
    background: #4e385f;
    border-radius: 3px;
}

.documentation-sidebar::-webkit-scrollbar-thumb:hover {
    background: #5d4370;
}
