/**
 * Documentation JavaScript
 * Fonctionnalités pour l'interface de documentation
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // ===== ÉDITEUR RICHE =====
    initRichEditor();
    
    // ===== RECHERCHE EN TEMPS RÉEL =====
    initLiveSearch();
    
    // ===== ACTIONS AJAX =====
    initAjaxActions();
    
    // ===== RACCOURCIS CLAVIER =====
    initKeyboardShortcuts();
    
    // ===== TOOLTIPS ET POPOVERS =====
    initTooltips();
    
    // ===== AUTO-SAVE =====
    initAutoSave();
});

/**
 * Initialise l'éditeur de texte riche
 */
function initRichEditor() {
    const editor = document.querySelector('.rich-editor');
    if (!editor) return;
    
    // Ajouter une barre d'outils simple
    const toolbar = document.createElement('div');
    toolbar.className = 'editor-toolbar mb-2';
    toolbar.innerHTML = `
        <div class="btn-group btn-group-sm" role="group">
            <button type="button" class="btn btn-outline-secondary" data-command="bold" title="Gras (Ctrl+B)">
                <i class="fas fa-bold"></i>
            </button>
            <button type="button" class="btn btn-outline-secondary" data-command="italic" title="Italique (Ctrl+I)">
                <i class="fas fa-italic"></i>
            </button>
            <button type="button" class="btn btn-outline-secondary" data-command="underline" title="Souligné (Ctrl+U)">
                <i class="fas fa-underline"></i>
            </button>
        </div>
        <div class="btn-group btn-group-sm ms-2" role="group">
            <button type="button" class="btn btn-outline-secondary" data-command="insertUnorderedList" title="Liste à puces">
                <i class="fas fa-list-ul"></i>
            </button>
            <button type="button" class="btn btn-outline-secondary" data-command="insertOrderedList" title="Liste numérotée">
                <i class="fas fa-list-ol"></i>
            </button>
        </div>
        <div class="btn-group btn-group-sm ms-2" role="group">
            <button type="button" class="btn btn-outline-secondary" data-command="formatBlock" data-value="h2" title="Titre 2">
                H2
            </button>
            <button type="button" class="btn btn-outline-secondary" data-command="formatBlock" data-value="h3" title="Titre 3">
                H3
            </button>
            <button type="button" class="btn btn-outline-secondary" data-command="formatBlock" data-value="p" title="Paragraphe">
                P
            </button>
        </div>
        <div class="btn-group btn-group-sm ms-2" role="group">
            <button type="button" class="btn btn-outline-secondary" data-command="createLink" title="Lien">
                <i class="fas fa-link"></i>
            </button>
            <button type="button" class="btn btn-outline-secondary" data-command="insertHorizontalRule" title="Ligne horizontale">
                <i class="fas fa-minus"></i>
            </button>
        </div>
    `;
    
    editor.parentNode.insertBefore(toolbar, editor);
    
    // Rendre l'éditeur contenteditable
    editor.contentEditable = true;
    editor.style.border = '1px solid #ced4da';
    editor.style.borderRadius = '0.375rem';
    editor.style.padding = '15px';
    editor.style.minHeight = '400px';
    
    // Gestionnaires d'événements pour la barre d'outils
    toolbar.addEventListener('click', function(e) {
        const button = e.target.closest('button');
        if (!button) return;
        
        e.preventDefault();
        
        const command = button.dataset.command;
        const value = button.dataset.value;
        
        if (command === 'createLink') {
            const url = prompt('URL du lien:');
            if (url) {
                document.execCommand(command, false, url);
            }
        } else if (value) {
            document.execCommand(command, false, value);
        } else {
            document.execCommand(command, false, null);
        }
        
        editor.focus();
    });
    
    // Synchroniser avec le textarea original
    const originalTextarea = document.getElementById(editor.id.replace('_editor', ''));
    if (originalTextarea) {
        originalTextarea.style.display = 'none';
        
        // Charger le contenu initial
        if (originalTextarea.value) {
            editor.innerHTML = originalTextarea.value.replace(/\n/g, '<br>');
        }
        
        // Synchroniser les changements
        editor.addEventListener('input', function() {
            originalTextarea.value = editor.innerHTML;
        });
        
        // Synchroniser avant soumission du formulaire
        const form = editor.closest('form');
        if (form) {
            form.addEventListener('submit', function() {
                originalTextarea.value = editor.innerHTML;
            });
        }
    }
}

/**
 * Initialise la recherche en temps réel
 */
function initLiveSearch() {
    const searchInput = document.querySelector('input[name="query"]');
    if (!searchInput) return;
    
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        
        searchTimeout = setTimeout(() => {
            const query = this.value.trim();
            if (query.length >= 2) {
                performLiveSearch(query);
            }
        }, 300);
    });
}

/**
 * Effectue une recherche en temps réel
 */
function performLiveSearch(query) {
    // Ici on pourrait implémenter une recherche AJAX
    // Pour l'instant, on se contente de surligner les résultats
    highlightSearchResults(query);
}

/**
 * Surligne les résultats de recherche
 */
function highlightSearchResults(query) {
    const cards = document.querySelectorAll('.document-card, .category-card');
    
    cards.forEach(card => {
        const title = card.querySelector('.card-title, h6');
        const content = card.querySelector('.card-text');
        
        if (title || content) {
            const text = (title ? title.textContent : '') + ' ' + (content ? content.textContent : '');
            const matches = text.toLowerCase().includes(query.toLowerCase());
            
            card.style.opacity = matches ? '1' : '0.5';
            card.style.transform = matches ? 'scale(1)' : 'scale(0.98)';
        }
    });
}

/**
 * Initialise les actions AJAX
 */
function initAjaxActions() {
    // Toggle status
    document.addEventListener('click', function(e) {
        if (e.target.closest('.toggle-status')) {
            e.preventDefault();
            const button = e.target.closest('.toggle-status');
            const slug = button.dataset.slug;
            
            toggleDocumentStatus(slug);
        }
        
        if (e.target.closest('.toggle-pin')) {
            e.preventDefault();
            const button = e.target.closest('.toggle-pin');
            const slug = button.dataset.slug;
            
            toggleDocumentPin(slug);
        }
    });
}

/**
 * Toggle le statut d'un document
 */
function toggleDocumentStatus(slug) {
    fetch(`/documentation/documents/${slug}/toggle-status/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Erreur lors de la modification', 'error');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showNotification('Erreur de connexion', 'error');
    });
}

/**
 * Toggle l'épinglage d'un document
 */
function toggleDocumentPin(slug) {
    fetch(`/documentation/documents/${slug}/toggle-pin/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Erreur lors de la modification', 'error');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showNotification('Erreur de connexion', 'error');
    });
}

/**
 * Récupère le token CSRF
 */
function getCsrfToken() {
    const token = document.querySelector('[name=csrfmiddlewaretoken]');
    return token ? token.value : '';
}

/**
 * Affiche une notification
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-suppression après 5 secondes
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Initialise les raccourcis clavier
 */
function initKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+S pour sauvegarder
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            const saveButton = document.querySelector('button[type="submit"]');
            if (saveButton) {
                saveButton.click();
            }
        }
        
        // Ctrl+N pour nouveau document
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            const newButton = document.querySelector('a[href*="create"]');
            if (newButton) {
                window.location.href = newButton.href;
            }
        }
        
        // Échap pour annuler
        if (e.key === 'Escape') {
            const cancelButton = document.querySelector('a[href*="list"], a[href*="dashboard"]');
            if (cancelButton && confirm('Voulez-vous vraiment quitter sans sauvegarder ?')) {
                window.location.href = cancelButton.href;
            }
        }
    });
}

/**
 * Initialise les tooltips et popovers
 */
function initTooltips() {
    // Initialiser les tooltips Bootstrap
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"], [title]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Initialise l'auto-sauvegarde
 */
function initAutoSave() {
    const form = document.querySelector('.document-form, .category-form');
    if (!form) return;
    
    const inputs = form.querySelectorAll('input, textarea, select');
    let autoSaveTimeout;
    
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(autoSaveTimeout);
            
            autoSaveTimeout = setTimeout(() => {
                saveFormData(form);
            }, 5000); // Auto-save après 5 secondes d'inactivité
        });
    });
}

/**
 * Sauvegarde les données du formulaire en localStorage
 */
function saveFormData(form) {
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    const storageKey = `documentation_autosave_${window.location.pathname}`;
    localStorage.setItem(storageKey, JSON.stringify(data));
    
    showNotification('Brouillon sauvegardé automatiquement', 'info');
}

/**
 * Restaure les données du formulaire depuis localStorage
 */
function restoreFormData(form) {
    const storageKey = `documentation_autosave_${window.location.pathname}`;
    const savedData = localStorage.getItem(storageKey);
    
    if (savedData) {
        try {
            const data = JSON.parse(savedData);
            
            for (let [key, value] of Object.entries(data)) {
                const input = form.querySelector(`[name="${key}"]`);
                if (input && !input.value) {
                    input.value = value;
                }
            }
            
            showNotification('Brouillon restauré', 'info');
        } catch (error) {
            console.error('Erreur lors de la restauration:', error);
        }
    }
}

/**
 * Nettoie les données auto-sauvegardées
 */
function clearAutoSaveData() {
    const storageKey = `documentation_autosave_${window.location.pathname}`;
    localStorage.removeItem(storageKey);
}
