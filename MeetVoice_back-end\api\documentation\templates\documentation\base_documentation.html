{% extends "base.html" %}
{% load static %}

{% block head %}
<link rel="stylesheet" href="{% static 'documentation/css/documentation.css' %}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
{% endblock %}

{% block content %}
<div class="documentation-wrapper">
    <!-- Sidebar de navigation style Notion -->
    <div class="documentation-sidebar">
        <div class="sidebar-header">
            <h5><i class="fas fa-book"></i>Documentation</h5>
        </div>
        
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {% if 'dashboard' in request.resolver_match.url_name %}active{% endif %}"
                       href="{% url 'documentation:dashboard' %}">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'document_list' in request.resolver_match.url_name %}active{% endif %}"
                       href="{% url 'documentation:document_list' %}">
                        <i class="fas fa-file-alt"></i>Documents
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'category_list' in request.resolver_match.url_name %}active{% endif %}"
                       href="{% url 'documentation:category_list' %}">
                        <i class="fas fa-folder"></i>Catégories
                    </a>
                </li>
            </ul>
        </nav>
        
        <!-- Actions rapides -->
        <div class="sidebar-actions">
            <a href="{% url 'documentation:document_create' %}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i>Nouveau document
            </a>
            <a href="{% url 'documentation:category_create' %}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-folder-plus"></i>Nouvelle catégorie
            </a>
        </div>
    </div>
    
    <!-- Contenu principal -->
    <div class="documentation-content">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        {% block documentation_content %}
        {% endblock %}
    </div>
</div>

<script>
// Initialiser la coloration syntaxique
document.addEventListener('DOMContentLoaded', function() {
    hljs.highlightAll();
});
</script>
{% endblock %}
