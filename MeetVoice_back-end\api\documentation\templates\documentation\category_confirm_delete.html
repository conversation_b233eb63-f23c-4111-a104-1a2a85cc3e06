{% extends "documentation/base_documentation.html" %}
{% load static %}

{% block documentation_content %}
<div class="delete-confirmation">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Confirmer la suppression
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-folder fa-3x text-danger mb-3"></i>
                        <h4>Supprimer la catégorie</h4>
                        <p class="text-muted">Cette action est irréversible</p>
                    </div>
                    
                    <div class="category-info bg-light p-3 rounded mb-4">
                        <div class="d-flex align-items-center mb-2">
                            <i class="{{ category.icon }} me-2" style="color: {{ category.color }}; font-size: 1.5em;"></i>
                            <h6 class="mb-0"><strong>{{ category.name }}</strong></h6>
                        </div>
                        {% if category.description %}
                            <p class="text-muted mb-2">{{ category.description }}</p>
                        {% endif %}
                        <div class="category-meta">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>Créée le {{ category.created_at|date:"d/m/Y" }}
                                <i class="fas fa-file-alt ms-3 me-1"></i>{{ category.get_documents_count }} document{{ category.get_documents_count|pluralize }}
                            </small>
                        </div>
                    </div>
                    
                    {% if category.get_documents_count > 0 %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Attention !</strong> Cette catégorie contient <strong>{{ category.get_documents_count }} document{{ category.get_documents_count|pluralize }}</strong>.
                            <br><br>
                            La suppression de cette catégorie entraînera :
                            <ul class="mb-0 mt-2">
                                <li>La suppression de la catégorie</li>
                                <li>Les documents associés ne seront <strong>pas supprimés</strong></li>
                                <li>Les documents seront classés comme "Sans catégorie"</li>
                            </ul>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Cette catégorie ne contient aucun document. Sa suppression n'affectera aucun contenu.
                        </div>
                    {% endif %}
                    
                    <form method="post" class="text-center">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger me-3">
                            <i class="fas fa-trash me-2"></i>Oui, supprimer la catégorie
                        </button>
                        <a href="{% url 'documentation:category_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
