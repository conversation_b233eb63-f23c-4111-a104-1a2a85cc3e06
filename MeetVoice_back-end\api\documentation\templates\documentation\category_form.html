{% extends "documentation/base_documentation.html" %}
{% load static %}

{% block documentation_content %}
<div class="category-form-header">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-folder me-2"></i>{{ title }}</h2>
            {% if category %}
                <p class="text-muted">Modification de la catégorie "{{ category.name }}"</p>
            {% else %}
                <p class="text-muted">Création d'une nouvelle catégorie</p>
            {% endif %}
        </div>
        <a href="{% url 'documentation:category_list' %}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-1"></i>Retour aux catégories
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-cog me-2"></i>Informations de la catégorie</h5>
            </div>
            <div class="card-body">
                <form method="post" class="category-form">
                    {% csrf_token %}
                    
                    <!-- Nom de la catégorie -->
                    <div class="mb-4">
                        <label for="{{ form.name.id_for_label }}" class="form-label">
                            <strong>Nom de la catégorie</strong>
                        </label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="text-danger">{{ form.name.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Description -->
                    <div class="mb-4">
                        <label for="{{ form.description.id_for_label }}" class="form-label">
                            Description (optionnel)
                        </label>
                        {{ form.description }}
                        <div class="form-text">Description de la catégorie pour aider à l'organisation</div>
                        {% if form.description.errors %}
                            <div class="text-danger">{{ form.description.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <!-- Couleur -->
                        <div class="col-md-6 mb-4">
                            <label for="{{ form.color.id_for_label }}" class="form-label">
                                Couleur
                            </label>
                            <div class="d-flex align-items-center">
                                {{ form.color }}
                                <div class="color-preview ms-3" id="colorPreview" 
                                     style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid #dee2e6; background-color: {{ form.color.value|default:'#007bff' }};"></div>
                            </div>
                            <div class="form-text">Couleur d'affichage de la catégorie</div>
                            {% if form.color.errors %}
                                <div class="text-danger">{{ form.color.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- Icône -->
                        <div class="col-md-6 mb-4">
                            <label for="{{ form.icon.id_for_label }}" class="form-label">
                                Icône FontAwesome
                            </label>
                            <div class="d-flex align-items-center">
                                {{ form.icon }}
                                <div class="icon-preview ms-3" id="iconPreview">
                                    <i class="{{ form.icon.value|default:'fas fa-folder' }}" style="font-size: 1.5em; color: {{ form.color.value|default:'#007bff' }};"></i>
                                </div>
                            </div>
                            <div class="form-text">
                                Classe FontAwesome (ex: fas fa-folder, fas fa-book, fas fa-cog)
                                <br>
                                <a href="https://fontawesome.com/icons" target="_blank" class="text-decoration-none">
                                    <i class="fas fa-external-link-alt me-1"></i>Voir toutes les icônes
                                </a>
                            </div>
                            {% if form.icon.errors %}
                                <div class="text-danger">{{ form.icon.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Aperçu -->
                    <div class="mb-4">
                        <label class="form-label">Aperçu</label>
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <i id="previewIcon" class="{{ form.icon.value|default:'fas fa-folder' }} me-2" 
                                       style="color: {{ form.color.value|default:'#007bff' }}; font-size: 1.2em;"></i>
                                    <span id="previewName" class="fw-bold">{{ form.name.value|default:'Nom de la catégorie' }}</span>
                                    <span class="badge ms-2" id="previewBadge" 
                                          style="background-color: {{ form.color.value|default:'#007bff' }};">
                                        Catégorie
                                    </span>
                                </div>
                                <p class="text-muted mt-2 mb-0" id="previewDescription">
                                    {{ form.description.value|default:'Description de la catégorie' }}
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'documentation:category_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>{{ submit_text }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        {% if category %}
            <!-- Informations sur la catégorie existante -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle me-2"></i>Informations</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <div class="mb-2">
                                    <strong>Créée le :</strong> {{ category.created_at|date:"d/m/Y H:i" }}
                                </div>
                                <div class="mb-2">
                                    <strong>Modifiée le :</strong> {{ category.updated_at|date:"d/m/Y H:i" }}
                                </div>
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <div class="mb-2">
                                    <strong>Documents :</strong> {{ category.get_documents_count }}
                                </div>
                                {% if category.get_documents_count > 0 %}
                                    <a href="{% url 'documentation:document_list' %}?category={{ category.id }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>Voir les documents
                                    </a>
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameField = document.getElementById('{{ form.name.id_for_label }}');
    const descriptionField = document.getElementById('{{ form.description.id_for_label }}');
    const colorField = document.getElementById('{{ form.color.id_for_label }}');
    const iconField = document.getElementById('{{ form.icon.id_for_label }}');
    
    const colorPreview = document.getElementById('colorPreview');
    const iconPreview = document.getElementById('iconPreview');
    const previewIcon = document.getElementById('previewIcon');
    const previewName = document.getElementById('previewName');
    const previewDescription = document.getElementById('previewDescription');
    const previewBadge = document.getElementById('previewBadge');
    
    // Mise à jour de l'aperçu en temps réel
    function updatePreview() {
        const name = nameField.value || 'Nom de la catégorie';
        const description = descriptionField.value || 'Description de la catégorie';
        const color = colorField.value || '#007bff';
        const icon = iconField.value || 'fas fa-folder';
        
        // Mise à jour des aperçus
        colorPreview.style.backgroundColor = color;
        iconPreview.innerHTML = `<i class="${icon}" style="font-size: 1.5em; color: ${color};"></i>`;
        
        previewIcon.className = icon + ' me-2';
        previewIcon.style.color = color;
        previewName.textContent = name;
        previewDescription.textContent = description;
        previewBadge.style.backgroundColor = color;
    }
    
    // Écouteurs d'événements
    nameField.addEventListener('input', updatePreview);
    descriptionField.addEventListener('input', updatePreview);
    colorField.addEventListener('input', updatePreview);
    iconField.addEventListener('input', updatePreview);
    
    // Suggestions d'icônes populaires
    const iconSuggestions = [
        'fas fa-folder', 'fas fa-book', 'fas fa-cog', 'fas fa-users', 'fas fa-chart-bar',
        'fas fa-lightbulb', 'fas fa-tools', 'fas fa-graduation-cap', 'fas fa-heart',
        'fas fa-star', 'fas fa-flag', 'fas fa-home', 'fas fa-envelope', 'fas fa-phone'
    ];
    
    // Créer un conteneur pour les suggestions
    const suggestionsContainer = document.createElement('div');
    suggestionsContainer.className = 'icon-suggestions mt-2';
    suggestionsContainer.innerHTML = '<small class="text-muted">Suggestions : </small>';
    
    iconSuggestions.forEach(iconClass => {
        const suggestion = document.createElement('button');
        suggestion.type = 'button';
        suggestion.className = 'btn btn-sm btn-outline-secondary me-1 mb-1';
        suggestion.innerHTML = `<i class="${iconClass}"></i>`;
        suggestion.title = iconClass;
        suggestion.addEventListener('click', function() {
            iconField.value = iconClass;
            updatePreview();
        });
        suggestionsContainer.appendChild(suggestion);
    });
    
    iconField.parentNode.appendChild(suggestionsContainer);
});
</script>
{% endblock %}
