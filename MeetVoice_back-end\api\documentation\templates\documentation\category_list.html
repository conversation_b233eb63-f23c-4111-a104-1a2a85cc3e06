{% extends "documentation/base_documentation.html" %}
{% load static %}

{% block documentation_content %}
<div class="category-list-header">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-folder me-2"></i>Catégories</h2>
            <p class="text-muted">Organisez vos documents par catégories</p>
        </div>
        <a href="{% url 'documentation:category_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Nouvelle catégorie
        </a>
    </div>
</div>

{% if categories %}
    <div class="row">
        {% for category in categories %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card category-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="{{ category.icon }} me-2" style="color: {{ category.color }}; font-size: 1.2em;"></i>
                            <h6 class="mb-0">{{ category.name }}</h6>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="{% url 'documentation:category_edit' category.pk %}">
                                        <i class="fas fa-edit me-2"></i>Modifier
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'documentation:document_list' %}?category={{ category.id }}">
                                        <i class="fas fa-eye me-2"></i>Voir les documents
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-danger" href="{% url 'documentation:category_delete' category.pk %}">
                                        <i class="fas fa-trash me-2"></i>Supprimer
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        {% if category.description %}
                            <p class="card-text text-muted">{{ category.description|truncatewords:15 }}</p>
                        {% else %}
                            <p class="card-text text-muted fst-italic">Aucune description</p>
                        {% endif %}
                        
                        <div class="category-stats">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted">
                                    <i class="fas fa-file-alt me-1"></i>
                                    {{ category.documents_count }} document{{ category.documents_count|pluralize }}
                                </span>
                                <div class="color-preview" style="background-color: {{ category.color }}; width: 20px; height: 20px; border-radius: 50%;"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                Créée le {{ category.created_at|date:"d/m/Y" }}
                            </small>
                            <a href="{% url 'documentation:document_list' %}?category={{ category.id }}" 
                               class="btn btn-sm btn-outline-primary">
                                Voir les documents
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-folder fa-3x text-muted mb-3"></i>
        <h4>Aucune catégorie créée</h4>
        <p class="text-muted">Créez votre première catégorie pour organiser vos documents.</p>
        <a href="{% url 'documentation:category_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Créer une catégorie
        </a>
    </div>
{% endif %}

<!-- Statistiques des catégories -->
{% if categories %}
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar me-2"></i>Statistiques des catégories</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Catégorie</th>
                                    <th>Description</th>
                                    <th class="text-center">Documents</th>
                                    <th class="text-center">Couleur</th>
                                    <th class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in categories %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="{{ category.icon }} me-2" style="color: {{ category.color }};"></i>
                                                <strong>{{ category.name }}</strong>
                                            </div>
                                        </td>
                                        <td>
                                            {% if category.description %}
                                                {{ category.description|truncatewords:10 }}
                                            {% else %}
                                                <span class="text-muted fst-italic">Aucune description</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-primary">{{ category.documents_count }}</span>
                                        </td>
                                        <td class="text-center">
                                            <div class="color-preview mx-auto" 
                                                 style="background-color: {{ category.color }}; width: 25px; height: 25px; border-radius: 50%; border: 2px solid #dee2e6;"
                                                 title="{{ category.color }}"></div>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'documentation:category_edit' category.pk %}" 
                                                   class="btn btn-outline-primary" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'documentation:document_list' %}?category={{ category.id }}" 
                                                   class="btn btn-outline-info" title="Voir les documents">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'documentation:category_delete' category.pk %}" 
                                                   class="btn btn-outline-danger" title="Supprimer">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}
