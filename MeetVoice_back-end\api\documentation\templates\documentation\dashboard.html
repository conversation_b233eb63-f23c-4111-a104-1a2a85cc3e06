{% extends "documentation/base_documentation.html" %}
{% load static %}

{% block documentation_content %}
<div class="notion-page">
    <div class="notion-page-header">
        <h1 class="notion-page-title">Documentation</h1>
        <p class="notion-page-subtitle">Vue d'ensemble de votre base de connaissances interne</p>
    </div>

<!-- Statistiques -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-primary">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="stat-content">
                <h3>{{ stats.total_documents }}</h3>
                <p>Documents total</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
                <h3>{{ stats.published_documents }}</h3>
                <p>Publiés</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-warning">
                <i class="fas fa-edit"></i>
            </div>
            <div class="stat-content">
                <h3>{{ stats.draft_documents }}</h3>
                <p>Brouillons</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-info">
                <i class="fas fa-eye"></i>
            </div>
            <div class="stat-content">
                <h3>{{ stats.total_views }}</h3>
                <p>Vues totales</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Documents récents -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock me-2"></i>Documents récents</h5>
            </div>
            <div class="card-body">
                {% if recent_documents %}
                    {% for document in recent_documents %}
                        <div class="document-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6><a href="{{ document.get_absolute_url }}">{{ document.title }}</a></h6>
                                    <small class="text-muted">
                                        Par {{ document.author.username }} • {{ document.updated_at|date:"d/m/Y H:i" }}
                                    </small>
                                </div>
                                <span class="badge badge-{{ document.get_status_badge_class }}">
                                    {{ document.get_status_display }}
                                </span>
                            </div>
                        </div>
                        {% if not forloop.last %}<hr>{% endif %}
                    {% endfor %}
                {% else %}
                    <p class="text-muted">Aucun document récent</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Documents populaires -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-fire me-2"></i>Documents populaires</h5>
            </div>
            <div class="card-body">
                {% if popular_documents %}
                    {% for document in popular_documents %}
                        <div class="document-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6><a href="{{ document.get_absolute_url }}">{{ document.title }}</a></h6>
                                    <small class="text-muted">
                                        {{ document.view_count }} vue{{ document.view_count|pluralize }}
                                    </small>
                                </div>
                                {% if document.category %}
                                    <span class="badge" style="background-color: {{ document.category.color }};">
                                        {{ document.category.name }}
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        {% if not forloop.last %}<hr>{% endif %}
                    {% endfor %}
                {% else %}
                    <p class="text-muted">Aucun document consulté</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Documents épinglés -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-thumbtack me-2"></i>Documents épinglés</h5>
            </div>
            <div class="card-body">
                {% if pinned_documents %}
                    {% for document in pinned_documents %}
                        <div class="document-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6><a href="{{ document.get_absolute_url }}">{{ document.title }}</a></h6>
                                    <small class="text-muted">
                                        {{ document.updated_at|date:"d/m/Y" }}
                                    </small>
                                </div>
                                <span class="badge badge-{{ document.get_priority_badge_class }}">
                                    {{ document.get_priority_display }}
                                </span>
                            </div>
                        </div>
                        {% if not forloop.last %}<hr>{% endif %}
                    {% endfor %}
                {% else %}
                    <p class="text-muted">Aucun document épinglé</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Catégories -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-folder me-2"></i>Catégories</h5>
            </div>
            <div class="card-body">
                {% if categories_stats %}
                    {% for category in categories_stats %}
                        <div class="category-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="{{ category.icon }} me-2" style="color: {{ category.color }};"></i>
                                    <span>{{ category.name }}</span>
                                </div>
                                <span class="badge bg-secondary">
                                    {{ category.documents_count }} doc{{ category.documents_count|pluralize }}
                                </span>
                            </div>
                        </div>
                        {% if not forloop.last %}<hr>{% endif %}
                    {% endfor %}
                {% else %}
                    <p class="text-muted">Aucune catégorie créée</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt me-2"></i>Actions rapides</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="{% url 'documentation:document_create' %}" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-plus me-2"></i>Nouveau document
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'documentation:category_create' %}" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-folder-plus me-2"></i>Nouvelle catégorie
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'documentation:document_list' %}" class="btn btn-outline-secondary w-100 mb-2">
                            <i class="fas fa-search me-2"></i>Rechercher
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'documentation:category_list' %}" class="btn btn-outline-info w-100 mb-2">
                            <i class="fas fa-cogs me-2"></i>Gérer catégories
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}
