{% extends "documentation/base_documentation.html" %}
{% load static %}

{% block documentation_content %}
<div class="document-detail">
    <!-- En-tête style Notion -->
    <div class="document-header">
        <!-- Métadonn<PERSON> discrètes -->
        <div class="document-meta">
            <i class="fas fa-user"></i> {{ document.author.username }} •
            <i class="fas fa-calendar"></i> {{ document.created_at|date:"d/m/Y" }}
            {% if document.updated_at != document.created_at %}
                • <i class="fas fa-edit"></i> Modifié le {{ document.updated_at|date:"d/m/Y" }}
            {% endif %}
            • <i class="fas fa-eye"></i> {{ document.view_count }} vue{{ document.view_count|pluralize }}
        </div>

        <!-- Titre principal -->
        <h1 class="document-title">{{ document.title }}</h1>

        <!-- Résumé -->
        {% if document.summary %}
            <p class="document-summary">{{ document.summary }}</p>
        {% endif %}

    </div>

    <!-- Contenu principal style Notion -->
    <div class="document-content">
        {{ document.content_html|safe }}
    </div>
</div>
{% endblock %}
