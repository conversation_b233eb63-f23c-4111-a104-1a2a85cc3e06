{% extends "documentation/base_documentation.html" %}
{% load static %}

{% block extra_head %}
<!-- <PERSON><PERSON><PERSON> (Markdown Editor) -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/easymde@2.18.0/dist/easymde.min.css">
<script src="https://cdn.jsdelivr.net/npm/easymde@2.18.0/dist/easymde.min.js"></script>
{% endblock %}

{% block documentation_content %}
<div class="document-form-header">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-edit me-2"></i>{{ title }}</h2>
            {% if document %}
                <p class="text-muted">Modification du document "{{ document.title }}"</p>
            {% else %}
                <p class="text-muted">Création d'un nouveau document</p>
            {% endif %}
        </div>
        <div>
            {% if document %}
                <a href="{{ document.get_absolute_url }}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-eye me-1"></i>Aperçu
                </a>
            {% endif %}
            <a href="{% url 'documentation:document_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i>Retour à la liste
            </a>
        </div>
    </div>
</div>

<form method="post" class="document-form">
    {% csrf_token %}
    
    <div class="row">
        <!-- Colonne principale -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-file-alt me-2"></i>Contenu du document</h5>
                </div>
                <div class="card-body">
                    <!-- Titre -->
                    <div class="mb-3">
                        <label for="{{ form.title.id_for_label }}" class="form-label">
                            <strong>Titre du document</strong>
                        </label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <div class="text-danger">{{ form.title.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Slug -->
                    <div class="mb-3">
                        <label for="{{ form.slug.id_for_label }}" class="form-label">
                            URL du document
                        </label>
                        {{ form.slug }}
                        <div class="form-text">Laissez vide pour générer automatiquement depuis le titre</div>
                        {% if form.slug.errors %}
                            <div class="text-danger">{{ form.slug.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Résumé -->
                    <div class="mb-3">
                        <label for="{{ form.summary.id_for_label }}" class="form-label">
                            Résumé (optionnel)
                        </label>
                        {{ form.summary }}
                        <div class="form-text">Résumé court du document (max 500 caractères)</div>
                        {% if form.summary.errors %}
                            <div class="text-danger">{{ form.summary.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Contenu -->
                    <div class="mb-3">
                        <label for="{{ form.content.id_for_label }}" class="form-label">
                            <strong>Contenu</strong>
                            <small class="text-muted">(Markdown supporté)</small>
                        </label>
                        {{ form.content }}
                        {% if form.content.errors %}
                            <div class="text-danger">{{ form.content.errors }}</div>
                        {% endif %}
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Utilisez la syntaxe Markdown pour formater votre contenu.
                            <a href="#" onclick="showMarkdownHelp(); return false;">Aide Markdown</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Métadonnées -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-cog me-2"></i>Paramètres</h5>
                </div>
                <div class="card-body">
                    <!-- Catégorie -->
                    <div class="mb-3">
                        <label for="{{ form.category.id_for_label }}" class="form-label">
                            Catégorie
                        </label>
                        {{ form.category }}
                        {% if form.category.errors %}
                            <div class="text-danger">{{ form.category.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Statut -->
                    <div class="mb-3">
                        <label for="{{ form.status.id_for_label }}" class="form-label">
                            Statut
                        </label>
                        {{ form.status }}
                        {% if form.status.errors %}
                            <div class="text-danger">{{ form.status.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Priorité -->
                    <div class="mb-3">
                        <label for="{{ form.priority.id_for_label }}" class="form-label">
                            Priorité
                        </label>
                        {{ form.priority }}
                        {% if form.priority.errors %}
                            <div class="text-danger">{{ form.priority.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Tags -->
                    <div class="mb-3">
                        <label for="{{ form.tags.id_for_label }}" class="form-label">
                            Tags
                        </label>
                        {{ form.tags }}
                        <div class="form-text">Séparés par des virgules</div>
                        {% if form.tags.errors %}
                            <div class="text-danger">{{ form.tags.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Options avancées -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-star me-2"></i>Options avancées</h5>
                </div>
                <div class="card-body">
                    <!-- Épinglé -->
                    <div class="form-check mb-3">
                        {{ form.is_pinned }}
                        <label class="form-check-label" for="{{ form.is_pinned.id_for_label }}">
                            <i class="fas fa-thumbtack me-1"></i>Épingler ce document
                        </label>
                        <div class="form-text">Les documents épinglés apparaissent en premier</div>
                        {% if form.is_pinned.errors %}
                            <div class="text-danger">{{ form.is_pinned.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Mis en avant -->
                    <div class="form-check mb-3">
                        {{ form.is_featured }}
                        <label class="form-check-label" for="{{ form.is_featured.id_for_label }}">
                            <i class="fas fa-star me-1"></i>Mettre en avant
                        </label>
                        <div class="form-text">Documents importants mis en avant</div>
                        {% if form.is_featured.errors %}
                            <div class="text-danger">{{ form.is_featured.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="card">
                <div class="card-body">
                    <button type="submit" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-save me-2"></i>{{ submit_text }}
                    </button>
                    
                    {% if document %}
                        <a href="{{ document.get_absolute_url }}" class="btn btn-outline-secondary w-100 mb-2">
                            <i class="fas fa-eye me-2"></i>Aperçu
                        </a>
                    {% endif %}
                    
                    <a href="{% url 'documentation:document_list' %}" class="btn btn-outline-danger w-100">
                        <i class="fas fa-times me-2"></i>Annuler
                    </a>
                </div>
            </div>
            
            {% if document %}
                <!-- Informations -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6><i class="fas fa-info-circle me-2"></i>Informations</h6>
                    </div>
                    <div class="card-body">
                        <small class="text-muted">
                            <div class="mb-2">
                                <strong>Créé par :</strong> {{ document.author.username }}
                            </div>
                            <div class="mb-2">
                                <strong>Créé le :</strong> {{ document.created_at|date:"d/m/Y H:i" }}
                            </div>
                            {% if document.last_editor %}
                                <div class="mb-2">
                                    <strong>Modifié par :</strong> {{ document.last_editor.username }}
                                </div>
                            {% endif %}
                            <div class="mb-2">
                                <strong>Modifié le :</strong> {{ document.updated_at|date:"d/m/Y H:i" }}
                            </div>
                            <div class="mb-2">
                                <strong>Vues :</strong> {{ document.view_count }}
                            </div>
                            {% if document.reading_time %}
                                <div>
                                    <strong>Temps de lecture :</strong> {{ document.reading_time }} min
                                </div>
                            {% endif %}
                        </small>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</form>

<!-- Modal d'aide Markdown -->
<div class="modal fade" id="markdownHelpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fab fa-markdown me-2"></i>Guide Markdown
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Formatage de base</h6>
                        <pre><code># Titre 1
## Titre 2
### Titre 3

**Gras**
*Italique*
~~Barré~~

- Liste à puces
1. Liste numérotée

[Lien](https://example.com)
![Image](url-image.jpg)</code></pre>
                    </div>
                    <div class="col-md-6">
                        <h6>Éléments avancés</h6>
                        <pre><code>```python
# Bloc de code
def hello():
    print("Hello!")
```

> Citation

| Tableau | Colonne |
|---------|---------|
| Ligne 1 | Valeur  |

---
Ligne horizontale</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialiser l'éditeur Markdown
    const contentTextarea = document.getElementById('{{ form.content.id_for_label }}');
    if (contentTextarea) {
        const easyMDE = new EasyMDE({
            element: contentTextarea,
            spellChecker: false,
            placeholder: "Écrivez votre documentation ici...",
            toolbar: [
                "bold", "italic", "strikethrough", "|",
                "heading-1", "heading-2", "heading-3", "|",
                "quote", "unordered-list", "ordered-list", "|",
                "link", "image", "table", "|",
                "code", "horizontal-rule", "|",
                "preview", "side-by-side", "fullscreen", "|",
                "guide"
            ],
            status: ["autosave", "lines", "words", "cursor"],
            autosave: {
                enabled: true,
                uniqueId: "document-content-{{ form.instance.pk|default:'new' }}",
                delay: 1000,
            },
            renderingConfig: {
                singleLineBreaks: false,
                codeSyntaxHighlighting: true,
            }
        });
    }

    // Auto-génération du slug depuis le titre
    const titleField = document.getElementById('{{ form.title.id_for_label }}');
    const slugField = document.getElementById('{{ form.slug.id_for_label }}');

    if (titleField && slugField && !slugField.value) {
        titleField.addEventListener('input', function() {
            const title = this.value;
            const slug = title
                .toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            slugField.value = slug;
        });
    }

    // Compteur de caractères pour le résumé
    const summaryField = document.getElementById('{{ form.summary.id_for_label }}');
    if (summaryField) {
        const maxLength = 500;
        const counter = document.createElement('div');
        counter.className = 'form-text text-end';
        summaryField.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = maxLength - summaryField.value.length;
            counter.textContent = `${remaining} caractères restants`;
            counter.className = remaining < 50 ? 'form-text text-end text-warning' : 'form-text text-end';
        }

        summaryField.addEventListener('input', updateCounter);
        updateCounter();
    }
});

// Fonction pour afficher l'aide Markdown
function showMarkdownHelp() {
    const modal = new bootstrap.Modal(document.getElementById('markdownHelpModal'));
    modal.show();
}
</script>
{% endblock %}
