{% extends "documentation/base_documentation.html" %}
{% load static %}

{% block documentation_content %}
<div class="notion-page">
    <div class="notion-page-header">
        <h1 class="notion-page-title">Documents</h1>
        <p class="notion-page-subtitle">G<PERSON>rez votre base de connaissances interne</p>
    </div>

    <!-- Barre de recherche en une ligne -->
    <div class="notion-search-bar">
        <form method="get" class="search-form">
            <div class="search-input-group">
                <i class="fas fa-search search-icon"></i>
                {{ search_form.query }}
            </div>
            {{ search_form.category }}
            {{ search_form.status }}
            {{ search_form.priority }}
            <button type="submit" class="search-btn">Rechercher</button>
        </form>
    </div>

    <!-- Documents en cartes modernes -->
    {% if page_obj %}
        <div class="documents-grid">
            {% for document in page_obj %}
                <a href="{{ document.get_absolute_url }}" class="document-card">
                    <div class="document-card-header">
                        <div class="document-header-left">
                            <i class="fas fa-file-alt document-icon"></i>
                            <div class="document-category-info">
                                {% if document.category %}
                                    <span class="document-category-badge">{{ document.category.name }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="document-status-badge">
                            <span class="status-dot {{ document.status }}"></span>
                            <span>{{ document.get_status_display }}</span>
                        </div>
                    </div>

                    <div class="document-card-body">
                        <h3 class="document-title">{{ document.title }}</h3>

                        {% if document.summary %}
                            <p class="document-summary">{{ document.summary|truncatewords:20 }}</p>
                        {% endif %}

                        {% if document.tags %}
                            <div class="document-tags">
                                {% for tag in document.get_tags_list|slice:":3" %}
                                    <span class="document-tag">{{ tag }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="document-card-footer">
                            <div class="document-meta-info">
                                <div class="document-meta-item">
                                    <i class="fas fa-user"></i>
                                    <span>{{ document.author.username }}</span>
                                </div>
                                <div class="document-meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <span>{{ document.updated_at|date:"d/m/Y" }}</span>
                                </div>
                                <div class="document-meta-item">
                                    <i class="fas fa-eye"></i>
                                    <span>{{ document.view_count }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            {% endfor %}
        </div>
    {% else %}
        <div class="no-documents">
            <div class="no-documents-content">
                <i class="fas fa-file-alt no-documents-icon"></i>
                <h3>Aucun document trouvé</h3>
                <p>Commencez par créer votre premier document.</p>
                <a href="{% url 'documentation:document_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Créer un document
                </a>
            </div>
        </div>
    {% endif %}

    <!-- Pagination style Notion -->
    {% if page_obj.has_other_pages %}
        <div class="notion-pagination">
            {% if page_obj.has_previous %}
                <a href="?{% for key, value in current_filters.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" class="pagination-btn">
                    ← Précédent
                </a>
            {% endif %}

            <span class="pagination-info">
                Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages }}
            </span>

            {% if page_obj.has_next %}
                <a href="?{% for key, value in current_filters.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" class="pagination-btn">
                    Suivant →
                </a>
            {% endif %}
        </div>
    {% endif %}
</div>
{% endblock %}
