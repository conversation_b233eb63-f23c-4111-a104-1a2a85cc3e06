"""
Template tags et filtres pour le rendu Markdown
"""

from django import template
from django.utils.safestring import mark_safe
import re

register = template.Library()

# Test simple pour vérifier que le module fonctionne
@register.simple_tag
def test_tag():
    return "Template tags fonctionnent !"

@register.filter(name='markdown')
def markdown_filter(text):
    """
    Filtre pour convertir le Markdown en HTML
    Version simplifiée sans dépendance externe
    """
    if not text:
        return ''
    
    # Échapper le HTML existant
    import html
    text = html.escape(text)
    
    # Titres
    text = re.sub(r'^### (.*$)', r'<h3>\1</h3>', text, flags=re.MULTILINE)
    text = re.sub(r'^## (.*$)', r'<h2>\1</h2>', text, flags=re.MULTILINE)
    text = re.sub(r'^# (.*$)', r'<h1>\1</h1>', text, flags=re.MULTILINE)
    
    # Gras et italique
    text = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', text)
    text = re.sub(r'\*(.*?)\*', r'<em>\1</em>', text)
    
    # Code inline
    text = re.sub(r'`(.*?)`', r'<code>\1</code>', text)
    
    # Liens
    text = re.sub(r'\[([^\]]+)\]\(([^)]+)\)', r'<a href="\2">\1</a>', text)
    
    # Listes à puces
    lines = text.split('\n')
    in_list = False
    result_lines = []
    
    for line in lines:
        if line.strip().startswith('- '):
            if not in_list:
                result_lines.append('<ul>')
                in_list = True
            content = line.strip()[2:]
            result_lines.append(f'<li>{content}</li>')
        elif line.strip().startswith('* '):
            if not in_list:
                result_lines.append('<ul>')
                in_list = True
            content = line.strip()[2:]
            result_lines.append(f'<li>{content}</li>')
        else:
            if in_list:
                result_lines.append('</ul>')
                in_list = False
            if line.strip():
                result_lines.append(f'<p>{line}</p>')
            else:
                result_lines.append('<br>')
    
    if in_list:
        result_lines.append('</ul>')
    
    # Citations
    text = '\n'.join(result_lines)
    text = re.sub(r'^> (.*$)', r'<blockquote>\1</blockquote>', text, flags=re.MULTILINE)
    
    # Blocs de code
    text = re.sub(r'```(\w+)?\n(.*?)\n```', r'<pre><code>\2</code></pre>', text, flags=re.DOTALL)
    
    # Lignes horizontales
    text = re.sub(r'^---$', r'<hr>', text, flags=re.MULTILINE)
    
    return mark_safe(text)


@register.filter(name='markdown_advanced')
def markdown_advanced_filter(text):
    """
    Filtre Markdown avancé avec support complet
    Utilise la bibliothèque markdown si disponible
    """
    if not text:
        return ''
    
    try:
        import markdown
        from markdown.extensions import codehilite, tables, toc
        
        md = markdown.Markdown(
            extensions=[
                'markdown.extensions.extra',
                'markdown.extensions.codehilite',
                'markdown.extensions.toc',
                'markdown.extensions.tables',
                'markdown.extensions.fenced_code',
            ],
            extension_configs={
                'markdown.extensions.codehilite': {
                    'css_class': 'highlight',
                    'use_pygments': True,
                },
                'markdown.extensions.toc': {
                    'permalink': True,
                },
            }
        )
        
        html = md.convert(text)
        return mark_safe(html)
        
    except ImportError:
        # Fallback vers le filtre simple
        return markdown_filter(text)


@register.inclusion_tag('documentation/partials/toc.html')
def render_toc(content):
    """
    Génère une table des matières à partir du contenu Markdown
    """
    if not content:
        return {'toc_items': []}
    
    # Extraire les titres
    import re
    headings = re.findall(r'^(#{1,6})\s+(.+)$', content, re.MULTILINE)
    
    toc_items = []
    for level_chars, title in headings:
        level = len(level_chars)
        slug = re.sub(r'[^\w\s-]', '', title.lower())
        slug = re.sub(r'[-\s]+', '-', slug).strip('-')
        
        toc_items.append({
            'level': level,
            'title': title,
            'slug': slug,
        })
    
    return {'toc_items': toc_items}


@register.simple_tag
def reading_time(content):
    """
    Calcule le temps de lecture estimé
    """
    if not content:
        return 0
    
    # Compter les mots (approximatif)
    import re
    words = len(re.findall(r'\w+', content))
    
    # 250 mots par minute en moyenne
    minutes = max(1, round(words / 250))
    
    return minutes


@register.filter
def truncate_words_html(value, arg):
    """
    Tronque le texte en préservant le HTML
    """
    try:
        length = int(arg)
    except ValueError:
        return value
    
    if not value:
        return ''
    
    # Supprimer les balises HTML pour compter les mots
    import re
    text_only = re.sub(r'<[^>]+>', '', value)
    words = text_only.split()
    
    if len(words) <= length:
        return value
    
    # Tronquer et ajouter "..."
    truncated_words = words[:length]
    truncated_text = ' '.join(truncated_words)
    
    return mark_safe(f"{truncated_text}...")
