from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils.text import slugify
from .models import Document, DocumentCategory, DocumentView


class DocumentationModelsTest(TestCase):
    """Tests pour les modèles de documentation"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )
        
        self.category = DocumentCategory.objects.create(
            name='Test Category',
            description='Test description',
            color='#007bff',
            icon='fas fa-test'
        )
        
        self.document = Document.objects.create(
            title='Test Document',
            slug='test-document',
            content='Test content',
            summary='Test summary',
            category=self.category,
            author=self.user,
            status='published'
        )
    
    def test_document_creation(self):
        """Test de création d'un document"""
        self.assertEqual(self.document.title, 'Test Document')
        self.assertEqual(self.document.slug, 'test-document')
        self.assertEqual(self.document.author, self.user)
        self.assertEqual(self.document.category, self.category)
        self.assertTrue(self.document.is_published)
    
    def test_document_str(self):
        """Test de la représentation string du document"""
        self.assertEqual(str(self.document), 'Test Document')
    
    def test_document_absolute_url(self):
        """Test de l'URL absolue du document"""
        expected_url = reverse('documentation:document_detail', kwargs={'slug': 'test-document'})
        self.assertEqual(self.document.get_absolute_url(), expected_url)
    
    def test_document_reading_time(self):
        """Test du calcul du temps de lecture"""
        # Document avec peu de mots
        self.document.content = 'Test content'
        self.assertEqual(self.document.reading_time, 1)  # Minimum 1 minute
        
        # Document avec beaucoup de mots
        self.document.content = ' '.join(['word'] * 500)  # 500 mots
        self.assertEqual(self.document.reading_time, 2)  # 500/250 = 2 minutes
    
    def test_document_tags_list(self):
        """Test de la gestion des tags"""
        self.document.tags = 'tag1, tag2, tag3'
        expected_tags = ['tag1', 'tag2', 'tag3']
        self.assertEqual(self.document.get_tags_list(), expected_tags)
        
        # Test avec tags vides
        self.document.tags = ''
        self.assertEqual(self.document.get_tags_list(), [])
    
    def test_category_creation(self):
        """Test de création d'une catégorie"""
        self.assertEqual(self.category.name, 'Test Category')
        self.assertEqual(self.category.color, '#007bff')
        self.assertEqual(self.category.icon, 'fas fa-test')
    
    def test_category_documents_count(self):
        """Test du comptage des documents dans une catégorie"""
        self.assertEqual(self.category.get_documents_count(), 1)
        
        # Ajouter un autre document
        Document.objects.create(
            title='Another Document',
            slug='another-document',
            content='Another content',
            category=self.category,
            author=self.user
        )
        
        self.assertEqual(self.category.get_documents_count(), 2)
    
    def test_document_view_tracking(self):
        """Test du tracking des vues"""
        # Créer une vue
        view = DocumentView.objects.create(
            document=self.document,
            user=self.user,
            ip_address='127.0.0.1'
        )
        
        self.assertEqual(view.document, self.document)
        self.assertEqual(view.user, self.user)
        
        # Test de l'unicité (un utilisateur = une vue par document)
        with self.assertRaises(Exception):
            DocumentView.objects.create(
                document=self.document,
                user=self.user,
                ip_address='127.0.0.1'
            )


class DocumentationViewsTest(TestCase):
    """Tests pour les vues de documentation"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )
        
        self.category = DocumentCategory.objects.create(
            name='Test Category',
            description='Test description'
        )
        
        self.document = Document.objects.create(
            title='Test Document',
            slug='test-document',
            content='Test content',
            category=self.category,
            author=self.user,
            status='published'
        )
    
    def test_dashboard_view_requires_staff(self):
        """Test que le dashboard nécessite d'être staff"""
        url = reverse('documentation:dashboard')
        
        # Sans connexion
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirection vers login
        
        # Avec utilisateur non-staff
        non_staff_user = User.objects.create_user(
            username='nonstaff',
            password='testpass123',
            is_staff=False
        )
        self.client.login(username='nonstaff', password='testpass123')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Accès refusé
        
        # Avec utilisateur staff
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
    
    def test_dashboard_view_content(self):
        """Test du contenu du dashboard"""
        self.client.login(username='testuser', password='testpass123')
        url = reverse('documentation:dashboard')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Dashboard Documentation')
        self.assertContains(response, 'Test Document')
        self.assertIn('stats', response.context)
    
    def test_document_list_view(self):
        """Test de la vue liste des documents"""
        self.client.login(username='testuser', password='testpass123')
        url = reverse('documentation:document_list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Document')
        self.assertIn('page_obj', response.context)
    
    def test_document_detail_view(self):
        """Test de la vue détail d'un document"""
        self.client.login(username='testuser', password='testpass123')
        url = reverse('documentation:document_detail', kwargs={'slug': 'test-document'})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Document')
        self.assertContains(response, 'Test content')
        
        # Vérifier que la vue a été enregistrée
        self.assertTrue(
            DocumentView.objects.filter(
                document=self.document,
                user=self.user
            ).exists()
        )
    
    def test_document_create_view(self):
        """Test de la vue création de document"""
        self.client.login(username='testuser', password='testpass123')
        url = reverse('documentation:document_create')
        
        # GET
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Créer un document')
        
        # POST
        data = {
            'title': 'New Document',
            'content': 'New content',
            'status': 'draft',
            'priority': 'normal'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 302)  # Redirection après création
        
        # Vérifier que le document a été créé
        new_document = Document.objects.get(title='New Document')
        self.assertEqual(new_document.author, self.user)
        self.assertEqual(new_document.slug, 'new-document')
    
    def test_document_edit_view(self):
        """Test de la vue édition de document"""
        self.client.login(username='testuser', password='testpass123')
        url = reverse('documentation:document_edit', kwargs={'slug': 'test-document'})
        
        # GET
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Document')
        
        # POST
        data = {
            'title': 'Updated Document',
            'slug': 'test-document',  # Garder le même slug
            'content': 'Updated content',
            'status': 'published',
            'priority': 'high'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 302)

        # Vérifier que le document a été modifié
        updated_document = Document.objects.get(slug='test-document')
        self.assertEqual(updated_document.title, 'Updated Document')
        self.assertEqual(updated_document.last_editor, self.user)
    
    def test_document_delete_view(self):
        """Test de la vue suppression de document"""
        self.client.login(username='testuser', password='testpass123')
        url = reverse('documentation:document_delete', kwargs={'slug': 'test-document'})
        
        # GET (confirmation)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Confirmer la suppression')
        
        # POST (suppression)
        response = self.client.post(url)
        self.assertEqual(response.status_code, 302)
        
        # Vérifier que le document a été supprimé
        self.assertFalse(Document.objects.filter(slug='test-document').exists())
    
    def test_category_views(self):
        """Test des vues de catégories"""
        self.client.login(username='testuser', password='testpass123')
        
        # Liste des catégories
        url = reverse('documentation:category_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Category')
        
        # Création de catégorie
        url = reverse('documentation:category_create')
        data = {
            'name': 'New Category',
            'description': 'New description',
            'color': '#ff0000',
            'icon': 'fas fa-new'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 302)
        
        # Vérifier que la catégorie a été créée
        new_category = DocumentCategory.objects.get(name='New Category')
        self.assertEqual(new_category.color, '#ff0000')


class DocumentationFormsTest(TestCase):
    """Tests pour les formulaires de documentation"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
    
    def test_document_form_slug_generation(self):
        """Test de la génération automatique du slug"""
        from .forms import DocumentForm
        
        data = {
            'title': 'Test Document Title',
            'content': 'Test content',
            'status': 'draft',
            'priority': 'normal'
        }
        
        form = DocumentForm(data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['slug'], 'test-document-title')
    
    def test_document_form_tags_cleaning(self):
        """Test du nettoyage des tags"""
        from .forms import DocumentForm
        
        data = {
            'title': 'Test Document',
            'content': 'Test content',
            'tags': 'tag1, TAG1, tag2,  tag3  , tag2',  # Doublons et espaces
            'status': 'draft',
            'priority': 'normal'
        }
        
        form = DocumentForm(data)
        self.assertTrue(form.is_valid())
        # Les doublons doivent être supprimés et tout en minuscules
        self.assertEqual(form.cleaned_data['tags'], 'tag1, tag2, tag3')
