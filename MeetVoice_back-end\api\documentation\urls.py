from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

app_name = 'documentation'

# Configuration du router DRF
router = DefaultRouter()
router.register(r'categories', views.DocumentCategoryViewSet)
router.register(r'documents', views.DocumentViewSet)
router.register(r'views', views.DocumentViewViewSet)

urlpatterns = [
    # API REST
    path('api/', include(router.urls)),

    # Dashboard
    path('', views.documentation_dashboard, name='dashboard'),
    
    # Documents
    path('documents/', views.document_list, name='document_list'),
    path('documents/create/', views.document_create, name='document_create'),
    path('documents/<slug:slug>/', views.document_detail, name='document_detail'),
    path('documents/<slug:slug>/edit/', views.document_edit, name='document_edit'),
    path('documents/<slug:slug>/delete/', views.document_delete, name='document_delete'),
    
    # Actions AJAX sur les documents
    path('documents/<slug:slug>/toggle-status/', views.document_toggle_status, name='document_toggle_status'),
    path('documents/<slug:slug>/toggle-pin/', views.document_toggle_pin, name='document_toggle_pin'),
    
    # Catégories
    path('categories/', views.category_list, name='category_list'),
    path('categories/create/', views.category_create, name='category_create'),
    path('categories/<int:pk>/edit/', views.category_edit, name='category_edit'),
    path('categories/<int:pk>/delete/', views.category_delete, name='category_delete'),
]
