from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.utils.text import slugify
from django.utils.safestring import mark_safe
from django.urls import reverse
# DRF imports
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend

from .models import Document, DocumentCategory, DocumentView
from .forms import DocumentForm, DocumentCategoryForm, DocumentSearchForm
from .serializers import (
    DocumentSerializer, DocumentListSerializer, DocumentCreateSerializer,
    DocumentUpdateSerializer, DocumentCategorySerializer, DocumentCategoryListSerializer,
    DocumentViewSerializer, DocumentStatsSerializer
)
import re


# ============================================================================
# API REST VIEWSETS
# ============================================================================

class DocumentCategoryViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion des catégories de documents via API REST"""

    queryset = DocumentCategory.objects.all()
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'order', 'created_at']
    ordering = ['order', 'name']

    def get_serializer_class(self):
        """Retourne le serializer approprié selon l'action"""
        if self.action == 'list':
            return DocumentCategoryListSerializer
        return DocumentCategorySerializer

    def get_queryset(self):
        """Filtre les catégories selon les permissions"""
        queryset = DocumentCategory.objects.all()

        # Pour les utilisateurs non-staff, ne montrer que les catégories actives
        if not self.request.user.is_staff:
            queryset = queryset.filter(is_active=True)

        return queryset

    @action(detail=False, methods=['get'])
    def active(self, request):
        """Retourne uniquement les catégories actives"""
        active_categories = self.get_queryset().filter(is_active=True)
        serializer = self.get_serializer(active_categories, many=True)
        return Response(serializer.data)


class DocumentViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion des documents via API REST"""

    queryset = Document.objects.all()
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'priority', 'category', 'author', 'is_pinned', 'is_featured']
    search_fields = ['title', 'content', 'summary', 'tags']
    ordering_fields = ['title', 'created_at', 'updated_at', 'view_count', 'priority']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Retourne le serializer approprié selon l'action"""
        if self.action == 'list':
            return DocumentListSerializer
        elif self.action == 'create':
            return DocumentCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return DocumentUpdateSerializer
        elif self.action == 'stats':
            return DocumentStatsSerializer
        return DocumentSerializer

    def get_queryset(self):
        """Filtre les documents selon les permissions"""
        queryset = Document.objects.all()

        # Pour les utilisateurs non-staff, ne montrer que les documents publiés
        if not self.request.user.is_staff:
            queryset = queryset.filter(status='published')

        return queryset

    def perform_create(self, serializer):
        """Définit l'auteur lors de la création"""
        serializer.save(author=self.request.user, last_editor=self.request.user)

    def perform_update(self, serializer):
        """Met à jour le dernier éditeur lors de la modification"""
        serializer.save(last_editor=self.request.user)

    @action(detail=False, methods=['get'])
    def published(self, request):
        """Retourne uniquement les documents publiés"""
        published_docs = self.get_queryset().filter(status='published')
        serializer = self.get_serializer(published_docs, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Retourne les documents mis en avant"""
        featured_docs = self.get_queryset().filter(is_featured=True, status='published')
        serializer = self.get_serializer(featured_docs, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def pinned(self, request):
        """Retourne les documents épinglés"""
        pinned_docs = self.get_queryset().filter(is_pinned=True, status='published')
        serializer = self.get_serializer(pinned_docs, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def increment_view(self, request, pk=None):
        """Incrémente le compteur de vues d'un document"""
        document = self.get_object()

        # Enregistrer la vue (une seule par utilisateur)
        if request.user.is_authenticated:
            view_obj, created = DocumentView.objects.get_or_create(
                document=document,
                user=request.user,
                defaults={'ip_address': request.META.get('REMOTE_ADDR')}
            )

            # Incrémenter le compteur de vues si nouvelle vue
            if created:
                document.increment_view_count()
                return Response({'message': 'Vue enregistrée'})
            else:
                return Response({'message': 'Vue déjà enregistrée'})
        else:
            # Pour les utilisateurs non connectés, incrémenter directement
            document.increment_view_count()
            return Response({'message': 'Vue enregistrée'})

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Retourne les statistiques des documents"""
        documents = self.get_queryset()
        serializer = DocumentStatsSerializer(documents, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """Recherche avancée dans les documents"""
        query = request.query_params.get('q', '')
        category_id = request.query_params.get('category', '')

        documents = self.get_queryset()

        if query:
            documents = documents.filter(
                Q(title__icontains=query) |
                Q(content__icontains=query) |
                Q(summary__icontains=query) |
                Q(tags__icontains=query)
            )

        if category_id:
            documents = documents.filter(category_id=category_id)

        # Pagination
        page = self.paginate_queryset(documents)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(documents, many=True)
        return Response(serializer.data)


class DocumentViewViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet pour consulter les vues de documents via API REST"""

    queryset = DocumentView.objects.all()
    permission_classes = [IsAuthenticated]
    serializer_class = DocumentViewSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['document', 'user']
    ordering_fields = ['viewed_at']
    ordering = ['-viewed_at']

    def get_queryset(self):
        """Filtre les vues selon les permissions"""
        queryset = DocumentView.objects.all()

        # Les utilisateurs non-staff ne voient que leurs propres vues
        if not self.request.user.is_staff:
            queryset = queryset.filter(user=self.request.user)

        return queryset


# ============================================================================
# VUES DJANGO TRADITIONNELLES (EXISTANTES)
# ============================================================================

def is_staff_user(user):
    """Vérifie si l'utilisateur est staff"""
    return user.is_authenticated and user.is_staff


def simple_markdown_to_html(text):
    """Convertir Markdown simple en HTML"""
    if not text:
        return ''

    # Traitement ligne par ligne pour plus de contrôle
    lines = text.split('\n')
    result_lines = []
    in_list = False
    in_code_block = False
    code_content = []

    i = 0
    while i < len(lines):
        line = lines[i]
        stripped = line.strip()

        # Blocs de code
        if stripped.startswith('```'):
            # Fermer la liste si nécessaire avant le bloc de code
            if in_list:
                result_lines.append('</ul>')
                in_list = False

            if not in_code_block:
                in_code_block = True
                code_content = []
                i += 1
                continue
            else:
                # Fin du bloc de code
                in_code_block = False
                code_html = '<pre><code>' + '\n'.join(code_content) + '</code></pre>'
                result_lines.append(code_html)
                i += 1
                continue

        if in_code_block:
            code_content.append(line)
            i += 1
            continue

        # Fermer la liste si nécessaire
        if in_list and not (stripped.startswith('- ') or stripped.startswith('* ')):
            result_lines.append('</ul>')
            in_list = False

        # Lignes horizontales
        if stripped == '---' or stripped == '***':
            result_lines.append('<hr class="my-4">')

        # Titres
        elif stripped.startswith('### '):
            result_lines.append(f'<h3>{stripped[4:]}</h3>')
        elif stripped.startswith('## '):
            result_lines.append(f'<h2>{stripped[3:]}</h2>')
        elif stripped.startswith('# '):
            result_lines.append(f'<h1>{stripped[2:]}</h1>')

        # Citations
        elif stripped.startswith('> '):
            content = stripped[2:]
            result_lines.append(f'<blockquote class="blockquote">{content}</blockquote>')

        # Listes
        elif stripped.startswith('- ') or stripped.startswith('* '):
            if not in_list:
                result_lines.append('<ul>')
                in_list = True
            content = stripped[2:]
            # Traiter le formatage dans les éléments de liste
            content = format_inline_markdown(content)
            result_lines.append(f'<li>{content}</li>')

        # Paragraphes normaux
        elif stripped:
            formatted_line = format_inline_markdown(line)
            result_lines.append(f'<p>{formatted_line}</p>')

        # Lignes vides
        else:
            result_lines.append('<br>')

        i += 1

    # Fermer la liste si elle est encore ouverte
    if in_list:
        result_lines.append('</ul>')

    return mark_safe('\n'.join(result_lines))


def format_inline_markdown(text):
    """Formater les éléments Markdown inline"""
    # Échapper le HTML
    import html
    text = html.escape(text)

    # Gras
    text = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', text)

    # Italique
    text = re.sub(r'\*(.*?)\*', r'<em>\1</em>', text)

    # Code inline
    text = re.sub(r'`(.*?)`', r'<code>\1</code>', text)

    # Liens
    text = re.sub(r'\[([^\]]+)\]\(([^)]+)\)', r'<a href="\2" target="_blank">\1</a>', text)

    return text


def document_list(request):
    """Liste des documents avec recherche et filtres"""
    
    # Formulaire de recherche
    search_form = DocumentSearchForm(request.GET)
    documents = Document.objects.select_related('category', 'author').all()
    
    # Appliquer les filtres de recherche
    if search_form.is_valid():
        query = search_form.cleaned_data.get('query')
        category = search_form.cleaned_data.get('category')
        status = search_form.cleaned_data.get('status')
        priority = search_form.cleaned_data.get('priority')
        sort_by = search_form.cleaned_data.get('sort_by')
        
        # Recherche textuelle
        if query:
            documents = documents.filter(
                Q(title__icontains=query) |
                Q(content__icontains=query) |
                Q(summary__icontains=query) |
                Q(tags__icontains=query)
            )
        
        # Filtres
        if category:
            documents = documents.filter(category=category)
        if status:
            documents = documents.filter(status=status)
        if priority:
            documents = documents.filter(priority=priority)
        
        # Tri
        if sort_by:
            documents = documents.order_by(sort_by)
    
    # Pagination
    paginator = Paginator(documents, 12)  # 12 documents par page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Statistiques
    stats = {
        'total': Document.objects.count(),
        'published': Document.objects.filter(status='published').count(),
        'draft': Document.objects.filter(status='draft').count(),
        'categories': DocumentCategory.objects.count(),
    }
    
    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'stats': stats,
        'current_filters': request.GET.dict(),
    }
    
    return render(request, 'documentation/document_list.html', context)


def document_detail(request, slug):
    """Affichage détaillé d'un document"""
    
    document = get_object_or_404(Document, slug=slug)
    
    # Enregistrer la vue (seulement si utilisateur connecté)
    if request.user.is_authenticated:
        view_obj, created = DocumentView.objects.get_or_create(
            document=document,
            user=request.user,
            defaults={'ip_address': request.META.get('REMOTE_ADDR')}
        )
    
    # Incrémenter le compteur de vues si nouvelle vue
    if created:
        document.increment_view_count()
    
    # Documents similaires (même catégorie)
    related_documents = Document.objects.filter(
        category=document.category,
        status='published'
    ).exclude(id=document.id)[:5]
    
    # Convertir le contenu Markdown en HTML
    document.content_html = simple_markdown_to_html(document.content)

    context = {
        'document': document,
        'related_documents': related_documents,
    }

    return render(request, 'documentation/document_detail.html', context)


@login_required
@user_passes_test(is_staff_user)
def document_create(request):
    """Création d'un nouveau document"""
    
    if request.method == 'POST':
        form = DocumentForm(request.POST)
        if form.is_valid():
            document = form.save(commit=False)
            document.author = request.user
            document.last_editor = request.user
            document.save()
            
            messages.success(request, f'Document "{document.title}" créé avec succès!')
            return redirect('documentation:document_detail', slug=document.slug)
    else:
        form = DocumentForm()
    
    context = {
        'form': form,
        'title': 'Créer un document',
        'submit_text': 'Créer le document',
    }
    
    return render(request, 'documentation/document_form.html', context)


@login_required
@user_passes_test(is_staff_user)
def document_edit(request, slug):
    """Édition d'un document existant"""
    
    document = get_object_or_404(Document, slug=slug)
    
    if request.method == 'POST':
        form = DocumentForm(request.POST, instance=document)
        if form.is_valid():
            document = form.save(commit=False)
            document.last_editor = request.user
            document.save()
            
            messages.success(request, f'Document "{document.title}" modifié avec succès!')
            return redirect('documentation:document_detail', slug=document.slug)
    else:
        form = DocumentForm(instance=document)
    
    context = {
        'form': form,
        'document': document,
        'title': f'Modifier "{document.title}"',
        'submit_text': 'Enregistrer les modifications',
    }
    
    return render(request, 'documentation/document_form.html', context)


@login_required
@user_passes_test(is_staff_user)
def document_delete(request, slug):
    """Suppression d'un document"""
    
    document = get_object_or_404(Document, slug=slug)
    
    if request.method == 'POST':
        title = document.title
        document.delete()
        messages.success(request, f'Document "{title}" supprimé avec succès!')
        return redirect('documentation:document_list')
    
    context = {
        'document': document,
    }

    return render(request, 'documentation/document_confirm_delete.html', context)


# ===== VUES POUR LES CATÉGORIES =====

def category_list(request):
    """Liste des catégories de documents"""

    categories = DocumentCategory.objects.annotate(
        documents_count=Count('documents')
    ).order_by('name')

    context = {
        'categories': categories,
    }

    return render(request, 'documentation/category_list.html', context)


@login_required
@user_passes_test(is_staff_user)
def category_create(request):
    """Création d'une nouvelle catégorie"""

    if request.method == 'POST':
        form = DocumentCategoryForm(request.POST)
        if form.is_valid():
            category = form.save()
            messages.success(request, f'Catégorie "{category.name}" créée avec succès!')
            return redirect('documentation:category_list')
    else:
        form = DocumentCategoryForm()

    context = {
        'form': form,
        'title': 'Créer une catégorie',
        'submit_text': 'Créer la catégorie',
    }

    return render(request, 'documentation/category_form.html', context)


@login_required
@user_passes_test(is_staff_user)
def category_edit(request, pk):
    """Édition d'une catégorie existante"""

    category = get_object_or_404(DocumentCategory, pk=pk)

    if request.method == 'POST':
        form = DocumentCategoryForm(request.POST, instance=category)
        if form.is_valid():
            category = form.save()
            messages.success(request, f'Catégorie "{category.name}" modifiée avec succès!')
            return redirect('documentation:category_list')
    else:
        form = DocumentCategoryForm(instance=category)

    context = {
        'form': form,
        'category': category,
        'title': f'Modifier "{category.name}"',
        'submit_text': 'Enregistrer les modifications',
    }

    return render(request, 'documentation/category_form.html', context)


@login_required
@user_passes_test(is_staff_user)
def category_delete(request, pk):
    """Suppression d'une catégorie"""

    category = get_object_or_404(DocumentCategory, pk=pk)

    if request.method == 'POST':
        name = category.name
        category.delete()
        messages.success(request, f'Catégorie "{name}" supprimée avec succès!')
        return redirect('documentation:category_list')

    context = {
        'category': category,
    }

    return render(request, 'documentation/category_confirm_delete.html', context)


# ===== VUES AJAX =====

@login_required
@user_passes_test(is_staff_user)
@require_http_methods(["POST"])
def document_toggle_status(request, slug):
    """Toggle le statut d'un document (publié/brouillon)"""

    document = get_object_or_404(Document, slug=slug)

    if document.status == 'published':
        document.status = 'draft'
        message = 'Document mis en brouillon'
    else:
        document.status = 'published'
        message = 'Document publié'

    document.last_editor = request.user
    document.save()

    return JsonResponse({
        'success': True,
        'message': message,
        'new_status': document.status,
        'status_display': document.get_status_display()
    })


@login_required
@user_passes_test(is_staff_user)
@require_http_methods(["POST"])
def document_toggle_pin(request, slug):
    """Toggle l'épinglage d'un document"""

    document = get_object_or_404(Document, slug=slug)
    document.is_pinned = not document.is_pinned
    document.last_editor = request.user
    document.save()

    message = 'Document épinglé' if document.is_pinned else 'Document désépinglé'

    return JsonResponse({
        'success': True,
        'message': message,
        'is_pinned': document.is_pinned
    })


# ===== VUE DASHBOARD =====

def documentation_dashboard(request):
    """Dashboard de la documentation avec statistiques"""

    # Statistiques générales
    stats = {
        'total_documents': Document.objects.count(),
        'published_documents': Document.objects.filter(status='published').count(),
        'draft_documents': Document.objects.filter(status='draft').count(),
        'archived_documents': Document.objects.filter(status='archived').count(),
        'total_categories': DocumentCategory.objects.count(),
        'total_views': sum(doc.view_count for doc in Document.objects.all()),
    }

    # Documents récents
    recent_documents = Document.objects.select_related('category', 'author').order_by('-updated_at')[:5]

    # Documents les plus consultés
    popular_documents = Document.objects.select_related('category', 'author').order_by('-view_count')[:5]

    # Documents épinglés
    pinned_documents = Document.objects.filter(is_pinned=True).select_related('category', 'author')[:5]

    # Catégories avec nombre de documents
    categories_stats = DocumentCategory.objects.annotate(
        documents_count=Count('documents')
    ).order_by('-documents_count')[:5]

    context = {
        'stats': stats,
        'recent_documents': recent_documents,
        'popular_documents': popular_documents,
        'pinned_documents': pinned_documents,
        'categories_stats': categories_stats,
    }

    return render(request, 'documentation/dashboard.html', context)
