from django.contrib import admin
from django.utils.html import format_html
from .models import Event

@admin.register(Event)
class EventAdmin(admin.ModelAdmin):
    """Administration des événements"""

    list_display = [
        'title', 'creator', 'event_date', 'event_time', 'event_type',
        'participants_info', 'is_approved', 'is_active', 'created_at'
    ]

    list_filter = [
        'event_type', 'is_approved', 'is_active', 'event_date', 'created_at'
    ]

    search_fields = [
        'title', 'description', 'location', 'creator__username',
        'creator__email', 'creator__nom', 'creator__prenom'
    ]

    readonly_fields = [
        'created_at', 'updated_at', 'current_participants_count',
        'is_full', 'available_spots'
    ]

    fieldsets = (
        ('Informations générales', {
            'fields': ('title', 'description', 'creator', 'event_type')
        }),
        ('Date et lieu', {
            'fields': ('event_date', 'event_time', 'location')
        }),
        ('Participants', {
            'fields': ('max_participants', 'participants', 'current_participants_count', 'available_spots', 'is_full')
        }),
        ('Modération', {
            'fields': ('is_approved', 'is_active')
        }),
        ('Métadonnées', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    filter_horizontal = ['participants']

    date_hierarchy = 'event_date'

    ordering = ['-created_at']

    def participants_info(self, obj):
        """Affiche les informations sur les participants"""
        count = obj.current_participants_count
        max_count = obj.max_participants
        percentage = (count / max_count * 100) if max_count > 0 else 0

        color = 'green' if percentage < 80 else 'orange' if percentage < 100 else 'red'

        return format_html(
            '<span style="color: {};">{}/{} ({}%)</span>',
            color, count, max_count, int(percentage)
        )
    participants_info.short_description = 'Participants'

    def get_queryset(self, request):
        """Optimise les requêtes avec select_related et prefetch_related"""
        return super().get_queryset(request).select_related('creator').prefetch_related('participants')

    actions = ['approve_events', 'disapprove_events', 'activate_events', 'deactivate_events']

    def approve_events(self, request, queryset):
        """Action pour approuver les événements sélectionnés"""
        updated = queryset.update(is_approved=True)
        self.message_user(request, f'{updated} événement(s) approuvé(s).')
    approve_events.short_description = "Approuver les événements sélectionnés"

    def disapprove_events(self, request, queryset):
        """Action pour désapprouver les événements sélectionnés"""
        updated = queryset.update(is_approved=False)
        self.message_user(request, f'{updated} événement(s) désapprouvé(s).')
    disapprove_events.short_description = "Désapprouver les événements sélectionnés"

    def activate_events(self, request, queryset):
        """Action pour activer les événements sélectionnés"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} événement(s) activé(s).')
    activate_events.short_description = "Activer les événements sélectionnés"

    def deactivate_events(self, request, queryset):
        """Action pour désactiver les événements sélectionnés"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} événement(s) désactivé(s).')
    deactivate_events.short_description = "Désactiver les événements sélectionnés"
