from django import forms
from django.utils import timezone
from .models import Event, EVENT_TYPE_CHOICES

class EventForm(forms.ModelForm):
    """Formulaire pour créer et modifier des événements"""
    
    class Meta:
        model = Event
        fields = [
            'title', 'description', 'max_participants', 'event_date',
            'event_time', 'location', 'event_type'
        ]
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Titre de l\'événement'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Description de l\'événement'
            }),
            'max_participants': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 1,
                'max': 100
            }),
            'event_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'event_time': forms.TimeInput(attrs={
                'class': 'form-control',
                'type': 'time'
            }),
            'location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Lieu de l\'événement'
            }),
            'event_type': forms.Select(attrs={
                'class': 'form-control'
            }),
        }
        labels = {
            'title': 'Titre',
            'description': 'Description',
            'max_participants': 'Nombre maximum de participants',
            'event_date': 'Date de l\'événement',
            'event_time': 'Heure de l\'événement',
            'location': 'Lieu',
            'event_type': 'Type d\'événement',
        }
    
    def clean_event_date(self):
        """Validation de la date de l'événement"""
        event_date = self.cleaned_data.get('event_date')
        if event_date and event_date < timezone.now().date():
            raise forms.ValidationError("La date de l'événement ne peut pas être dans le passé.")
        return event_date
    
    def clean_max_participants(self):
        """Validation du nombre de participants"""
        max_participants = self.cleaned_data.get('max_participants')
        if max_participants and max_participants < 1:
            raise forms.ValidationError("Le nombre de participants doit être au moins 1.")
        if max_participants and max_participants > 100:
            raise forms.ValidationError("Le nombre de participants ne peut pas dépasser 100.")
        return max_participants

class EventFilterForm(forms.Form):
    """Formulaire pour filtrer les événements"""
    
    event_type = forms.ChoiceField(
        choices=[('', 'Tous les types')] + list(EVENT_TYPE_CHOICES),
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Rechercher...'
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
