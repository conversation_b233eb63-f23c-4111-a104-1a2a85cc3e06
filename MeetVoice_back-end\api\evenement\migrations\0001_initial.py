# Generated by Django 5.2.3 on 2025-06-17 02:26

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Event',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Date de mise à jour')),
                ('title', models.CharField(max_length=200, verbose_name="Titre de l'événement")),
                ('description', models.TextField(verbose_name="Description de l'événement")),
                ('max_participants', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='Nombre maximum de participants')),
                ('event_date', models.DateField(verbose_name="Date de l'événement")),
                ('event_time', models.TimeField(verbose_name="Heure de l'événement")),
                ('location', models.CharField(max_length=300, verbose_name="Lieu de l'événement")),
                ('event_type', models.CharField(choices=[('soiree', 'Soirée'), ('boire_verre', 'Boire un verre'), ('cinema', 'Cinéma'), ('sport', 'Sport'), ('restaurant', 'Restaurant'), ('concert', 'Concert'), ('exposition', 'Exposition'), ('autre', 'Autre')], default='autre', max_length=20, verbose_name="Type d'événement")),
                ('is_approved', models.BooleanField(default=True, verbose_name='Approuvé')),
                ('is_active', models.BooleanField(default=True, verbose_name='Actif')),
                ('creator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_events', to=settings.AUTH_USER_MODEL, verbose_name='Créateur')),
                ('participants', models.ManyToManyField(blank=True, related_name='joined_events', to=settings.AUTH_USER_MODEL, verbose_name='Participants')),
            ],
            options={
                'verbose_name': 'Événement',
                'verbose_name_plural': 'Événements',
                'ordering': ['-created_at'],
            },
        ),
    ]
