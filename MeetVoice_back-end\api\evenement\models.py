from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator

# Choix pour les types d'événements
EVENT_TYPE_CHOICES = [
    ('soiree', 'Soirée'),
    ('boire_verre', 'Boire un verre'),
    ('cinema', 'Cinéma'),
    ('sport', 'Sport'),
    ('restaurant', 'Restaurant'),
    ('concert', 'Concert'),
    ('exposition', 'Exposition'),
    ('autre', 'Autre'),
]

class Event(models.Model):
    """Modèle pour les événements créés par les utilisateurs"""

    # Champs de base
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Date de mise à jour")

    # Créateur de l'événement
    creator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_events',
        verbose_name="Créateur"
    )

    # Détails de l'événement
    title = models.CharField(max_length=200, verbose_name="Titre de l'événement")
    description = models.TextField(verbose_name="Description de l'événement")

    # Participants
    max_participants = models.IntegerField(
        validators=[MinValueValidator(1)],
        verbose_name="Nombre maximum de participants"
    )
    participants = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        related_name='joined_events',
        blank=True,
        verbose_name="Participants"
    )

    # Date et heure
    event_date = models.DateField(verbose_name="Date de l'événement")
    event_time = models.TimeField(verbose_name="Heure de l'événement")

    # Lieu
    location = models.CharField(max_length=300, verbose_name="Lieu de l'événement")

    # Type d'événement
    event_type = models.CharField(
        max_length=20,
        choices=EVENT_TYPE_CHOICES,
        default='autre',
        verbose_name="Type d'événement"
    )

    # Statut de modération
    is_approved = models.BooleanField(default=True, verbose_name="Approuvé")
    is_active = models.BooleanField(default=True, verbose_name="Actif")

    class Meta:
        verbose_name = "Événement"
        verbose_name_plural = "Événements"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.event_date} à {self.event_time}"

    @property
    def current_participants_count(self):
        """Retourne le nombre actuel de participants"""
        return self.participants.count()

    @property
    def is_full(self):
        """Vérifie si l'événement est complet"""
        return self.current_participants_count >= self.max_participants

    @property
    def available_spots(self):
        """Retourne le nombre de places disponibles"""
        return self.max_participants - self.current_participants_count
