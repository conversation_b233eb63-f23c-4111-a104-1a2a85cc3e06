from rest_framework import serializers
from .models import Event
from django.contrib.auth import get_user_model

User = get_user_model()

class SimpleUserSerializer(serializers.ModelSerializer):
    """Serializer simplifié pour les utilisateurs"""
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']

class EventSerializer(serializers.ModelSerializer):
    """Serializer pour les événements"""

    creator = SimpleUserSerializer(read_only=True)
    participants = SimpleUserSerializer(many=True, read_only=True)
    current_participants_count = serializers.ReadOnlyField()
    is_full = serializers.ReadOnlyField()
    available_spots = serializers.ReadOnlyField()
    
    class Meta:
        model = Event
        fields = [
            'id', 'title', 'description', 'creator', 'max_participants',
            'participants', 'event_date', 'event_time', 'location',
            'event_type', 'created_at', 'updated_at', 'is_approved',
            'is_active', 'current_participants_count', 'is_full',
            'available_spots'
        ]
        read_only_fields = ['creator', 'created_at', 'updated_at', 'is_approved']

class EventCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création d'événements"""
    
    class Meta:
        model = Event
        fields = [
            'title', 'description', 'max_participants', 'event_date',
            'event_time', 'location', 'event_type'
        ]
    
    def validate_max_participants(self, value):
        if value < 1:
            raise serializers.ValidationError("Le nombre de participants doit être au moins 1.")
        if value > 100:
            raise serializers.ValidationError("Le nombre de participants ne peut pas dépasser 100.")
        return value
    
    def validate_event_date(self, value):
        from django.utils import timezone
        if value < timezone.now().date():
            raise serializers.ValidationError("La date de l'événement ne peut pas être dans le passé.")
        return value
