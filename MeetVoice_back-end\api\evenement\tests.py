from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import date, time, timedelta
from rest_framework.test import APITestCase
from rest_framework import status

from .models import Event, EVENT_TYPE_CHOICES
from .forms import EventForm

User = get_user_model()

class EventModelTest(TestCase):
    """Tests pour le modèle Event"""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        self.event = Event.objects.create(
            title='Test Event',
            description='Test description',
            creator=self.user,
            max_participants=10,
            event_date=date.today() + timedelta(days=7),
            event_time=time(14, 30),
            location='Test Location',
            event_type='soiree'
        )

    def test_event_creation(self):
        """Test de création d'un événement"""
        self.assertEqual(self.event.title, 'Test Event')
        self.assertEqual(self.event.creator, self.user)
        self.assertEqual(self.event.max_participants, 10)
        self.assertTrue(self.event.is_approved)
        self.assertTrue(self.event.is_active)

    def test_event_str_representation(self):
        """Test de la représentation string de l'événement"""
        expected = f"{self.event.title} - {self.event.event_date} à {self.event.event_time}"
        self.assertEqual(str(self.event), expected)

    def test_current_participants_count(self):
        """Test du comptage des participants"""
        self.assertEqual(self.event.current_participants_count, 0)

        # Ajouter des participants
        user2 = User.objects.create_user(email='<EMAIL>', username='user2', password='pass')
        user3 = User.objects.create_user(email='<EMAIL>', username='user3', password='pass')

        self.event.participants.add(user2, user3)
        self.assertEqual(self.event.current_participants_count, 2)

    def test_is_full_property(self):
        """Test de la propriété is_full"""
        self.assertFalse(self.event.is_full)

        # Remplir l'événement
        for i in range(10):
            user = User.objects.create_user(
                email=f'user{i}@example.com',
                username=f'user{i}',
                password='pass'
            )
            self.event.participants.add(user)

        self.assertTrue(self.event.is_full)

    def test_available_spots(self):
        """Test du calcul des places disponibles"""
        self.assertEqual(self.event.available_spots, 10)

        user2 = User.objects.create_user(email='<EMAIL>', username='user2', password='pass')
        self.event.participants.add(user2)
        self.assertEqual(self.event.available_spots, 9)

class EventFormTest(TestCase):
    """Tests pour le formulaire Event"""

    def test_valid_form(self):
        """Test d'un formulaire valide"""
        form_data = {
            'title': 'Test Event',
            'description': 'Test description',
            'max_participants': 10,
            'event_date': date.today() + timedelta(days=7),
            'event_time': time(14, 30),
            'location': 'Test Location',
            'event_type': 'soiree'
        }
        form = EventForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_past_date_validation(self):
        """Test de validation pour une date passée"""
        form_data = {
            'title': 'Test Event',
            'description': 'Test description',
            'max_participants': 10,
            'event_date': date.today() - timedelta(days=1),  # Date passée
            'event_time': time(14, 30),
            'location': 'Test Location',
            'event_type': 'soiree'
        }
        form = EventForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('event_date', form.errors)

    def test_invalid_participants_count(self):
        """Test de validation pour un nombre de participants invalide"""
        form_data = {
            'title': 'Test Event',
            'description': 'Test description',
            'max_participants': 0,  # Invalide
            'event_date': date.today() + timedelta(days=7),
            'event_time': time(14, 30),
            'location': 'Test Location',
            'event_type': 'soiree'
        }
        form = EventForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('max_participants', form.errors)

class EventViewTest(TestCase):
    """Tests pour les vues Event"""

    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        self.event = Event.objects.create(
            title='Test Event',
            description='Test description',
            creator=self.user,
            max_participants=10,
            event_date=date.today() + timedelta(days=7),
            event_time=time(14, 30),
            location='Test Location',
            event_type='soiree'
        )

    def test_event_list_view_requires_login(self):
        """Test que la liste des événements nécessite une connexion"""
        response = self.client.get(reverse('evenement:event_list'))
        self.assertEqual(response.status_code, 302)  # Redirection vers login

    def test_event_list_view_authenticated(self):
        """Test de la vue liste pour un utilisateur connecté"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('evenement:event_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.event.title)

    def test_event_detail_view(self):
        """Test de la vue détail d'un événement"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('evenement:event_detail', kwargs={'pk': self.event.pk}))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.event.title)
        self.assertContains(response, self.event.description)

    def test_join_event_view(self):
        """Test de rejoindre un événement"""
        user2 = User.objects.create_user(email='<EMAIL>', username='user2', password='pass')
        self.client.login(email='<EMAIL>', password='pass')

        response = self.client.post(reverse('evenement:join_event', kwargs={'pk': self.event.pk}))
        self.assertEqual(response.status_code, 302)  # Redirection

        # Vérifier que l'utilisateur a rejoint l'événement
        self.assertTrue(self.event.participants.filter(id=user2.id).exists())

    def test_leave_event_view(self):
        """Test de quitter un événement"""
        user2 = User.objects.create_user(email='<EMAIL>', username='user2', password='pass')
        self.event.participants.add(user2)

        self.client.login(email='<EMAIL>', password='pass')
        response = self.client.post(reverse('evenement:leave_event', kwargs={'pk': self.event.pk}))
        self.assertEqual(response.status_code, 302)  # Redirection

        # Vérifier que l'utilisateur a quitté l'événement
        self.assertFalse(self.event.participants.filter(id=user2.id).exists())

class EventAPITest(APITestCase):
    """Tests pour l'API REST des événements"""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        self.event = Event.objects.create(
            title='Test Event',
            description='Test description',
            creator=self.user,
            max_participants=10,
            event_date=date.today() + timedelta(days=7),
            event_time=time(14, 30),
            location='Test Location',
            event_type='soiree'
        )

    def test_api_requires_authentication(self):
        """Test que l'API nécessite une authentification"""
        response = self.client.get('/evenement/api/events/')
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])

    def test_api_list_events(self):
        """Test de listage des événements via API"""
        self.client.force_authenticate(user=self.user)
        response = self.client.get('/evenement/api/events/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

    def test_api_create_event(self):
        """Test de création d'événement via API"""
        self.client.force_authenticate(user=self.user)
        data = {
            'title': 'New Event',
            'description': 'New description',
            'max_participants': 5,
            'event_date': str(date.today() + timedelta(days=10)),
            'event_time': '15:00:00',
            'location': 'New Location',
            'event_type': 'cinema'
        }
        response = self.client.post('/evenement/api/events/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Event.objects.count(), 2)

    def test_api_join_event(self):
        """Test de rejoindre un événement via API"""
        user2 = User.objects.create_user(email='<EMAIL>', username='user2', password='pass')
        self.client.force_authenticate(user=user2)

        response = self.client.post(f'/evenement/api/events/{self.event.pk}/join/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(self.event.participants.filter(id=user2.id).exists())

    def test_api_leave_event(self):
        """Test de quitter un événement via API"""
        user2 = User.objects.create_user(email='<EMAIL>', username='user2', password='pass')
        self.event.participants.add(user2)
        self.client.force_authenticate(user=user2)

        response = self.client.post(f'/evenement/api/events/{self.event.pk}/leave/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(self.event.participants.filter(id=user2.id).exists())
