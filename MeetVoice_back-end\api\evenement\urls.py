from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

# Configuration du router pour l'API REST
router = DefaultRouter()
router.register(r'events', views.EventViewSet)

app_name = 'evenement'

urlpatterns = [
    # Vues basées sur les fonctions
    path('', views.event_list_view, name='event_list'),
    path('create/', views.event_create_view, name='event_create'),
    path('<int:pk>/', views.event_detail_view, name='event_detail'),
    path('<int:pk>/edit/', views.event_edit_view, name='event_edit'),
    path('<int:pk>/join/', views.join_event_view, name='join_event'),
    path('<int:pk>/leave/', views.leave_event_view, name='leave_event'),
    
    # API REST
    path('api/', include(router.urls)),
]
