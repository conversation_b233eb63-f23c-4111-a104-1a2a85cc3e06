from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from .models import Event
from .serializers import EventSerializer
from .forms import EventForm

# Vues basées sur les fonctions
@login_required
def event_list_view(request):
    """Liste des événements avec pagination et filtres"""
    events = Event.objects.filter(is_active=True, is_approved=True)

    # Filtres
    event_type = request.GET.get('type')
    search = request.GET.get('search')

    if event_type:
        events = events.filter(event_type=event_type)

    if search:
        events = events.filter(
            Q(title__icontains=search) |
            Q(description__icontains=search) |
            Q(location__icontains=search)
        )

    # Pagination
    paginator = Paginator(events, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'event_types': Event.EVENT_TYPE_CHOICES,
        'current_type': event_type,
        'search_query': search,
    }
    return render(request, 'evenement/event_list.html', context)

@login_required
def event_detail_view(request, pk):
    """Détail d'un événement"""
    event = get_object_or_404(Event, pk=pk, is_active=True, is_approved=True)
    is_participant = event.participants.filter(id=request.user.id).exists()

    context = {
        'event': event,
        'is_participant': is_participant,
        'can_join': not event.is_full and not is_participant,
    }
    return render(request, 'evenement/event_detail.html', context)

@login_required
def event_create_view(request):
    """Création d'un nouvel événement"""
    if request.method == 'POST':
        form = EventForm(request.POST)
        if form.is_valid():
            event = form.save(commit=False)
            event.creator = request.user
            event.save()
            messages.success(request, 'Événement créé avec succès!')
            return redirect('evenement:event_detail', pk=event.pk)
    else:
        form = EventForm()

    return render(request, 'evenement/event_form.html', {'form': form, 'title': 'Créer un événement'})

@login_required
def event_edit_view(request, pk):
    """Modification d'un événement"""
    event = get_object_or_404(Event, pk=pk, creator=request.user)

    if request.method == 'POST':
        form = EventForm(request.POST, instance=event)
        if form.is_valid():
            form.save()
            messages.success(request, 'Événement modifié avec succès!')
            return redirect('evenement:event_detail', pk=event.pk)
    else:
        form = EventForm(instance=event)

    return render(request, 'evenement/event_form.html', {'form': form, 'title': 'Modifier l\'événement'})

@login_required
def join_event_view(request, pk):
    """Rejoindre un événement"""
    event = get_object_or_404(Event, pk=pk, is_active=True, is_approved=True)

    if event.is_full:
        messages.error(request, 'Cet événement est complet.')
    elif event.participants.filter(id=request.user.id).exists():
        messages.warning(request, 'Vous participez déjà à cet événement.')
    else:
        event.participants.add(request.user)
        messages.success(request, 'Vous avez rejoint l\'événement avec succès!')

    return redirect('evenement:event_detail', pk=pk)

@login_required
def leave_event_view(request, pk):
    """Quitter un événement"""
    event = get_object_or_404(Event, pk=pk)

    if event.participants.filter(id=request.user.id).exists():
        event.participants.remove(request.user)
        messages.success(request, 'Vous avez quitté l\'événement.')
    else:
        messages.warning(request, 'Vous ne participez pas à cet événement.')

    return redirect('evenement:event_detail', pk=pk)

# ViewSet pour l'API REST
class EventViewSet(viewsets.ModelViewSet):
    """ViewSet pour l'API REST des événements"""
    queryset = Event.objects.filter(is_active=True, is_approved=True)
    serializer_class = EventSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(creator=self.request.user)

    @action(detail=True, methods=['post'])
    def join(self, request, pk=None):
        """Rejoindre un événement via API"""
        event = self.get_object()

        if event.is_full:
            return Response({'error': 'Événement complet'}, status=status.HTTP_400_BAD_REQUEST)

        if event.participants.filter(id=request.user.id).exists():
            return Response({'error': 'Déjà participant'}, status=status.HTTP_400_BAD_REQUEST)

        event.participants.add(request.user)
        return Response({'message': 'Rejoint avec succès'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def leave(self, request, pk=None):
        """Quitter un événement via API"""
        event = self.get_object()

        if not event.participants.filter(id=request.user.id).exists():
            return Response({'error': 'Pas participant'}, status=status.HTTP_400_BAD_REQUEST)

        event.participants.remove(request.user)
        return Response({'message': 'Quitté avec succès'}, status=status.HTTP_200_OK)
