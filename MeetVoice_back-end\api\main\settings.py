from pathlib import Path
import os
from datetime import timedelta
import warnings
from dotenv import load_dotenv

warnings.filterwarnings("ignore", message="Auto-created primary key used when not defining a primary key type")

# Charger les variables d'environnement depuis .env
load_dotenv()


BASE_DIR = Path(__file__).resolve().parent.parent

SECRET_KEY = 'django-insecure-9s6-h9!!v97hkoix^7smjb241ov)kojl(&t(^h=v5$aube0uqj'


DEBUG = True

ALLOWED_HOSTS = ['*']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'drf_yasg',
    'rest_framework',
    'rest_framework.authtoken',
    'rest_framework_simplejwt',
    'corsheaders',
    'import_export',
    'ckeditor',
    'ckeditor_uploader',
    'channels',
    'django_filters',
    # Apps du projet
    'compte',
    'actualite',
    'abonnement',
    'mur',
    'commentaire',
    'evenement',
    'contact',  # Nouvelle app contact
    # Apps administratives
    'dashboard',
    'backoffice',  # À supprimer après migration
    'reseaux_social',
    'documentation',
    'newsletter',
    'meetvoice_tts',  # Service de synthèse vocale
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'compte.middleware.AutoLoginByIPMiddleware',  # 🚀 Connexion automatique par IP
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# Exemptions CSRF pour les endpoints publics TTS
CSRF_TRUSTED_ORIGINS = [
    'http://127.0.0.1:8000',
    'http://localhost:8000',
]

ROOT_URLCONF = 'main.urls'


SIMPLE_JWT ={
    'ACCES_TOKEN_LIFETIME' : timedelta(days=30),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=180),
    'ROTATE_REFRESH_TOKENS' : False
}

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, "main/templates")],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'main.wsgi.application'

CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True


CORS_ALLOWED_ORIGINS = [
    'http://127.0.0.1:8080',
    'http://localhost:8080',
    'http://localhost:5000',
    'http://127.0.0.1:5173',
    'http://************:8080'
]


DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = 'fr-FR'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True

# Configuration Email
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'pzht hnhm whzc qovn'
DEFAULT_FROM_EMAIL = 'MeetVoice <<EMAIL>>'


STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, "main/static"),
    os.path.join(BASE_DIR, "backoffice/static"),
    os.path.join(BASE_DIR, "documentation/static"),
    os.path.join(BASE_DIR, "newsletter/static"),
    os.path.join(BASE_DIR, "static"),
]

MEDIA_URL = '/media/' 
MEDIA_ROOT = BASE_DIR /'media'

SWAGGER_SETTINGS= {
    'LOGIN_URL' : '/admin/login/',
    'LOGOUT_URL' : '/admin/logout/'
}

DJANGORESIZED_DEFAULT_SIZE = [1920, 1080]
DJANGORESIZED_DEFAULT_KEEP_META = True
DJANGORESIZED_DEFAULT_FORCE_FORMAT = 'JPEG'
DJANGORESIZED_DEFAULT_FORMAT_EXTENSIONS = {'JPEG': ".jpg"}
DJANGORESIZED_DEFAULT_NORMALIZE_ROTATION = True

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Configuration CKEditor
CKEDITOR_UPLOAD_PATH = "uploads/"
CKEDITOR_CONFIGS = {
    'default': {
        'toolbar': 'full',
        'height': 300,
        'width': '100%',
    },
}

# Supprimer les warnings
import warnings
warnings.filterwarnings('ignore', module='ckeditor')
warnings.filterwarnings('ignore', message='pkg_resources is deprecated')

# Supprimer le warning CKEditor W001 via SILENCED_SYSTEM_CHECKS
SILENCED_SYSTEM_CHECKS = ['ckeditor.W001']

# Configuration Channels pour WebSockets
ASGI_APPLICATION = 'main.asgi.application'

# Configuration Redis pour Channels
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            "hosts": [('127.0.0.1', 6379)],
        },
    },
}

# Configuration Stripe
STRIPE_PUBLISHABLE_KEY = os.getenv('STRIPE_PUBLISHABLE_KEY', 'pk_test_51RccPxFbwqibHSvWgPSM7Pn0pHqMH8sP0xXUorXJh4sQO5r9PE5c2mANieuiaBv1iypJPMLa3dtJId0Zcaz6IoG700wokVH0zq')
STRIPE_SECRET_KEY = os.getenv('STRIPE_SECRET_KEY', 'sk_test_51RccPxFbwqibHSvWYOUR_REAL_SECRET_KEY_HERE')
STRIPE_WEBHOOK_SECRET = os.getenv('STRIPE_WEBHOOK_SECRET', 'whsec_YOUR_WEBHOOK_SECRET_HERE')

# Configuration Google Analytics
GOOGLE_ANALYTICS_PROPERTY_ID = os.getenv('GOOGLE_ANALYTICS_PROPERTY_ID', '')
GOOGLE_ANALYTICS_CREDENTIALS_FILE = os.getenv('GOOGLE_ANALYTICS_CREDENTIALS_FILE', '')

# Configuration APIs pour réseaux sociaux
FACEBOOK_ACCESS_TOKEN = os.getenv('FACEBOOK_ACCESS_TOKEN')
FACEBOOK_PAGE_ID = os.getenv('FACEBOOK_PAGE_ID')
INSTAGRAM_BUSINESS_ACCOUNT_ID = os.getenv('INSTAGRAM_BUSINESS_ACCOUNT_ID')
TWITTER_API_KEY = os.getenv('TWITTER_API_KEY')
TWITTER_API_SECRET = os.getenv('TWITTER_API_SECRET')
TWITTER_ACCESS_TOKEN = os.getenv('TWITTER_ACCESS_TOKEN')
TWITTER_ACCESS_TOKEN_SECRET = os.getenv('TWITTER_ACCESS_TOKEN_SECRET')
LINKEDIN_ACCESS_TOKEN = os.getenv('LINKEDIN_ACCESS_TOKEN')
LINKEDIN_COMPANY_ID = os.getenv('LINKEDIN_COMPANY_ID')

# Configuration IA
GOOGLE_GEMINI_API_KEY = os.getenv('GOOGLE_GEMINI_API_KEY', 'AIzaSyBJGKJJOKJOKJOKJOKJOKJOKJOKJOKJOKJ')
POLLINATIONS_API_URL = 'https://image.pollinations.ai/prompt/'

# Configuration TTS (Text-to-Speech)
TTS_TEMP_DIR = os.path.join(BASE_DIR, 'temp', 'tts')
TTS_MAX_TEXT_LENGTH = 5000
TTS_AUDIO_FORMAT = 'mp3'
TTS_DEFAULT_VOICE_TYPE = 'female_young'
TTS_DEFAULT_LANGUAGE = 'fr'
TTS_CLEANUP_DAYS = 7  # Nettoyer les fichiers audio après 7 jours

# Configuration d'authentification Django
AUTH_USER_MODEL = 'compte.Compte'  # Utiliser le modèle Compte personnalisé

# Backends d'authentification personnalisés
AUTHENTICATION_BACKENDS = [
    'compte.backends.EmailOrUsernameBackend',  # Authentification email/username
    'compte.backends.IPTrustedBackend',        # Authentification par IP de confiance
    'django.contrib.auth.backends.ModelBackend',  # Backend par défaut (fallback)
]

LOGIN_URL = '/accounts/login/'
LOGIN_REDIRECT_URL = '/'
LOGOUT_REDIRECT_URL = '/'

# Configuration Django REST Framework
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.TokenAuthentication',
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticatedOrReadOnly',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
}

# Configuration CORS
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:8080",
    "http://127.0.0.1:8080",
]

CORS_ALLOW_CREDENTIALS = True

CORS_ALLOW_ALL_ORIGINS = DEBUG  # Permet toutes les origines en mode debug

CORS_ALLOWED_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

# Configuration JWT
from datetime import timedelta

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
}
