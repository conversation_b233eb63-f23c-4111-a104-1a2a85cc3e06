"""
Views pour les services de synthèse vocale (TTS) et extraction d'informations
Vosk STT supprimé du projet
"""

import logging
import json
import tempfile
import os
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile

# Configuration du logging
logger = logging.getLogger(__name__)

# Import pour conversion audio
try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError as e:
    PYDUB_AVAILABLE = False
    logger = logging.getLogger(__name__)
    if logger:
        logger.warning(f"PyDub non disponible: {e}")

# Import pour IA d'extraction
try:
    from transformers import pipeline
    AI_EXTRACTION_AVAILABLE = True
except ImportError as e:
    AI_EXTRACTION_AVAILABLE = False
    logger.warning(f"Transformers non disponible: {e}")

# Import pour TTS
try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError as e:
    TTS_AVAILABLE = False
    logger.warning(f"pyttsx3 non disponible: {e}")


# === ENDPOINTS TTS PUBLICS ===

@csrf_exempt
@require_http_methods(["POST"])
def synthesize_api(request):
    """
    API de synthèse vocale - ACCÈS PUBLIC SANS TOKEN
    
    POST Body (JSON):
        - text: Texte à synthétiser
        - voice: Voix (optionnel)
        - speed: Vitesse (optionnel, défaut: 200)
        - pitch: Tonalité (optionnel, défaut: 0)
    """
    if not TTS_AVAILABLE:
        return JsonResponse({
            'error': 'Service TTS non disponible',
            'tts_available': False
        }, status=503)

    try:
        data = json.loads(request.body)
        text = data.get('text', '').strip()
        
        if not text:
            return JsonResponse({
                'error': 'Texte requis',
                'required_fields': ['text']
            }, status=400)

        voice = data.get('voice', 'default')
        speed = data.get('speed', 200)
        pitch = data.get('pitch', 0)

        # Synthèse vocale
        engine = pyttsx3.init()
        
        # Configuration de la voix
        voices = engine.getProperty('voices')
        if voices and voice != 'default':
            for v in voices:
                if voice.lower() in v.name.lower():
                    engine.setProperty('voice', v.id)
                    break

        # Configuration vitesse et tonalité
        engine.setProperty('rate', speed)
        
        # Créer un fichier temporaire pour l'audio
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            temp_path = temp_file.name
            
        engine.save_to_file(text, temp_path)
        engine.runAndWait()

        # Lire le fichier audio généré
        with open(temp_path, 'rb') as audio_file:
            audio_data = audio_file.read()

        # Nettoyer le fichier temporaire
        os.unlink(temp_path)

        # Encoder en base64 pour la réponse
        import base64
        audio_base64 = base64.b64encode(audio_data).decode('utf-8')

        return JsonResponse({
            'success': True,
            'audio_base64': audio_base64,
            'text': text,
            'voice': voice,
            'speed': speed,
            'pitch': pitch,
            'format': 'wav',
            'access': 'PUBLIC - No token required'
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'JSON invalide',
            'format': 'application/json'
        }, status=400)
    except Exception as e:
        logger.error(f"Erreur synthèse vocale: {e}")
        return JsonResponse({
            'error': f'Erreur synthèse: {str(e)}',
            'method': 'pyttsx3'
        }, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def voices_api(request):
    """
    API pour lister les voix disponibles - ACCÈS PUBLIC SANS TOKEN
    """
    if not TTS_AVAILABLE:
        return JsonResponse({
            'error': 'Service TTS non disponible',
            'tts_available': False
        }, status=503)

    try:
        engine = pyttsx3.init()
        voices = engine.getProperty('voices')
        
        voice_list = []
        if voices:
            for voice in voices:
                voice_info = {
                    'id': voice.id,
                    'name': voice.name,
                    'languages': getattr(voice, 'languages', []),
                    'gender': getattr(voice, 'gender', 'unknown'),
                    'age': getattr(voice, 'age', 'unknown')
                }
                voice_list.append(voice_info)

        return JsonResponse({
            'success': True,
            'voices': voice_list,
            'count': len(voice_list),
            'tts_available': True,
            'access': 'PUBLIC - No token required'
        })

    except Exception as e:
        logger.error(f"Erreur listage voix: {e}")
        return JsonResponse({
            'error': f'Erreur voix: {str(e)}',
            'method': 'pyttsx3'
        }, status=500)


# === SERVICE D'EXTRACTION D'INFORMATIONS AVEC IA ===

class InformationExtractor:
    """Service d'extraction d'informations avec IA et patterns intelligents"""

    def __init__(self):
        self.ner_pipeline = None
        self._init_ai_models()

    def _init_ai_models(self):
        """Initialiser les modèles IA pour l'extraction"""
        if not AI_EXTRACTION_AVAILABLE:
            logger.warning("⚠️ Transformers non disponible")
            return

        try:
            # Modèle NER français pour extraction d'entités
            self.ner_pipeline = pipeline(
                "ner", 
                model="Jean-Baptiste/camembert-ner",
                aggregation_strategy="simple"
            )
            logger.info("✅ Modèle NER français initialisé")
        except Exception as e:
            logger.warning(f"⚠️ Erreur initialisation NER: {e}")

    def extract_information(self, text, question_field=None):
        """
        Extraire des informations d'un texte avec IA et patterns
        
        Args:
            text (str): Texte à analyser
            question_field (str): Champ spécifique du questionnaire
            
        Returns:
            dict: Informations extraites
        """
        if not text or not text.strip():
            return {'error': 'Texte vide'}

        text_clean = text.strip().lower()
        
        # Extraction spécifique par champ
        if question_field:
            return self._extract_by_field(text_clean, question_field)
        
        # Extraction générale
        return self._extract_general_info(text_clean)

    def _extract_by_field(self, text, field):
        """Extraction spécifique par champ du questionnaire"""
        extractors = {
            'nom': self._extract_name,
            'prenom': self._extract_name,
            'username': self._extract_name,
            'date_naissance': self._extract_date,
            'sexe': self._extract_gender,
            'taille': self._extract_height,
            'poids': self._extract_weight,
            'recherche': self._extract_search_type
        }
        
        extractor = extractors.get(field, self._extract_free_text)
        return extractor(text, field)

    def _extract_name(self, text, field):
        """Extraire un nom/prénom"""
        # Patterns pour noms
        import re
        
        patterns = [
            r'je m\'appelle\s+([a-zA-ZÀ-ÿ\-\s]+)',
            r'mon nom est\s+([a-zA-ZÀ-ÿ\-\s]+)',
            r'c\'est\s+([a-zA-ZÀ-ÿ\-\s]+)',
            r'^([a-zA-ZÀ-ÿ\-\s]+)$'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                name = match.group(1).strip().title()
                if len(name) > 1 and name.replace('-', '').replace(' ', '').isalpha():
                    return {
                        'field': field,
                        'value': name,
                        'confidence': 0.9,
                        'method': 'pattern_matching'
                    }
        
        return {
            'field': field,
            'value': None,
            'confidence': 0.0,
            'method': 'pattern_matching'
        }

    def _extract_gender(self, text, field):
        """Extraire le sexe/genre"""
        # Corrections phonétiques avec regex pour mots entiers
        import re
        corrected_text = text.lower()
        
        phonetic_corrections = {
            r'\bhum\b': 'homme',
            r'\bhume\b': 'homme', 
            r'\bhome\b': 'homme',
            r'\bom\b': 'homme',
            r'\bomme\b': 'homme',
            r'\bfem\b': 'femme',
            r'\bfame\b': 'femme',
            r'\bfam\b': 'femme'
        }
        
        for pattern, correct in phonetic_corrections.items():
            if re.search(pattern, corrected_text):
                corrected_text = re.sub(pattern, correct, corrected_text)
        
        # Mapping des genres
        gender_mapping = {
            'homme': 'M',
            'masculin': 'M',
            'mâle': 'M',
            'garçon': 'M',
            'monsieur': 'M',
            'femme': 'F',
            'féminin': 'F',
            'fille': 'F',
            'madame': 'F',
            'mademoiselle': 'F'
        }
        
        for keyword, gender in gender_mapping.items():
            if keyword in corrected_text:
                return {
                    'field': field,
                    'value': gender,
                    'confidence': 0.95,
                    'method': 'keyword_matching',
                    'original_text': text,
                    'corrected_text': corrected_text
                }
        
        return {
            'field': field,
            'value': None,
            'confidence': 0.0,
            'method': 'keyword_matching'
        }

    def _extract_date(self, text, field):
        """Extraire une date de naissance"""
        import re
        from datetime import datetime
        
        # Patterns pour dates
        patterns = [
            r'(\d{1,2})\s*(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)\s*(\d{4})',
            r'(\d{1,2})/(\d{1,2})/(\d{4})',
            r'(\d{1,2})-(\d{1,2})-(\d{4})',
            r'j\'ai\s*(\d{1,2})\s*ans',
            r'(\d{1,2})\s*ans'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                if 'ans' in pattern:
                    # Calculer la date de naissance à partir de l'âge
                    age = int(match.group(1))
                    current_year = datetime.now().year
                    birth_year = current_year - age
                    return {
                        'field': field,
                        'value': f"01/01/{birth_year}",
                        'confidence': 0.8,
                        'method': 'age_calculation',
                        'age': age
                    }
                else:
                    # Date directe
                    return {
                        'field': field,
                        'value': match.group(0),
                        'confidence': 0.9,
                        'method': 'date_pattern'
                    }
        
        return {
            'field': field,
            'value': None,
            'confidence': 0.0,
            'method': 'date_pattern'
        }

    def _extract_height(self, text, field):
        """Extraire la taille"""
        import re
        
        patterns = [
            r'(\d{1,3})\s*(?:centimètres|cm)',
            r'(\d{1})\s*mètre\s*(\d{1,2})',
            r'je\s*mesure\s*(\d{1,3})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                if 'mètre' in pattern:
                    meters = int(match.group(1))
                    cm = int(match.group(2)) if len(match.groups()) > 1 and match.group(2) else 0
                    height = meters * 100 + cm
                else:
                    height = int(match.group(1))
                
                if 140 <= height <= 220:  # Validation réaliste
                    return {
                        'field': field,
                        'value': height,
                        'confidence': 0.9,
                        'method': 'measurement_pattern'
                    }
        
        return {
            'field': field,
            'value': None,
            'confidence': 0.0,
            'method': 'measurement_pattern'
        }

    def _extract_weight(self, text, field):
        """Extraire le poids"""
        import re
        
        patterns = [
            r'(\d{1,3})\s*(kilos|kg|kilogrammes)',
            r'je\s*pèse\s*(\d{1,3})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                weight = int(match.group(1))
                if 30 <= weight <= 200:  # Validation réaliste
                    return {
                        'field': field,
                        'value': weight,
                        'confidence': 0.9,
                        'method': 'measurement_pattern'
                    }
        
        return {
            'field': field,
            'value': None,
            'confidence': 0.0,
            'method': 'measurement_pattern'
        }

    def _extract_search_type(self, text, field):
        """Extraire le type de recherche"""
        search_mapping = {
            'amical': 'Amical',
            'amitié': 'Amical',
            'ami': 'Amical',
            'amour': 'Amour',
            'relation': 'Amour',
            'sérieux': 'Amour',
            'libertin': 'Libertin',
            'coquin': 'Libertin',
            'sexe': 'Libertin'
        }
        
        for keyword, search_type in search_mapping.items():
            if keyword in text:
                return {
                    'field': field,
                    'value': search_type,
                    'confidence': 0.85,
                    'method': 'keyword_matching'
                }
        
        return {
            'field': field,
            'value': None,
            'confidence': 0.0,
            'method': 'keyword_matching'
        }

    def _extract_free_text(self, text, field):
        """Extraction de texte libre"""
        return {
            'field': field,
            'value': text.strip(),
            'confidence': 0.7,
            'method': 'free_text'
        }

    def _extract_general_info(self, text):
        """Extraction générale d'informations"""
        results = {}
        
        # Essayer d'extraire différents types d'informations
        fields_to_try = ['nom', 'prenom', 'date_naissance', 'sexe', 'taille', 'poids']
        
        for field in fields_to_try:
            result = self._extract_by_field(text, field)
            if result.get('value') and result.get('confidence', 0) > 0.5:
                results[field] = result
        
        return results


@csrf_exempt
@require_http_methods(["POST"])
def extract_info_api(request):
    """
    API d'extraction d'informations - ACCÈS PUBLIC SANS TOKEN
    
    POST Body (JSON):
        - text: Texte à analyser
        - question_field: Champ spécifique (optionnel)
    """
    try:
        data = json.loads(request.body)
        text = data.get('text', '').strip()
        
        if not text:
            return JsonResponse({
                'error': 'Texte requis',
                'required_fields': ['text']
            }, status=400)

        question_field = data.get('question_field')
        
        # Extraction d'informations
        extractor = InformationExtractor()
        result = extractor.extract_information(text, question_field)

        return JsonResponse({
            'success': True,
            'text': text,
            'question_field': question_field,
            'extracted_info': result,
            'ai_available': AI_EXTRACTION_AVAILABLE,
            'access': 'PUBLIC - No token required'
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'JSON invalide',
            'format': 'application/json'
        }, status=400)
    except Exception as e:
        logger.error(f"Erreur extraction: {e}")
        return JsonResponse({
            'error': f'Erreur extraction: {str(e)}',
            'method': 'ai_extraction'
        }, status=500)
