<!-- @format -->

<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Meet Voice - {% block title %}{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    {% load static %}
    {% csrf_token %}

    <!-- CSS locaux -->
    <link rel="stylesheet" href="{% static 'styles.css' %}" />
    <link rel="stylesheet" href="{% static 'index.css' %}" />

    <style>
      /* Styles pour la navbar */
      .navbar {
        background-color: #2a1d34;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1030;
        height: 70px;
      }

      .navbar-brand img {
        height: 40px;
        width: auto;
      }

      .navbar-nav .nav-link {
        color: rgba(255, 255, 255, 0.9) !important;
        font-weight: 500;
        padding: 0.75rem 1rem !important;
        border-radius: 0;
        margin: 0 0.25rem;
        transition: all 0.3s ease;
        position: relative;
      }

      .navbar-nav .nav-link:hover {
        color: #ffffff !important;
        background-color: rgba(255, 255, 255, 0.15);
        transform: translateY(-1px);
      }

      .navbar-nav .nav-link.active {
        color: #ffffff !important;
        background-color: rgba(255, 255, 255, 0.2);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }

      /* Styles pour les dropdowns */
      .dropdown-menu {
        background-color: #2a1d34 !important;
        border: 1px solid rgba(255, 255, 255, 0.15) !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
        margin-top: 0.5rem !important;
        min-width: 200px !important;
      }

      .dropdown-item {
        color: rgba(255, 255, 255, 0.9) !important;
        padding: 0.75rem 1rem !important;
        transition: all 0.3s ease !important;
        border-radius: 0 !important;
        font-weight: 500 !important;
      }

      .dropdown-item:hover {
        background-color: rgba(255, 255, 255, 0.15) !important;
        color: #ffffff !important;
        transform: translateY(-1px);
      }

      .dropdown-item:focus {
        background-color: rgba(255, 255, 255, 0.15) !important;
        color: #ffffff !important;
        box-shadow: none !important;
      }

      .dropdown-item:active {
        background-color: rgba(255, 255, 255, 0.2) !important;
        color: #ffffff !important;
      }

      .dropdown-toggle::after {
        margin-left: 0.5rem;
      }

      .dropdown-divider {
        border-color: rgba(255, 255, 255, 0.2) !important;
        margin: 0.5rem 0 !important;
      }

      /* Animation pour les dropdowns */
      .dropdown-menu {
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease;
        display: block !important;
        visibility: hidden;
      }

      .dropdown-menu.show {
        opacity: 1;
        transform: translateY(0);
        visibility: visible;
      }

      .navbar-nav .nav-link i {
        margin-right: 0.5rem;
      }

      /* Dropdown styles */
      .dropdown-menu {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: none;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-radius: 0;
        margin-top: 0.5rem;
      }

      .dropdown-item {
        color: #495057;
        padding: 0.75rem 1.25rem;
        border-radius: 0;
        margin: 0.25rem;
        transition: all 0.2s ease;
      }

      .dropdown-item:hover {
        background-color: rgba(102, 126, 234, 0.1);
        color: #667eea;
        transform: translateX(5px);
      }

      .dropdown-item i {
        width: 20px;
        text-align: center;
      }

      /* Responsive navbar */
      @media (max-width: 991.98px) {
        .navbar-nav {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 0;
          padding: 1rem;
          margin-top: 1rem;
        }

        .navbar-nav .nav-link {
          margin: 0.25rem 0;
          text-align: center;
        }
      }

      .navbar-toggler {
        border: none;
        padding: 0.25rem 0.5rem;
      }

      .navbar-toggler:focus {
        box-shadow: none;
      }

      .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
      }

      /* Ajustement pour éviter le débordement */
      body {
        padding-top: 70px;
      }

      .container-fluid {
        padding-left: 0;
        padding-right: 0;
      }
    </style>

    {% block head %}{% endblock %}
  </head>
  <body>
    <!-- Navbar fixe en haut -->
    <nav class="navbar navbar-expand-lg navbar-light">
      <div class="container-fluid">
        <a class="navbar-brand" href="{% url 'index' %}">
          <img src="{% static 'logo.png' %}" alt="Meet Voice Logo" />
        </a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarSupportedContent"
          aria-controls="navbarSupportedContent"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarSupportedContent">
          <ul class="navbar-nav ms-auto mb-2 mb-lg-0">

            {% if user.is_authenticated and user.is_staff %}
            <!-- Menu Newsletter + Contact -->
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle me-0 me-lg-3 {% if 'newsletter' in request.path or 'contact' in request.path %} active {% endif %}"
                 href="#" id="newsletterDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-envelope me-1"></i>Communication
              </a>
              <ul class="dropdown-menu" aria-labelledby="newsletterDropdown">
                <li>
                  <a class="dropdown-item" href="{% url 'newsletter:dashboard' %}">
                    <i class="fas fa-newspaper me-2"></i>Newsletter
                  </a>
                </li>
                <li>
                  <a class="dropdown-item" href="/contact/">
                    <i class="fas fa-headset me-2"></i>Messages Contact
                  </a>
                </li>
                <li>
                  <a class="dropdown-item" href="/contact/api/">
                    <i class="fas fa-cogs me-2"></i>API Contact
                  </a>
                </li>

              </ul>
            </li>
            {% endif %}

            <!-- Onglet Tarifs (visible pour tous) -->
            <li class="nav-item">
              <a
                class="nav-link me-0 me-lg-3 {% if 'abonnement' in request.path %} active {% endif %}"
                href="{% url 'abonnement:liste' %}"
              >
                <i class="fas fa-tags me-1"></i>Tarifs
              </a>
            </li>



            {% if user.is_authenticated %} {% if user.is_staff %}
            <!-- Menu Dashboard + Traffic + Analytics -->
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle me-0 me-lg-3 {% if 'dashboard' in request.path or 'traffic' in request.path or 'analytics' in request.path %} active {% endif %}"
                 href="#" id="dashboardDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
              </a>
              <ul class="dropdown-menu" aria-labelledby="dashboardDropdown">
                <li>
                  <a class="dropdown-item" href="{% url 'backoffice:dashboard' %}">
                    <i class="fas fa-home me-2"></i>Dashboard Principal
                  </a>
                </li>
                <li><hr class="dropdown-divider" style="border-color: rgba(255, 255, 255, 0.2);"></li>
                <li>
                  <a class="dropdown-item" href="{% url 'backoffice:traffic_manager' %}">
                    <i class="fas fa-chart-line me-2"></i>Traffic
                  </a>
                </li>
                <li>
                  <a class="dropdown-item" href="{% url 'backoffice:google_analytics' %}">
                    <i class="fab fa-google me-2"></i>Analytics
                  </a>
                </li>
              </ul>
            </li>


            <li class="nav-item">
              <a
                class="nav-link me-0 me-lg-3 {% if 'articles' in request.path %} active {% endif %}"
                href="{% url 'backoffice:articles' %}"
              >
                <i class="fas fa-newspaper me-1"></i>Articles
              </a>
            </li>
            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle me-0 me-lg-3 {% if 'moderation' in request.path %} active {% endif %}"
                href="#"
                id="moderationDropdown"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <i class="fas fa-shield-alt me-1"></i>Modération
              </a>
              <ul class="dropdown-menu" aria-labelledby="moderationDropdown">
                <li>
                  <a
                    class="dropdown-item"
                    href="{% url 'backoffice:moderation_posts' %}"
                  >
                    <i class="fas fa-comments me-2"></i>Posts du Mur
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="{% url 'backoffice:moderation_events' %}"
                  >
                    <i class="fas fa-calendar-check me-2"></i>Événements
                  </a>
                </li>
              </ul>
            </li>

            <!-- Onglet Réseaux Sociaux (visible pour les employés) -->
            <li class="nav-item">
              <a
                class="nav-link me-0 me-lg-3 {% if navbar == 'reseaux_social' %} active {% endif %}"
                href="{% url 'reseaux_social:liste' %}"
              >
                <i class="fas fa-share-alt me-1"></i>Réseaux Sociaux
              </a>
            </li>

            <!-- Onglet Documentation (visible pour les employés) -->
            <li class="nav-item">
              <a
                class="nav-link me-0 me-lg-3 {% if 'documentation' in request.path %} active {% endif %}"
                href="{% url 'documentation:dashboard' %}"
              >
                <i class="fas fa-book me-1"></i>Documentation
              </a>
            </li>
            {% endif %}

            <!-- Onglets utilisateur -->
            <li class="nav-item">
              <a class="nav-link me-0 me-lg-3" href="{% url 'logout' %}">
                <i class="fas fa-sign-out-alt me-1"></i>Déconnexion
              </a>
            </li>
            {% else %}
            <!-- Onglet connexion pour les non-connectés -->
            <li class="nav-item">
              <a
                class="nav-link me-0 me-lg-3 {% if navbar == 'login' %} active {% endif %}"
                href="{% url 'login' %}"
              >
                <i class="fas fa-sign-in-alt me-1"></i>Connexion
              </a>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container-fluid">{% block content %}{% endblock %}</div>

    <script>
      // Fonction pour récupérer les cookies (nécessaire pour CSRF)
      function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
          const cookies = document.cookie.split(';');
          for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
              cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
              break;
            }
          }
        }
        return cookieValue;
      }

      // Animation pour la navbar
      document.addEventListener("DOMContentLoaded", function () {
        // Fermer automatiquement la navbar mobile après clic sur un lien
        const navLinks = document.querySelectorAll(".navbar-nav .nav-link");
        const navbarCollapse = document.querySelector(".navbar-collapse");

        navLinks.forEach(function (link) {
          link.addEventListener("click", function () {
            if (window.innerWidth < 992) {
              const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
                toggle: false,
              });
              bsCollapse.hide();
            }
          });
        });
      });
    </script>

    {% block extra_js %}{% endblock %}
  </body>
</html>
