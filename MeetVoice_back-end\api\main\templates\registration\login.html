<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Meet Voice</title>
    {% load static %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            color: white;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            background-image: url("{% static 'Accueil.webp' %}");
            background-repeat: no-repeat;
            background-size: cover;
            overflow-x: hidden;
            margin: 0;
            padding: 0;
            height: 100vh;
        }

        h4 {
            padding-top: 30px;
            font-size: 28px;
            font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', <PERSON>l, sans-serif;
            font-style: italic;
            color: #ffffff;
            padding-bottom: 20px;
            margin: 0;
        }

        .bloc {
            text-align: center;
            width: 380px;
            height: auto;
            background: #3c0940;
            border: 1px solid black;
            margin: 150px 0 0 15%;
            padding: 0 20px 30px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .text {
            font-family: 'Times New Roman', Times, serif;
            color: white !important;
            font-weight: lighter;
            font-size: 20px;
            position: relative;
            display: flex;
            flex-flow: column wrap;
            align-items: center;
            padding: 0 10px;
        }

        .btnr {
            margin-top: 30px;
            background-color: rgb(58, 58, 64) !important;
            color: white !important;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            font-weight: 500;
            width: 200px;
        }

        .btnr:hover {
            border: 1px solid #1f8ea5;
            color: #1f8ea5 !important;
            background-color: black !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .form {
            margin-top: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .align {
            font-family: 'Times New Roman', Times, serif;
            font-weight: lighter;
            font-size: 22px;
            display: flex;
            flex-flow: column wrap;
            align-items: center;
        }

        .label {
            padding-top: 5px;
        }

        .form-group {
            margin-bottom: 20px;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        input[type="email"], input[type="password"] {
            margin: 8px 0;
            padding: 12px 15px;
            border: 1px solid #ccc;
            border-radius: 6px;
            width: 280px;
            max-width: 90%;
            font-size: 16px;
            font-family: Arial, sans-serif;
            transition: border-color 0.3s ease;
        }

        input[type="email"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #1f8ea5;
            box-shadow: 0 0 5px rgba(31, 142, 165, 0.3);
        }

        label {
            color: white;
            font-family: 'Times New Roman', Times, serif;
            font-size: 18px;
            margin-bottom: 8px;
            display: block;
            text-align: center;
        }

        .error-message {
            color: #ff6b6b;
            background-color: rgba(255, 107, 107, 0.1);
            border: 1px solid #ff6b6b;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 14px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .bloc {
                width: 95%;
                max-width: 350px;
                margin: 100px auto 0 auto;
                padding: 0 15px 25px 15px;
            }

            h4 {
                font-size: 24px;
                padding-top: 25px;
                padding-bottom: 15px;
            }

            input[type="email"], input[type="password"] {
                width: 250px;
                max-width: 85%;
            }

            .btnr {
                width: 180px;
                padding: 10px 20px;
            }

            .text {
                font-size: 18px;
            }
        }

        @media (max-width: 480px) {
            .bloc {
                width: 90%;
                margin: 80px auto 0 auto;
            }

            input[type="email"], input[type="password"] {
                width: 220px;
            }

            .btnr {
                width: 160px;
            }
        }
    </style>
</head>
<body>
    <div class="bloc">
        <h4>Connexion</h4>
        <div class="text">
            <form method="post">
                {% csrf_token %}

                {% if form.errors %}
                    <div class="error-message">
                        {% for field, errors in form.errors.items %}
                            {% for error in errors %}
                                {{ error }}
                            {% endfor %}
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="form-group">
                    <label for="username">Pseudo ou Email :</label>
                    <input type="text" id="username" name="username" placeholder="votre_<NAME_EMAIL>" required />
                </div>

                <div class="form-group">
                    <label for="password">Mot de passe :</label>
                    <input type="password" id="password" name="password" placeholder="staffpass123" required />
                </div>

                <button class="btn btnr" type="submit">Se connecter</button>
            </form>
        </div>
    </div>
</body>
</html>
