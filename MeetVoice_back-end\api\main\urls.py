from django.contrib import admin
from django.conf.urls.static import static
from django.urls import path, include
from django.views.decorators.csrf import csrf_exempt
from main import settings
from .views import *
from .stt_views import synthesize_api, voices_api, extract_info_api
from compte.facker import exect

from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

schema_view = get_schema_view(
   openapi.Info(
      title="MeetVoice API",
      default_version='v1',
      description="API de la plateforme de rencontres vocales MeetVoice avec service TTS intégré",
   ),
   public=True,
   permission_classes=(permissions.AllowAny,),
   patterns=[
       # Swagger documentation uniquement
   ],
)

urlpatterns = [
    path('swagger<format>/', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    path('admin/', admin.site.urls),
    path("", index, name="index"),
    path('profils/', profils, name='profils'),
    path('administration/', administration, name='admin'),
    path('chatres/',chatres, name='chatres'),
    path('cgu/',cgu,name='gcu'),
    path('reg/', exect, name='exect'),
    path('api/', include('compte.urls')),
    path('dashboard/', include('dashboard.urls')),
    path('backoffice/', include('backoffice.urls')),  # À supprimer après migration
    path('evenement/', include('evenement.urls')),
    path('actualite/', include('actualite.urls')),
    path('abonnement/', include('abonnement.urls')),
    path('reseaux-social/', include('reseaux_social.urls')),
    path('documentation/', include('documentation.urls')),
    path('newsletter/', include('newsletter.urls')),
    path('contact/', include('contact.urls')),  # API de contact
    path('api/contact/', include('contact.urls')),  # API de contact (alias)
    path('mur/', include('mur.urls')),
    path('commentaire/', include('commentaire.urls')),






    # === ENDPOINTS TTS ET EXTRACTION (SANS TOKEN) ===
    path('api/synthesize/', synthesize_api, name='api-synthesize'),
    path('api/voices/', voices_api, name='api-voices'),
    path('api/extract-info/', extract_info_api, name='api-extract-info'),

    # URLs d'authentification Django
    path('accounts/', include('django.contrib.auth.urls')),

]

# Servir les fichiers statiques et media en mode développement
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATICFILES_DIRS[0])