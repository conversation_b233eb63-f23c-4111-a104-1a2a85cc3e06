from django.contrib import admin
from .models import VoiceProfile, TTSRequest, UserVoicePreference


@admin.register(VoiceProfile)
class VoiceProfileAdmin(admin.ModelAdmin):
    list_display = ['name', 'voice_type', 'language', 'is_active', 'is_premium', 'created_at']
    list_filter = ['voice_type', 'language', 'is_active', 'is_premium']
    search_fields = ['name', 'description']
    ordering = ['voice_type', 'name']

    fieldsets = (
        ('Informations générales', {
            'fields': ('name', 'voice_type', 'language', 'description')
        }),
        ('Configuration', {
            'fields': ('is_active', 'is_premium')
        }),
    )


@admin.register(TTSRequest)
class TTSRequestAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'user', 'voice_profile', 'status', 'text_preview',
        'duration', 'file_size_mb', 'created_at'
    ]
    list_filter = ['status', 'voice_profile__voice_type', 'created_at']
    search_fields = ['user__username', 'text']
    readonly_fields = ['id', 'created_at', 'completed_at', 'file_size', 'duration']
    ordering = ['-created_at']

    fieldsets = (
        ('Demande', {
            'fields': ('id', 'user', 'text', 'voice_profile')
        }),
        ('Statut', {
            'fields': ('status', 'error_message')
        }),
        ('Résultat', {
            'fields': ('audio_file', 'duration', 'file_size')
        }),
        ('Dates', {
            'fields': ('created_at', 'completed_at')
        }),
    )

    def text_preview(self, obj):
        """Aperçu du texte (50 premiers caractères)"""
        return obj.text[:50] + '...' if len(obj.text) > 50 else obj.text
    text_preview.short_description = 'Texte'

    def file_size_mb(self, obj):
        """Taille du fichier en MB"""
        if obj.file_size:
            return f"{obj.file_size / (1024*1024):.2f} MB"
        return "-"
    file_size_mb.short_description = 'Taille'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'voice_profile')


@admin.register(UserVoicePreference)
class UserVoicePreferenceAdmin(admin.ModelAdmin):
    list_display = ['user', 'preferred_voice', 'speech_rate', 'volume', 'updated_at']
    list_filter = ['preferred_voice__voice_type', 'updated_at']
    search_fields = ['user__username']
    ordering = ['-updated_at']

    fieldsets = (
        ('Utilisateur', {
            'fields': ('user',)
        }),
        ('Préférences', {
            'fields': ('preferred_voice', 'speech_rate', 'volume')
        }),
        ('Dates', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    readonly_fields = ['created_at', 'updated_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'preferred_voice')
