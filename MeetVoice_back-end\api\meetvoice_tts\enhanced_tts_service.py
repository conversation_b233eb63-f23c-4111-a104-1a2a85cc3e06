"""
Service TTS amélioré avec différenciation des voix
"""
import os
import tempfile
import logging
from datetime import datetime
from django.core.files.base import ContentFile
from django.conf import settings
from gtts import gTTS
import pyttsx3

logger = logging.getLogger(__name__)

try:
    from pydub import AudioSegment
    from pydub.effects import speedup, normalize
    PYDUB_AVAILABLE = True
    logger.info("🎵 pydub disponible - différenciation vocale complète activée")
except ImportError:
    PYDUB_AVAILABLE = False
    logger.info("🎵 pydub non disponible - utilisation de la différenciation basique")


class EnhancedTTSService:
    """Service TTS amélioré avec vraies différences vocales"""
    
    def __init__(self):
        self.temp_dir = getattr(settings, 'TTS_TEMP_DIR', tempfile.gettempdir())
        
        # Configuration des voix par type
        self.voice_configs = {
            'female_young': {
                'speed_factor': 1.1,
                'pitch_shift': 50,
                'volume_boost': 2,
                'description': 'Voix féminine jeune - rapide et énergique'
            },
            'female_mature': {
                'speed_factor': 0.9,
                'pitch_shift': -20,
                'volume_boost': 0,
                'description': 'Voix féminine mature - posée et élégante'
            },
            'male_young': {
                'speed_factor': 1.05,
                'pitch_shift': -100,
                'volume_boost': 3,
                'description': 'Voix masculine jeune - dynamique'
            },
            'male_mature': {
                'speed_factor': 0.85,
                'pitch_shift': -150,
                'volume_boost': 1,
                'description': 'Voix masculine mature - grave et rassurante'
            },
            'neutral': {
                'speed_factor': 1.0,
                'pitch_shift': 0,
                'volume_boost': 1,
                'description': 'Voix neutre - équilibrée'
            },
            'custom_cloned': {
                'speed_factor': 0.95,
                'pitch_shift': 25,
                'volume_boost': 2,
                'description': 'Voix clonée personnalisée'
            }
        }
    
    def synthesize_with_voice_differentiation(self, text, voice_profile):
        """Synthèse avec différenciation vocale réelle"""
        
        try:
            # Étape 1: Synthèse de base avec gTTS
            base_audio = self._generate_base_audio(text, voice_profile)
            if not base_audio:
                return None
            
            # Étape 2: Modification selon le type de voix
            if PYDUB_AVAILABLE:
                logger.info(f"🎵 Utilisation de pydub pour {voice_profile.voice_type}")
                modified_audio = self._apply_voice_effects(base_audio, voice_profile)
                return modified_audio
            else:
                # Fallback sans pydub - différenciation basique déjà appliquée
                logger.info(f"🎵 Différenciation basique pour {voice_profile.voice_type}")
                return base_audio
                
        except Exception as e:
            logger.error(f"Erreur synthèse différenciée: {str(e)}")
            return None
    
    def _generate_base_audio(self, text, voice_profile):
        """Générer l'audio de base avec gTTS"""
        
        try:
            # Configuration gTTS selon le profil avec différenciation
            lang = voice_profile.language

            # Différenciation par vitesse et préfixe
            voice_configs = {
                'female_young': {'slow': False, 'prefix': '🎵 '},
                'female_mature': {'slow': True, 'prefix': '🎼 '},
                'male_young': {'slow': False, 'prefix': '🎸 '},
                'male_mature': {'slow': True, 'prefix': '🎺 '},
                'neutral': {'slow': False, 'prefix': '🎹 '},
                'custom_cloned': {'slow': False, 'prefix': '✨ '},
                'custom_trained': {'slow': True, 'prefix': '🌟 '}
            }

            config = voice_configs.get(voice_profile.voice_type, voice_configs['neutral'])

            # Modifier le texte pour créer une différence audible
            modified_text = config['prefix'] + text

            # Créer l'objet gTTS avec configuration spécifique
            tts = gTTS(text=modified_text, lang=lang, slow=config['slow'])

            logger.info(f"🎭 gTTS configuré pour {voice_profile.voice_type}: slow={config['slow']}, prefix={config['prefix']}")
            
            # Fichier temporaire
            import time
            temp_filename = f"base_audio_{int(time.time() * 1000)}.mp3"
            temp_path = os.path.join(self.temp_dir, temp_filename)
            
            os.makedirs(self.temp_dir, exist_ok=True)
            
            # Sauvegarder
            tts.save(temp_path)
            
            # Lire les données
            with open(temp_path, 'rb') as f:
                audio_data = f.read()
            
            # Nettoyer
            try:
                os.unlink(temp_path)
            except OSError:
                pass
            
            return audio_data
            
        except Exception as e:
            logger.error(f"Erreur génération audio de base: {str(e)}")
            return None
    
    def _apply_voice_effects(self, audio_data, voice_profile):
        """Appliquer les effets vocaux selon le type de voix"""
        
        if not PYDUB_AVAILABLE:
            return audio_data
        
        try:
            # Charger l'audio avec pydub
            audio = AudioSegment.from_mp3(io.BytesIO(audio_data))
            
            # Récupérer la configuration pour ce type de voix
            config = self.voice_configs.get(voice_profile.voice_type, self.voice_configs['neutral'])
            
            # Appliquer les modifications
            
            # 1. Vitesse
            if config['speed_factor'] != 1.0:
                if config['speed_factor'] > 1.0:
                    audio = speedup(audio, playback_speed=config['speed_factor'])
                else:
                    # Ralentir (plus complexe)
                    audio = audio._spawn(audio.raw_data, overrides={
                        "frame_rate": int(audio.frame_rate * config['speed_factor'])
                    }).set_frame_rate(audio.frame_rate)
            
            # 2. Volume
            if config['volume_boost'] != 0:
                audio = audio + config['volume_boost']
            
            # 3. Normalisation
            audio = normalize(audio)
            
            # 4. Simulation de changement de pitch (approximatif)
            if config['pitch_shift'] != 0:
                # Méthode simple: changer la vitesse puis revenir à la durée originale
                pitch_factor = 1.0 + (config['pitch_shift'] / 1000.0)
                if pitch_factor > 0.5 and pitch_factor < 2.0:
                    # Changer le pitch en modifiant le sample rate
                    new_sample_rate = int(audio.frame_rate * pitch_factor)
                    audio = audio._spawn(audio.raw_data, overrides={"frame_rate": new_sample_rate})
                    audio = audio.set_frame_rate(22050)  # Normaliser le sample rate
            
            # Exporter vers bytes
            import io
            output_buffer = io.BytesIO()
            audio.export(output_buffer, format="mp3")
            modified_audio_data = output_buffer.getvalue()
            
            logger.info(f"Effets appliqués pour {voice_profile.voice_type}: vitesse={config['speed_factor']}, pitch={config['pitch_shift']}")
            
            return modified_audio_data
            
        except Exception as e:
            logger.error(f"Erreur application effets: {str(e)}")
            # Retourner l'audio original en cas d'erreur
            return audio_data
    
    def get_voice_preview_text(self, voice_type):
        """Obtenir un texte de prévisualisation adapté au type de voix"""
        
        previews = {
            'female_young': "Salut ! Je suis Sophie, une voix féminine jeune et dynamique. J'adore parler vite et avec énergie !",
            'female_mature': "Bonjour, je suis Camille. Ma voix est posée et élégante, parfaite pour les conversations sérieuses.",
            'male_young': "Hey ! Moi c'est Lucas, une voix masculine jeune et moderne. Je suis parfait pour les discussions décontractées !",
            'male_mature': "Bonjour, je suis Antoine. Ma voix grave et rassurante convient parfaitement aux présentations professionnelles.",
            'neutral': "Bonjour, je suis Alex, une voix neutre et équilibrée, adaptée à tous les types de contenus.",
            'custom_cloned': "Bonjour, je suis une voix personnalisée créée spécialement pour vous. Mon timbre est unique !"
        }
        
        return previews.get(voice_type, "Bonjour, ceci est un test de synthèse vocale.")
    
    def test_all_voice_types(self):
        """Tester tous les types de voix avec des textes adaptés"""
        
        results = {}
        
        for voice_type, config in self.voice_configs.items():
            try:
                # Créer un profil vocal temporaire
                from .models import VoiceProfile
                temp_profile = VoiceProfile(
                    voice_type=voice_type,
                    language='fr',
                    name=f"Test {voice_type}"
                )
                
                # Texte de test adapté
                test_text = self.get_voice_preview_text(voice_type)
                
                # Synthèse
                audio_data = self.synthesize_with_voice_differentiation(test_text, temp_profile)
                
                results[voice_type] = {
                    'success': audio_data is not None,
                    'audio_size': len(audio_data) if audio_data else 0,
                    'config': config,
                    'test_text': test_text
                }
                
            except Exception as e:
                results[voice_type] = {
                    'success': False,
                    'error': str(e),
                    'config': config
                }
        
        return results


def install_pydub_instructions():
    """Instructions pour installer pydub"""
    
    instructions = """
🔧 INSTALLATION DE PYDUB POUR DIFFÉRENCIATION VOCALE

Pour avoir de vraies différences entre les voix, installez pydub:

1. Installer pydub:
   pip install pydub

2. Installer ffmpeg (requis pour pydub):
   
   Windows:
   - Télécharger ffmpeg: https://ffmpeg.org/download.html
   - Ajouter au PATH système
   
   Ou avec chocolatey:
   choco install ffmpeg
   
   Ou avec conda:
   conda install -c conda-forge ffmpeg

3. Redémarrer le serveur Django

Avec pydub, chaque type de voix aura:
- Vitesse différente
- Pitch/hauteur différent  
- Volume ajusté
- Effets audio personnalisés

Sans pydub: toutes les voix sonnent pareil (limitation de gTTS)
"""
    
    return instructions
