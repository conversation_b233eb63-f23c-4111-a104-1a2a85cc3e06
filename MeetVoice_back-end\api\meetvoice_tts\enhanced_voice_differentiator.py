"""
Différenciateur de voix amélioré avec vraie différenciation
"""
import os
import tempfile
import logging
from gtts import gTTS
import time

logger = logging.getLogger(__name__)


class EnhancedVoiceDifferentiator:
    """Différenciateur de voix avec techniques avancées"""
    
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
        
        # Configuration avancée des voix
        self.voice_configs = {
            'female_young': {
                'engine': 'gtts',
                'lang': 'fr',
                'tld': 'fr',
                'slow': False,
                'text_prefix': "",
                'text_suffix': "",
                'description': 'Voix féminine jeune - France standard'
            },
            'female_mature': {
                'engine': 'gtts',
                'lang': 'fr',
                'tld': 'ca',  # Accent canadien
                'slow': True,
                'text_prefix': "",
                'text_suffix': "",
                'description': 'Voix féminine mature - Accent canadien lent'
            },
            'male_young': {
                'engine': 'gtts',
                'lang': 'en',  # Utiliser l'anglais pour différencier
                'tld': 'us',
                'slow': False,
                'text_prefix': "",
                'text_suffix': "",
                'translate_to_french': True,  # Traduire en français d'abord
                'description': 'Voix masculine jeune - Anglais US (traduit)'
            },
            'male_mature': {
                'engine': 'gtts',
                'lang': 'en',  # Utiliser l'anglais pour différencier
                'tld': 'co.uk',  # Accent britannique
                'slow': True,
                'text_prefix': "",
                'text_suffix': "",
                'translate_to_french': True,  # Traduire en français d'abord
                'description': 'Voix masculine mature - Anglais UK lent (traduit)'
            },
            'neutral': {
                'engine': 'gtts',
                'lang': 'fr',
                'tld': 'be',  # Accent belge
                'slow': False,
                'text_prefix': "",
                'text_suffix': "",
                'description': 'Voix neutre - Accent belge'
            }
        }
    
    def differentiate_voice(self, text, voice_type, language='fr'):
        """Créer une voix vraiment différenciée"""
        
        try:
            config = self.voice_configs.get(voice_type, self.voice_configs['neutral'])
            
            # Préparer le texte
            processed_text = self._process_text(text, config, voice_type)
            
            # Synthétiser selon la configuration
            return self._synthesize_voice(processed_text, config, voice_type)
            
        except Exception as e:
            logger.error(f"❌ Erreur différenciation améliorée {voice_type}: {e}")
            return None
    
    def _process_text(self, text, config, voice_type):
        """Traiter le texte selon le type de voix"""

        # Modifications spécifiques par type - SANS emojis ni préfixes
        if voice_type == 'female_young':
            # Voix énergique - juste le texte original
            processed = text

        elif voice_type == 'female_mature':
            # Voix élégante et posée - juste le texte original
            processed = text

        elif voice_type == 'male_young':
            # Voix dynamique - traduire en anglais pour différenciation
            if config.get('translate_to_french'):
                processed = self._simple_translate_to_english(text)
            else:
                processed = text

        elif voice_type == 'male_mature':
            # Voix autoritaire - traduire en anglais pour différenciation
            if config.get('translate_to_french'):
                processed = self._simple_translate_to_english(text)
            else:
                processed = text

        else:  # neutral
            # Voix équilibrée - juste le texte original
            processed = text
        
        return processed
    
    def _simple_translate_to_english(self, french_text):
        """Traduction simple français -> anglais pour différenciation"""
        
        # Dictionnaire de traductions simples
        translations = {
            'bonjour': 'hello',
            'salut': 'hi',
            'merci': 'thank you',
            'au revoir': 'goodbye',
            'oui': 'yes',
            'non': 'no',
            'je suis': 'I am',
            'vous êtes': 'you are',
            'il est': 'he is',
            'elle est': 'she is',
            'nous sommes': 'we are',
            'voix': 'voice',
            'synthèse': 'synthesis',
            'vocale': 'vocal',
            'technologie': 'technology',
            'avancée': 'advanced',
            'naturelle': 'natural',
            'expressive': 'expressive',
            'test': 'test',
            'cette': 'this',
            'pour': 'for',
            'avec': 'with',
            'une': 'a',
            'le': 'the',
            'la': 'the',
            'les': 'the',
            'de': 'of',
            'du': 'of the',
            'des': 'of the'
        }
        
        # Traduction mot par mot (simple)
        words = french_text.lower().split()
        translated_words = []
        
        for word in words:
            # Nettoyer la ponctuation
            clean_word = word.strip('.,!?;:')
            punctuation = word[len(clean_word):]
            
            # Traduire si possible
            translated = translations.get(clean_word, clean_word)
            translated_words.append(translated + punctuation)
        
        return ' '.join(translated_words)
    
    def _synthesize_voice(self, text, config, voice_type):
        """Synthétiser la voix avec la configuration donnée"""
        
        try:
            lang = config.get('lang', 'fr')
            tld = config.get('tld', 'fr')
            slow = config.get('slow', False)
            
            logger.info(f"🎭 Synthèse améliorée {voice_type}: lang={lang}, tld={tld}, slow={slow}")
            
            # Créer gTTS avec configuration spécifique
            tts = gTTS(
                text=text,
                lang=lang,
                slow=slow,
                tld=tld
            )
            
            # Fichier temporaire unique
            temp_filename = f"enhanced_voice_{voice_type}_{int(time.time() * 1000000)}.mp3"
            temp_path = os.path.join(self.temp_dir, temp_filename)
            
            os.makedirs(self.temp_dir, exist_ok=True)
            
            # Sauvegarder
            tts.save(temp_path)
            
            # Lire les données
            with open(temp_path, 'rb') as f:
                audio_data = f.read()
            
            # Nettoyer
            try:
                os.unlink(temp_path)
            except OSError:
                pass
            
            logger.info(f"✅ Synthèse améliorée réussie pour {voice_type}: {len(audio_data)} bytes")
            return audio_data
            
        except Exception as e:
            logger.error(f"❌ Erreur synthèse améliorée {voice_type}: {e}")
            return None
    
    def get_voice_description(self, voice_type):
        """Obtenir la description d'une voix"""
        config = self.voice_configs.get(voice_type, {})
        return config.get('description', f'Voix {voice_type}')
    
    def list_available_voices(self):
        """Lister toutes les voix disponibles"""
        voices = []
        for voice_type, config in self.voice_configs.items():
            voices.append({
                'type': voice_type,
                'description': config.get('description', ''),
                'language': config.get('lang', 'fr'),
                'tld': config.get('tld', 'fr'),
                'slow': config.get('slow', False)
            })
        return voices
