# Generated by Django 5.2.3 on 2025-06-29 22:51

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='VoiceProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Nom du profil')),
                ('voice_type', models.CharField(choices=[('male_young', 'Homme Jeune'), ('male_mature', 'Homme Mature'), ('female_young', 'Femme Jeune'), ('female_mature', 'Femme Mature'), ('neutral', 'Neutre')], max_length=20, verbose_name='Type de voix')),
                ('language', models.CharField(choices=[('fr', 'Français'), ('en', 'Anglais'), ('es', 'Espagnol'), ('it', 'Italien')], default='fr', max_length=5, verbose_name='Langue')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Actif')),
                ('is_premium', models.BooleanField(default=False, verbose_name='Premium')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Profil vocal',
                'verbose_name_plural': 'Profils vocaux',
                'ordering': ['voice_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='UserVoicePreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('speech_rate', models.FloatField(default=1.0, verbose_name='Vitesse de parole')),
                ('volume', models.FloatField(default=1.0, verbose_name='Volume')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Utilisateur')),
                ('preferred_voice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='meetvoice_tts.voiceprofile', verbose_name='Voix préférée')),
            ],
            options={
                'verbose_name': 'Préférence vocale',
                'verbose_name_plural': 'Préférences vocales',
            },
        ),
        migrations.CreateModel(
            name='TTSRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('text', models.TextField(validators=[django.core.validators.MinLengthValidator(1), django.core.validators.MaxLengthValidator(5000)], verbose_name='Texte à synthétiser')),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('processing', 'En cours'), ('completed', 'Terminé'), ('failed', 'Échec')], default='pending', max_length=20, verbose_name='Statut')),
                ('audio_file', models.FileField(blank=True, null=True, upload_to='tts_audio/', verbose_name='Fichier audio')),
                ('duration', models.FloatField(blank=True, null=True, verbose_name='Durée (secondes)')),
                ('file_size', models.IntegerField(blank=True, null=True, verbose_name='Taille du fichier (bytes)')),
                ('error_message', models.TextField(blank=True, verbose_name="Message d'erreur")),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Créé le')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='Terminé le')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Utilisateur')),
                ('voice_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='meetvoice_tts.voiceprofile', verbose_name='Profil vocal')),
            ],
            options={
                'verbose_name': 'Demande TTS',
                'verbose_name_plural': 'Demandes TTS',
                'ordering': ['-created_at'],
            },
        ),
    ]
