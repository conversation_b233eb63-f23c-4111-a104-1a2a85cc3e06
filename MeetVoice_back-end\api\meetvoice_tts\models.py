from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinLengthValidator, MaxLengthValidator
import uuid
import os

User = get_user_model()

class VoiceProfile(models.Model):
    """Profils vocaux disponibles pour la synthèse"""

    VOICE_TYPES = [
        ('male_young', 'Homme Jeune'),
        ('male_mature', 'Homme Mature'),
        ('female_young', 'Femme Jeune'),
        ('female_mature', 'Femme Mature'),
        ('neutral', 'Neutre'),
    ]

    LANGUAGES = [
        ('fr', 'Français'),
        ('en', 'Anglais'),
        ('es', 'Espagnol'),
        ('it', 'Italien'),
    ]

    name = models.CharField(max_length=100, verbose_name="Nom du profil")
    voice_type = models.CharField(max_length=20, choices=VOICE_TYPES, verbose_name="Type de voix")
    language = models.Char<PERSON><PERSON>(max_length=5, choices=LANGUAGES, default='fr', verbose_name="Langue")
    description = models.TextField(blank=True, verbose_name="Description")
    is_active = models.BooleanField(default=True, verbose_name="Actif")
    is_premium = models.BooleanField(default=False, verbose_name="Premium")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Profil vocal"
        verbose_name_plural = "Profils vocaux"
        ordering = ['voice_type', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_voice_type_display()})"


class TTSRequest(models.Model):
    """Demandes de synthèse vocale"""

    STATUS_CHOICES = [
        ('pending', 'En attente'),
        ('processing', 'En cours'),
        ('completed', 'Terminé'),
        ('failed', 'Échec'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="Utilisateur")
    text = models.TextField(
        validators=[MinLengthValidator(1), MaxLengthValidator(5000)],
        verbose_name="Texte à synthétiser"
    )
    voice_profile = models.ForeignKey(VoiceProfile, on_delete=models.CASCADE, verbose_name="Profil vocal")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="Statut")
    audio_file = models.FileField(upload_to='tts_audio/', blank=True, null=True, verbose_name="Fichier audio")
    duration = models.FloatField(blank=True, null=True, verbose_name="Durée (secondes)")
    file_size = models.IntegerField(blank=True, null=True, verbose_name="Taille du fichier (bytes)")
    error_message = models.TextField(blank=True, verbose_name="Message d'erreur")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Créé le")
    completed_at = models.DateTimeField(blank=True, null=True, verbose_name="Terminé le")

    class Meta:
        verbose_name = "Demande TTS"
        verbose_name_plural = "Demandes TTS"
        ordering = ['-created_at']

    def __str__(self):
        return f"TTS {self.id} - {self.user.username} ({self.status})"

    def get_audio_url(self):
        """Retourne l'URL du fichier audio si disponible"""
        if self.audio_file:
            return self.audio_file.url
        return None

    def delete_audio_file(self):
        """Supprime le fichier audio du système de fichiers"""
        if self.audio_file and os.path.isfile(self.audio_file.path):
            os.remove(self.audio_file.path)


class UserVoicePreference(models.Model):
    """Préférences vocales des utilisateurs"""

    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name="Utilisateur")
    preferred_voice = models.ForeignKey(
        VoiceProfile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="Voix préférée"
    )
    speech_rate = models.FloatField(default=1.0, verbose_name="Vitesse de parole")
    volume = models.FloatField(default=1.0, verbose_name="Volume")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Préférence vocale"
        verbose_name_plural = "Préférences vocales"

    def __str__(self):
        return f"Préférences de {self.user.username}"
