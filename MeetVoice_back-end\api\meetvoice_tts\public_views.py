"""
API TTS Simplifiée - Un seul endpoint pour tout
"""
import json
import os
import uuid
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.conf import settings
from .models import VoiceProfile
from .services import TTSService


@csrf_exempt
@require_http_methods(["GET", "POST"])
def simple_tts_api(request):
    """
    API TTS Unique et Simplifiée
    
    GET: Retourne toutes les voix disponibles
    POST: Synthétise le texte avec la voix choisie
    """
    
    if request.method == 'GET':
        # === LISTE DES VOIX DISPONIBLES ===
        try:
            # Toutes les voix actives (plus de restriction premium)
            voices = VoiceProfile.objects.filter(is_active=True)
            
            voices_data = []
            for voice in voices:
                voices_data.append({
                    'id': str(voice.id),
                    'voice_type': voice.voice_type,
                    'language': voice.language,
                    'name': voice.name,
                    'description': voice.description
                })
            
            return JsonResponse({
                'success': True,
                'voices': voices_data,
                'total': len(voices_data)
            })
            
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
    elif request.method == 'POST':
        # === SYNTHÈSE VOCALE ===
        try:
            # Parser les données JSON
            if request.content_type == 'application/json':
                data = json.loads(request.body)
            else:
                data = request.POST
            
            # Validation du texte
            text = data.get('text', '').strip()
            if not text:
                return JsonResponse({'success': False, 'error': 'Le texte ne peut pas être vide'}, status=400)
            
            if len(text) > 5000:
                return JsonResponse({'success': False, 'error': 'Texte limité à 5000 caractères'}, status=400)
            
            # Récupérer la voix
            voice_id = data.get('voice_id')
            voice_type = data.get('voice_type', 'female_young')
            language = data.get('language', 'fr')
            
            voice_profile = None
            
            if voice_id:
                try:
                    voice_profile = VoiceProfile.objects.get(id=voice_id, is_active=True)
                    voice_type = voice_profile.voice_type
                    language = voice_profile.language
                except VoiceProfile.DoesNotExist:
                    return JsonResponse({'success': False, 'error': f'Voix avec ID {voice_id} non trouvée'}, status=404)
            else:
                # Chercher une voix correspondante
                voice_profile = VoiceProfile.objects.filter(
                    voice_type=voice_type,
                    language=language,
                    is_active=True
                ).first()
            
            # Synthèse vocale (limitation: gTTS utilise le même moteur Google)
            tts_service = TTSService()
            audio_data = tts_service.quick_synthesize(text, voice_type, language)
            
            if audio_data:
                # Sauvegarder temporairement
                filename = f"tts_{uuid.uuid4().hex[:8]}.mp3"
                temp_dir = os.path.join(settings.MEDIA_ROOT, 'temp')
                os.makedirs(temp_dir, exist_ok=True)
                filepath = os.path.join(temp_dir, filename)
                
                with open(filepath, 'wb') as f:
                    f.write(audio_data)
                
                # URL publique
                audio_url = f"{settings.MEDIA_URL}temp/{filename}"
                if not audio_url.startswith('http'):
                    scheme = 'https' if request.is_secure() else 'http'
                    host = request.get_host()
                    audio_url = f"{scheme}://{host}{audio_url}"
                
                return JsonResponse({
                    'success': True,
                    'audio_url': audio_url,
                    'voice_name': voice_profile.name if voice_profile else f"Voix {voice_type}",
                    'language': language,
                    'voice_type': voice_type,
                    'text_length': len(text)
                })
            else:
                return JsonResponse({'success': False, 'error': 'Échec de la synthèse vocale'}, status=400)
                
        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'JSON invalide'}, status=400)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)


# Note: gTTS (Google Text-to-Speech) utilise le même moteur vocal de base
# Les différences entre voice_type sont subtiles (accent, vitesse)
# Pour de vraies voix différentes, il faudrait utiliser des services premium
