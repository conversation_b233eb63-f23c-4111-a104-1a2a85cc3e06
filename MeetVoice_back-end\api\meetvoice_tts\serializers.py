from rest_framework import serializers
from .models import VoiceProfile, TTSRequest, UserVoicePreference


class VoiceProfileSerializer(serializers.ModelSerializer):
    """Serializer pour les profils vocaux"""
    
    class Meta:
        model = VoiceProfile
        fields = [
            'id', 'name', 'voice_type', 'language', 'description', 
            'is_active', 'is_premium', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class TTSRequestSerializer(serializers.ModelSerializer):
    """Serializer pour les demandes TTS"""
    
    audio_url = serializers.SerializerMethodField()
    voice_profile_name = serializers.CharField(source='voice_profile.name', read_only=True)
    
    class Meta:
        model = TTSRequest
        fields = [
            'id', 'text', 'voice_profile', 'voice_profile_name', 'status', 
            'audio_file', 'audio_url', 'duration', 'file_size', 
            'error_message', 'created_at', 'completed_at'
        ]
        read_only_fields = [
            'id', 'status', 'audio_file', 'audio_url', 'duration', 
            'file_size', 'error_message', 'created_at', 'completed_at'
        ]
    
    def get_audio_url(self, obj):
        """Retourne l'URL complète du fichier audio"""
        if obj.audio_file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.audio_file.url)
            return obj.audio_file.url
        return None
    
    def validate_text(self, value):
        """Validation du texte à synthétiser"""
        if len(value.strip()) < 1:
            raise serializers.ValidationError("Le texte ne peut pas être vide.")
        if len(value) > 5000:
            raise serializers.ValidationError("Le texte ne peut pas dépasser 5000 caractères.")
        return value.strip()


class TTSCreateSerializer(serializers.ModelSerializer):
    """Serializer pour créer une demande TTS"""
    
    class Meta:
        model = TTSRequest
        fields = ['text', 'voice_profile']
    
    def validate_text(self, value):
        """Validation du texte à synthétiser"""
        if len(value.strip()) < 1:
            raise serializers.ValidationError("Le texte ne peut pas être vide.")
        if len(value) > 5000:
            raise serializers.ValidationError("Le texte ne peut pas dépasser 5000 caractères.")
        return value.strip()
    
    def validate_voice_profile(self, value):
        """Validation du profil vocal"""
        if not value.is_active:
            raise serializers.ValidationError("Ce profil vocal n'est pas disponible.")
        return value


class UserVoicePreferenceSerializer(serializers.ModelSerializer):
    """Serializer pour les préférences vocales utilisateur"""
    
    preferred_voice_name = serializers.CharField(source='preferred_voice.name', read_only=True)
    
    class Meta:
        model = UserVoicePreference
        fields = [
            'preferred_voice', 'preferred_voice_name', 'speech_rate', 
            'volume', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate_speech_rate(self, value):
        """Validation de la vitesse de parole"""
        if not 0.5 <= value <= 2.0:
            raise serializers.ValidationError("La vitesse de parole doit être entre 0.5 et 2.0.")
        return value
    
    def validate_volume(self, value):
        """Validation du volume"""
        if not 0.1 <= value <= 1.0:
            raise serializers.ValidationError("Le volume doit être entre 0.1 et 1.0.")
        return value


class TTSQuickSerializer(serializers.Serializer):
    """Serializer pour la synthèse vocale rapide (sans sauvegarde)"""
    
    text = serializers.CharField(max_length=1000)
    voice_type = serializers.ChoiceField(choices=VoiceProfile.VOICE_TYPES, default='female_young')
    language = serializers.ChoiceField(choices=VoiceProfile.LANGUAGES, default='fr')
    
    def validate_text(self, value):
        """Validation du texte à synthétiser"""
        if len(value.strip()) < 1:
            raise serializers.ValidationError("Le texte ne peut pas être vide.")
        return value.strip()
