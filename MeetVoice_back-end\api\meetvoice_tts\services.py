import os
import tempfile
import logging
from datetime import datetime
from django.core.files.base import ContentFile
from django.conf import settings
from gtts import gTTS
import pyttsx3
from .models import TTSRequest, VoiceProfile

logger = logging.getLogger(__name__)

# Importer le différenciateur de voix amélioré
try:
    from .enhanced_voice_differentiator import EnhancedVoiceDifferentiator
    ENHANCED_VOICE_DIFF_AVAILABLE = True
    logger.info("🎭 Différenciateur de voix AMÉLIORÉ importé avec succès")
except ImportError as e:
    ENHANCED_VOICE_DIFF_AVAILABLE = False
    logger.error(f"❌ Impossible d'importer le différenciateur amélioré: {e}")

# Importer l'ancien différenciateur en fallback
try:
    from .voice_differentiator import VoiceDifferentiator
    VOICE_DIFF_AVAILABLE = True
    logger.info("🎭 Différenciateur de voix standard importé avec succès")
except ImportError as e:
    VOICE_DIFF_AVAILABLE = False
    logger.error(f"❌ Impossible d'importer le différenciateur standard: {e}")

# Importer le service amélioré (optionnel)
try:
    from .enhanced_tts_service import EnhancedTTSService
    ENHANCED_TTS_AVAILABLE = True
    logger.info("🎵 Service TTS amélioré importé avec succès")
except ImportError as e:
    ENHANCED_TTS_AVAILABLE = False
    logger.warning(f"⚠️  Service amélioré non disponible: {e}")


class TTSService:
    """Service principal pour la synthèse vocale"""
    
    def __init__(self):
        self.temp_dir = getattr(settings, 'TTS_TEMP_DIR', tempfile.gettempdir())

        # Initialiser le différenciateur de voix amélioré en priorité
        if ENHANCED_VOICE_DIFF_AVAILABLE:
            try:
                self.voice_differentiator = EnhancedVoiceDifferentiator()
                logger.info("🎭 Différenciateur de voix AMÉLIORÉ activé - vraie différenciation homme/femme")
            except Exception as e:
                logger.error(f"❌ Erreur initialisation différenciateur amélioré: {e}")
                self.voice_differentiator = None
        elif VOICE_DIFF_AVAILABLE:
            try:
                self.voice_differentiator = VoiceDifferentiator()
                logger.info("🎭 Différenciateur de voix standard activé")
            except Exception as e:
                logger.error(f"❌ Erreur initialisation différenciateur standard: {e}")
                self.voice_differentiator = None
        else:
            self.voice_differentiator = None

        # Initialiser le service amélioré si disponible (optionnel)
        if ENHANCED_TTS_AVAILABLE:
            try:
                self.enhanced_service = EnhancedTTSService()
                logger.info("🎵 Service TTS amélioré activé - différenciation vocale avancée")
            except Exception as e:
                logger.error(f"❌ Erreur initialisation service amélioré: {e}")
                self.enhanced_service = None
        else:
            self.enhanced_service = None
        
    def synthesize_text(self, text, voice_profile, user=None, save_to_db=True):
        """
        Synthétise un texte en audio
        
        Args:
            text (str): Texte à synthétiser
            voice_profile (VoiceProfile): Profil vocal à utiliser
            user (User): Utilisateur (optionnel)
            save_to_db (bool): Sauvegarder en base de données
            
        Returns:
            dict: Résultat de la synthèse
        """
        try:
            # Créer une demande TTS si nécessaire
            tts_request = None
            if save_to_db and user:
                tts_request = TTSRequest.objects.create(
                    user=user,
                    text=text,
                    voice_profile=voice_profile,
                    status='processing'
                )
            
            # Utiliser le différenciateur de voix en priorité
            if self.voice_differentiator:
                logger.info(f"🎭 Synthèse avec différenciateur: {voice_profile.voice_type}")
                audio_data = self.voice_differentiator.differentiate_voice(text, voice_profile.voice_type, voice_profile.language)
                if audio_data:
                    logger.info(f"✅ Différenciateur réussi: {len(audio_data)} bytes")
                else:
                    logger.error("❌ Différenciateur a échoué, fallback vers service amélioré")
                    if self.enhanced_service:
                        audio_data = self.enhanced_service.synthesize_with_voice_differentiation(text, voice_profile)
                    else:
                        audio_data = self._synthesize_with_basic_differentiation(text, voice_profile)
            elif self.enhanced_service:
                logger.info(f"🎭 Synthèse avec service amélioré: {voice_profile.voice_type}")
                audio_data = self.enhanced_service.synthesize_with_voice_differentiation(text, voice_profile)
                if audio_data:
                    logger.info(f"✅ Service amélioré réussi: {len(audio_data)} bytes")
                else:
                    logger.error("❌ Service amélioré a échoué, fallback vers basique")
                    audio_data = self._synthesize_with_basic_differentiation(text, voice_profile)
            else:
                # Fallback avec différenciation basique
                logger.info(f"🎭 Synthèse avec différenciation basique: {voice_profile.voice_type}")
                audio_data = self._synthesize_with_basic_differentiation(text, voice_profile)
            
            if audio_data:
                # Sauvegarder le fichier audio
                if tts_request:
                    filename = f"tts_{tts_request.id}.mp3"
                    tts_request.audio_file.save(
                        filename,
                        ContentFile(audio_data),
                        save=True
                    )
                    tts_request.status = 'completed'
                    tts_request.completed_at = datetime.now()
                    tts_request.file_size = len(audio_data)
                    tts_request.save()
                
                return {
                    'success': True,
                    'audio_data': audio_data,
                    'tts_request': tts_request,
                    'message': 'Synthèse vocale réussie'
                }
            else:
                if tts_request:
                    tts_request.status = 'failed'
                    tts_request.error_message = 'Échec de la synthèse vocale'
                    tts_request.save()
                
                return {
                    'success': False,
                    'error': 'Échec de la synthèse vocale'
                }
                
        except Exception as e:
            logger.error(f"Erreur lors de la synthèse TTS: {str(e)}")
            
            if tts_request:
                tts_request.status = 'failed'
                tts_request.error_message = str(e)
                tts_request.save()
            
            return {
                'success': False,
                'error': str(e)
            }
    
    def _synthesize_with_gtts(self, text, voice_profile):
        """Synthèse avec Google TTS (gTTS)"""
        try:
            # Configuration selon le profil vocal
            lang = voice_profile.language
            slow = voice_profile.voice_type in ['mature', 'male_mature', 'female_mature']

            # Créer l'objet gTTS
            tts = gTTS(text=text, lang=lang, slow=slow)

            # Sauvegarder dans un fichier temporaire avec un nom unique
            import time
            temp_filename = f"tts_temp_{int(time.time() * 1000)}.mp3"
            temp_path = os.path.join(self.temp_dir, temp_filename)

            # Créer le répertoire temporaire s'il n'existe pas
            os.makedirs(self.temp_dir, exist_ok=True)

            # Sauvegarder le fichier
            tts.save(temp_path)

            # Lire le contenu du fichier
            with open(temp_path, 'rb') as f:
                audio_data = f.read()

            # Nettoyer le fichier temporaire
            try:
                os.unlink(temp_path)
            except OSError:
                pass  # Ignorer si le fichier ne peut pas être supprimé immédiatement

            return audio_data

        except Exception as e:
            logger.error(f"Erreur gTTS: {str(e)}")
            return None
    
    def _synthesize_with_pyttsx3(self, text, voice_profile):
        """Synthèse avec pyttsx3 (voix système)"""
        try:
            engine = pyttsx3.init()

            # Configuration de la voix
            voices = engine.getProperty('voices')
            if voices:
                # Sélectionner une voix selon le profil
                if 'female' in voice_profile.voice_type and len(voices) > 1:
                    engine.setProperty('voice', voices[1].id)  # Voix féminine
                else:
                    engine.setProperty('voice', voices[0].id)  # Voix masculine

            # Configuration de la vitesse
            rate = engine.getProperty('rate')
            if 'young' in voice_profile.voice_type:
                engine.setProperty('rate', rate + 20)
            elif 'mature' in voice_profile.voice_type:
                engine.setProperty('rate', rate - 20)

            # Créer un nom de fichier unique
            import time
            temp_filename = f"tts_pyttsx3_{int(time.time() * 1000)}.wav"
            temp_path = os.path.join(self.temp_dir, temp_filename)

            # Créer le répertoire temporaire s'il n'existe pas
            os.makedirs(self.temp_dir, exist_ok=True)

            try:
                # Sauvegarder le fichier
                engine.save_to_file(text, temp_path)
                engine.runAndWait()

                # Attendre un peu pour que le fichier soit complètement écrit
                import time
                time.sleep(0.5)

                # Lire le contenu du fichier
                with open(temp_path, 'rb') as f:
                    audio_data = f.read()

                return audio_data

            finally:
                # Nettoyer le fichier temporaire (avec retry)
                for attempt in range(3):
                    try:
                        if os.path.exists(temp_path):
                            os.unlink(temp_path)
                        break
                    except OSError:
                        if attempt < 2:
                            time.sleep(0.1)  # Attendre un peu avant de réessayer
                        else:
                            logger.warning(f"Impossible de supprimer {temp_path}")

        except Exception as e:
            logger.error(f"Erreur pyttsx3: {str(e)}")
            return None
    
    def quick_synthesize(self, text, voice_type='female_young', language='fr'):
        """
        Synthèse rapide sans sauvegarde en base
        
        Args:
            text (str): Texte à synthétiser
            voice_type (str): Type de voix
            language (str): Langue
            
        Returns:
            bytes: Données audio ou None
        """
        try:
            # Créer un profil vocal temporaire
            temp_profile = VoiceProfile(
                voice_type=voice_type,
                language=language,
                name="Temporaire"
            )
            
            result = self.synthesize_text(text, temp_profile, save_to_db=False)
            return result.get('audio_data') if result.get('success') else None
            
        except Exception as e:
            logger.error(f"Erreur synthèse rapide: {str(e)}")
            return None

    def _synthesize_with_basic_differentiation(self, text, voice_profile):
        """Synthèse avec différenciation basique (sans pydub)"""
        try:
            logger.info(f"🎭 Différenciation basique pour {voice_profile.voice_type}")

            # Configuration selon le type de voix
            voice_configs = {
                'female_young': {'slow': False, 'lang': voice_profile.language, 'prefix': '🎵 '},
                'female_mature': {'slow': True, 'lang': voice_profile.language, 'prefix': '🎼 '},
                'male_young': {'slow': False, 'lang': voice_profile.language, 'prefix': '🎸 '},
                'male_mature': {'slow': True, 'lang': voice_profile.language, 'prefix': '🎺 '},
                'neutral': {'slow': False, 'lang': voice_profile.language, 'prefix': '🎹 '},
                'custom_cloned': {'slow': False, 'lang': voice_profile.language, 'prefix': '✨ '},
                'custom_trained': {'slow': True, 'lang': voice_profile.language, 'prefix': '🌟 '}
            }

            config = voice_configs.get(voice_profile.voice_type, voice_configs['neutral'])

            # Modifier le texte selon le type de voix pour créer une différence
            modified_text = config['prefix'] + text

            # Utiliser gTTS avec les paramètres spécifiques
            if voice_profile.language == 'fr':
                return self._synthesize_with_gtts_enhanced(modified_text, voice_profile, config)
            else:
                return self._synthesize_with_pyttsx3_enhanced(modified_text, voice_profile, config)

        except Exception as e:
            logger.error(f"Erreur différenciation basique: {str(e)}")
            # Fallback vers la synthèse normale
            return self._synthesize_with_gtts(text, voice_profile)

    def _synthesize_with_gtts_enhanced(self, text, voice_profile, config):
        """gTTS amélioré avec configuration spécifique"""
        try:
            from gtts import gTTS

            # Créer gTTS avec configuration
            tts = gTTS(
                text=text,
                lang=config['lang'],
                slow=config['slow']
            )

            # Fichier temporaire
            import time
            temp_filename = f"gtts_enhanced_{voice_profile.voice_type}_{int(time.time() * 1000)}.mp3"
            temp_path = os.path.join(self.temp_dir, temp_filename)

            os.makedirs(self.temp_dir, exist_ok=True)

            # Sauvegarder
            tts.save(temp_path)

            # Lire les données
            with open(temp_path, 'rb') as f:
                audio_data = f.read()

            # Nettoyer
            try:
                os.unlink(temp_path)
            except OSError:
                pass

            logger.info(f"✅ gTTS amélioré réussi pour {voice_profile.voice_type} ({len(audio_data)} bytes)")
            return audio_data

        except Exception as e:
            logger.error(f"Erreur gTTS amélioré: {str(e)}")
            return None

    def _synthesize_with_pyttsx3_enhanced(self, text, voice_profile, config):
        """pyttsx3 amélioré avec configuration spécifique"""
        try:
            import pyttsx3

            engine = pyttsx3.init()

            # Configuration selon le type de voix
            voices = engine.getProperty('voices')
            if voices:
                if 'female' in voice_profile.voice_type and len(voices) > 1:
                    engine.setProperty('voice', voices[1].id)
                else:
                    engine.setProperty('voice', voices[0].id)

            # Ajuster la vitesse selon la configuration
            rate = engine.getProperty('rate')
            if config['slow']:
                engine.setProperty('rate', rate - 30)
            else:
                engine.setProperty('rate', rate + 20)

            # Fichier temporaire
            import time
            temp_filename = f"pyttsx3_enhanced_{voice_profile.voice_type}_{int(time.time() * 1000)}.wav"
            temp_path = os.path.join(self.temp_dir, temp_filename)

            os.makedirs(self.temp_dir, exist_ok=True)

            try:
                engine.save_to_file(text, temp_path)
                engine.runAndWait()

                time.sleep(0.5)  # Attendre que le fichier soit écrit

                with open(temp_path, 'rb') as f:
                    audio_data = f.read()

                logger.info(f"✅ pyttsx3 amélioré réussi pour {voice_profile.voice_type} ({len(audio_data)} bytes)")
                return audio_data

            finally:
                for attempt in range(3):
                    try:
                        if os.path.exists(temp_path):
                            os.unlink(temp_path)
                        break
                    except OSError:
                        if attempt < 2:
                            time.sleep(0.1)
                        else:
                            logger.warning(f"Impossible de supprimer {temp_path}")

        except Exception as e:
            logger.error(f"Erreur pyttsx3 amélioré: {str(e)}")
            return None


class TTSManager:
    """Gestionnaire pour les opérations TTS avancées"""
    
    @staticmethod
    def get_available_voices():
        """Retourne les voix disponibles (uniquement Sophie)"""
        return VoiceProfile.objects.filter(is_active=True, name__icontains='sophie')
    
    @staticmethod
    def get_user_preferred_voice(user):
        """Retourne la voix préférée de l'utilisateur"""
        try:
            from .models import UserVoicePreference
            preference = UserVoicePreference.objects.get(user=user)
            return preference.preferred_voice
        except UserVoicePreference.DoesNotExist:
            # Retourner une voix par défaut
            return VoiceProfile.objects.filter(is_active=True, voice_type='female_young').first()
    
    @staticmethod
    def cleanup_old_files(days=7):
        """Nettoie les anciens fichiers audio"""
        from datetime import timedelta
        from django.utils import timezone
        
        cutoff_date = timezone.now() - timedelta(days=days)
        old_requests = TTSRequest.objects.filter(
            created_at__lt=cutoff_date,
            status='completed'
        )
        
        deleted_count = 0
        for request in old_requests:
            if request.audio_file:
                request.delete_audio_file()
                deleted_count += 1
        
        old_requests.delete()
        return deleted_count
