import os
import tempfile
import logging
from datetime import datetime
from django.core.files.base import ContentFile
from django.conf import settings
import speech_recognition as sr
from pydub import AudioSegment
from pydub.utils import which

logger = logging.getLogger(__name__)

class STTService:
    """Service pour la reconnaissance vocale (Speech-to-Text)"""
    
    def __init__(self):
        self.recognizer = sr.Recognizer()
        self.temp_dir = getattr(settings, 'STT_TEMP_DIR', tempfile.gettempdir())
        
        # Configuration du recognizer
        self.recognizer.energy_threshold = 300
        self.recognizer.dynamic_energy_threshold = True
        self.recognizer.pause_threshold = 0.8
        self.recognizer.operation_timeout = 10
        
    def convert_audio_to_wav(self, audio_file_path):
        """
        Convertit un fichier audio en WAV format compatible
        
        Args:
            audio_file_path (str): Chemin vers le fichier audio
            
        Returns:
            str: Chemin vers le fichier WAV converti
        """
        try:
            # Charger l'audio avec pydub
            audio = AudioSegment.from_file(audio_file_path)
            
            # Convertir en mono, 16kHz, 16-bit (format optimal pour STT)
            audio = audio.set_channels(1)  # Mono
            audio = audio.set_frame_rate(16000)  # 16kHz
            audio = audio.set_sample_width(2)  # 16-bit
            
            # Créer un fichier temporaire WAV
            temp_wav = tempfile.NamedTemporaryFile(
                suffix='.wav', 
                dir=self.temp_dir, 
                delete=False
            )
            
            # Exporter en WAV
            audio.export(temp_wav.name, format="wav")
            temp_wav.close()
            
            logger.info(f"Audio converti en WAV: {temp_wav.name}")
            return temp_wav.name
            
        except Exception as e:
            logger.error(f"Erreur conversion audio: {e}")
            raise Exception(f"Impossible de convertir l'audio: {str(e)}")
    
    def transcribe_audio(self, audio_file_path, language='fr-FR'):
        """
        Transcrit un fichier audio en texte
        
        Args:
            audio_file_path (str): Chemin vers le fichier audio
            language (str): Code langue (fr-FR, en-US, etc.)
            
        Returns:
            dict: Résultat de la transcription
        """
        wav_file_path = None
        
        try:
            # Convertir en WAV si nécessaire
            if not audio_file_path.lower().endswith('.wav'):
                wav_file_path = self.convert_audio_to_wav(audio_file_path)
            else:
                wav_file_path = audio_file_path
            
            # Charger le fichier audio
            with sr.AudioFile(wav_file_path) as source:
                # Ajuster le bruit ambiant
                self.recognizer.adjust_for_ambient_noise(source, duration=0.5)
                
                # Enregistrer l'audio
                audio_data = self.recognizer.record(source)
            
            # Transcription avec Google Speech Recognition (gratuit)
            try:
                text = self.recognizer.recognize_google(
                    audio_data, 
                    language=language
                )
                
                return {
                    'success': True,
                    'text': text,
                    'language': language,
                    'confidence': 'high',  # Google ne retourne pas de score
                    'duration': self._get_audio_duration(wav_file_path),
                    'method': 'google'
                }
                
            except sr.UnknownValueError:
                return {
                    'success': False,
                    'error': 'Audio incompréhensible',
                    'text': '',
                    'language': language,
                    'method': 'google'
                }
                
            except sr.RequestError as e:
                # Fallback vers reconnaissance offline si disponible
                logger.warning(f"Google STT failed: {e}, trying offline...")
                return self._try_offline_recognition(audio_data, language)
                
        except Exception as e:
            logger.error(f"Erreur transcription: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'language': language
            }
            
        finally:
            # Nettoyer les fichiers temporaires
            if wav_file_path and wav_file_path != audio_file_path:
                try:
                    os.unlink(wav_file_path)
                except:
                    pass
    
    def _try_offline_recognition(self, audio_data, language):
        """Tentative de reconnaissance offline en fallback"""
        try:
            # Essayer avec Sphinx (offline) si disponible
            text = self.recognizer.recognize_sphinx(audio_data)
            return {
                'success': True,
                'text': text,
                'language': language,
                'confidence': 'medium',
                'method': 'sphinx_offline'
            }
        except:
            return {
                'success': False,
                'error': 'Aucun service de reconnaissance disponible',
                'text': '',
                'language': language,
                'method': 'none'
            }
    
    def _get_audio_duration(self, audio_file_path):
        """Obtient la durée de l'audio en secondes"""
        try:
            audio = AudioSegment.from_wav(audio_file_path)
            return len(audio) / 1000.0  # Convertir ms en secondes
        except:
            return 0.0
    
    def get_supported_languages(self):
        """Retourne les langues supportées"""
        return {
            'fr-FR': 'Français (France)',
            'en-US': 'English (US)',
            'en-GB': 'English (UK)',
            'es-ES': 'Español (España)',
            'it-IT': 'Italiano (Italia)',
            'de-DE': 'Deutsch (Deutschland)',
            'pt-BR': 'Português (Brasil)'
        }
    
    def validate_audio_file(self, audio_file):
        """
        Valide un fichier audio uploadé
        
        Args:
            audio_file: Fichier Django UploadedFile
            
        Returns:
            dict: Résultat de la validation
        """
        # Formats supportés
        supported_formats = ['.wav', '.mp3', '.m4a', '.ogg', '.flac', '.aac']
        
        # Vérifier l'extension
        file_ext = os.path.splitext(audio_file.name)[1].lower()
        if file_ext not in supported_formats:
            return {
                'valid': False,
                'error': f'Format non supporté. Formats acceptés: {", ".join(supported_formats)}'
            }
        
        # Vérifier la taille (max 10MB)
        max_size = 10 * 1024 * 1024  # 10MB
        if audio_file.size > max_size:
            return {
                'valid': False,
                'error': f'Fichier trop volumineux. Taille max: {max_size // (1024*1024)}MB'
            }
        
        return {
            'valid': True,
            'format': file_ext,
            'size': audio_file.size
        }
