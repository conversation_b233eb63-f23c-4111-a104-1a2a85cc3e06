from rest_framework import generics, status, permissions
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.decorators import api_view, permission_classes, parser_classes
from django.views.decorators.http import require_http_methods
from django.http import JsonResponse
from rest_framework.parsers import <PERSON>PartParser, FileUploadParser
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from .models import VoiceProfile, TTSRequest, UserVoicePreference
from .serializers import (
    VoiceProfileSerializer, TTSRequestSerializer, TTSCreateSerializer,
    UserVoicePreferenceSerializer, TTSQuickSerializer
)
from .services import TTSService, TTSManager
from .stt_service import STTService
import logging
import tempfile
import os
from datetime import datetime
import json
import wave
import numpy as np
from io import BytesIO

# Vosk supprimé du projet
VOSK_AVAILABLE = False

logger = logging.getLogger(__name__)


class VoiceProfileListView(generics.ListAPIView):
    """Liste des profils vocaux disponibles"""

    serializer_class = VoiceProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = VoiceProfile.objects.filter(is_active=True, name__icontains='sophie')

        # Filtrer par type de voix si spécifié
        voice_type = self.request.query_params.get('voice_type')
        if voice_type:
            queryset = queryset.filter(voice_type=voice_type)

        # Filtrer par langue si spécifié
        language = self.request.query_params.get('language')
        if language:
            queryset = queryset.filter(language=language)

        return queryset


class TTSCreateView(APIView):
    """Créer une nouvelle demande de synthèse vocale"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = TTSCreateSerializer(data=request.data)
        if serializer.is_valid():
            text = serializer.validated_data['text']
            voice_profile = serializer.validated_data['voice_profile']

            # Vérifier si l'utilisateur peut utiliser cette voix
            if voice_profile.is_premium and not request.user.is_premium:
                return Response(
                    {'error': 'Cette voix nécessite un abonnement premium'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Lancer la synthèse vocale
            tts_service = TTSService()
            result = tts_service.synthesize_text(
                text=text,
                voice_profile=voice_profile,
                user=request.user,
                save_to_db=True
            )

            if result['success']:
                tts_request = result['tts_request']
                response_serializer = TTSRequestSerializer(
                    tts_request,
                    context={'request': request}
                )
                return Response(response_serializer.data, status=status.HTTP_201_CREATED)
            else:
                return Response(
                    {'error': result['error']},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class TTSRequestListView(generics.ListAPIView):
    """Liste des demandes TTS de l'utilisateur"""

    serializer_class = TTSRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return TTSRequest.objects.filter(user=self.request.user)


class TTSRequestDetailView(generics.RetrieveAPIView):
    """Détail d'une demande TTS"""

    serializer_class = TTSRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return TTSRequest.objects.filter(user=self.request.user)


class TTSQuickView(APIView):
    """Synthèse vocale rapide sans sauvegarde"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = TTSQuickSerializer(data=request.data)
        if serializer.is_valid():
            text = serializer.validated_data['text']
            voice_type = serializer.validated_data['voice_type']
            language = serializer.validated_data['language']

            # Synthèse rapide
            tts_service = TTSService()
            audio_data = tts_service.quick_synthesize(text, voice_type, language)

            if audio_data:
                response = HttpResponse(audio_data, content_type='audio/mpeg')
                response['Content-Disposition'] = f'attachment; filename="tts_quick.mp3"'
                return response
            else:
                return Response(
                    {'error': 'Échec de la synthèse vocale'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserVoicePreferenceView(APIView):
    """Gestion des préférences vocales utilisateur"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """Récupérer les préférences de l'utilisateur"""
        try:
            preference = UserVoicePreference.objects.get(user=request.user)
            serializer = UserVoicePreferenceSerializer(preference)
            return Response(serializer.data)
        except UserVoicePreference.DoesNotExist:
            return Response(
                {'message': 'Aucune préférence définie'},
                status=status.HTTP_404_NOT_FOUND
            )

    def post(self, request):
        """Créer ou mettre à jour les préférences"""
        try:
            preference = UserVoicePreference.objects.get(user=request.user)
            serializer = UserVoicePreferenceSerializer(preference, data=request.data, partial=True)
        except UserVoicePreference.DoesNotExist:
            serializer = UserVoicePreferenceSerializer(data=request.data)

        if serializer.is_valid():
            serializer.save(user=request.user)
            return Response(serializer.data, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class TTSStatsView(APIView):
    """Statistiques TTS pour l'utilisateur"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """Récupérer les statistiques TTS de l'utilisateur"""
        user_requests = TTSRequest.objects.filter(user=request.user)

        stats = {
            'total_requests': user_requests.count(),
            'completed_requests': user_requests.filter(status='completed').count(),
            'failed_requests': user_requests.filter(status='failed').count(),
            'pending_requests': user_requests.filter(status='pending').count(),
            'total_audio_duration': sum(
                req.duration for req in user_requests.filter(duration__isnull=False)
            ) or 0,
            'preferred_voice': None
        }

        # Ajouter la voix préférée si elle existe
        preferred_voice = TTSManager.get_user_preferred_voice(request.user)
        if preferred_voice:
            stats['preferred_voice'] = VoiceProfileSerializer(preferred_voice).data

        return Response(stats)


class TTSDownloadView(APIView):
    """Télécharger un fichier audio TTS"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, tts_id):
        """Télécharger le fichier audio d'une demande TTS"""
        try:
            tts_request = TTSRequest.objects.get(
                id=tts_id,
                user=request.user,
                status='completed'
            )

            if tts_request.audio_file:
                response = HttpResponse(
                    tts_request.audio_file.read(),
                    content_type='audio/mpeg'
                )
                response['Content-Disposition'] = f'attachment; filename="tts_{tts_id}.mp3"'
                return response
            else:
                return Response(
                    {'error': 'Fichier audio non disponible'},
                    status=status.HTTP_404_NOT_FOUND
                )

        except TTSRequest.DoesNotExist:
            return Response(
                {'error': 'Demande TTS non trouvée'},
                status=status.HTTP_404_NOT_FOUND
            )


@method_decorator(csrf_exempt, name='dispatch')
class PublicTTSView(APIView):
    """Synthèse vocale publique sans authentification (limitée)"""

    permission_classes = [permissions.AllowAny]

    def post(self, request):
        """Synthèse vocale publique avec limitations"""

        # Limitations pour l'usage public
        text = request.data.get('text', '')
        if len(text) > 200:  # Limite à 200 caractères pour l'usage public
            return Response(
                {'error': 'Texte limité à 200 caractères pour l\'usage public'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not text.strip():
            return Response(
                {'error': 'Le texte ne peut pas être vide'},
                status=status.HTTP_400_BAD_REQUEST
            )

        voice_type = request.data.get('voice_type', 'female_young')
        language = request.data.get('language', 'fr')

        # Toutes les voix sont autorisées pour l'usage public
        # Validation basique du type de voix
        valid_voice_types = ['female_young', 'female_mature', 'male_young', 'male_mature', 'neutral', 'custom_cloned', 'custom_trained']
        if voice_type not in valid_voice_types:
            voice_type = 'female_young'

        # Synthèse rapide
        tts_service = TTSService()
        audio_data = tts_service.quick_synthesize(text, voice_type, language)

        if audio_data:
            response = HttpResponse(audio_data, content_type='audio/mpeg')
            response['Content-Disposition'] = f'attachment; filename="tts_public.mp3"'
            return response
        else:
            return Response(
                {'error': 'Échec de la synthèse vocale'},
                status=status.HTTP_400_BAD_REQUEST
            )


@method_decorator(csrf_exempt, name='dispatch')
class PublicVoicesView(APIView):
    """Liste publique des voix disponibles (limitée)"""

    permission_classes = [permissions.AllowAny]

    def get(self, request):
        """Récupérer les voix publiques disponibles"""

        # Uniquement la voix Sophie
        public_voices = VoiceProfile.objects.filter(is_active=True, name__icontains='sophie')

        # Serializer complet pour l'usage public
        voices_data = []
        for voice in public_voices:
            voices_data.append({
                'id': voice.id,
                'voice_type': voice.voice_type,
                'language': voice.language,
                'name': voice.name,
                'description': voice.description,
                'is_premium': voice.is_premium,
                'is_active': voice.is_active
            })

        return Response(voices_data)


# === VUES BASÉES SUR DES FONCTIONS POUR ÉVITER LES PROBLÈMES CSRF ===

@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def public_voices_function(request):
    """Liste publique des voix disponibles (fonction)"""

    # Uniquement la voix Sophie
    public_voices = VoiceProfile.objects.filter(is_active=True, name__icontains='sophie')

    # Serializer complet pour l'usage public
    voices_data = []
    for voice in public_voices:
        voices_data.append({
            'id': voice.id,
            'voice_type': voice.voice_type,
            'language': voice.language,
            'name': voice.name,
            'description': voice.description,
            'is_premium': voice.is_premium,
            'is_active': voice.is_active
        })

    return Response(voices_data)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
@csrf_exempt
def public_tts_function(request):
    """Synthèse vocale publique sans authentification (fonction)"""

    # Limitations pour l'usage public
    text = request.data.get('text', '')
    if len(text) > 200:  # Limite à 200 caractères pour l'usage public
        return Response(
            {'error': 'Texte limité à 200 caractères pour l\'usage public'},
            status=status.HTTP_400_BAD_REQUEST
        )

    if not text.strip():
        return Response(
            {'error': 'Le texte ne peut pas être vide'},
            status=status.HTTP_400_BAD_REQUEST
        )

    voice_type = request.data.get('voice_type', 'female_young')
    language = request.data.get('language', 'fr')

    # Toutes les voix sont autorisées pour l'usage public
    # Validation basique du type de voix
    valid_voice_types = ['female_young', 'female_mature', 'male_young', 'male_mature', 'neutral', 'custom_cloned', 'custom_trained']
    if voice_type not in valid_voice_types:
        voice_type = 'female_young'

    # Synthèse rapide
    tts_service = TTSService()
    audio_data = tts_service.quick_synthesize(text, voice_type, language)

    if audio_data:
        response = HttpResponse(audio_data, content_type='audio/mpeg')
        response['Content-Disposition'] = f'attachment; filename="tts_public.mp3"'
        return response
    else:
        return Response(
            {'error': 'Échec de la synthèse vocale'},
            status=status.HTTP_400_BAD_REQUEST
        )


# === SPEECH-TO-TEXT ENDPOINT ===

@csrf_exempt
@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def speech_to_text_api(request):
    """
    Convertit un fichier audio en texte (Speech-to-Text) - ACCÈS PUBLIC SANS TOKEN

    Usage:
        POST /tts/speech-to-text/
        Content-Type: multipart/form-data

        Form data:
        - audio: fichier audio (wav, mp3, m4a, ogg, flac, aac)
        - language: code langue optionnel (fr-FR par défaut)

    Returns:
        200: {
            "success": true,
            "text": "texte transcrit",
            "language": "fr-FR",
            "confidence": "high",
            "duration": 3.5,
            "method": "google"
        }
        400: {"error": "message d'erreur"}
    """
    try:
            # Vérifier qu'un fichier audio est fourni
            if 'audio' not in request.FILES:
                return Response({
                    'error': 'Fichier audio requis',
                    'usage': 'POST avec form-data contenant le champ "audio"'
                }, status=status.HTTP_400_BAD_REQUEST)

            audio_file = request.FILES['audio']
            language = request.data.get('language', 'fr-FR')

            # Initialiser le service STT
            stt_service = STTService()

            # Valider le fichier audio
            validation = stt_service.validate_audio_file(audio_file)
            if not validation['valid']:
                return Response({
                    'error': validation['error']
                }, status=status.HTTP_400_BAD_REQUEST)

            # Sauvegarder temporairement le fichier
            temp_file = tempfile.NamedTemporaryFile(
                suffix=validation['format'],
                delete=False
            )

            try:
                # Écrire le contenu du fichier
                for chunk in audio_file.chunks():
                    temp_file.write(chunk)
                temp_file.close()

                # Transcrire l'audio
                result = stt_service.transcribe_audio(temp_file.name, language)

                # Ajouter des métadonnées
                result.update({
                    'file_size': audio_file.size,
                    'file_format': validation['format'],
                    'timestamp': str(datetime.now())
                })

                return Response(result, status=status.HTTP_200_OK)

            finally:
                # Nettoyer le fichier temporaire
                try:
                    os.unlink(temp_file.name)
                except:
                    pass

    except Exception as e:
        logger.error(f"Erreur STT API: {e}")
        return Response({
            'error': f'Erreur interne: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def stt_languages(request):
    """
    Retourne les langues supportées pour la reconnaissance vocale

    Returns:
        200: {
            "languages": {
                "fr-FR": "Français (France)",
                "en-US": "English (US)",
                ...
            }
        }
    """
    stt_service = STTService()
    languages = stt_service.get_supported_languages()

    return Response({
        'languages': languages,
        'default': 'fr-FR',
        'total': len(languages)
    }, status=status.HTTP_200_OK)


# === VOSK SUPPRIMÉ DU PROJET ===




# === VOSK SUPPRIMÉ - ENDPOINTS DÉSACTIVÉS ===

def vosk_status_api(request):
    """VOSK SUPPRIMÉ - Endpoint désactivé"""
    return JsonResponse({
        'error': 'Vosk supprimé du projet',
        'status': 'disabled',
        'alternative': 'Utilisez /api/extract-info/ pour l\'extraction d\'informations'
    }, status=503)


def vosk_languages_api(request):
    """VOSK SUPPRIMÉ - Endpoint désactivé"""
    return JsonResponse({
        'error': 'Vosk supprimé du projet',
        'status': 'disabled'
    }, status=503)


def vosk_transcribe_api(request):
    """VOSK SUPPRIMÉ - Endpoint désactivé"""
    return JsonResponse({
        'error': 'Vosk supprimé du projet',
        'status': 'disabled',
        'alternative': 'Utilisez /api/extract-info/ pour l\'extraction d\'informations'
    }, status=503)
