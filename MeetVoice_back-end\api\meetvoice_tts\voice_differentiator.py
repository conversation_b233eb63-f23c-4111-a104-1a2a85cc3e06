"""
Différenciateur de voix sans dépendances externes
"""
import os
import tempfile
import logging
from gtts import gTTS
import time

logger = logging.getLogger(__name__)


class VoiceDifferentiator:
    """Différenciateur de voix utilisant uniquement gTTS avec modifications"""
    
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
        
        # Configuration des voix avec différenciation RÉELLE améliorée
        self.voice_configs = {
            'female_young': {
                'engine': 'gtts',
                'slow': False,
                'lang': 'fr',
                'tld': 'fr',  # France - voix féminine standard
                'text_modifier': self._add_energy_markers,
                'description': 'Voix féminine jeune et énergique'
            },
            'female_mature': {
                'engine': 'gtts',
                'slow': True,
                'lang': 'fr',
                'tld': 'ca',  # Canada français - accent différent + lent
                'text_modifier': self._add_elegance_markers,
                'description': 'Voix féminine mature et élégante'
            },
            'male_young': {
                'engine': 'pyttsx3',  # Utiliser pyttsx3 pour différenciation
                'voice_index': 0,  # Première voix disponible
                'rate_modifier': 1.2,  # Plus rapide pour "jeune"
                'text_modifier': self._add_dynamic_markers,
                'description': 'Voix masculine jeune et dynamique'
            },
            'male_mature': {
                'engine': 'pyttsx3',  # Utiliser pyttsx3 pour différenciation
                'voice_index': 0,  # Première voix disponible
                'rate_modifier': 0.8,  # Plus lent pour "mature"
                'text_modifier': self._add_authority_markers,
                'description': 'Voix masculine mature et autoritaire'
            },
            'neutral': {
                'engine': 'gtts',
                'slow': False,
                'lang': 'fr',
                'tld': 'be',  # Belgique - accent neutre
                'text_modifier': self._add_neutral_markers,
                'description': 'Voix neutre et équilibrée'
            },
            'custom_cloned': {
                'engine': 'gtts',
                'slow': False,
                'lang': 'fr',
                'tld': 'ch',  # Suisse (accent différent)
                'text_modifier': self._add_custom_markers,
                'description': 'Voix personnalisée clonée'
            },
            'custom_trained': {
                'engine': 'gtts',
                'slow': True,
                'lang': 'fr',
                'tld': 'ch',  # Suisse (accent différent) + lent
                'text_modifier': self._add_trained_markers,
                'description': 'Voix personnalisée entraînée'
            }
        }
    
    def differentiate_voice(self, text, voice_type, language='fr'):
        """Créer une voix différenciée avec moteurs multiples"""

        try:
            config = self.voice_configs.get(voice_type, self.voice_configs['neutral'])

            # Modifier le texte selon le type de voix
            modified_text = config['text_modifier'](text, voice_type)

            engine = config.get('engine', 'gtts')

            if engine == 'pyttsx3':
                return self._synthesize_with_pyttsx3(modified_text, config, voice_type)
            else:
                return self._synthesize_with_gtts(modified_text, config, voice_type, language)

        except Exception as e:
            logger.error(f"❌ Erreur différenciation {voice_type}: {e}")
            return None

    def _synthesize_with_gtts(self, text, config, voice_type, language='fr'):
        """Synthèse avec gTTS"""
        try:
            # Utiliser différents TLD pour créer des accents différents
            lang = config.get('lang', 'fr') if language == 'fr' else language
            tld = config.get('tld', 'fr')
            slow = config.get('slow', False)

            logger.info(f"🎭 gTTS {voice_type}: slow={slow}, tld={tld}")

            # Créer gTTS avec configuration spécifique
            tts = gTTS(
                text=text,
                lang=lang,
                slow=slow,
                tld=tld  # Différents accents
            )

            # Fichier temporaire unique
            temp_filename = f"voice_gtts_{voice_type}_{int(time.time() * 1000000)}.mp3"
            temp_path = os.path.join(self.temp_dir, temp_filename)

            os.makedirs(self.temp_dir, exist_ok=True)

            # Sauvegarder
            tts.save(temp_path)

            # Lire les données
            with open(temp_path, 'rb') as f:
                audio_data = f.read()

            # Nettoyer
            try:
                os.unlink(temp_path)
            except OSError:
                pass

            logger.info(f"✅ gTTS réussi pour {voice_type}: {len(audio_data)} bytes")
            return audio_data

        except Exception as e:
            logger.error(f"❌ Erreur gTTS {voice_type}: {e}")
            return None

    def _synthesize_with_pyttsx3(self, text, config, voice_type):
        """Synthèse avec pyttsx3 pour voix masculines"""
        try:
            import pyttsx3

            engine = pyttsx3.init()

            # Obtenir les voix disponibles
            voices = engine.getProperty('voices')
            if not voices:
                logger.error("❌ Aucune voix pyttsx3 disponible")
                return None

            # Sélectionner la voix
            voice_index = config.get('voice_index', 0)
            if voice_index < len(voices):
                engine.setProperty('voice', voices[voice_index].id)
                logger.info(f"🎭 pyttsx3 {voice_type}: voix {voices[voice_index].name}")

            # Modifier la vitesse
            rate = engine.getProperty('rate')
            rate_modifier = config.get('rate_modifier', 1.0)
            new_rate = int(rate * rate_modifier)
            engine.setProperty('rate', new_rate)

            logger.info(f"🎭 pyttsx3 {voice_type}: rate={new_rate} (modifier={rate_modifier})")

            # Fichier temporaire unique
            temp_filename = f"voice_pyttsx3_{voice_type}_{int(time.time() * 1000000)}.wav"
            temp_path = os.path.join(self.temp_dir, temp_filename)

            os.makedirs(self.temp_dir, exist_ok=True)

            # Sauvegarder
            engine.save_to_file(text, temp_path)
            engine.runAndWait()

            # Attendre que le fichier soit créé
            import time
            time.sleep(1)

            if os.path.exists(temp_path):
                # Lire les données
                with open(temp_path, 'rb') as f:
                    audio_data = f.read()

                # Nettoyer
                try:
                    os.unlink(temp_path)
                except OSError:
                    pass

                logger.info(f"✅ pyttsx3 réussi pour {voice_type}: {len(audio_data)} bytes")
                return audio_data
            else:
                logger.error(f"❌ pyttsx3 {voice_type}: fichier non créé")
                return None

        except Exception as e:
            logger.error(f"❌ Erreur pyttsx3 {voice_type}: {e}")
            return None
    
    def _add_energy_markers(self, text, voice_type):
        """Ajouter des marqueurs d'énergie pour voix jeune"""
        # Ajouter des pauses et emphases pour simuler l'énergie
        modified = text.replace('.', '! ')
        modified = modified.replace(',', ', ')
        return f"Salut ! {modified} C'est génial !"
    
    def _add_elegance_markers(self, text, voice_type):
        """Ajouter des marqueurs d'élégance pour voix mature"""
        # Ajouter des pauses plus longues et un ton plus posé
        modified = text.replace('.', '... ')
        modified = modified.replace(',', '... ')
        return f"Bonjour. {modified} Merci."
    
    def _add_dynamic_markers(self, text, voice_type):
        """Ajouter des marqueurs dynamiques pour voix masculine jeune"""
        modified = text.replace('!', ' ! ')
        modified = modified.replace('?', ' ? ')
        return f"Hey ! {modified} Super !"
    
    def _add_authority_markers(self, text, voice_type):
        """Ajouter des marqueurs d'autorité pour voix masculine mature"""
        modified = text.replace('.', '. ')
        modified = modified.replace(',', ', ')
        return f"Monsieur. {modified} Bien entendu."
    
    def _add_neutral_markers(self, text, voice_type):
        """Ajouter des marqueurs neutres"""
        return f"Bonjour. {text} Merci."
    
    def _add_custom_markers(self, text, voice_type):
        """Ajouter des marqueurs pour voix clonée"""
        modified = text.replace(' ', '  ')  # Espaces doubles pour ralentir
        return f"Voix personnalisée. {modified} Unique."
    
    def _add_trained_markers(self, text, voice_type):
        """Ajouter des marqueurs pour voix entraînée"""
        modified = text.replace('.', '... ')
        return f"Modèle entraîné. {modified} Précision."
    
    def test_all_voices(self, test_text="Test de différenciation vocale"):
        """Tester toutes les voix"""
        results = {}
        
        for voice_type, config in self.voice_configs.items():
            logger.info(f"🧪 Test de {voice_type}...")
            
            audio_data = self.differentiate_voice(test_text, voice_type)
            
            if audio_data:
                # Sauvegarder pour test
                filename = f"test_diff_{voice_type}.mp3"
                with open(filename, 'wb') as f:
                    f.write(audio_data)
                
                results[voice_type] = {
                    'success': True,
                    'size': len(audio_data),
                    'filename': filename,
                    'config': config
                }
                
                logger.info(f"✅ {voice_type}: {len(audio_data)} bytes -> {filename}")
            else:
                results[voice_type] = {
                    'success': False,
                    'error': 'Échec de la synthèse'
                }
                logger.error(f"❌ {voice_type}: Échec")
        
        return results
    
    def get_voice_preview_text(self, voice_type):
        """Obtenir un texte de prévisualisation adapté"""
        previews = {
            'female_young': "Salut ! Je suis une voix féminine jeune et pleine d'énergie ! J'adore parler vite !",
            'female_mature': "Bonjour... Je suis une voix féminine mature... Posée et élégante... Merci.",
            'male_young': "Hey ! Moi c'est une voix masculine jeune ! Dynamique et moderne ! Super !",
            'male_mature': "Monsieur. Je suis une voix masculine mature. Grave et rassurante. Bien entendu.",
            'neutral': "Bonjour. Je suis une voix neutre et équilibrée. Adaptée à tous usages. Merci.",
            'custom_cloned': "Voix  personnalisée.  Je  suis  unique  et  spéciale.  Clonée  avec  précision.",
            'custom_trained': "Modèle entraîné... Je suis une voix personnalisée... Créée spécialement... Précision."
        }
        
        return previews.get(voice_type, "Test de synthèse vocale différenciée.")


def install_ffmpeg_instructions():
    """Instructions pour installer ffmpeg (optionnel)"""
    
    instructions = """
🔧 INSTALLATION DE FFMPEG (OPTIONNEL)

Pour une différenciation encore meilleure, installez ffmpeg:

Windows:
1. Télécharger: https://ffmpeg.org/download.html#build-windows
2. Extraire dans C:\\ffmpeg
3. Ajouter C:\\ffmpeg\\bin au PATH système
4. Redémarrer le terminal

Ou avec chocolatey:
choco install ffmpeg

Ou avec conda:
conda install -c conda-forge ffmpeg

MAIS: Le système fonctionne déjà sans ffmpeg !
La différenciation utilise maintenant:
- Différents accents (TLD)
- Vitesses variables (slow)
- Modifications de texte
- Marqueurs spécifiques par voix
"""
    
    return instructions
