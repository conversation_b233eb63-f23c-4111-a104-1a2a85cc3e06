# Generated by Django 5.2.3 on 2025-06-17 02:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('compte', '0002_compte_audio_alter_compte_credit_alter_compte_sexe'),
    ]

    operations = [
        migrations.CreateModel(
            name='Mur',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('titre', models.CharField(default=None, max_length=250)),
                ('image', models.ImageField(blank=True, default=None, null=True, upload_to='mur')),
                ('video', models.URLField(blank=True, default=None, max_length=500, null=True)),
                ('text', models.TextField()),
                ('pouce_bleu', models.IntegerField(default=0)),
                ('boost', models.IntegerField(default=0)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('date_update', models.DateTimeField(auto_now=True)),
                ('is_recruteur', models.BooleanField(default=False)),
                ('is_applicant', models.BooleanField(default=False)),
                ('is_taff', models.BooleanField(default=False)),
                ('likers', models.ManyToManyField(blank=True, related_name='liked_posts', to='compte.compte')),
                ('user', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='author_mur', to='compte.compte')),
            ],
        ),
    ]
