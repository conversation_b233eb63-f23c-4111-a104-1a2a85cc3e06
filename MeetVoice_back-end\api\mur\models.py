from django.db import models
from compte.models import Compte


class Mur(models.Model):
    user = models.ForeignKey(Compte, on_delete=models.CASCADE,default=None, blank=True, null=True, related_name='author_mur')
    titre = models.CharField(max_length=250, default=None)
    image = models.ImageField(upload_to='mur', default=None, blank=True, null=True)
    video = models.URLField(max_length=500, default=None, blank=True, null=True)
    text = models.TextField()
    pouce_bleu = models.IntegerField(default=0)
    likers = models.ManyToManyField(Compte, related_name='liked_posts', blank=True) 
    boost = models.IntegerField(default=0)
    date_creation = models.DateTimeField(auto_now_add=True)
    date_update = models.DateTimeField(auto_now=True)
    is_recruteur = models.BooleanField(default=False)
    is_applicant = models.BooleanField(default=False)
    is_taff = models.BooleanField(default=False)
  
    def __str__(self):
        return self.titre if self.titre else "Titre non défini"