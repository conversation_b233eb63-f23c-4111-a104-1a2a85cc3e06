from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Mur

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    """Serializer pour les informations utilisateur"""
    
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['id', 'username', 'nom', 'prenom', 'full_name']
    
    def get_full_name(self, obj):
        """Retourne le nom complet de l'utilisateur"""
        if hasattr(obj, 'nom') and hasattr(obj, 'prenom') and obj.nom and obj.prenom:
            return f"{obj.prenom} {obj.nom}"
        elif hasattr(obj, 'first_name') and hasattr(obj, 'last_name') and obj.first_name and obj.last_name:
            return f"{obj.first_name} {obj.last_name}"
        return obj.username


class MurSerializer(serializers.ModelSerializer):
    """Serializer pour les posts du mur"""
    
    user_name = serializers.SerializerMethodField()
    likers_list = UserSerializer(source='likers', many=True, read_only=True)
    likes_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Mur
        fields = [
            'id',
            'user',
            'user_name',
            'titre',
            'image',
            'video',
            'text',
            'pouce_bleu',
            'likers',
            'likers_list',
            'likes_count',
            'boost',
            'date_creation',
            'date_update',
            'is_recruteur',
            'is_applicant',
            'is_taff'
        ]
        read_only_fields = [
            'id', 'user', 'pouce_bleu', 'date_creation', 'date_update'
        ]
    
    def get_user_name(self, obj):
        """Retourne le nom complet de l'utilisateur"""
        if obj.user:
            if hasattr(obj.user, 'nom') and hasattr(obj.user, 'prenom') and obj.user.nom and obj.user.prenom:
                return f"{obj.user.prenom} {obj.user.nom}"
            elif hasattr(obj.user, 'first_name') and hasattr(obj.user, 'last_name') and obj.user.first_name and obj.user.last_name:
                return f"{obj.user.first_name} {obj.user.last_name}"
            return obj.user.username
        return None
    
    def get_likes_count(self, obj):
        """Retourne le nombre de likes"""
        return obj.likers.count()


class MurListSerializer(serializers.ModelSerializer):
    """Serializer simplifié pour la liste des posts du mur"""
    
    user_name = serializers.SerializerMethodField()
    likes_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Mur
        fields = [
            'id',
            'user_name',
            'titre',
            'image',
            'video',
            'text',
            'pouce_bleu',
            'likes_count',
            'boost',
            'date_creation',
            'is_recruteur',
            'is_applicant',
            'is_taff'
        ]
    
    def get_user_name(self, obj):
        """Retourne le nom complet de l'utilisateur"""
        if obj.user:
            if hasattr(obj.user, 'nom') and hasattr(obj.user, 'prenom') and obj.user.nom and obj.user.prenom:
                return f"{obj.user.prenom} {obj.user.nom}"
            elif hasattr(obj.user, 'first_name') and hasattr(obj.user, 'last_name') and obj.user.first_name and obj.user.last_name:
                return f"{obj.user.first_name} {obj.user.last_name}"
            return obj.user.username
        return None
    
    def get_likes_count(self, obj):
        """Retourne le nombre de likes"""
        return obj.likers.count()


class MurCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création de posts du mur"""
    
    class Meta:
        model = Mur
        fields = [
            'titre',
            'image',
            'video',
            'text',
            'is_recruteur',
            'is_applicant',
            'is_taff'
        ]
    
    def validate_titre(self, value):
        """Validation du titre"""
        if value and len(value.strip()) < 3:
            raise serializers.ValidationError("Le titre doit contenir au moins 3 caractères.")
        return value.strip() if value else value
    
    def validate_text(self, value):
        """Validation du texte"""
        if len(value.strip()) < 5:
            raise serializers.ValidationError("Le texte doit contenir au moins 5 caractères.")
        if len(value) > 2000:
            raise serializers.ValidationError("Le texte ne peut pas dépasser 2000 caractères.")
        return value.strip()
    
    def validate(self, data):
        """Validation globale"""
        # Au moins un contenu doit être fourni
        if not data.get('text') and not data.get('image') and not data.get('video'):
            raise serializers.ValidationError("Au moins un contenu (texte, image ou vidéo) doit être fourni.")
        return data


class MurUpdateSerializer(serializers.ModelSerializer):
    """Serializer pour la mise à jour de posts du mur"""
    
    class Meta:
        model = Mur
        fields = [
            'titre',
            'image',
            'video',
            'text',
            'boost',
            'is_recruteur',
            'is_applicant',
            'is_taff'
        ]
    
    def validate_titre(self, value):
        """Validation du titre"""
        if value and len(value.strip()) < 3:
            raise serializers.ValidationError("Le titre doit contenir au moins 3 caractères.")
        return value.strip() if value else value
    
    def validate_text(self, value):
        """Validation du texte"""
        if len(value.strip()) < 5:
            raise serializers.ValidationError("Le texte doit contenir au moins 5 caractères.")
        if len(value) > 2000:
            raise serializers.ValidationError("Le texte ne peut pas dépasser 2000 caractères.")
        return value.strip()
    
    def validate_boost(self, value):
        """Validation du boost"""
        if value < 0:
            raise serializers.ValidationError("Le boost ne peut pas être négatif.")
        return value


class MurStatsSerializer(serializers.ModelSerializer):
    """Serializer pour les statistiques des posts du mur"""
    
    user_name = serializers.SerializerMethodField()
    likes_count = serializers.SerializerMethodField()
    engagement_score = serializers.SerializerMethodField()
    
    class Meta:
        model = Mur
        fields = [
            'id',
            'titre',
            'user_name',
            'pouce_bleu',
            'likes_count',
            'boost',
            'engagement_score',
            'date_creation',
            'is_recruteur',
            'is_applicant',
            'is_taff'
        ]
    
    def get_user_name(self, obj):
        """Retourne le nom complet de l'utilisateur"""
        if obj.user:
            if hasattr(obj.user, 'nom') and hasattr(obj.user, 'prenom') and obj.user.nom and obj.user.prenom:
                return f"{obj.user.prenom} {obj.user.nom}"
            elif hasattr(obj.user, 'first_name') and hasattr(obj.user, 'last_name') and obj.user.first_name and obj.user.last_name:
                return f"{obj.user.first_name} {obj.user.last_name}"
            return obj.user.username
        return None
    
    def get_likes_count(self, obj):
        """Retourne le nombre de likes"""
        return obj.likers.count()
    
    def get_engagement_score(self, obj):
        """Calcule un score d'engagement basique"""
        likes = obj.likers.count()
        boost = obj.boost
        return likes + (boost * 2)  # Score simple basé sur likes et boost


class LikeActionSerializer(serializers.Serializer):
    """Serializer pour l'action de like/unlike"""
    
    action = serializers.ChoiceField(choices=['like', 'unlike'])
    
    def validate_action(self, value):
        """Validation de l'action"""
        if value not in ['like', 'unlike']:
            raise serializers.ValidationError("L'action doit être 'like' ou 'unlike'.")
        return value
