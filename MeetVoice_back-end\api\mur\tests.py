"""
Tests pour l'application mur
"""
import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status

from .models import Mur
from .serializers import MurSerializer, MurCreateSerializer
from compte.models import Compte

User = get_user_model()


class MurModelTest(TestCase):
    """Tests pour le modèle Mur"""

    def setUp(self):
        self.user = Compte.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        self.post = Mur.objects.create(
            user=self.user,
            titre="Post Test",
            text="Contenu du post de test"
        )

    def test_mur_creation(self):
        """Test de création d'un post"""
        self.assertEqual(self.post.user, self.user)
        self.assertEqual(self.post.titre, "Post Test")
        self.assertEqual(self.post.text, "Contenu du post de test")
        self.assertEqual(self.post.pouce_bleu, 0)
        self.assertEqual(self.post.boost, 0)

    def test_mur_str(self):
        """Test de la représentation string"""
        self.assertEqual(str(self.post), "Post Test")

    def test_mur_without_title(self):
        """Test d'un post sans titre"""
        post_no_title = Mur.objects.create(
            user=self.user,
            titre="",  # Titre vide mais pas None
            text="Post sans titre"
        )
        self.assertEqual(str(post_no_title), "Titre non défini")

    def test_likes_functionality(self):
        """Test de la fonctionnalité de likes"""
        other_user = Compte.objects.create_user(
            email='<EMAIL>',
            username='otheruser',
            password='testpass123'
        )

        # Ajouter un like
        self.post.likers.add(other_user)
        self.post.pouce_bleu = self.post.likers.count()
        self.post.save()

        self.assertEqual(self.post.pouce_bleu, 1)
        self.assertTrue(self.post.likers.filter(id=other_user.id).exists())


class MurAPITest(APITestCase):
    """Tests pour l'API du mur"""

    def setUp(self):
        self.client = APIClient()
        self.user = Compte.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        self.other_user = Compte.objects.create_user(
            email='<EMAIL>',
            username='otheruser',
            password='testpass123'
        )
        self.post = Mur.objects.create(
            user=self.user,
            titre="Post Test",
            text="Contenu du post de test"
        )

    def test_list_posts_anonymous(self):
        """Test de récupération des posts sans authentification"""
        url = '/mur/api/posts/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

    def test_create_post_authentication_required(self):
        """Test que l'authentification est requise pour créer un post"""
        url = '/mur/api/posts/'
        data = {
            'titre': 'Nouveau Post',
            'text': 'Contenu du nouveau post'
        }
        response = self.client.post(url, data)
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])

    def test_create_post_authenticated(self):
        """Test de création d'un post avec authentification"""
        self.client.force_authenticate(user=self.user)
        url = '/mur/api/posts/'
        data = {
            'titre': 'Nouveau Post',
            'text': 'Contenu du nouveau post'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['titre'], 'Nouveau Post')

    def test_like_post(self):
        """Test de like d'un post"""
        self.client.force_authenticate(user=self.other_user)
        url = f'/mur/api/posts/{self.post.id}/like/'
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['likes_count'], 1)

        # Vérifier en base
        self.post.refresh_from_db()
        self.assertEqual(self.post.pouce_bleu, 1)
        self.assertTrue(self.post.likers.filter(id=self.other_user.id).exists())

    def test_unlike_post(self):
        """Test de unlike d'un post"""
        # D'abord liker le post
        self.post.likers.add(self.other_user)
        self.post.pouce_bleu = 1
        self.post.save()

        self.client.force_authenticate(user=self.other_user)
        url = f'/mur/api/posts/{self.post.id}/unlike/'
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['likes_count'], 0)

        # Vérifier en base
        self.post.refresh_from_db()
        self.assertEqual(self.post.pouce_bleu, 0)
        self.assertFalse(self.post.likers.filter(id=self.other_user.id).exists())
