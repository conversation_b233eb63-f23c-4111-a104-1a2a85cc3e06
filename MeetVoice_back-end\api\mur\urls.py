"""
URLs pour l'application mur
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'mur'

# Configuration du router DRF
router = DefaultRouter()
router.register(r'posts', views.MurViewSet)

urlpatterns = [
    # API REST
    path('api/', include(router.urls)),
    
    # Vues Django traditionnelles
    path('mur/', views.mur_list, name='list'),
    path('mur/create/', views.mur_create, name='create'),
    path('mur/toggle-like/<int:post_id>/', views.toggle_like, name='toggle_like'),
]
