"""
Vues pour la gestion du mur (posts utilisateurs)
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.core.paginator import Paginator
from django.db.models import Q

# DRF imports
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend

from .models import Mur
from .serializers import (
    MurSerializer, MurListSerializer, MurCreateSerializer,
    MurUpdateSerializer, MurStatsSerializer, LikeActionSerializer
)


# ============================================================================
# API REST VIEWSETS
# ============================================================================

class MurViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion des posts du mur via API REST"""

    queryset = Mur.objects.all()
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['user', 'is_recruteur', 'is_applicant', 'is_taff']
    search_fields = ['titre', 'text']
    ordering_fields = ['date_creation', 'date_update', 'pouce_bleu', 'boost']
    ordering = ['-date_creation']

    def get_serializer_class(self):
        """Retourne le serializer approprié selon l'action"""
        if self.action == 'list':
            return MurListSerializer
        elif self.action == 'create':
            return MurCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return MurUpdateSerializer
        elif self.action == 'stats':
            return MurStatsSerializer
        elif self.action in ['like', 'unlike']:
            return LikeActionSerializer
        return MurSerializer

    def get_queryset(self):
        """Filtre les posts selon les permissions"""
        queryset = Mur.objects.all()

        # Filtrer par utilisateur si spécifié
        user_id = self.request.query_params.get('user_id')
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        return queryset

    def perform_create(self, serializer):
        """Définit l'utilisateur lors de la création"""
        serializer.save(user=self.request.user)

    def perform_update(self, serializer):
        """Vérifie les permissions lors de la mise à jour"""
        instance = self.get_object()

        # Seul l'auteur ou un staff peut modifier
        if instance.user != self.request.user and not self.request.user.is_staff:
            raise PermissionError("Vous ne pouvez modifier que vos propres posts")

        serializer.save()

    def perform_destroy(self, instance):
        """Vérifie les permissions lors de la suppression"""
        # Seul l'auteur ou un staff peut supprimer
        if instance.user != self.request.user and not self.request.user.is_staff:
            raise PermissionError("Vous ne pouvez supprimer que vos propres posts")

        instance.delete()

    @action(detail=False, methods=['get'])
    def my_posts(self, request):
        """Retourne les posts de l'utilisateur connecté"""
        if not request.user.is_authenticated:
            return Response(
                {'error': 'Authentification requise'},
                status=status.HTTP_401_UNAUTHORIZED
            )

        user_posts = self.get_queryset().filter(user=request.user)
        serializer = self.get_serializer(user_posts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def recruiter_posts(self, request):
        """Retourne les posts de recruteurs"""
        recruiter_posts = self.get_queryset().filter(is_recruteur=True)
        serializer = self.get_serializer(recruiter_posts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def applicant_posts(self, request):
        """Retourne les posts de candidats"""
        applicant_posts = self.get_queryset().filter(is_applicant=True)
        serializer = self.get_serializer(applicant_posts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def staff_posts(self, request):
        """Retourne les posts du staff"""
        staff_posts = self.get_queryset().filter(is_taff=True)
        serializer = self.get_serializer(staff_posts, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def like(self, request, pk=None):
        """Ajoute un like au post"""
        if not request.user.is_authenticated:
            return Response(
                {'error': 'Authentification requise'},
                status=status.HTTP_401_UNAUTHORIZED
            )

        post = self.get_object()

        # Vérifier si l'utilisateur a déjà liké
        if post.likers.filter(id=request.user.id).exists():
            return Response(
                {'error': 'Vous avez déjà liké ce post'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Ajouter le like
        post.likers.add(request.user)
        post.pouce_bleu = post.likers.count()
        post.save()

        return Response({
            'message': 'Like ajouté avec succès',
            'likes_count': post.pouce_bleu
        })

    @action(detail=True, methods=['post'])
    def unlike(self, request, pk=None):
        """Retire un like du post"""
        if not request.user.is_authenticated:
            return Response(
                {'error': 'Authentification requise'},
                status=status.HTTP_401_UNAUTHORIZED
            )

        post = self.get_object()

        # Vérifier si l'utilisateur a liké
        if not post.likers.filter(id=request.user.id).exists():
            return Response(
                {'error': 'Vous n\'avez pas liké ce post'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Retirer le like
        post.likers.remove(request.user)
        post.pouce_bleu = post.likers.count()
        post.save()

        return Response({
            'message': 'Like retiré avec succès',
            'likes_count': post.pouce_bleu
        })

    @action(detail=True, methods=['post'])
    def boost(self, request, pk=None):
        """Booste un post (augmente sa visibilité)"""
        if not request.user.is_authenticated:
            return Response(
                {'error': 'Authentification requise'},
                status=status.HTTP_401_UNAUTHORIZED
            )

        post = self.get_object()
        boost_amount = request.data.get('amount', 1)

        try:
            boost_amount = int(boost_amount)
            if boost_amount < 1:
                raise ValueError("Le boost doit être positif")
        except (ValueError, TypeError):
            return Response(
                {'error': 'Montant de boost invalide'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Seul l'auteur ou un staff peut booster
        if post.user != request.user and not request.user.is_staff:
            return Response(
                {'error': 'Vous ne pouvez booster que vos propres posts'},
                status=status.HTTP_403_FORBIDDEN
            )

        post.boost += boost_amount
        post.save()

        return Response({
            'message': f'Post boosté de {boost_amount}',
            'total_boost': post.boost
        })

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Retourne les statistiques des posts"""
        posts = self.get_queryset()
        serializer = MurStatsSerializer(posts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def trending(self, request):
        """Retourne les posts tendance (basé sur likes et boost)"""
        trending_posts = self.get_queryset().order_by('-pouce_bleu', '-boost')[:20]
        serializer = self.get_serializer(trending_posts, many=True)
        return Response(serializer.data)


# ============================================================================
# VUES DJANGO TRADITIONNELLES
# ============================================================================

@login_required
def mur_list(request):
    """Vue pour afficher la liste des posts du mur"""
    posts = Mur.objects.all().order_by('-date_creation')

    # Pagination
    paginator = Paginator(posts, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
    }

    return render(request, 'mur/liste.html', context)


@login_required
def mur_create(request):
    """Vue pour créer un nouveau post"""
    if request.method == 'POST':
        # Traitement du formulaire
        titre = request.POST.get('titre', '')
        text = request.POST.get('text', '')
        image = request.FILES.get('image')
        video = request.POST.get('video', '')

        if not text:
            messages.error(request, 'Le texte est obligatoire.')
            return redirect('mur:create')

        # Créer le post
        post = Mur.objects.create(
            user=request.user,
            titre=titre,
            text=text,
            image=image,
            video=video
        )

        messages.success(request, 'Post créé avec succès!')
        return redirect('mur:list')

    return render(request, 'mur/create.html')


@login_required
@require_POST
@csrf_exempt
def toggle_like(request, post_id):
    """Vue AJAX pour liker/unliker un post"""
    try:
        post = get_object_or_404(Mur, id=post_id)

        if post.likers.filter(id=request.user.id).exists():
            # Retirer le like
            post.likers.remove(request.user)
            liked = False
        else:
            # Ajouter le like
            post.likers.add(request.user)
            liked = True

        # Mettre à jour le compteur
        post.pouce_bleu = post.likers.count()
        post.save()

        return JsonResponse({
            'success': True,
            'liked': liked,
            'likes_count': post.pouce_bleu
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=400)
