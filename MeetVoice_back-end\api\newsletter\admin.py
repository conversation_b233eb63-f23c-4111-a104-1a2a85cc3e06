from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    Campaign, EmailTemplate, EmailTracking, 
    EmailOpen, EmailClick, NewsletterSettings
)


@admin.register(EmailTemplate)
class EmailTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'created_by', 'is_active', 'created_at', 'updated_at']
    list_filter = ['is_active', 'created_at', 'created_by']
    search_fields = ['name', 'subject_template']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Informations générales', {
            'fields': ('name', 'subject_template', 'preview_text', 'is_active')
        }),
        ('Contenu HTML', {
            'fields': ('header_html', 'content_html', 'footer_html'),
            'classes': ('wide',)
        }),
        ('Styles CSS', {
            'fields': ('css_styles',),
            'classes': ('wide',)
        }),
        ('Métadonnées', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if not change:  # Création
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Campaign)
class CampaignAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'subject', 'status', 'audience_type', 
        'recipient_count', 'open_rate_display', 'click_rate_display',
        'created_at', 'sent_at'
    ]
    list_filter = ['status', 'audience_type', 'created_at', 'sent_at']
    search_fields = ['name', 'subject', 'ai_prompt']
    readonly_fields = [
        'tracking_id', 'recipient_count', 'created_at', 
        'updated_at', 'sent_at', 'open_rate_display', 'click_rate_display'
    ]
    
    fieldsets = (
        ('Informations générales', {
            'fields': ('name', 'subject', 'template', 'status')
        }),
        ('Contenu IA', {
            'fields': ('ai_prompt', 'generated_content'),
            'classes': ('wide',)
        }),
        ('Audience et envoi', {
            'fields': ('audience_type', 'recipient_count', 'scheduled_at', 'sent_at')
        }),
        ('Tracking et statistiques', {
            'fields': ('tracking_id', 'open_rate_display', 'click_rate_display'),
            'classes': ('collapse',)
        }),
        ('HTML final', {
            'fields': ('final_html',),
            'classes': ('collapse', 'wide')
        }),
        ('Métadonnées', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def open_rate_display(self, obj):
        return f"{obj.open_rate}%"
    open_rate_display.short_description = "Taux d'ouverture"
    
    def click_rate_display(self, obj):
        return f"{obj.click_rate}%"
    click_rate_display.short_description = "Taux de clic"
    
    def save_model(self, request, obj, form, change):
        if not change:  # Création
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(EmailTracking)
class EmailTrackingAdmin(admin.ModelAdmin):
    list_display = [
        'campaign', 'recipient', 'sent_at', 'delivered', 
        'bounced', 'email_provider', 'opens_count', 'clicks_count'
    ]
    list_filter = ['delivered', 'bounced', 'sent_at', 'email_provider']
    search_fields = ['campaign__name', 'recipient__email', 'recipient__username']
    readonly_fields = ['tracking_hash', 'sent_at', 'opens_count', 'clicks_count']
    
    def opens_count(self, obj):
        return obj.emailopen_set.count()
    opens_count.short_description = "Ouvertures"
    
    def clicks_count(self, obj):
        return obj.emailclick_set.count()
    clicks_count.short_description = "Clics"


@admin.register(EmailOpen)
class EmailOpenAdmin(admin.ModelAdmin):
    list_display = [
        'campaign', 'recipient', 'opened_at', 'email_client', 
        'device_type', 'country', 'city'
    ]
    list_filter = ['opened_at', 'email_client', 'device_type', 'country']
    search_fields = ['campaign__name', 'recipient__email', 'recipient__username']
    readonly_fields = ['opened_at']
    
    def has_add_permission(self, request):
        return False  # Pas de création manuelle


@admin.register(EmailClick)
class EmailClickAdmin(admin.ModelAdmin):
    list_display = [
        'campaign', 'recipient', 'clicked_at', 'url', 'link_text'
    ]
    list_filter = ['clicked_at']
    search_fields = ['campaign__name', 'recipient__email', 'url', 'link_text']
    readonly_fields = ['clicked_at']
    
    def has_add_permission(self, request):
        return False  # Pas de création manuelle


@admin.register(NewsletterSettings)
class NewsletterSettingsAdmin(admin.ModelAdmin):
    list_display = ['from_email', 'from_name', 'smtp_host', 'updated_at', 'updated_by']
    readonly_fields = ['updated_at']
    
    fieldsets = (
        ('Configuration SMTP', {
            'fields': (
                'smtp_host', 'smtp_port', 'smtp_username', 
                'smtp_password', 'smtp_use_tls'
            )
        }),
        ('Configuration expéditeur', {
            'fields': ('from_email', 'from_name', 'reply_to')
        }),
        ('Configuration IA', {
            'fields': ('ai_api_key', 'ai_model')
        }),
        ('Limites d\'envoi', {
            'fields': ('max_emails_per_hour', 'max_emails_per_day')
        }),
        ('Métadonnées', {
            'fields': ('updated_by', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)
    
    def has_add_permission(self, request):
        # Un seul objet de paramètres
        return not NewsletterSettings.objects.exists()
    
    def has_delete_permission(self, request, obj=None):
        return False  # Pas de suppression des paramètres


# Personnalisation de l'admin
admin.site.site_header = "Administration Newsletter MeetVoice"
admin.site.site_title = "Newsletter Admin"
admin.site.index_title = "Gestion de la Newsletter"
