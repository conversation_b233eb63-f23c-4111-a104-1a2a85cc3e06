
# 🚫 RÈGLE ABSOLUE: AUCUN OVERLAY CSS
# - Pas de position: absolute/relative
# - Pas de rgba() avec opacité
# - Pas de z-index
# - Utiliser des backgrounds solides uniquement
# - Compatible Gmail, Outlook, Apple Mail
#!/usr/bin/env python
"""
Générateur de templates newsletter IA avec Gemini
"""
import os
try:
    import google.generativeai as genai
    GENAI_AVAILABLE = True
except ImportError:
    GENAI_AVAILABLE = False
    genai = None
from django.conf import settings


class AITemplateGenerator:
    """Générateur de templates newsletter avec IA"""
    
    def __init__(self):
        """Initialiser le générateur IA"""
        try:
            if GENAI_AVAILABLE and hasattr(settings, 'GOOGLE_GEMINI_API_KEY'):
                # Configuration Gemini
                genai.configure(api_key=settings.GOOGLE_GEMINI_API_KEY)
                self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
                self.ai_available = True
                print("✅ IA Gemini disponible")
            else:
                raise Exception("Module google.generativeai non disponible")
        except Exception as e:
            print(f"⚠️ IA non disponible, utilisation du fallback : {e}")
            self.model = None
            self.ai_available = False
        
        # Contexte MeetVoice
        self.meetvoice_context = """
        MeetVoice est une plateforme de rencontres révolutionnaire basée sur l'Intelligence Artificielle.
        
        CONCEPT PRINCIPAL :
        - Rencontres authentiques par la voix et l'IA
        - Matching intelligent basé sur la personnalité vocale
        - Connexions profondes au-delà des apparences
        - Technologie IA avancée pour des rencontres personnalisées
        
        FONCTIONNALITÉS :
        - Analyse vocale IA pour le matching
        - Conversations guidées par l'IA
        - Profils basés sur la personnalité vocale
        - Recommandations intelligentes
        - Sécurité et confidentialité renforcées
        
        PUBLIC CIBLE :
        - Personnes cherchant des connexions authentiques
        - Utilisateurs fatigués des apps de rencontre superficielles
        - Personnes valorisant la personnalité sur l'apparence
        - Early adopters de technologies IA
        
        VALEURS :
        - Authenticité
        - Innovation technologique
        - Respect et sécurité
        - Connexions humaines profondes
        """
    
    def generate_newsletter_from_title(self, title, template_style="moderne", target_audience="general", generate_images=False):
        """
        Générer une newsletter complète à partir d'un titre

        Args:
            title (str): Titre de la newsletter
            template_style (str): Style du template (moderne, minimaliste, promotionnel, premium)
            target_audience (str): Audience cible (general, nouveaux_utilisateurs, utilisateurs_actifs, premium)
            generate_images (bool): Générer des images professionnelles automatiquement
        """
        
        # Générer des images si demandé
        images_html = ""
        if generate_images:
            print(f"🖼️ Génération d'images professionnelles demandée")
            images = self._generate_professional_images(title, template_style)
            if images:
                # Générer le HTML pour toutes les images
                images_html_parts = []
                for i, image in enumerate(images):
                    img_html = f"""
                    <div class="image-section" style="text-align: center; margin: 20px 0;">
                        <img src="{image['url']}" alt="{image['alt']}"
                             style="width: 100%; max-width: 600px; height: auto; border-radius: 8px;
                                    box-shadow: 0 4px 8px rgba(0,0,0,0.1); display: block; margin: 0 auto;">
                    </div>
                    """
                    images_html_parts.append(img_html)
                    print(f"✅ Image professionnelle {i+1} générée: {image['url']}")

                images_html = "\n".join(images_html_parts)
                print(f"🎨 {len(images)} images intégrées dans le template")

        # Prompt personnalisé selon le style
        style_prompts = {
            "moderne": "Design moderne avec dégradés, couleurs vives, sections bien définies",
            "minimaliste": "Design épuré, beaucoup d'espace blanc, typographie claire",
            "promotionnel": "Design accrocheur, call-to-action proéminents, couleurs énergiques",
            "premium": "Design luxueux, couleurs sophistiquées, finitions élégantes"
        }

        # Audience spécifique
        audience_context = {
            "general": "Utilisateurs mixtes de MeetVoice",
            "nouveaux_utilisateurs": "Nouveaux inscrits découvrant MeetVoice",
            "utilisateurs_actifs": "Utilisateurs réguliers et engagés",
            "premium": "Utilisateurs premium avec abonnement payant"
        }
        
        prompt = f"""
        Tu es un DIRECTEUR ARTISTIQUE SENIOR spécialisé dans les newsletters d'entreprises Fortune 500.

        CONTEXTE MEETVOICE :
        {self.meetvoice_context}

        MISSION CRITIQUE :
        Créer une newsletter HTML de NIVEAU ENTERPRISE PREMIUM basée sur : "{title}"

        RÉFÉRENCES DESIGN OBLIGATOIRES :
        - Apple Newsletter (minimalisme premium, typography parfaite)
        - Stripe Email Design (clean, professional, data-driven)
        - Notion Weekly (modern, engaging, well-structured)
        - Linear Updates (sleek, tech-forward, sophisticated)
        - Figma Newsletter (creative, professional, inspiring)

        STANDARDS ENTERPRISE :
        - Style : {style_prompts.get(template_style, style_prompts['moderne'])}
        - Audience : {audience_context.get(target_audience, audience_context['general'])}
        - Responsive design mobile-first OBLIGATOIRE
        - Palette couleurs PREMIUM : Violet (#4e385f), Noir (#2A1D34), Accent (#6c5ce7), Gris (#f8f9fa)

        ARCHITECTURE TEMPLATE ENTERPRISE :
        1. HEADER PREMIUM : Logo vectoriel, navigation subtile, branding cohérent
        2. HERO SECTION : Image haute résolution, /* overlay - BANNI */ gradient, typography impactante
        3. CONTENT BLOCKS : Sections modulaires, espacement parfait, hiérarchie claire
        4. FEATURE GRID : Cards élégantes, icônes SVG, descriptions concises
        5. SOCIAL PROOF : Statistiques impressionnantes, témoignages authentiques
        6. VISUAL BREAK : Image secondaire, citation inspirante
        7. CTA PREMIUM : Bouton design system, micro-interactions
        8. FOOTER ENTERPRISE : Links organisés, mentions légales, social media

        LOGO MEETVOICE OBLIGATOIRE :
        - Logo image : https://image.pollinations.ai/prompt/MeetVoice%20logo%2C%20modern%20microphone%20icon%2C%20purple%20gradient%2C%20professional%20design%2C%20dating%20app%20logo%2C%20voice%20technology%2C%20clean%20minimalist%20style%2C%20high%20resolution?width=100&height=100&seed=meetvoice&enhance=true
        - Logo texte stylisé : "🎙️ MeetVoice" avec gradient et ombre
        - Positionnement : Header centré avec tagline "L'IA qui révolutionne les rencontres"

        IMAGES HAUTE QUALITÉ REQUISES :
        - Hero : https://image.pollinations.ai/prompt/premium%20business%20professionals%2C%20modern%20office%2C%20technology%20innovation%2C%20corporate%20photography%2C%20high%20end?width=600&height=350&enhance=true
        - Section : https://image.pollinations.ai/prompt/elegant%20couple%20using%20technology%2C%20voice%20communication%2C%20premium%20lifestyle%2C%20professional%20photography?width=500&height=300&enhance=true

        COPYWRITING ENTERPRISE :
        - Headlines percutants et mémorables
        - Value propositions claires et mesurables
        - Storytelling émotionnel mais professionnel
        - Call-to-actions irrésistibles
        - Ton premium mais accessible

        SPÉCIFICATIONS TECHNIQUES STRICTES :
        - HTML5 sémantique PARFAIT
        - CSS inline optimisé pour TOUS clients email
        - Largeur max : 600px (standard enterprise)
        - Compatible Outlook 2016+, Gmail, Apple Mail, Thunderbird
        - Accessibilité WCAG 2.1 AA (alt text, contraste, focus)
        - Performance optimisée (< 100KB total)

        DESIGN SYSTEM PREMIUM :
        - Typography scale harmonieuse (16px base, 1.25 ratio)
        - Spacing system cohérent (8px grid)
        - Color palette avec variations tonales
        - Border-radius consistant (8px, 12px, 16px)
        - Shadows subtiles et élégantes
        - Hover states pour interactivité

        INNOVATION VISUELLE OBLIGATOIRE :
        - Micro-animations CSS (transitions fluides)
        - Cards avec depth et elevation
        - Gradients subtils et professionnels
        - Icons cohérents et modernes
        - Layout asymétrique mais équilibré
        - White space généreux et intentionnel

        QUALITÉ ENTERPRISE ATTENDUE :
        - Design digne d'une agence créative premium
        - Code propre et maintenable
        - Performance optimale
        - Expérience utilisateur exceptionnelle
        - Branding MeetVoice cohérent et mémorable

        {f"IMAGES À INTÉGRER DANS LE DESIGN : {images_html}" if images_html else ""}

        RETOURNE UNIQUEMENT LE CODE HTML COMPLET DE NIVEAU ENTERPRISE, PRÊT POUR ENVOI PROFESSIONNEL.
        """
        
        try:
            if self.ai_available:
                response = self.model.generate_content(prompt)
                # Nettoyer le HTML retourné par l'IA
                clean_html = self._clean_ai_html_response(response.text)
                return {
                    'success': True,
                    'html_content': clean_html,
                    'title': title,
                    'style': template_style,
                    'audience': target_audience,
                    'generate_images': generate_images,
                    'images_included': bool(images_html)
                }
            else:
                # Fallback : utiliser un template prédéfini
                return self._generate_fallback_template(title, template_style, target_audience)
        except Exception as e:
            # En cas d'erreur IA, utiliser le fallback
            return self._generate_fallback_template(title, template_style, target_audience)
    
    def generate_template_variations(self, base_title, count=3):
        """
        Générer plusieurs variations d'un template
        
        Args:
            base_title (str): Titre de base
            count (int): Nombre de variations à générer
        """
        
        styles = ['moderne', 'minimaliste', 'promotionnel', 'premium']
        variations = []
        
        for i in range(min(count, len(styles))):
            style = styles[i]
            result = self.generate_newsletter_from_title(base_title, style)
            if result['success']:
                variations.append(result)
        
        return variations
    
    def generate_seasonal_newsletter(self, season, special_event=None):
        """
        Générer une newsletter saisonnière
        
        Args:
            season (str): Saison (printemps, été, automne, hiver)
            special_event (str): Événement spécial (saint-valentin, noël, etc.)
        """
        
        seasonal_titles = {
            'printemps': 'Nouvelles Rencontres de Printemps avec MeetVoice IA',
            'été': 'Été des Connexions Authentiques - MeetVoice IA',
            'automne': 'Automne des Rencontres Profondes avec l\'IA',
            'hiver': 'Réchauffez vos Rencontres cet Hiver avec MeetVoice'
        }
        
        special_titles = {
            'saint-valentin': 'Saint-Valentin 2025 : L\'IA au Service de l\'Amour',
            'noël': 'Noël 2024 : Offrez-vous de Vraies Rencontres',
            'nouvel-an': 'Nouvelle Année, Nouvelles Rencontres IA',
            'fête-des-mères': 'Fête des Mères : Partagez MeetVoice en Famille'
        }
        
        if special_event and special_event in special_titles:
            title = special_titles[special_event]
        else:
            title = seasonal_titles.get(season, 'Newsletter MeetVoice - Rencontres IA')
        
        return self.generate_newsletter_from_title(title, 'premium', 'general')
    
    def generate_feature_announcement(self, feature_name, feature_description):
        """
        Générer une newsletter d'annonce de nouvelle fonctionnalité
        
        Args:
            feature_name (str): Nom de la fonctionnalité
            feature_description (str): Description de la fonctionnalité
        """
        
        title = f"Nouvelle Fonctionnalité MeetVoice : {feature_name}"
        
        enhanced_prompt = f"""
        FONCTIONNALITÉ À ANNONCER :
        Nom : {feature_name}
        Description : {feature_description}
        
        Créer une newsletter d'annonce excitante qui :
        - Présente la nouvelle fonctionnalité de manière attractive
        - Explique les bénéfices pour les utilisateurs
        - Incite à tester la nouveauté
        - Maintient l'excitation autour de l'innovation MeetVoice
        """
        
        return self.generate_newsletter_from_title(title, 'moderne', 'utilisateurs_actifs')
    
    def generate_welcome_series(self, user_name=None):
        """
        Générer une série d'emails de bienvenue
        
        Args:
            user_name (str): Nom de l'utilisateur (optionnel)
        """
        
        welcome_emails = [
            {
                'title': f"Bienvenue sur MeetVoice {user_name or ''} ! 🎙️",
                'style': 'moderne',
                'audience': 'nouveaux_utilisateurs'
            },
            {
                'title': 'Découvrez la Magie de l\'IA dans les Rencontres',
                'style': 'minimaliste',
                'audience': 'nouveaux_utilisateurs'
            },
            {
                'title': 'Vos Premières Connexions Authentiques vous Attendent',
                'style': 'promotionnel',
                'audience': 'nouveaux_utilisateurs'
            }
        ]
        
        series = []
        for email_config in welcome_emails:
            result = self.generate_newsletter_from_title(
                email_config['title'],
                email_config['style'],
                email_config['audience']
            )
            if result['success']:
                series.append(result)
        
        return series
    
    def generate_retention_newsletter(self, user_activity_level="low"):
        """
        Générer une newsletter de rétention
        
        Args:
            user_activity_level (str): Niveau d'activité (low, medium, high)
        """
        
        retention_titles = {
            'low': 'Nous vous avons manqué ! Redécouvrez MeetVoice IA',
            'medium': 'Boostez vos Rencontres avec nos Nouvelles Fonctionnalités IA',
            'high': 'Merci d\'être un Membre Actif de MeetVoice !'
        }
        
        title = retention_titles.get(user_activity_level, retention_titles['medium'])
        
        return self.generate_newsletter_from_title(title, 'premium', 'utilisateurs_actifs')

    def _generate_professional_images(self, title, style):
        """Générer des URLs d'images professionnelles avec prompts avancés"""
        import urllib.parse

        # Images selon le style et le contexte MeetVoice avec prompts plus spécifiques
        base_prompts = {
            'moderne': [
                'modern couple using smartphones, AI dating app interface, sleek technology, professional photography, warm lighting',
                'young professionals networking event, voice technology, modern office space, business casual, high quality',
                'AI technology visualization, voice waveforms, modern interface design, purple and blue colors, professional'
            ],
            'minimaliste': [
                'minimalist couple portrait, clean background, simple elegant design, professional photography, soft lighting',
                'clean modern workspace, voice technology equipment, minimal design, white background, professional',
                'simple elegant interface, voice dating app, minimal UI design, clean typography, professional'
            ],
            'promotionnel': [
                'excited couple celebrating, success story, dynamic composition, vibrant colors, professional photography',
                'promotional lifestyle photo, happy people using voice technology, energetic, marketing style, high quality',
                'success celebration, achievement moment, business growth visualization, dynamic, professional'
            ],
            'premium': [
                'luxury couple dining, premium restaurant setting, sophisticated atmosphere, high-end photography, elegant',
                'executive business meeting, premium office environment, sophisticated design, luxury workspace, professional',
                'high-end technology interface, premium design, sophisticated UI, luxury branding, professional photography'
            ]
        }

        # Contexte MeetVoice plus spécifique
        meetvoice_contexts = {
            'moderne': "AI-powered dating app, voice technology, modern romance, tech-savvy users",
            'minimaliste': "elegant dating platform, voice-first approach, sophisticated users, premium experience",
            'promotionnel': "revolutionary dating app, AI matching, success stories, happy couples",
            'premium': "luxury dating service, exclusive members, high-end experience, sophisticated matching"
        }

        context = meetvoice_contexts.get(style, meetvoice_contexts['moderne'])
        prompts = base_prompts.get(style, base_prompts['moderne'])

        images = []
        # Limiter à 2 images maximum pour éviter la surcharge
        limited_prompts = prompts[:2]

        for i, prompt in enumerate(limited_prompts):
            # Créer des prompts plus spécifiques selon le titre
            if 'bienvenue' in title.lower() or 'welcome' in title.lower():
                enhanced_prompt = f"welcome scene, {prompt}, onboarding experience, {context}"
            elif 'nouveauté' in title.lower() or 'innovation' in title.lower():
                enhanced_prompt = f"innovation showcase, {prompt}, new features, {context}"
            elif 'premium' in title.lower() or 'exclusif' in title.lower():
                enhanced_prompt = f"premium experience, {prompt}, exclusive features, {context}"
            else:
                enhanced_prompt = f"{prompt}, {context}"

            # Ajouter des paramètres de qualité
            final_prompt = f"{enhanced_prompt}, professional photography, high resolution, marketing quality, no text /* overlay - BANNI */"
            encoded_prompt = urllib.parse.quote(final_prompt)

            # Utiliser des seeds différents pour plus de variété
            seed = (hash(title + str(i) + style) % 9999) + 1000
            image_url = f"https://image.pollinations.ai/prompt/{encoded_prompt}?width=600&height=400&seed={seed}&enhance=true"

            images.append({
                'url': image_url,
                'alt': f"Image professionnelle {i+1} pour {title}",
                'description': prompt,
                'enhanced_prompt': final_prompt
            })

        return images

    def _clean_ai_html_response(self, ai_response):
        """Nettoyer la réponse de l'IA pour extraire le HTML pur"""

        # Supprimer les balises markdown communes
        clean_text = ai_response.strip()

        # Supprimer les balises de code markdown
        if clean_text.startswith('```html'):
            clean_text = clean_text[7:]  # Supprimer ```html
        elif clean_text.startswith('```'):
            clean_text = clean_text[3:]   # Supprimer ```

        if clean_text.endswith('```'):
            clean_text = clean_text[:-3]  # Supprimer ``` final

        # Supprimer les espaces en début/fin
        clean_text = clean_text.strip()

        # Vérifier que c'est du HTML valide
        if not clean_text.startswith('<!DOCTYPE') and not clean_text.startswith('<html'):
            # Si ce n'est pas du HTML complet, l'envelopper
            clean_text = f"""
            <!DOCTYPE html>
            <html lang="fr">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Newsletter MeetVoice</title>
            </head>
            <body>
                {clean_text}
            </body>
            </html>
            """

        return clean_text.strip()

    def _generate_fallback_template(self, title, style, audience):
        """Générer un template professionnel sans IA"""

        # Couleurs selon le style
        style_colors = {
            'moderne': {'primary': '#4e385f', 'secondary': '#2A1D34', 'accent': '#6c5ce7', 'bg': '#f8f9fa'},
            'minimaliste': {'primary': '#2A1D34', 'secondary': '#6c757d', 'accent': '#4e385f', 'bg': '#ffffff'},
            'promotionnel': {'primary': '#e74c3c', 'secondary': '#c0392b', 'accent': '#f39c12', 'bg': '#fff5f5'},
            'premium': {'primary': '#2c3e50', 'secondary': '#34495e', 'accent': '#9b59b6', 'bg': '#f7f8fc'}
        }

        colors = style_colors.get(style, style_colors['moderne'])

        # Générer des images professionnelles
        images = self._generate_professional_images(title, style)

        # Image par défaut si aucune image générée
        if not images:
            images = [{
                'url': 'https://image.pollinations.ai/prompt/modern%20dating%20app%20interface%2C%20AI%20technology%2C%20professional%20design%2C%20purple%20theme%2C%20MeetVoice%20branding?width=600&height=400&seed=1234&enhance=true',
                'alt': f'Image pour {title}',
                'description': 'Image par défaut MeetVoice'
            }]

        # Contenu selon l'audience
        audience_content = {
            'general': {
                'greeting': 'Cher utilisateur MeetVoice',
                'intro': 'Découvrez les dernières nouveautés de notre plateforme de rencontres IA.',
                'cta': 'Découvrir MeetVoice'
            },
            'nouveaux_utilisateurs': {
                'greeting': 'Bienvenue sur MeetVoice !',
                'intro': 'Vous venez de rejoindre la révolution des rencontres par l\'IA. Voici comment commencer.',
                'cta': 'Commencer maintenant'
            },
            'utilisateurs_actifs': {
                'greeting': 'Cher membre actif',
                'intro': 'Merci pour votre fidélité ! Découvrez nos dernières améliorations.',
                'cta': 'Voir les nouveautés'
            },
            'premium': {
                'greeting': 'Cher membre Premium',
                'intro': 'Profitez de vos avantages exclusifs et des dernières fonctionnalités premium.',
                'cta': 'Accéder aux fonctionnalités Premium'
            }
        }

        content = audience_content.get(audience, audience_content['general'])

        # Template HTML professionnel avec images
        html_template = f"""
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            background-color: {colors['bg']};
            color: #333;
        }}
        .email-container {{
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-radius: 12px;
            overflow: hidden;
        }}

        /* Header avec logo et navigation */
        .header {{
            background: {colors['primary']};
            padding: 30px;
            text-align: center;
            position: relative;
        }}
        .header::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, {colors['accent']}, {colors['secondary']});
        }}
        .logo-container {{
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-bottom: 15px;
        }}
        .logo-image {{
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: white;
            padding: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }}
        .logo-text {{
            font-size: 32px;
            font-weight: 800;
            color: white;
            letter-spacing: -1px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }}
        .tagline {{
            color: rgba(255,255,255,0.9);
            font-size: 16px;
            font-weight: 400;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }}

        /* Hero section avec image */
        .hero {{
            position: relative;
            height: 300px;
            background: linear-gradient(135deg, {colors['primary']}22, {colors['accent']}22);
            overflow: hidden;
        }}
        .hero-image {{
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0.8;
        }}
        .hero-overlay {{
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, {colors['primary']}CC, {colors['secondary']}AA);
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 40px;
        }}
        .hero-content h1 {{
            color: white;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 16px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }}
        .hero-content p {{
            color: rgba(255,255,255,0.95);
            font-size: 18px;
            font-weight: 400;
            max-width: 400px;
        }}

        /* Content sections */
        .content {{
            padding: 50px 40px;
        }}
        .section {{
            margin-bottom: 50px;
        }}
        .section-title {{
            font-size: 28px;
            font-weight: 700;
            color: {colors['primary']};
            margin-bottom: 20px;
            text-align: center;
        }}
        .section-subtitle {{
            font-size: 16px;
            color: #666;
            text-align: center;
            margin-bottom: 40px;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }}

        /* Features grid */
        .features-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }}
        .feature-card {{
            background: white;
            border: 2px solid #f0f0f0;
            border-radius: 16px;
            padding: 30px 25px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }}
        .feature-card:hover {{
            border-color: {colors['accent']};
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }}
        .feature-icon {{
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }}
        .feature-title {{
            font-size: 20px;
            font-weight: 600;
            color: {colors['primary']};
            margin-bottom: 12px;
        }}
        .feature-description {{
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }}

        /* CTA Section */
        .cta-section {{
            background: linear-gradient(135deg, {colors['primary']}, {colors['secondary']});
            padding: 50px 40px;
            text-align: center;
            margin: 50px -40px 0 -40px;
        }}
        .cta-title {{
            color: white;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 16px;
        }}
        .cta-subtitle {{
            color: rgba(255,255,255,0.9);
            font-size: 18px;
            margin-bottom: 30px;
        }}
        .cta-button {{
            display: inline-block;
            background: white;
            color: {colors['primary']} !important;
            text-decoration: none;
            padding: 18px 40px;
            border-radius: 50px;
            font-weight: 700;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }}
        .cta-button:hover {{
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }}

        /* Stats section */
        .stats {{
            display: flex;
            justify-content: space-around;
            background: #f8f9fa;
            padding: 40px 20px;
            margin: 40px -40px;
            border-radius: 16px;
        }}
        .stat-item {{
            text-align: center;
        }}
        .stat-number {{
            font-size: 36px;
            font-weight: 800;
            color: {colors['primary']};
            display: block;
        }}
        .stat-label {{
            font-size: 14px;
            color: #666;
            margin-top: 8px;
        }}

        /* Footer */
        .footer {{
            background: #f8f9fa;
            padding: 40px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }}
        .footer-links {{
            margin-bottom: 30px;
        }}
        .footer-links a {{
            color: {colors['primary']};
            text-decoration: none;
            margin: 0 15px;
            font-weight: 500;
        }}
        .footer-text {{
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }}
        .footer-text a {{
            color: {colors['primary']};
            text-decoration: none;
        }}

        /* Responsive */
        @media (max-width: 600px) {{
            .email-container {{
                margin: 0;
                border-radius: 0;
            }}
            .content {{
                padding: 30px 20px;
            }}
            .hero-content h1 {{
                font-size: 28px;
            }}
            .features-grid {{
                grid-template-columns: 1fr;
                gap: 20px;
            }}
            .stats {{
                flex-direction: column;
                gap: 30px;
            }}
            .cta-section {{
                padding: 40px 20px;
                margin-left: -20px;
                margin-right: -20px;
            }}
        }}
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header avec logo -->
        <div class="header">
            <div class="logo-container">
                <img src="https://image.pollinations.ai/prompt/MeetVoice%20logo%2C%20modern%20microphone%20icon%2C%20purple%20gradient%2C%20professional%20design%2C%20dating%20app%20logo%2C%20voice%20technology%2C%20clean%20minimalist%20style%2C%20high%20resolution?width=100&height=100&seed=meetvoice&enhance=true"
                     alt="MeetVoice Logo" class="logo-image">
                <div class="logo-text">🎙️ MeetVoice</div>
            </div>
            <div class="tagline">L'IA qui révolutionne les rencontres</div>
        </div>

        <!-- Hero section avec image -->
        <div class="hero">
            <img src="{images[0]['url']}" alt="{images[0]['alt']}" class="hero-image">
            <div class="hero-overlay">
                <div class="hero-content">
                    <h1>{title}</h1>
                    <p>{content['intro']}</p>
                </div>
            </div>
        </div>

        <!-- Contenu principal -->
        <div class="content">
            <!-- Section d'introduction -->
            <div class="section">
                <h2 class="section-title">Bonjour cher membre MeetVoice,</h2>
                <p class="section-subtitle">
                    Découvrez comment notre technologie révolutionnaire transforme
                    la façon dont les gens se rencontrent et créent des connexions authentiques.
                </p>
            </div>

            <!-- Grille de fonctionnalités -->
            <div class="section">
                <h2 class="section-title">Pourquoi MeetVoice ?</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <span class="feature-icon">🤖</span>
                        <h3 class="feature-title">IA Avancée</h3>
                        <p class="feature-description">
                            Notre intelligence artificielle analyse votre voix pour vous connecter
                            avec des personnes vraiment compatibles.
                        </p>
                    </div>

                    <div class="feature-card">
                        <span class="feature-icon">🎙️</span>
                        <h3 class="feature-title">Rencontres Vocales</h3>
                        <p class="feature-description">
                            Créez des connexions authentiques basées sur la personnalité
                            plutôt que sur les apparences.
                        </p>
                    </div>

                    <div class="feature-card">
                        <span class="feature-icon">🔒</span>
                        <h3 class="feature-title">Sécurité Totale</h3>
                        <p class="feature-description">
                            Environnement sécurisé avec modération IA et vérification
                            complète des profils utilisateurs.
                        </p>
                    </div>

                    <div class="feature-card">
                        <span class="feature-icon">💝</span>
                        <h3 class="feature-title">Connexions Profondes</h3>
                        <p class="feature-description">
                            Développez des relations durables basées sur une véritable
                            compatibilité émotionnelle et intellectuelle.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Section statistiques -->
            <div class="section">
                <h2 class="section-title">MeetVoice en chiffres</h2>
                <div class="stats">
                    <div class="stat-item">
                        <span class="stat-number">50K+</span>
                        <div class="stat-label">Utilisateurs actifs</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">15K+</span>
                        <div class="stat-label">Couples formés</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">95%</span>
                        <div class="stat-label">Satisfaction</div>
                    </div>
                </div>
            </div>

            <!-- Section avec deuxième image (si disponible) -->
            {"" if len(images) < 2 else f'''
            <div class="section">
                <div style="text-align: center; margin: 40px 0;">
                    <img src="{images[1]['url']}" alt="{images[1]['alt']}"
                         style="width: 100%; max-width: 500px; border-radius: 16px; box-shadow: 0 8px 25px rgba(0,0,0,0.1);">
                </div>
                <p style="text-align: center; color: #666; font-style: italic; margin-top: 20px;">
                    Des milliers de personnes ont déjà trouvé l'amour grâce à MeetVoice
                </p>
            </div>
            '''}
        </div>

        <!-- Section CTA -->
        <div class="cta-section">
            <h2 class="cta-title">Prêt à trouver votre âme sœur ?</h2>
            <p class="cta-subtitle">
                Rejoignez la communauté MeetVoice et découvrez des connexions authentiques
            </p>
            <a href="https://meetvoice.com/app" class="cta-button">
                {content['cta']}
            </a>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-links">
                <a href="https://meetvoice.com">Accueil</a>
                <a href="https://meetvoice.com/app">Application</a>
                <a href="https://meetvoice.com/support">Support</a>
                <a href="https://meetvoice.com/blog">Blog</a>
            </div>

            <div class="footer-text">
                <p>Vous recevez cet email car vous êtes membre de MeetVoice.</p>
                <p>
                    <a href="#unsubscribe">Se désinscrire</a> •
                    <a href="https://meetvoice.com/privacy">Confidentialité</a> •
                    <a href="https://meetvoice.com/contact">Contact</a>
                </p>
                <p style="margin-top: 20px;">
                    © 2025 MeetVoice. Tous droits réservés.<br>
                    L'avenir des rencontres commence ici.
                </p>
            </div>
        </div>
    </div>
</body>
</html>
        """

        return {
            'success': True,
            'html_content': html_template.strip(),
            'title': title,
            'style': style,
            'audience': audience,
            'source': 'fallback'
        }


# Fonctions utilitaires pour l'intégration Django

def create_ai_template(title, style='moderne', audience='general'):
    """
    Fonction utilitaire pour créer un template IA
    
    Args:
        title (str): Titre de la newsletter
        style (str): Style du template
        audience (str): Audience cible
    
    Returns:
        dict: Résultat de la génération
    """
    generator = AITemplateGenerator()
    return generator.generate_newsletter_from_title(title, style, audience)


def create_seasonal_template(season, special_event=None):
    """
    Fonction utilitaire pour créer un template saisonnier
    
    Args:
        season (str): Saison
        special_event (str): Événement spécial
    
    Returns:
        dict: Résultat de la génération
    """
    generator = AITemplateGenerator()
    return generator.generate_seasonal_newsletter(season, special_event)


def create_welcome_series(user_name=None):
    """
    Fonction utilitaire pour créer une série de bienvenue
    
    Args:
        user_name (str): Nom de l'utilisateur
    
    Returns:
        list: Liste des emails générés
    """
    generator = AITemplateGenerator()
    return generator.generate_welcome_series(user_name)
