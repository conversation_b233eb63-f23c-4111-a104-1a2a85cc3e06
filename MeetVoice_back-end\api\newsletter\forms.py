from django import forms
from django.contrib.auth.models import User
from .models import Campaign, EmailTemplate, NewsletterSettings


class CampaignForm(forms.ModelForm):
    """Formulaire pour créer/modifier une campagne"""
    
    class Meta:
        model = Campaign
        fields = [
            'name', 'subject', 'template', 'ai_prompt', 
            'audience_type', 'scheduled_at'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom de la campagne'
            }),
            'subject': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Sujet de l\'email'
            }),
            'template': forms.Select(attrs={
                'class': 'form-select'
            }),
            'ai_prompt': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Décrivez le contenu que vous souhaitez générer...'
            }),
            'audience_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'scheduled_at': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
        }
        labels = {
            'name': 'Nom de la campagne',
            'subject': 'Sujet de l\'email',
            'template': 'Template à utiliser',
            'ai_prompt': 'Instructions pour l\'IA',
            'audience_type': 'Type d\'audience',
            'scheduled_at': 'Programmer pour (optionnel)',
        }
        help_texts = {
            'ai_prompt': 'Décrivez le contenu que vous souhaitez que l\'IA génère pour cette newsletter.',
            'scheduled_at': 'Laissez vide pour envoyer immédiatement.',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filtrer les templates actifs
        self.fields['template'].queryset = EmailTemplate.objects.filter(is_active=True)
        
        # Rendre certains champs obligatoires
        self.fields['name'].required = True
        self.fields['subject'].required = True
        self.fields['template'].required = True
        self.fields['ai_prompt'].required = True
    
    def clean_subject(self):
        """Validation du sujet"""
        subject = self.cleaned_data.get('subject')
        if len(subject) > 300:
            raise forms.ValidationError('Le sujet ne peut pas dépasser 300 caractères.')
        return subject
    
    def clean_ai_prompt(self):
        """Validation du prompt IA"""
        prompt = self.cleaned_data.get('ai_prompt')
        if len(prompt) < 10:
            raise forms.ValidationError('Le prompt doit contenir au moins 10 caractères.')
        return prompt


class EmailTemplateForm(forms.ModelForm):
    """Formulaire pour créer/modifier un template d'email"""
    
    class Meta:
        model = EmailTemplate
        fields = [
            'name', 'subject_template', 'header_html', 'content_html', 
            'footer_html', 'css_styles', 'preview_text'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom du template'
            }),
            'subject_template': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Modèle de sujet (ex: Newsletter MeetVoice - {{ date }})'
            }),
            'header_html': forms.Textarea(attrs={
                'class': 'form-control code-editor',
                'rows': 8,
                'placeholder': 'HTML du header (logo, navigation...)'
            }),
            'content_html': forms.Textarea(attrs={
                'class': 'form-control code-editor',
                'rows': 10,
                'placeholder': 'Zone de contenu principal (sera remplacée par l\'IA)'
            }),
            'footer_html': forms.Textarea(attrs={
                'class': 'form-control code-editor',
                'rows': 6,
                'placeholder': 'HTML du footer (liens, désinscription...)'
            }),
            'css_styles': forms.Textarea(attrs={
                'class': 'form-control code-editor',
                'rows': 12,
                'placeholder': 'Styles CSS inline pour compatibilité email'
            }),
            'preview_text': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Texte de prévisualisation (150 caractères max)',
                'maxlength': 150
            }),
        }
        labels = {
            'name': 'Nom du template',
            'subject_template': 'Modèle de sujet',
            'header_html': 'Header HTML',
            'content_html': 'Contenu HTML',
            'footer_html': 'Footer HTML',
            'css_styles': 'Styles CSS',
            'preview_text': 'Texte de prévisualisation',
        }
        help_texts = {
            'subject_template': 'Utilisez des variables comme {{ date }}, {{ campaign_name }}',
            'header_html': 'HTML pour le header (logo, navigation). Utilisez des styles inline.',
            'content_html': 'Zone qui sera remplacée par le contenu généré par l\'IA.',
            'footer_html': 'HTML pour le footer. Incluez {{ unsubscribe_url }} pour la désinscription.',
            'css_styles': 'CSS inline pour assurer la compatibilité avec tous les clients email.',
            'preview_text': 'Texte affiché dans la prévisualisation de l\'email.',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Valeurs par défaut pour un nouveau template
        if not self.instance.pk:
            self.fields['header_html'].initial = self._get_default_header()
            self.fields['footer_html'].initial = self._get_default_footer()
            self.fields['css_styles'].initial = self._get_default_css()
            self.fields['content_html'].initial = self._get_default_content()
    
    def _get_default_header(self):
        """Header par défaut"""
        return '''
<div style="background-color: #2A1D34; padding: 20px; text-align: center;">
    <img src="https://votre-domaine.com/static/logo.png" alt="MeetVoice" style="height: 50px;">
    <h1 style="color: white; margin: 10px 0 0 0; font-family: Arial, sans-serif;">MeetVoice</h1>
</div>
        '''.strip()
    
    def _get_default_footer(self):
        """Footer par défaut"""
        return '''
<div style="background-color: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #dee2e6;">
    <p style="margin: 0 0 10px 0; color: #6c757d; font-size: 14px;">
        Vous recevez cet email car vous êtes inscrit sur MeetVoice.
    </p>
    <p style="margin: 0 0 15px 0;">
        <a href="{{ unsubscribe_url }}" style="color: #007bff; text-decoration: none;">Se désinscrire</a> |
        <a href="https://votre-domaine.com/contact" style="color: #007bff; text-decoration: none;">Contact</a>
    </p>
    <p style="margin: 0; color: #6c757d; font-size: 12px;">
        © 2025 MeetVoice. Tous droits réservés.
    </p>
</div>
        '''.strip()
    
    def _get_default_css(self):
        """CSS par défaut"""
        return '''
/* Reset et base */
body { margin: 0; padding: 0; font-family: Arial, sans-serif; line-height: 1.6; }
table { border-collapse: collapse; width: 100%; }
img { max-width: 100%; height: auto; }

/* Container principal */
.email-container { max-width: 600px; margin: 0 auto; background: white; }

/* Typographie */
h1, h2, h3 { color: #333; margin: 20px 0 10px 0; }
p { margin: 0 0 15px 0; color: #555; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }

/* Boutons */
.btn { 
    display: inline-block; 
    padding: 12px 24px; 
    background: #007bff; 
    color: white; 
    text-decoration: none; 
    border-radius: 4px; 
    margin: 10px 0; 
}
.btn:hover { background: #0056b3; }

/* Responsive */
@media only screen and (max-width: 600px) {
    .email-container { width: 100% !important; }
    .btn { display: block; text-align: center; }
}
        '''.strip()
    
    def _get_default_content(self):
        """Contenu par défaut"""
        return '''
<div style="padding: 30px;">
    <!-- Le contenu généré par l'IA sera inséré ici -->
    <div class="ai-content">
        {{ ai_generated_content }}
    </div>
</div>
        '''.strip()
    
    def clean_preview_text(self):
        """Validation du texte de prévisualisation"""
        preview_text = self.cleaned_data.get('preview_text', '')
        if len(preview_text) > 150:
            raise forms.ValidationError('Le texte de prévisualisation ne peut pas dépasser 150 caractères.')
        return preview_text


class NewsletterSettingsForm(forms.ModelForm):
    """Formulaire pour les paramètres de la newsletter"""
    
    class Meta:
        model = NewsletterSettings
        fields = [
            'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_use_tls',
            'from_email', 'from_name', 'reply_to',
            'ai_api_key', 'ai_model',
            'max_emails_per_hour', 'max_emails_per_day'
        ]
        widgets = {
            'smtp_host': forms.TextInput(attrs={'class': 'form-control'}),
            'smtp_port': forms.NumberInput(attrs={'class': 'form-control'}),
            'smtp_username': forms.TextInput(attrs={'class': 'form-control'}),
            'smtp_password': forms.PasswordInput(attrs={'class': 'form-control'}),
            'smtp_use_tls': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'from_email': forms.EmailInput(attrs={'class': 'form-control'}),
            'from_name': forms.TextInput(attrs={'class': 'form-control'}),
            'reply_to': forms.EmailInput(attrs={'class': 'form-control'}),
            'ai_api_key': forms.PasswordInput(attrs={'class': 'form-control'}),
            'ai_model': forms.TextInput(attrs={'class': 'form-control'}),
            'max_emails_per_hour': forms.NumberInput(attrs={'class': 'form-control'}),
            'max_emails_per_day': forms.NumberInput(attrs={'class': 'form-control'}),
        }
        labels = {
            'smtp_host': 'Serveur SMTP',
            'smtp_port': 'Port SMTP',
            'smtp_username': 'Nom d\'utilisateur SMTP',
            'smtp_password': 'Mot de passe SMTP',
            'smtp_use_tls': 'Utiliser TLS',
            'from_email': 'Email expéditeur',
            'from_name': 'Nom expéditeur',
            'reply_to': 'Email de réponse',
            'ai_api_key': 'Clé API IA (Gemini)',
            'ai_model': 'Modèle IA',
            'max_emails_per_hour': 'Max emails par heure',
            'max_emails_per_day': 'Max emails par jour',
        }
        help_texts = {
            'smtp_host': 'Ex: smtp.gmail.com',
            'smtp_port': 'Ex: 587 pour TLS, 465 pour SSL',
            'smtp_use_tls': 'Recommandé pour la sécurité',
            'ai_api_key': 'Clé API Google Gemini pour la génération de contenu',
            'ai_model': 'Modèle à utiliser (ex: gemini-pro)',
            'max_emails_per_hour': 'Limite pour éviter le spam',
            'max_emails_per_day': 'Limite quotidienne',
        }
    
    def clean_smtp_port(self):
        """Validation du port SMTP"""
        port = self.cleaned_data.get('smtp_port')
        if port and (port < 1 or port > 65535):
            raise forms.ValidationError('Le port doit être entre 1 et 65535.')
        return port
    
    def clean_max_emails_per_hour(self):
        """Validation du nombre max d'emails par heure"""
        max_emails = self.cleaned_data.get('max_emails_per_hour')
        if max_emails and max_emails < 1:
            raise forms.ValidationError('Le nombre doit être supérieur à 0.')
        return max_emails
    
    def clean_max_emails_per_day(self):
        """Validation du nombre max d'emails par jour"""
        max_emails = self.cleaned_data.get('max_emails_per_day')
        if max_emails and max_emails < 1:
            raise forms.ValidationError('Le nombre doit être supérieur à 0.')
        return max_emails


class GenerateContentForm(forms.Form):
    """Formulaire pour la génération de contenu IA"""
    
    prompt = forms.CharField(
        label='Instructions pour l\'IA',
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'Décrivez le contenu que vous souhaitez générer...'
        }),
        help_text='Soyez précis dans votre demande pour obtenir un meilleur résultat.'
    )
    
    theme = forms.CharField(
        label='Thème',
        max_length=100,
        initial='newsletter',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Ex: newsletter, promotion, actualités...'
        })
    )
    
    audience = forms.ChoiceField(
        label='Audience cible',
        choices=[
            ('utilisateurs', 'Tous les utilisateurs'),
            ('nouveaux', 'Nouveaux utilisateurs'),
            ('actifs', 'Utilisateurs actifs'),
            ('premium', 'Utilisateurs premium'),
        ],
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    def clean_prompt(self):
        """Validation du prompt"""
        prompt = self.cleaned_data.get('prompt')
        if len(prompt) < 10:
            raise forms.ValidationError('Le prompt doit contenir au moins 10 caractères.')
        if len(prompt) > 1000:
            raise forms.ValidationError('Le prompt ne peut pas dépasser 1000 caractères.')
        return prompt
