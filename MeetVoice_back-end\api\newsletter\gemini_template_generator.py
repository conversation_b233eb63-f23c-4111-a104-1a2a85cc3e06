"""
Générateur de templates professionnels utilisant Gemini AI et génération d'images
"""
import os
import requests
import json
import urllib.parse
from typing import Dict, List, Any
import google.generativeai as genai

class GeminiTemplateGenerator:
    """Générateur de templates utilisant Gemini AI et génération d'images"""
    
    def __init__(self):
        # Configuration Gemini - charger depuis Django settings
        from django.conf import settings

        # Essayer d'abord les variables d'environnement, puis Django settings
        self.api_key = os.getenv('GOOGLE_GEMINI_API_KEY') or getattr(settings, 'GOOGLE_GEMINI_API_KEY', None)

        # Si toujours pas trouvé, utiliser la clé directement
        if not self.api_key:
            self.api_key = "AIzaSyBJGKJJOKJOKJOKJOKJOKJOKJOKJOKJOKJ"

        if not self.api_key:
            raise ValueError("GOOGLE_GEMINI_API_KEY non trouvée")
        
        genai.configure(api_key=self.api_key)
        # Utiliser le modèle Gemini 1.5 Flash qui est stable et rapide
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Configuration pour la génération d'images
        self.image_generators = {
            'pollinations': 'https://image.pollinations.ai/prompt/',
            'picsum': 'https://picsum.photos/',
            'unsplash': 'https://source.unsplash.com/'
        }
        
        # Contexte MeetVoice
        self.meetvoice_context = """
        MeetVoice est une plateforme révolutionnaire de rencontres basée sur l'Intelligence Artificielle.
        Notre technologie analyse les voix pour créer des connexions authentiques au-delà des apparences.
        Nous utilisons l'IA avancée pour matcher les utilisateurs selon leur personnalité vocale.
        Public cible : adultes 25-45 ans, tech-savvy, cherchant des relations authentiques.
        Valeurs : authenticité, innovation, sécurité, connexions profondes.
        """
    
    def generate_professional_template(self, title: str, event_type: str, style: str = 'premium') -> Dict[str, Any]:
        """
        Générer un template professionnel avec Gemini et images IA
        
        Args:
            title: Titre de la newsletter
            event_type: Type d'événement (nouvel_an, saint_valentin, halloween, etc.)
            style: Style du template (premium, moderne, minimaliste)
        
        Returns:
            Dict contenant le HTML généré et les métadonnées
        """
        try:
            # 1. Générer les images avec l'IA
            images = self._generate_event_images(event_type, title)
            
            # 2. Créer le prompt pour Gemini
            prompt = self._create_gemini_prompt(title, event_type, style, images)
            
            # 3. Générer le contenu avec Gemini
            response = self.model.generate_content(prompt)
            html_content = response.text
            
            # 4. Post-traiter le HTML
            html_content = self._post_process_html(html_content, images)
            
            return {
                'success': True,
                'html_content': html_content,
                'title': title,
                'event_type': event_type,
                'style': style,
                'images': images,
                'source': 'gemini_ai'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'source': 'gemini_ai'
            }
    
    def _generate_event_images(self, event_type: str, title: str) -> List[Dict[str, str]]:
        """Générer des images spécifiques à l'événement"""
        
        # Prompts spécifiques par événement
        event_prompts = {
            'nouvel_an': [
                'elegant new year celebration, champagne glasses, golden confetti, luxury party, professional photography',
                'modern couple celebrating new year, fireworks background, romantic atmosphere, high quality',
                'new year resolutions, fresh start, modern lifestyle, professional business setting'
            ],
            'saint_valentin': [
                'romantic couple using voice technology, elegant restaurant, valentine day, professional photography',
                'modern valentine celebration, heart shapes, romantic atmosphere, luxury setting, high quality',
                'love connection through technology, romantic dinner, elegant couple, professional photo'
            ],
            'halloween': [
                'elegant halloween party, mysterious atmosphere, sophisticated costumes, professional photography',
                'modern halloween celebration, orange and black colors, mysterious couple, high quality',
                'halloween dating event, mysterious atmosphere, elegant party, professional setting'
            ]
        }
        
        prompts = event_prompts.get(event_type, event_prompts['nouvel_an'])
        images = []
        
        for i, prompt in enumerate(prompts):
            # Ajouter le contexte MeetVoice
            enhanced_prompt = f"{prompt}, MeetVoice dating app, voice technology, AI matching, professional quality"
            encoded_prompt = urllib.parse.quote(enhanced_prompt)
            
            # Générer avec Pollinations.ai
            seed = hash(title + str(i) + event_type) % 9999 + 1000
            image_url = f"{self.image_generators['pollinations']}{encoded_prompt}?width=600&height=400&seed={seed}&enhance=true"
            
            images.append({
                'url': image_url,
                'alt': f"Image professionnelle {i+1} pour {title}",
                'prompt': enhanced_prompt,
                'type': 'hero' if i == 0 else 'content'
            })
        
        return images
    
    def _create_gemini_prompt(self, title: str, event_type: str, style: str, images: List[Dict]) -> str:
        """Créer un prompt optimisé pour Gemini"""
        
        event_contexts = {
            'nouvel_an': {
                'theme': 'Nouvelle année, nouveaux départs, résolutions amoureuses',
                'colors': 'Or, argent, blanc, violet MeetVoice',
                'mood': 'Optimiste, festif, élégant, premium'
            },
            'saint_valentin': {
                'theme': 'Amour, romance, connexions authentiques par la voix',
                'colors': 'Rose, rouge, violet MeetVoice, blanc',
                'mood': 'Romantique, chaleureux, intime, sophistiqué'
            },
            'halloween': {
                'theme': 'Mystère, rencontres surprenantes, magie de l\'IA',
                'colors': 'Orange, noir, violet MeetVoice, gris',
                'mood': 'Mystérieux, amusant, intriguant, moderne'
            }
        }
        
        context = event_contexts.get(event_type, event_contexts['nouvel_an'])
        
        return f"""
        Tu es un DIRECTEUR CRÉATIF SENIOR d'une agence de design premium spécialisée dans les newsletters d'entreprises Fortune 500.
        
        BRIEF CLIENT - MEETVOICE :
        {self.meetvoice_context}
        
        MISSION CRÉATIVE :
        Créer une newsletter HTML ULTRA-PROFESSIONNELLE pour "{title}"
        
        CONTEXTE ÉVÉNEMENTIEL :
        - Événement : {event_type.upper()}
        - Thème : {context['theme']}
        - Palette : {context['colors']}
        - Ambiance : {context['mood']}
        
        IMAGES DISPONIBLES :
        - Hero : {images[0]['url']} (alt: {images[0]['alt']})
        - Content 1 : {images[1]['url']} (alt: {images[1]['alt']})
        - Content 2 : {images[2]['url']} (alt: {images[2]['alt']})
        
        STANDARDS DESIGN ENTERPRISE :
        1. STRUCTURE PREMIUM :
           - Header avec logo MeetVoice et navigation
           - Hero section avec image et overlay gradient
           - 3-4 sections de contenu modulaires
           - Grille de fonctionnalités avec icônes
           - Section témoignages/statistiques
           - CTA premium avec micro-interactions
           - Footer complet et organisé

        2. DESIGN SYSTEM :
           - Typography : Inter/SF Pro (fallback Arial)
           - Couleurs : #4e385f (primary), #2A1D34 (dark), #6c5ce7 (accent)
           - Spacing : 8px grid system
           - Border-radius : 8px, 12px, 16px
           - Shadows : subtiles et élégantes
        
        3. RESPONSIVE DESIGN :
           - Mobile-first approach
           - Breakpoints : 480px, 600px
           - Images adaptatives
           - Typography responsive
        
        4. CONTENU MARKETING :
           - Headlines percutants liés à l'événement
           - Value propositions claires
           - Call-to-actions irrésistibles
           - Social proof avec chiffres
           - Ton premium mais accessible
        
        CONTRAINTES TECHNIQUES :
        - HTML5 sémantique parfait
        - CSS inline pour compatibilité email
        - Largeur max : 600px
        - Compatible tous clients email
        - Accessibilité WCAG 2.1
        - Performance < 100KB
        
        INNOVATION VISUELLE OBLIGATOIRE :
        - Micro-animations CSS subtiles
        - Hover effects élégants
        - Gradients professionnels
        - Layout asymétrique équilibré
        - White space intentionnel

        RETOURNE UNIQUEMENT LE CODE HTML COMPLET, PRÊT POUR ENVOI PROFESSIONNEL.
        Assure-toi que le design soit digne d'Apple, Stripe ou Notion.
        """
    
    def _post_process_html(self, html_content: str, images: List[Dict]) -> str:
        """Post-traiter le HTML généré par Gemini"""
        
        # Nettoyer le HTML (enlever les balises markdown si présentes)
        if '```html' in html_content:
            html_content = html_content.split('```html')[1].split('```')[0]
        elif '```' in html_content:
            html_content = html_content.split('```')[1]
        
        # S'assurer que les URLs d'images sont correctement intégrées
        for i, image in enumerate(images):
            placeholder = f"{{IMAGE_{i}}}"
            if placeholder in html_content:
                html_content = html_content.replace(placeholder, image['url'])
        
        # Ajouter le pixel de tracking MeetVoice
        tracking_pixel = '<img src="https://meetvoice.com/track/email/{{campaign_id}}/{{recipient_id}}" width="1" height="1" style="display:none;" alt="">'
        if '</body>' in html_content:
            html_content = html_content.replace('</body>', f'{tracking_pixel}</body>')
        
        return html_content.strip()
