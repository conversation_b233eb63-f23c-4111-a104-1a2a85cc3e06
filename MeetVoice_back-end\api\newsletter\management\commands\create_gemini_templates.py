from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from newsletter.models import EmailTemplate
from newsletter.gemini_template_generator import GeminiTemplateGenerator

class Command(BaseCommand):
    help = 'Créer des templates professionnels avec Gemini AI et génération d\'images'

    def handle(self, *args, **options):
        """Supprimer tous les templates et créer de nouveaux templates avec Gemini"""
        
        # 1. Supprimer tous les templates existants
        self.stdout.write("🗑️ Suppression de tous les templates existants...")
        deleted_count = EmailTemplate.objects.count()
        EmailTemplate.objects.all().delete()
        self.stdout.write(
            self.style.WARNING(f"✅ {deleted_count} templates supprimés")
        )
        
        # 2. Initialiser le générateur Gemini
        try:
            generator = GeminiTemplateGenerator()
            self.stdout.write(
                self.style.SUCCESS("🤖 Générateur Gemini AI initialisé avec succès")
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Erreur d'initialisation Gemini: {str(e)}")
            )
            return
        
        # 3. Templates saisonniers professionnels à créer
        professional_templates = [
            {
                'name': '🎊 Nouvel An 2025 - Nouvelles Résolutions Amoureuses',
                'title': 'Nouvelle Année, Nouveaux Amours avec MeetVoice',
                'event_type': 'nouvel_an',
                'style': 'premium',
                'description': 'Template premium Gemini AI pour célébrer le Nouvel An avec design professionnel et images générées'
            },
            {
                'name': '💕 Saint-Valentin 2025 - L\'Amour par la Voix',
                'title': 'Saint-Valentin : Trouvez l\'Amour Authentique avec MeetVoice',
                'event_type': 'saint_valentin',
                'style': 'premium',
                'description': 'Template romantique Gemini AI pour la Saint-Valentin avec design sophistiqué et images d\'amour'
            },
            {
                'name': '🎃 Halloween 2025 - Rencontres Mystérieuses',
                'title': 'Halloween : Des Rencontres Envoûtantes vous Attendent',
                'event_type': 'halloween',
                'style': 'moderne',
                'description': 'Template mystérieux Gemini AI pour Halloween avec design moderne et images captivantes'
            }
        ]
        
        created_count = 0
        
        for template_config in professional_templates:
            try:
                self.stdout.write(f"\n🎨 Génération avec Gemini AI : {template_config['name']}")
                self.stdout.write("   📡 Connexion à Gemini...")
                self.stdout.write("   🖼️  Génération des images IA...")
                self.stdout.write("   ✨ Création du design professionnel...")
                
                # Générer le template avec Gemini et images IA
                result = generator.generate_professional_template(
                    title=template_config['title'],
                    event_type=template_config['event_type'],
                    style=template_config['style']
                )
                
                if result['success']:
                    # Obtenir un utilisateur admin
                    admin_user = User.objects.filter(is_superuser=True).first()
                    if not admin_user:
                        admin_user = User.objects.first()
                    
                    # Créer le template dans Django
                    template = EmailTemplate.objects.create(
                        name=template_config['name'],
                        subject_template=f"MeetVoice - {template_config['title']}",
                        header_html='<!-- Header généré par Gemini AI -->',
                        content_html=result['html_content'],
                        footer_html='<!-- Footer généré par Gemini AI -->',
                        css_styles='/* Styles générés par Gemini AI */',
                        created_by=admin_user,
                        is_active=True,
                        preview_text=template_config['description']
                    )
                    
                    self.stdout.write(
                        self.style.SUCCESS(f"✅ Template Gemini créé : {template.name} (ID: {template.id})")
                    )
                    
                    # Afficher les détails des images générées
                    if 'images' in result:
                        self.stdout.write("   🖼️  Images générées :")
                        for i, img in enumerate(result['images'], 1):
                            self.stdout.write(f"      {i}. {img['type'].title()}: {img['alt']}")
                    
                    created_count += 1
                    
                else:
                    self.stdout.write(
                        self.style.ERROR(f"❌ Erreur Gemini pour {template_config['name']}: {result.get('error', 'Erreur inconnue')}")
                    )
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ Erreur technique pour {template_config['name']}: {str(e)}")
                )
        
        # 4. Résumé final
        self.stdout.write("\n" + "="*80)
        self.stdout.write(
            self.style.SUCCESS(f"🎉 {created_count} templates professionnels Gemini AI créés avec succès !")
        )
        self.stdout.write("="*80)
        
        # Afficher la liste des nouveaux templates
        if created_count > 0:
            self.stdout.write("\n📋 Nouveaux templates Gemini AI :")
            templates = EmailTemplate.objects.all().order_by('-created_at')
            for template in templates:
                self.stdout.write(f"   ✨ {template.name}")
                self.stdout.write(f"      ID: {template.id} | Créé: {template.created_at.strftime('%d/%m/%Y %H:%M')}")
                self.stdout.write(f"      Aperçu: http://127.0.0.1:8000/newsletter/templates/{template.id}/preview/")
                self.stdout.write("")
        
        self.stdout.write(
            self.style.SUCCESS("🌟 Templates professionnels Gemini AI prêts à utiliser ! 🌟")
        )
        
        # Instructions d'utilisation
        self.stdout.write("\n💡 Pour utiliser ces templates :")
        self.stdout.write("   1. Allez sur http://127.0.0.1:8000/newsletter/templates/")
        self.stdout.write("   2. Sélectionnez un template Gemini AI")
        self.stdout.write("   3. Créez une campagne avec ce template")
        self.stdout.write("   4. Personnalisez et envoyez !")
        
        self.stdout.write(f"\n🤖 Powered by Gemini AI + Image Generation")
        self.stdout.write(f"📧 Templates optimisés pour tous les clients email")
        self.stdout.write(f"🎨 Design professionnel niveau entreprise")
