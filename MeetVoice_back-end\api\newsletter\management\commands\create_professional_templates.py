from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from newsletter.models import EmailTemplate
from newsletter.ai_template_generator import AITemplateGenerator

class Command(BaseCommand):
    help = 'Créer des templates professionnels avec le générateur IA'

    def handle(self, *args, **options):
        """Créer des templates professionnels avec le nouveau générateur"""
        
        generator = AITemplateGenerator()
        
        # Templates professionnels à créer
        professional_templates = [
            {
                'name': 'Premium Business - Nouveaux Utilisateurs',
                'title': 'Bienvenue dans l\'Avenir des Rencontres IA',
                'style': 'premium',
                'audience': 'nouveaux_utilisateurs',
                'description': 'Template premium pour accueillir les nouveaux utilisateurs avec un design sophistiqué'
            },
            {
                'name': 'Moderne Tech - Fonctionnalités',
                'title': 'Découvrez nos Dernières Innovations IA',
                'style': 'moderne',
                'audience': 'utilisateurs_actifs',
                'description': 'Template moderne pour présenter les nouvelles fonctionnalités'
            },
            {
                'name': 'Promotionnel Dynamique - Marketing',
                'title': 'Offre Spéciale : Trouvez l\'Amour avec l\'IA',
                'style': 'promotionnel',
                'audience': 'general',
                'description': 'Template promotionnel avec design énergique pour les campagnes marketing'
            },
            {
                'name': 'Minimaliste Élégant - Premium',
                'title': 'Votre Expérience Premium MeetVoice',
                'style': 'minimaliste',
                'audience': 'premium',
                'description': 'Template minimaliste et élégant pour les utilisateurs premium'
            },
            {
                'name': 'Corporate Professional - Entreprise',
                'title': 'MeetVoice : Révolution Technologique des Rencontres',
                'style': 'premium',
                'audience': 'general',
                'description': 'Template corporate pour communications professionnelles'
            }
        ]
        
        created_count = 0
        
        for template_config in professional_templates:
            try:
                # Vérifier si le template existe déjà
                if EmailTemplate.objects.filter(name=template_config['name']).exists():
                    self.stdout.write(
                        self.style.WARNING(f"⚠️  Template existe déjà : {template_config['name']}")
                    )
                    continue

                # Générer le template avec l'IA
                result = generator.generate_newsletter_from_title(
                    template_config['title'],
                    template_config['style'],
                    template_config['audience']
                )

                if result['success']:
                    # Obtenir un utilisateur admin pour created_by
                    admin_user = User.objects.filter(is_superuser=True).first()
                    if not admin_user:
                        admin_user = User.objects.first()

                    # Créer le template dans Django avec la structure EmailTemplate
                    template = EmailTemplate.objects.create(
                        name=template_config['name'],
                        subject_template=f"MeetVoice - {template_config['title']}",
                        header_html='<div class="header"><!-- Header généré automatiquement --></div>',
                        content_html=result['html_content'],
                        footer_html='<div class="footer"><!-- Footer généré automatiquement --></div>',
                        css_styles='/* Styles générés automatiquement */',
                        created_by=admin_user,
                        is_active=True,
                        preview_text=template_config['description']
                    )
                    
                    self.stdout.write(
                        self.style.SUCCESS(f"✅ Template créé : {template.name} (ID: {template.id})")
                    )
                    created_count += 1
                    
                else:
                    self.stdout.write(
                        self.style.ERROR(f"❌ Erreur lors de la génération : {template_config['name']}")
                    )
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ Erreur pour {template_config['name']}: {str(e)}")
                )
        
        self.stdout.write(
            self.style.SUCCESS(f"\n🎉 {created_count} templates professionnels créés avec succès !")
        )
        
        # Afficher la liste des templates
        self.stdout.write("\n📋 Templates disponibles :")
        templates = EmailTemplate.objects.all().order_by('-created_at')
        for template in templates:
            self.stdout.write(f"   - {template.name} (ID: {template.id}) - {template.created_at.strftime('%d/%m/%Y %H:%M')}")
