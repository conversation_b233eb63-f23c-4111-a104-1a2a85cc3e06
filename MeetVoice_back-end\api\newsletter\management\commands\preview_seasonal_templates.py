from django.core.management.base import BaseCommand
from newsletter.models import EmailTemplate

class Command(BaseCommand):
    help = 'Créer des aperçus HTML des templates saisonniers'

    def handle(self, *args, **options):
        """Créer des fichiers HTML pour prévisualiser les templates saisonniers"""
        
        # Récupérer tous les templates
        templates = EmailTemplate.objects.all().order_by('created_at')
        
        if not templates:
            self.stdout.write(
                self.style.ERROR("❌ Aucun template trouvé")
            )
            return
        
        self.stdout.write(f"🎨 Création des aperçus pour {templates.count()} templates...")
        
        for template in templates:
            try:
                # Générer le HTML complet
                full_html = template.get_full_html()
                
                # Nom de fichier sécurisé
                safe_name = template.name.lower().replace(' ', '_').replace('-', '_')
                safe_name = ''.join(c for c in safe_name if c.isalnum() or c == '_')
                filename = f"preview_{safe_name}_{template.id}.html"
                
                # <PERSON><PERSON><PERSON><PERSON> le fichier
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(full_html)
                
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Aperçu créé : {filename}")
                )
                self.stdout.write(f"   📄 Template : {template.name}")
                self.stdout.write(f"   📅 Créé le : {template.created_at.strftime('%d/%m/%Y %H:%M')}")
                self.stdout.write(f"   📝 Description : {template.preview_text}")
                self.stdout.write("")
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ Erreur pour {template.name}: {str(e)}")
                )
        
        self.stdout.write(
            self.style.SUCCESS("🌟 Tous les aperçus ont été créés ! Ouvrez les fichiers HTML dans votre navigateur.")
        )
