from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from newsletter.models import EmailTemplate
from newsletter.ai_template_generator import AITemplateGenerator

class Command(BaseCommand):
    help = 'Supprimer tous les templates et créer 3 templates saisonniers professionnels'

    def handle(self, *args, **options):
        """Supprimer tous les templates et créer les nouveaux templates saisonniers"""
        
        # 1. Supprimer tous les templates existants
        self.stdout.write("🗑️ Suppression de tous les templates existants...")
        deleted_count = EmailTemplate.objects.count()
        EmailTemplate.objects.all().delete()
        self.stdout.write(
            self.style.WARNING(f"✅ {deleted_count} templates supprimés")
        )
        
        # 2. Créer les nouveaux templates saisonniers
        generator = AITemplateGenerator()
        
        # Templates saisonniers à créer
        seasonal_templates = [
            {
                'name': 'Nouvel An 2025 - Nouvelles Résolutions Amoureuses',
                'title': 'Nouvelle Année, Nouveaux Amours avec MeetVoice',
                'style': 'premium',
                'audience': 'general',
                'description': 'Template premium pour célébrer le Nouvel An et encourager les nouvelles rencontres',
                'custom_prompt': 'Mettre l\'accent sur les nouvelles résolutions amoureuses, les nouveaux départs, l\'espoir et l\'optimisme pour 2025. Inclure des éléments festifs dorés et argentés, des feux d\'artifice, et l\'idée de trouver l\'amour en cette nouvelle année.'
            },
            {
                'name': 'Saint-Valentin 2025 - L\'Amour par la Voix',
                'title': 'Saint-Valentin : Trouvez l\'Amour Authentique avec MeetVoice',
                'style': 'promotionnel',
                'audience': 'nouveaux_utilisateurs',
                'description': 'Template romantique pour la Saint-Valentin mettant en avant les connexions vocales authentiques',
                'custom_prompt': 'Créer une atmosphère romantique et chaleureuse pour la Saint-Valentin. Mettre l\'accent sur l\'authenticité des connexions vocales, les émotions vraies, les cœurs qui battent. Utiliser des couleurs roses et rouges, des éléments romantiques, et l\'idée que la voix révèle la vraie personnalité.'
            },
            {
                'name': 'Halloween 2025 - Rencontres Mystérieuses',
                'title': 'Halloween : Des Rencontres Envoûtantes vous Attendent',
                'style': 'moderne',
                'audience': 'utilisateurs_actifs',
                'description': 'Template mystérieux et amusant pour Halloween avec une approche ludique des rencontres',
                'custom_prompt': 'Créer une ambiance mystérieuse et amusante pour Halloween. Jouer sur le mystère de la voix, les rencontres surprenantes, l\'aspect magique de l\'IA. Utiliser des couleurs orange et noires, des éléments d\'Halloween, et l\'idée que l\'amour peut être magique et surprenant.'
            }
        ]
        
        created_count = 0
        
        for template_config in seasonal_templates:
            try:
                self.stdout.write(f"🎨 Génération du template : {template_config['name']}")
                
                # Générer le template avec l'IA et le prompt personnalisé
                enhanced_title = f"{template_config['title']} - {template_config['custom_prompt']}"
                result = generator.generate_newsletter_from_title(
                    enhanced_title,
                    template_config['style'],
                    template_config['audience']
                )
                
                if result['success']:
                    # Obtenir un utilisateur admin pour created_by
                    admin_user = User.objects.filter(is_superuser=True).first()
                    if not admin_user:
                        admin_user = User.objects.first()
                    
                    # Créer le template dans Django avec la structure EmailTemplate
                    template = EmailTemplate.objects.create(
                        name=template_config['name'],
                        subject_template=f"MeetVoice - {template_config['title']}",
                        header_html='<div class="header"><!-- Header généré automatiquement --></div>',
                        content_html=result['html_content'],
                        footer_html='<div class="footer"><!-- Footer généré automatiquement --></div>',
                        css_styles='/* Styles générés automatiquement */',
                        created_by=admin_user,
                        is_active=True,
                        preview_text=template_config['description']
                    )
                    
                    self.stdout.write(
                        self.style.SUCCESS(f"✅ Template créé : {template.name} (ID: {template.id})")
                    )
                    created_count += 1
                    
                else:
                    self.stdout.write(
                        self.style.ERROR(f"❌ Erreur lors de la génération : {template_config['name']}")
                    )
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ Erreur pour {template_config['name']}: {str(e)}")
                )
        
        self.stdout.write(
            self.style.SUCCESS(f"\n🎉 {created_count} templates saisonniers créés avec succès !")
        )
        
        # Afficher la liste des nouveaux templates
        self.stdout.write("\n📋 Nouveaux templates créés :")
        templates = EmailTemplate.objects.all().order_by('-created_at')
        for template in templates:
            self.stdout.write(f"   - {template.name} (ID: {template.id}) - {template.created_at.strftime('%d/%m/%Y %H:%M')}")
        
        self.stdout.write(
            self.style.SUCCESS(f"\n🌟 Tous les templates saisonniers sont prêts à être utilisés !")
        )
