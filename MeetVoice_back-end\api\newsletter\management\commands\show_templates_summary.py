from django.core.management.base import BaseCommand
from newsletter.models import EmailTemplate

class Command(BaseCommand):
    help = 'Afficher un résumé des templates créés'

    def handle(self, *args, **options):
        """Afficher un résumé détaillé des templates saisonniers"""
        
        templates = EmailTemplate.objects.all().order_by('created_at')
        
        if not templates:
            self.stdout.write(
                self.style.ERROR("❌ Aucun template trouvé")
            )
            return
        
        self.stdout.write("=" * 80)
        self.stdout.write(
            self.style.SUCCESS("🎨 RÉSUMÉ DES TEMPLATES SAISONNIERS MEETVOICE")
        )
        self.stdout.write("=" * 80)
        
        for i, template in enumerate(templates, 1):
            # Déterminer l'emoji selon le nom
            if 'nouvel an' in template.name.lower():
                emoji = "🎊"
                theme = "NOUVEL AN"
            elif 'valentin' in template.name.lower():
                emoji = "💕"
                theme = "SAINT-VALENTIN"
            elif 'halloween' in template.name.lower():
                emoji = "🎃"
                theme = "HALLOWEEN"
            else:
                emoji = "📧"
                theme = "GÉNÉRAL"
            
            self.stdout.write(f"\n{emoji} TEMPLATE {i}: {theme}")
            self.stdout.write("-" * 50)
            self.stdout.write(f"📛 Nom: {template.name}")
            self.stdout.write(f"🆔 ID: {template.id}")
            self.stdout.write(f"📅 Créé: {template.created_at.strftime('%d/%m/%Y à %H:%M')}")
            self.stdout.write(f"👤 Créé par: {template.created_by.username}")
            self.stdout.write(f"📝 Description: {template.preview_text}")
            self.stdout.write(f"✅ Statut: {'Actif' if template.is_active else 'Inactif'}")
            
            # Analyser le contenu
            content_length = len(template.content_html)
            has_images = 'img' in template.content_html.lower()
            has_buttons = 'button' in template.content_html.lower() or 'btn' in template.content_html.lower()
            
            self.stdout.write(f"📊 Taille du contenu: {content_length:,} caractères")
            self.stdout.write(f"🖼️  Images: {'Oui' if has_images else 'Non'}")
            self.stdout.write(f"🔘 Boutons: {'Oui' if has_buttons else 'Non'}")
            
            # URL d'aperçu
            preview_url = f"http://127.0.0.1:8000/newsletter/templates/{template.id}/preview/"
            self.stdout.write(f"🌐 Aperçu: {preview_url}")
        
        self.stdout.write("\n" + "=" * 80)
        self.stdout.write(
            self.style.SUCCESS(f"📈 STATISTIQUES GLOBALES")
        )
        self.stdout.write("=" * 80)
        
        total_templates = templates.count()
        active_templates = templates.filter(is_active=True).count()
        total_content_size = sum(len(t.content_html) for t in templates)
        
        self.stdout.write(f"📊 Total des templates: {total_templates}")
        self.stdout.write(f"✅ Templates actifs: {active_templates}")
        self.stdout.write(f"📝 Taille totale du contenu: {total_content_size:,} caractères")
        self.stdout.write(f"📧 Moyenne par template: {total_content_size // total_templates:,} caractères")
        
        # Recommandations
        self.stdout.write("\n" + "=" * 80)
        self.stdout.write(
            self.style.WARNING("💡 RECOMMANDATIONS D'UTILISATION")
        )
        self.stdout.write("=" * 80)
        
        self.stdout.write("🎊 NOUVEL AN: Parfait pour janvier, nouvelles résolutions, nouveaux départs")
        self.stdout.write("💕 SAINT-VALENTIN: Idéal pour février, promotions romantiques, couples")
        self.stdout.write("🎃 HALLOWEEN: Excellent pour octobre, événements mystérieux, fun")
        
        self.stdout.write("\n📧 Pour utiliser ces templates:")
        self.stdout.write("   1. Allez sur http://127.0.0.1:8000/newsletter/campaigns/create/")
        self.stdout.write("   2. Sélectionnez un template saisonnier")
        self.stdout.write("   3. Personnalisez le contenu si nécessaire")
        self.stdout.write("   4. Envoyez à votre audience cible")
        
        self.stdout.write("\n" + "=" * 80)
        self.stdout.write(
            self.style.SUCCESS("🌟 TEMPLATES SAISONNIERS PRÊTS À UTILISER ! 🌟")
        )
        self.stdout.write("=" * 80)
