# Generated by Django 5.2.3

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='Nom du template')),
                ('subject_template', models.CharField(max_length=300, verbose_name='Modèle de sujet')),
                ('header_html', models.TextField(help_text='HTML du header (logo, navigation)', verbose_name='Header HTML')),
                ('content_html', models.TextField(help_text='Contenu principal (sera remplacé par l\'IA)', verbose_name='Contenu HTML')),
                ('footer_html', models.TextField(help_text='HTML du footer (liens, désinscription)', verbose_name='Footer HTML')),
                ('css_styles', models.TextField(help_text='CSS inline pour compatibilité email', verbose_name='Styles CSS')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True, verbose_name='Actif')),
                ('preview_text', models.CharField(blank=True, max_length=150, verbose_name='Texte de prévisualisation')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Créé par')),
            ],
            options={
                'verbose_name': "Template d'email",
                'verbose_name_plural': "Templates d'email",
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='Campaign',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='Nom de la campagne')),
                ('subject', models.CharField(max_length=300, verbose_name='Sujet de l\'email')),
                ('ai_prompt', models.TextField(help_text='Description du contenu à générer', verbose_name='Prompt pour l\'IA')),
                ('generated_content', models.TextField(blank=True, verbose_name='Contenu généré par l\'IA')),
                ('final_html', models.TextField(blank=True, verbose_name='HTML final de l\'email')),
                ('audience_type', models.CharField(choices=[('all', 'Tous les utilisateurs'), ('active', 'Utilisateurs actifs'), ('premium', 'Utilisateurs premium'), ('custom', 'Sélection personnalisée')], default='all', max_length=20, verbose_name='Type d\'audience')),
                ('recipient_count', models.IntegerField(default=0, verbose_name='Nombre de destinataires')),
                ('status', models.CharField(choices=[('draft', 'Brouillon'), ('scheduled', 'Programmée'), ('sending', 'En cours d\'envoi'), ('sent', 'Envoyée'), ('failed', 'Échec')], default='draft', max_length=20, verbose_name='Statut')),
                ('scheduled_at', models.DateTimeField(blank=True, null=True, verbose_name='Programmé pour')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='Envoyé le')),
                ('tracking_id', models.UUIDField(default=uuid.uuid4, unique=True, verbose_name='ID de tracking')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Créé par')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='newsletter.emailtemplate', verbose_name='Template')),
            ],
            options={
                'verbose_name': 'Campagne',
                'verbose_name_plural': 'Campagnes',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='EmailTracking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_hash', models.CharField(max_length=64, unique=True, verbose_name='Hash de tracking')),
                ('sent_at', models.DateTimeField(auto_now_add=True, verbose_name='Envoyé le')),
                ('delivered', models.BooleanField(default=False, verbose_name='Délivré')),
                ('bounced', models.BooleanField(default=False, verbose_name='Rejeté')),
                ('email_provider', models.CharField(blank=True, max_length=100, verbose_name='Fournisseur email')),
                ('user_agent', models.TextField(blank=True, verbose_name='User Agent')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='Adresse IP')),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='newsletter.campaign', verbose_name='Campagne')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Destinataire')),
            ],
            options={
                'verbose_name': "Tracking d'email",
                'verbose_name_plural': "Tracking d'emails",
            },
        ),
        migrations.CreateModel(
            name='EmailOpen',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('opened_at', models.DateTimeField(auto_now_add=True, verbose_name='Ouvert le')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='Adresse IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='User Agent')),
                ('country', models.CharField(blank=True, max_length=100, verbose_name='Pays')),
                ('city', models.CharField(blank=True, max_length=100, verbose_name='Ville')),
                ('email_client', models.CharField(blank=True, max_length=100, verbose_name='Client email')),
                ('device_type', models.CharField(blank=True, max_length=50, verbose_name='Type d\'appareil')),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='newsletter.campaign', verbose_name='Campagne')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Destinataire')),
                ('tracking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='newsletter.emailtracking', verbose_name='Tracking')),
            ],
            options={
                'verbose_name': "Ouverture d'email",
                'verbose_name_plural': "Ouvertures d'email",
                'ordering': ['-opened_at'],
            },
        ),
        migrations.CreateModel(
            name='EmailClick',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('clicked_at', models.DateTimeField(auto_now_add=True, verbose_name='Cliqué le')),
                ('url', models.URLField(verbose_name='URL cliquée')),
                ('link_text', models.CharField(blank=True, max_length=200, verbose_name='Texte du lien')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='Adresse IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='User Agent')),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='newsletter.campaign', verbose_name='Campagne')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Destinataire')),
                ('tracking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='newsletter.emailtracking', verbose_name='Tracking')),
            ],
            options={
                'verbose_name': "Clic d'email",
                'verbose_name_plural': "Clics d'email",
                'ordering': ['-clicked_at'],
            },
        ),
        migrations.CreateModel(
            name='NewsletterSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('smtp_host', models.CharField(default='smtp.gmail.com', max_length=200, verbose_name='Serveur SMTP')),
                ('smtp_port', models.IntegerField(default=587, verbose_name='Port SMTP')),
                ('smtp_username', models.CharField(max_length=200, verbose_name='Nom d\'utilisateur SMTP')),
                ('smtp_password', models.CharField(max_length=200, verbose_name='Mot de passe SMTP')),
                ('smtp_use_tls', models.BooleanField(default=True, verbose_name='Utiliser TLS')),
                ('from_email', models.EmailField(max_length=254, verbose_name='Email expéditeur')),
                ('from_name', models.CharField(max_length=100, verbose_name='Nom expéditeur')),
                ('reply_to', models.EmailField(blank=True, max_length=254, verbose_name='Email de réponse')),
                ('ai_api_key', models.CharField(blank=True, max_length=500, verbose_name='Clé API IA')),
                ('ai_model', models.CharField(default='gemini-pro', max_length=100, verbose_name='Modèle IA')),
                ('max_emails_per_hour', models.IntegerField(default=100, verbose_name='Max emails par heure')),
                ('max_emails_per_day', models.IntegerField(default=1000, verbose_name='Max emails par jour')),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Modifié par')),
            ],
            options={
                'verbose_name': 'Paramètres Newsletter',
                'verbose_name_plural': 'Paramètres Newsletter',
            },
        ),
        migrations.AlterUniqueTogether(
            name='emailtracking',
            unique_together={('campaign', 'recipient')},
        ),
    ]
