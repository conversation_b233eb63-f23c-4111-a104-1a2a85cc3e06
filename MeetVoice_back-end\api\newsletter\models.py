from django.db import models
from django.conf import settings
from django.utils import timezone
from django.urls import reverse
import uuid
import hashlib


class EmailTemplate(models.Model):
    """Template d'email réutilisable avec header et footer"""
    
    name = models.CharField(max_length=200, verbose_name="Nom du template")
    subject_template = models.CharField(max_length=300, verbose_name="Modèle de sujet")
    header_html = models.TextField(verbose_name="Header HTML", help_text="HTML du header (logo, navigation)")
    content_html = models.TextField(verbose_name="Contenu HTML", help_text="Contenu principal (sera remplacé par l'IA)")
    footer_html = models.TextField(verbose_name="Footer HTML", help_text="HTML du footer (liens, désinscription)")
    
    # Styles CSS intégrés pour compatibilité email
    css_styles = models.TextField(verbose_name="Styles CSS", help_text="CSS inline pour compatibilité email")
    
    # Métadonnées
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name="Créé par")
    is_active = models.BooleanField(default=True, verbose_name="Actif")
    
    # Prévisualisation
    preview_text = models.CharField(max_length=150, blank=True, verbose_name="Texte de prévisualisation")
    
    class Meta:
        verbose_name = "Template d'email"
        verbose_name_plural = "Templates d'email"
        ordering = ['-updated_at']
    
    def __str__(self):
        return self.name
    
    def get_full_html(self, content=""):
        """Génère le HTML complet avec header, contenu et footer"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{self.subject_template}</title>
            <style>{self.css_styles}</style>
        </head>
        <body>
            {self.header_html}
            <div class="content">
                {content or self.content_html}
            </div>
            {self.footer_html}
        </body>
        </html>
        """


class Campaign(models.Model):
    """Campagne de newsletter"""
    
    STATUS_CHOICES = [
        ('draft', 'Brouillon'),
        ('scheduled', 'Programmée'),
        ('sending', 'En cours d\'envoi'),
        ('sent', 'Envoyée'),
        ('failed', 'Échec'),
    ]
    
    AUDIENCE_CHOICES = [
        ('all', 'Tous les utilisateurs'),
        ('active', 'Utilisateurs actifs'),
        ('premium', 'Utilisateurs premium'),
        ('custom', 'Sélection personnalisée'),
    ]
    
    # Informations de base
    name = models.CharField(max_length=200, verbose_name="Nom de la campagne")
    subject = models.CharField(max_length=300, verbose_name="Sujet de l'email")
    template = models.ForeignKey(EmailTemplate, on_delete=models.CASCADE, verbose_name="Template")
    
    # Contenu généré par IA
    ai_prompt = models.TextField(verbose_name="Prompt pour l'IA", help_text="Description du contenu à générer")
    generated_content = models.TextField(blank=True, verbose_name="Contenu généré par l'IA")
    final_html = models.TextField(blank=True, verbose_name="HTML final de l'email")
    
    # Audience et envoi
    audience_type = models.CharField(max_length=20, choices=AUDIENCE_CHOICES, default='all', verbose_name="Type d'audience")
    recipient_count = models.IntegerField(default=0, verbose_name="Nombre de destinataires")
    
    # Planification
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="Statut")
    scheduled_at = models.DateTimeField(null=True, blank=True, verbose_name="Programmé pour")
    sent_at = models.DateTimeField(null=True, blank=True, verbose_name="Envoyé le")
    
    # Tracking
    tracking_id = models.UUIDField(default=uuid.uuid4, unique=True, verbose_name="ID de tracking")
    
    # Métadonnées
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name="Créé par")
    
    class Meta:
        verbose_name = "Campagne"
        verbose_name_plural = "Campagnes"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} - {self.get_status_display()}"
    
    def get_absolute_url(self):
        return reverse('newsletter:campaign_detail', kwargs={'pk': self.pk})
    
    def generate_final_html(self):
        """Génère le HTML final avec le contenu IA et les pixels de tracking"""
        if not self.generated_content:
            return ""
        
        # Pixel de tracking invisible
        tracking_pixel = f'<img src="{{{{ tracking_url }}}}" width="1" height="1" style="display:none;" alt="">'
        
        # Contenu avec tracking
        content_with_tracking = f"{self.generated_content}\n{tracking_pixel}"
        
        # HTML final avec template
        self.final_html = self.template.get_full_html(content_with_tracking)
        return self.final_html
    
    @property
    def open_rate(self):
        """Calcule le taux d'ouverture"""
        if self.recipient_count == 0:
            return 0
        opens = EmailOpen.objects.filter(campaign=self).count()
        return round((opens / self.recipient_count) * 100, 2)
    
    @property
    def click_rate(self):
        """Calcule le taux de clic"""
        if self.recipient_count == 0:
            return 0
        clicks = EmailClick.objects.filter(campaign=self).count()
        return round((clicks / self.recipient_count) * 100, 2)


class EmailTracking(models.Model):
    """Tracking individuel d'un email envoyé"""
    
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, verbose_name="Campagne")
    recipient = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name="Destinataire")
    
    # Identifiants de tracking
    tracking_hash = models.CharField(max_length=64, unique=True, verbose_name="Hash de tracking")
    
    # Statuts
    sent_at = models.DateTimeField(auto_now_add=True, verbose_name="Envoyé le")
    delivered = models.BooleanField(default=False, verbose_name="Délivré")
    bounced = models.BooleanField(default=False, verbose_name="Rejeté")
    
    # Métadonnées d'envoi
    email_provider = models.CharField(max_length=100, blank=True, verbose_name="Fournisseur email")
    user_agent = models.TextField(blank=True, verbose_name="User Agent")
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name="Adresse IP")
    
    class Meta:
        verbose_name = "Tracking d'email"
        verbose_name_plural = "Tracking d'emails"
        unique_together = ['campaign', 'recipient']
    
    def __str__(self):
        return f"{self.campaign.name} - {self.recipient.email}"
    
    def save(self, *args, **kwargs):
        if not self.tracking_hash:
            # Générer un hash unique pour le tracking
            data = f"{self.campaign.tracking_id}{self.recipient.id}{timezone.now()}"
            self.tracking_hash = hashlib.sha256(data.encode()).hexdigest()
        super().save(*args, **kwargs)
    
    def get_tracking_url(self):
        """URL du pixel de tracking"""
        return reverse('newsletter:track_open', kwargs={'hash': self.tracking_hash})


class EmailOpen(models.Model):
    """Tracking des ouvertures d'email"""
    
    tracking = models.ForeignKey(EmailTracking, on_delete=models.CASCADE, verbose_name="Tracking")
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, verbose_name="Campagne")
    recipient = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name="Destinataire")
    
    # Détails de l'ouverture
    opened_at = models.DateTimeField(auto_now_add=True, verbose_name="Ouvert le")
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name="Adresse IP")
    user_agent = models.TextField(blank=True, verbose_name="User Agent")
    
    # Géolocalisation (optionnel)
    country = models.CharField(max_length=100, blank=True, verbose_name="Pays")
    city = models.CharField(max_length=100, blank=True, verbose_name="Ville")
    
    # Client email détecté
    email_client = models.CharField(max_length=100, blank=True, verbose_name="Client email")
    device_type = models.CharField(max_length=50, blank=True, verbose_name="Type d'appareil")
    
    class Meta:
        verbose_name = "Ouverture d'email"
        verbose_name_plural = "Ouvertures d'email"
        ordering = ['-opened_at']
    
    def __str__(self):
        return f"{self.campaign.name} - {self.recipient.email} - {self.opened_at}"


class EmailClick(models.Model):
    """Tracking des clics dans les emails"""
    
    tracking = models.ForeignKey(EmailTracking, on_delete=models.CASCADE, verbose_name="Tracking")
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, verbose_name="Campagne")
    recipient = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name="Destinataire")
    
    # Détails du clic
    clicked_at = models.DateTimeField(auto_now_add=True, verbose_name="Cliqué le")
    url = models.URLField(verbose_name="URL cliquée")
    link_text = models.CharField(max_length=200, blank=True, verbose_name="Texte du lien")
    
    # Métadonnées
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name="Adresse IP")
    user_agent = models.TextField(blank=True, verbose_name="User Agent")
    
    class Meta:
        verbose_name = "Clic d'email"
        verbose_name_plural = "Clics d'email"
        ordering = ['-clicked_at']
    
    def __str__(self):
        return f"{self.campaign.name} - {self.recipient.email} - {self.url}"


class NewsletterSettings(models.Model):
    """Paramètres globaux de la newsletter"""
    
    # Configuration SMTP
    smtp_host = models.CharField(max_length=200, default='smtp.gmail.com', verbose_name="Serveur SMTP")
    smtp_port = models.IntegerField(default=587, verbose_name="Port SMTP")
    smtp_username = models.CharField(max_length=200, verbose_name="Nom d'utilisateur SMTP")
    smtp_password = models.CharField(max_length=200, verbose_name="Mot de passe SMTP")
    smtp_use_tls = models.BooleanField(default=True, verbose_name="Utiliser TLS")
    
    # Configuration expéditeur
    from_email = models.EmailField(verbose_name="Email expéditeur")
    from_name = models.CharField(max_length=100, verbose_name="Nom expéditeur")
    reply_to = models.EmailField(blank=True, verbose_name="Email de réponse")
    
    # Configuration IA
    ai_api_key = models.CharField(max_length=500, blank=True, verbose_name="Clé API IA")
    ai_model = models.CharField(max_length=100, default='gemini-pro', verbose_name="Modèle IA")
    
    # Limites d'envoi
    max_emails_per_hour = models.IntegerField(default=100, verbose_name="Max emails par heure")
    max_emails_per_day = models.IntegerField(default=1000, verbose_name="Max emails par jour")
    
    # Métadonnées
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name="Modifié par", null=True, blank=True)
    
    class Meta:
        verbose_name = "Paramètres Newsletter"
        verbose_name_plural = "Paramètres Newsletter"
    
    def __str__(self):
        return f"Paramètres Newsletter - {self.from_email}"
    
    @classmethod
    def get_settings(cls):
        """Récupère les paramètres (singleton)"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings
