from rest_framework import serializers
from .models import EmailTemplate, Campaign, NewsletterSettings


class EmailTemplateSerializer(serializers.ModelSerializer):
    """Serializer pour les templates d'email"""
    
    class Meta:
        model = EmailTemplate
        fields = [
            'id',
            'name',
            'subject_template',
            'header_html',
            'content_html',
            'footer_html',
            'css_styles',
            'is_active',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_name(self, value):
        """Validation du nom du template"""
        if len(value.strip()) < 3:
            raise serializers.ValidationError("Le nom du template doit contenir au moins 3 caractères.")
        return value.strip()


class EmailTemplateListSerializer(serializers.ModelSerializer):
    """Serializer simplifié pour la liste des templates"""
    
    class Meta:
        model = EmailTemplate
        fields = [
            'id',
            'name',
            'subject_template',
            'is_active',
            'created_at',
            'updated_at'
        ]


class CampaignSerializer(serializers.ModelSerializer):
    """Serializer pour les campagnes newsletter"""
    
    template_name = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    audience_display = serializers.SerializerMethodField()
    
    class Meta:
        model = Campaign
        fields = [
            'id',
            'name',
            'subject',
            'template',
            'template_name',
            'ai_prompt',
            'generated_content',
            'final_html',
            'audience_type',
            'audience_display',
            'recipient_count',
            'status',
            'status_display',
            'scheduled_at',
            'sent_at',
            'tracking_id',
            'opens_count',
            'clicks_count',
            'unsubscribes_count',
            'bounces_count',
            'created_at',
            'updated_at'
        ]
        read_only_fields = [
            'id', 'tracking_id', 'opens_count', 'clicks_count', 
            'unsubscribes_count', 'bounces_count', 'created_at', 'updated_at'
        ]
    
    def get_template_name(self, obj):
        """Retourne le nom du template"""
        return obj.template.name if obj.template else None
    
    def get_status_display(self, obj):
        """Retourne le statut en français"""
        return obj.get_status_display()
    
    def get_audience_display(self, obj):
        """Retourne le type d'audience en français"""
        return obj.get_audience_type_display()


class CampaignListSerializer(serializers.ModelSerializer):
    """Serializer simplifié pour la liste des campagnes"""
    
    template_name = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    open_rate = serializers.SerializerMethodField()
    click_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = Campaign
        fields = [
            'id',
            'name',
            'subject',
            'template_name',
            'recipient_count',
            'status',
            'status_display',
            'scheduled_at',
            'sent_at',
            'opens_count',
            'clicks_count',
            'open_rate',
            'click_rate',
            'created_at'
        ]
    
    def get_template_name(self, obj):
        """Retourne le nom du template"""
        return obj.template.name if obj.template else None
    
    def get_status_display(self, obj):
        """Retourne le statut en français"""
        return obj.get_status_display()
    
    def get_open_rate(self, obj):
        """Calcule le taux d'ouverture"""
        if obj.recipient_count > 0:
            return round((obj.opens_count / obj.recipient_count) * 100, 2)
        return 0
    
    def get_click_rate(self, obj):
        """Calcule le taux de clic"""
        if obj.recipient_count > 0:
            return round((obj.clicks_count / obj.recipient_count) * 100, 2)
        return 0


class CampaignCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création de campagnes"""
    
    class Meta:
        model = Campaign
        fields = [
            'name',
            'subject',
            'template',
            'ai_prompt',
            'audience_type',
            'scheduled_at'
        ]
    
    def validate_name(self, value):
        """Validation du nom de la campagne"""
        if len(value.strip()) < 3:
            raise serializers.ValidationError("Le nom de la campagne doit contenir au moins 3 caractères.")
        return value.strip()
    
    def validate_subject(self, value):
        """Validation du sujet"""
        if len(value.strip()) < 5:
            raise serializers.ValidationError("Le sujet doit contenir au moins 5 caractères.")
        return value.strip()
    
    def validate_ai_prompt(self, value):
        """Validation du prompt IA"""
        if len(value.strip()) < 10:
            raise serializers.ValidationError("Le prompt IA doit contenir au moins 10 caractères.")
        return value.strip()


class NewsletterSettingsSerializer(serializers.ModelSerializer):
    """Serializer pour les paramètres de newsletter"""
    
    class Meta:
        model = NewsletterSettings
        fields = [
            'id',
            'smtp_host',
            'smtp_port',
            'smtp_username',
            'smtp_password',
            'smtp_use_tls',
            'smtp_use_ssl',
            'from_email',
            'from_name',
            'reply_to',
            'ai_api_key',
            'ai_model',
            'max_emails_per_hour',
            'max_emails_per_day',
            'is_active',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
        extra_kwargs = {
            'smtp_password': {'write_only': True},
            'ai_api_key': {'write_only': True}
        }
    
    def validate_smtp_port(self, value):
        """Validation du port SMTP"""
        if not (1 <= value <= 65535):
            raise serializers.ValidationError("Le port SMTP doit être entre 1 et 65535.")
        return value
    
    def validate_max_emails_per_hour(self, value):
        """Validation du nombre max d'emails par heure"""
        if value <= 0:
            raise serializers.ValidationError("Le nombre max d'emails par heure doit être supérieur à 0.")
        return value
    
    def validate_max_emails_per_day(self, value):
        """Validation du nombre max d'emails par jour"""
        if value <= 0:
            raise serializers.ValidationError("Le nombre max d'emails par jour doit être supérieur à 0.")
        return value


class CampaignStatsSerializer(serializers.ModelSerializer):
    """Serializer pour les statistiques des campagnes"""
    
    open_rate = serializers.SerializerMethodField()
    click_rate = serializers.SerializerMethodField()
    unsubscribe_rate = serializers.SerializerMethodField()
    bounce_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = Campaign
        fields = [
            'id',
            'name',
            'recipient_count',
            'opens_count',
            'clicks_count',
            'unsubscribes_count',
            'bounces_count',
            'open_rate',
            'click_rate',
            'unsubscribe_rate',
            'bounce_rate',
            'sent_at'
        ]
    
    def get_open_rate(self, obj):
        """Calcule le taux d'ouverture"""
        if obj.recipient_count > 0:
            return round((obj.opens_count / obj.recipient_count) * 100, 2)
        return 0
    
    def get_click_rate(self, obj):
        """Calcule le taux de clic"""
        if obj.recipient_count > 0:
            return round((obj.clicks_count / obj.recipient_count) * 100, 2)
        return 0
    
    def get_unsubscribe_rate(self, obj):
        """Calcule le taux de désabonnement"""
        if obj.recipient_count > 0:
            return round((obj.unsubscribes_count / obj.recipient_count) * 100, 2)
        return 0
    
    def get_bounce_rate(self, obj):
        """Calcule le taux de rebond"""
        if obj.recipient_count > 0:
            return round((obj.bounces_count / obj.recipient_count) * 100, 2)
        return 0
