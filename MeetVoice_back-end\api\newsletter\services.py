
# 🚫 RÈGLE ABSOLUE: AUCUN OVERLAY CSS
# - Pas de position: absolute/relative
# - Pas de rgba() avec opacité
# - Pas de z-index
# - Utiliser des backgrounds solides uniquement
# - Compatible Gmail, Outlook, Apple Mail
import requests
import json
import logging
from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.contrib.auth.models import User
from django.utils import timezone
from django.db import models
from .models import Campaign, EmailTemplate, EmailTracking, NewsletterSettings
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.image import MIMEImage
import base64
import hashlib

logger = logging.getLogger(__name__)


class NewsletterAIService:
    """Service pour la génération de contenu newsletter avec IA"""
    
    def __init__(self):
        self.api_key = getattr(settings, 'GOOGLE_GEMINI_API_KEY', '')
        self.api_url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent'
    
    def generate_newsletter_content(self, prompt, theme="newsletter", target_audience="utilisateurs"):
        """
        Génère le contenu d'une newsletter avec Gemini IA
        
        Args:
            prompt (str): Description du contenu souhaité
            theme (str): Thème de la newsletter
            target_audience (str): Audience cible
            
        Returns:
            dict: Contenu généré avec titre, contenu HTML, et métadonnées
        """
        try:
            # Prompt optimisé pour newsletter
            enhanced_prompt = f"""
            Génère le contenu d'une newsletter professionnelle pour MeetVoice, une plateforme de rencontres vocales.

            CONTEXTE:
            - Plateforme: MeetVoice (rencontres par appels vocaux)
            - Audience: {target_audience}
            - Thème: {theme}
            - Demande spécifique: {prompt}

            INSTRUCTIONS:
            1. Crée un contenu engageant et professionnel
            2. Utilise un ton chaleureux mais professionnel
            3. Inclus des appels à l'action pertinents
            4. Structure le contenu avec des titres et sous-titres
            5. Optimise pour l'email (évite les éléments complexes)
            6. Longueur: 300-500 mots maximum

            FORMAT DE RÉPONSE:
            Retourne UNIQUEMENT un JSON avec cette structure exacte:
            {{
                "subject": "Sujet accrocheur de l'email",
                "content": "Contenu HTML de la newsletter avec balises <h2>, <p>, <ul>, <li>, <a>, <strong>",
                "preview_text": "Texte de prévisualisation (150 caractères max)",
                "call_to_action": "Texte du bouton d'action principal",
                "cta_url": "URL du bouton d'action (relatif comme /app/download)"
            }}

            IMPORTANT: Réponds UNIQUEMENT avec le JSON, sans texte avant ou après.
            """
            
            headers = {
                'Content-Type': 'application/json',
            }
            
            data = {
                "contents": [{
                    "parts": [{
                        "text": enhanced_prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 1024,
                }
            }
            
            # Appel à l'API Gemini
            response = requests.post(
                f"{self.api_url}?key={self.api_key}",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if 'candidates' in result and len(result['candidates']) > 0:
                    generated_text = result['candidates'][0]['parts'][0]['text']
                    
                    # Nettoyer et parser le JSON
                    try:
                        # Supprimer les balises markdown si présentes
                        if '```json' in generated_text:
                            generated_text = generated_text.split('```json')[1].split('```')[0]
                        elif '```' in generated_text:
                            generated_text = generated_text.split('```')[1].split('```')[0]
                        
                        # Parser le JSON
                        content_data = json.loads(generated_text.strip())
                        
                        # Valider la structure
                        required_fields = ['subject', 'content', 'preview_text']
                        if all(field in content_data for field in required_fields):
                            return {
                                'success': True,
                                'data': content_data
                            }
                        else:
                            logger.error(f"Structure JSON invalide: {content_data}")
                            return self._get_fallback_content(prompt)
                            
                    except json.JSONDecodeError as e:
                        logger.error(f"Erreur parsing JSON: {e}, Texte: {generated_text}")
                        return self._get_fallback_content(prompt)
                else:
                    logger.error("Aucun candidat dans la réponse Gemini")
                    return self._get_fallback_content(prompt)
            else:
                logger.error(f"Erreur API Gemini: {response.status_code} - {response.text}")
                return self._get_fallback_content(prompt)
                
        except Exception as e:
            logger.error(f"Erreur génération IA: {e}")
            return self._get_fallback_content(prompt)
    
    def _get_fallback_content(self, prompt):
        """Contenu de fallback en cas d'erreur IA"""
        return {
            'success': True,
            'data': {
                'subject': f'Newsletter MeetVoice - {prompt[:50]}...',
                'content': f'''
                <h2>Bonjour !</h2>
                <p>Nous espérons que vous allez bien et que vous profitez pleinement de votre expérience sur MeetVoice.</p>
                
                <h3>À propos de: {prompt}</h3>
                <p>Notre équipe travaille constamment pour améliorer votre expérience de rencontres vocales. 
                Découvrez les dernières nouveautés et fonctionnalités qui vous attendent.</p>
                
                <ul>
                    <li>Nouvelles fonctionnalités de matching vocal</li>
                    <li>Amélioration de la qualité audio</li>
                    <li>Interface utilisateur optimisée</li>
                </ul>
                
                <p><strong>Rejoignez-nous dès maintenant pour découvrir ces nouveautés !</strong></p>
                ''',
                'preview_text': f'Découvrez les dernières nouveautés MeetVoice concernant {prompt[:100]}...',
                'call_to_action': 'Découvrir',
                'cta_url': '/app/'
            }
        }


class NewsletterEmailService:
    """Service pour l'envoi d'emails de newsletter"""
    
    def __init__(self):
        self.settings = NewsletterSettings.get_settings()
    
    def send_campaign(self, campaign):
        """
        Envoie une campagne à tous les destinataires
        
        Args:
            campaign (Campaign): Campagne à envoyer
            
        Returns:
            dict: Résultats de l'envoi
        """
        try:
            # Récupérer les destinataires selon le type d'audience
            recipients = self._get_recipients(campaign.audience_type)
            
            if not recipients:
                return {
                    'success': False,
                    'error': 'Aucun destinataire trouvé'
                }
            
            # Mettre à jour le nombre de destinataires
            campaign.recipient_count = len(recipients)
            campaign.status = 'sending'
            campaign.save()
            
            sent_count = 0
            failed_count = 0
            
            # Envoyer à chaque destinataire
            for recipient in recipients:
                try:
                    success = self._send_individual_email(campaign, recipient)
                    if success:
                        sent_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    logger.error(f"Erreur envoi email à {recipient.email}: {e}")
                    failed_count += 1
            
            # Mettre à jour le statut de la campagne
            if sent_count > 0:
                campaign.status = 'sent'
                campaign.sent_at = timezone.now()
            else:
                campaign.status = 'failed'
            
            campaign.save()
            
            return {
                'success': True,
                'sent_count': sent_count,
                'failed_count': failed_count,
                'total_recipients': len(recipients)
            }
            
        except Exception as e:
            logger.error(f"Erreur envoi campagne {campaign.id}: {e}")
            campaign.status = 'failed'
            campaign.save()
            
            return {
                'success': False,
                'error': str(e)
            }
    
    def _get_recipients(self, audience_type):
        """Récupère la liste des destinataires selon le type d'audience"""
        if audience_type == 'all':
            return User.objects.filter(is_active=True).exclude(email='')
        elif audience_type == 'active':
            # Utilisateurs actifs (connectés dans les 30 derniers jours)
            from datetime import timedelta
            cutoff_date = timezone.now() - timedelta(days=30)
            return User.objects.filter(
                is_active=True,
                last_login__gte=cutoff_date
            ).exclude(email='')
        elif audience_type == 'premium':
            # Utilisateurs avec abonnement premium
            return User.objects.filter(
                is_active=True,
                # Ajouter ici la logique pour les utilisateurs premium
            ).exclude(email='')
        else:
            return User.objects.none()
    
    def _send_individual_email(self, campaign, recipient):
        """
        Envoie un email à un destinataire individuel
        
        Args:
            campaign (Campaign): Campagne
            recipient (User): Destinataire
            
        Returns:
            bool: Succès de l'envoi
        """
        try:
            # Créer ou récupérer le tracking
            tracking, created = EmailTracking.objects.get_or_create(
                campaign=campaign,
                recipient=recipient
            )
            
            # Générer l'URL de tracking
            tracking_url = f"https://votre-domaine.com{tracking.get_tracking_url()}"
            
            # Remplacer les variables dans le HTML
            html_content = campaign.final_html.replace('{{ tracking_url }}', tracking_url)
            html_content = html_content.replace('{{ recipient_name }}', recipient.first_name or recipient.username)
            html_content = html_content.replace('{{ unsubscribe_url }}', f"https://votre-domaine.com/newsletter/unsubscribe/{tracking.tracking_hash}/")
            
            # Créer l'email
            msg = EmailMultiAlternatives(
                subject=campaign.subject,
                body=f"Version texte de: {campaign.subject}",
                from_email=f"{self.settings.from_name} <{self.settings.from_email}>",
                to=[recipient.email],
                reply_to=[self.settings.reply_to] if self.settings.reply_to else None
            )
            
            # Ajouter le contenu HTML
            msg.attach_alternative(html_content, "text/html")
            
            # Envoyer l'email
            msg.send()
            
            # Marquer comme envoyé
            tracking.sent_at = timezone.now()
            tracking.delivered = True
            tracking.save()
            
            return True
            
        except Exception as e:
            logger.error(f"Erreur envoi email individuel: {e}")
            
            # Marquer comme échoué
            if 'tracking' in locals():
                tracking.bounced = True
                tracking.save()
            
            return False
    
    def get_email_provider(self, email):
        """Détecte le fournisseur d'email"""
        domain = email.split('@')[1].lower()
        
        providers = {
            'gmail.com': 'Gmail',
            'googlemail.com': 'Gmail',
            'outlook.com': 'Outlook',
            'hotmail.com': 'Hotmail',
            'live.com': 'Outlook',
            'yahoo.com': 'Yahoo',
            'yahoo.fr': 'Yahoo',
            'orange.fr': 'Orange',
            'free.fr': 'Free',
            'sfr.fr': 'SFR',
        }
        
        return providers.get(domain, 'Autre')


class NewsletterStatsService:
    """Service pour les statistiques et KPI"""
    
    @staticmethod
    def get_campaign_stats(campaign):
        """Récupère les statistiques détaillées d'une campagne"""
        from .models import EmailOpen, EmailClick
        
        total_sent = campaign.recipient_count
        total_opens = EmailOpen.objects.filter(campaign=campaign).count()
        unique_opens = EmailOpen.objects.filter(campaign=campaign).values('recipient').distinct().count()
        total_clicks = EmailClick.objects.filter(campaign=campaign).count()
        unique_clicks = EmailClick.objects.filter(campaign=campaign).values('recipient').distinct().count()
        
        return {
            'total_sent': total_sent,
            'total_opens': total_opens,
            'unique_opens': unique_opens,
            'total_clicks': total_clicks,
            'unique_clicks': unique_clicks,
            'open_rate': round((unique_opens / total_sent * 100), 2) if total_sent > 0 else 0,
            'click_rate': round((unique_clicks / total_sent * 100), 2) if total_sent > 0 else 0,
            'click_to_open_rate': round((unique_clicks / unique_opens * 100), 2) if unique_opens > 0 else 0,
        }
    
    @staticmethod
    def get_overview_stats():
        """Récupère les statistiques générales"""
        from .models import EmailOpen, EmailClick
        
        total_campaigns = Campaign.objects.count()
        total_sent = Campaign.objects.filter(status='sent').aggregate(
            total=models.Sum('recipient_count')
        )['total'] or 0
        
        total_opens = EmailOpen.objects.count()
        total_clicks = EmailClick.objects.count()
        
        return {
            'total_campaigns': total_campaigns,
            'total_sent': total_sent,
            'total_opens': total_opens,
            'total_clicks': total_clicks,
            'avg_open_rate': round((total_opens / total_sent * 100), 2) if total_sent > 0 else 0,
            'avg_click_rate': round((total_clicks / total_sent * 100), 2) if total_sent > 0 else 0,
        }
