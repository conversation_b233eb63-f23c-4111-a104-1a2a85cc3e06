/* ===== NEWSLETTER STYLES ===== */

/* Layout principal */
.newsletter-wrapper {
    display: block;
    min-height: 100vh;
    position: relative;
}

.newsletter-sidebar {
    width: 280px;
    background: #2A1D34;
    border-right: 2px solid #000;
    padding: 30px 20px 20px 20px;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    box-sizing: border-box;
}

.newsletter-content {
    flex: 1;
    padding: 20px;
    margin-left: 280px;
    min-height: 100vh;
    width: calc(100% - 280px);
}

/* Sidebar */
.sidebar-header h5 {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #4e385f;
}

.sidebar-nav .nav-link {
    color: rgba(255, 255, 255, 0.644);
    padding: 10px 15px;
    border-radius: 8px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
    font-size: 18px;
    font-family: roboco;
}

.sidebar-nav .nav-link:hover {
    background-color: #4e385f;
    color: #ffffff;
}

.sidebar-nav .nav-link.active {
    background-color: #4e385f;
    color: #ffffff;
}

.sidebar-nav .nav-link i {
    width: 20px;
}

.sidebar-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #4e385f;
}

.sidebar-actions .btn-primary {
    background-color: #4e385f;
    border-color: #4e385f;
    color: #ffffff;
}

.sidebar-actions .btn-primary:hover {
    background-color: #5d4370;
    border-color: #5d4370;
}

.sidebar-actions .btn-outline-primary {
    color: rgba(255, 255, 255, 0.644);
    border-color: #4e385f;
}

.sidebar-actions .btn-outline-primary:hover {
    background-color: #4e385f;
    border-color: #4e385f;
    color: #ffffff;
}

/* Dashboard */
.dashboard-header h2 {
    color: #495057;
    font-weight: 600;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 20px;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stat-card .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5em;
    float: left;
    margin-right: 15px;
}

.stat-card .stat-content h3 {
    margin: 0;
    font-size: 2em;
    font-weight: 700;
    color: #495057;
}

.stat-card .stat-content p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9em;
}

/* Campagnes */
.campaign-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #e9ecef;
}

.campaign-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.campaign-status {
    font-size: 0.8em;
    padding: 4px 8px;
    border-radius: 12px;
}

.campaign-stats {
    font-size: 0.9em;
}

.campaign-actions .btn {
    margin-right: 5px;
    margin-bottom: 5px;
}

/* Templates */
.template-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #e9ecef;
}

.template-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.template-preview {
    height: 200px;
    overflow: hidden;
    background: #f8f9fa;
    border-radius: 8px;
    position: relative;
}

.template-preview iframe {
    width: 100%;
    height: 100%;
    border: none;
    transform: scale(0.3);
    transform-origin: top left;
}

/* Formulaires */
.newsletter-form .form-label {
    font-weight: 600;
    color: #495057;
}

.code-editor {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
}

.ai-generator {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
}

.ai-generator h5 {
    margin-bottom: 15px;
}

.ai-generator .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
}

.ai-generator .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Statistiques */
.stats-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    margin-bottom: 20px;
}

.metric-item {
    text-align: center;
    padding: 15px;
}

.metric-value {
    font-size: 2em;
    font-weight: 700;
    color: #495057;
    display: block;
}

.metric-label {
    color: #6c757d;
    font-size: 0.9em;
    margin-top: 5px;
}

.metric-change {
    font-size: 0.8em;
    margin-top: 5px;
}

.metric-change.positive {
    color: #28a745;
}

.metric-change.negative {
    color: #dc3545;
}

/* Tracking et analytics */
.tracking-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.tracking-pixel {
    font-family: monospace;
    background: #f1f3f4;
    padding: 10px;
    border-radius: 4px;
    font-size: 0.8em;
}

/* Email preview */
.email-preview {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.email-preview-header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
    font-size: 0.9em;
    color: #6c757d;
}

.email-preview-content {
    padding: 0;
}

.email-preview iframe {
    width: 100%;
    min-height: 500px;
    border: none;
}

/* Responsive */
@media (max-width: 992px) {
    .newsletter-sidebar {
        width: 100%;
        position: relative;
        height: auto;
        border-right: none;
        border-bottom: 2px solid #000;
        z-index: 1001;
    }
    
    .newsletter-content {
        margin-left: 0;
        width: 100%;
        padding: 15px;
    }
    
    .stat-card {
        margin-bottom: 15px;
    }
    
    .campaign-actions .btn {
        width: 100%;
        margin-bottom: 10px;
    }
}

@media (max-width: 768px) {
    .newsletter-content {
        padding: 10px;
    }
    
    .stat-card .stat-icon {
        float: none;
        margin: 0 auto 10px auto;
    }
    
    .stat-card .stat-content {
        text-align: center;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.campaign-card,
.template-card,
.stat-card,
.stats-card {
    animation: fadeIn 0.6s ease-out;
}

/* Scrollbar personnalisée */
.newsletter-sidebar::-webkit-scrollbar {
    width: 6px;
}

.newsletter-sidebar::-webkit-scrollbar-track {
    background: #1a1125;
    border-radius: 3px;
}

.newsletter-sidebar::-webkit-scrollbar-thumb {
    background: #4e385f;
    border-radius: 3px;
}

.newsletter-sidebar::-webkit-scrollbar-thumb:hover {
    background: #5d4370;
}

/* Badges et statuts */
.badge-draft { background-color: #6c757d !important; }
.badge-scheduled { background-color: #ffc107 !important; color: #212529 !important; }
.badge-sending { background-color: #17a2b8 !important; }
.badge-sent { background-color: #28a745 !important; }
.badge-failed { background-color: #dc3545 !important; }

/* Loading states */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success/Error states */
.alert-ai {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.alert-ai .btn-close {
    filter: invert(1);
}

/* Code editor enhancements */
.CodeMirror {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    font-size: 14px;
}

.CodeMirror-focused {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Template selector */
.template-selector .template-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.template-selector .template-option:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.template-selector .template-option.selected {
    border-color: #007bff;
    background-color: #e7f3ff;
}

/* AI content generation */
.ai-content-preview {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
}

.ai-content-preview h6 {
    color: #495057;
    margin-bottom: 10px;
}

.ai-content-preview .content-preview {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    background: white;
}

/* Notifications toast */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

/* ========================================
   APERÇU RESPONSIVE AVEC MARGES
   ======================================== */

/* Conteneur principal de prévisualisation */
.template-preview-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
}

/* Iframe de prévisualisation responsive */
.template-preview-iframe {
    width: 100%;
    max-width: 600px !important; /* Largeur d'email standard */
    margin: 0 auto !important;
    display: block !important;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    background: white;
    min-height: 600px;
}

/* ========================================
   MEDIA QUERIES RESPONSIVE AVEC MARGES
   ======================================== */

/* Desktop - Marges moyennes */
@media (min-width: 1024px) {
    .template-preview-container {
        padding: 40px 80px !important; /* Marges importantes sur desktop */
        max-width: 1400px;
    }

    .template-preview-iframe {
        max-width: 650px !important; /* Légèrement plus large sur desktop */
    }
}

/* Grand écran - Marges maximales */
@media (min-width: 1440px) {
    .template-preview-container {
        padding: 60px 120px !important; /* Marges encore plus importantes */
        max-width: 1600px;
    }

    .template-preview-iframe {
        max-width: 700px !important;
    }
}

/* Très grand écran */
@media (min-width: 1920px) {
    .template-preview-container {
        padding: 80px 160px !important; /* Marges maximales */
        max-width: 1800px;
    }
}

/* ========================================
   CENTRAGE DU CONTENU SUR GRAND ÉCRAN
   ======================================== */

.template-detail-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

@media (min-width: 1200px) {
    .template-detail-container {
        padding: 0 60px;
    }
}

@media (min-width: 1440px) {
    .template-detail-container {
        padding: 0 100px;
    }
}

/* ========================================
   STYLES TEMPLATE DETAIL
   ======================================== */

.template-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.info-item label {
    color: #495057;
    font-size: 0.9rem;
}

.stat-item {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.stat-item:last-child {
    border-bottom: none;
}

.code-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    max-height: 150px;
    overflow-y: auto;
}

.code-preview pre {
    margin: 0;
    font-size: 0.8rem;
    color: #495057;
}

/* Actions responsive */
@media (max-width: 768px) {
    .template-actions .btn-group {
        flex-direction: column;
    }

    .template-actions .btn {
        margin-bottom: 5px;
    }
}

/* ========================================
   STYLES PRÉVISUALISATION VS GÉNÉRATION
   ======================================== */

/* Conteneur de prévisualisation */
.preview-container {
    margin-top: 30px;
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mode prévisualisation (aperçu sans sauvegarde) */
.preview-mode {
    border: 2px solid #17a2b8 !important;
    box-shadow: 0 4px 20px rgba(23, 162, 184, 0.2);
}

.preview-mode .card-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border-bottom: 1px solid #138496;
}

.preview-mode .card-header h5 {
    color: white;
}

/* Mode génération (template sauvegardé) */
.generated-mode {
    border: 2px solid #28a745 !important;
    box-shadow: 0 4px 20px rgba(40, 167, 69, 0.2);
}

.generated-mode .card-header {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
    border-bottom: 1px solid #1e7e34;
}

.generated-mode .card-header h5 {
    color: white;
}

/* Actions de prévisualisation */
.preview-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.preview-actions .btn {
    font-size: 0.875rem;
    padding: 6px 12px;
}

/* Conteneur iframe de prévisualisation */
.preview-iframe-container {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 0 0 8px 8px;
}

.preview-iframe-container iframe {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    background: white;
}

/* Responsive pour prévisualisation */
@media (max-width: 768px) {
    .preview-actions {
        flex-direction: column;
        gap: 5px;
    }

    .preview-actions .btn {
        width: 100%;
        font-size: 0.8rem;
    }

    .preview-iframe-container {
        padding: 10px;
    }

    .preview-iframe-container iframe {
        height: 400px !important;
    }
}
