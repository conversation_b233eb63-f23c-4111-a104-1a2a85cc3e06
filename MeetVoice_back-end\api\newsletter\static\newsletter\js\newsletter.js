/**
 * Newsletter JavaScript
 * Fonctionnalités pour l'interface de newsletter
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // ===== INITIALISATION =====
    initCodeEditors();
    initAIGenerator();
    initCampaignActions();
    initTemplateSelector();
    initPreviewModal();
    initStatsCharts();
    
    // ===== NOTIFICATIONS =====
    initToastNotifications();
});

/**
 * Initialise les éditeurs de code
 */
function initCodeEditors() {
    const codeEditors = document.querySelectorAll('.code-editor');
    
    codeEditors.forEach(textarea => {
        if (textarea.classList.contains('cm-initialized')) return;
        
        let mode = 'htmlmixed';
        if (textarea.name === 'css_styles') {
            mode = 'css';
        }
        
        const editor = CodeMirror.fromTextArea(textarea, {
            lineNumbers: true,
            mode: mode,
            theme: 'default',
            lineWrapping: true,
            autoCloseTags: true,
            autoCloseBrackets: true,
            matchBrackets: true,
            indentUnit: 2,
            tabSize: 2,
            extraKeys: {
                "Ctrl-Space": "autocomplete",
                "F11": function(cm) {
                    cm.setOption("fullScreen", !cm.getOption("fullScreen"));
                },
                "Esc": function(cm) {
                    if (cm.getOption("fullScreen")) cm.setOption("fullScreen", false);
                }
            }
        });
        
        // Synchroniser avec le textarea
        editor.on('change', function() {
            textarea.value = editor.getValue();
        });
        
        textarea.classList.add('cm-initialized');
    });
}

/**
 * Initialise le générateur IA
 */
function initAIGenerator() {
    const generateBtn = document.getElementById('generateContentBtn');
    const promptInput = document.getElementById('aiPrompt');
    const themeInput = document.getElementById('aiTheme');
    const audienceInput = document.getElementById('aiAudience');
    const previewDiv = document.getElementById('aiContentPreview');
    
    if (!generateBtn) return;
    
    generateBtn.addEventListener('click', function() {
        const prompt = promptInput?.value.trim();
        const theme = themeInput?.value || 'newsletter';
        const audience = audienceInput?.value || 'utilisateurs';
        
        if (!prompt) {
            showToast('Veuillez saisir des instructions pour l\'IA', 'warning');
            return;
        }
        
        generateContent(prompt, theme, audience);
    });
}

/**
 * Génère du contenu avec l'IA
 */
function generateContent(prompt, theme, audience) {
    const generateBtn = document.getElementById('generateContentBtn');
    const previewDiv = document.getElementById('aiContentPreview');
    
    // État de chargement
    generateBtn.disabled = true;
    generateBtn.innerHTML = '<span class="loading-spinner"></span> Génération en cours...';
    
    // Appel AJAX
    fetch('/newsletter/generate-content/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken(),
        },
        body: JSON.stringify({
            prompt: prompt,
            theme: theme,
            audience: audience
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayGeneratedContent(data.data);
            showToast('Contenu généré avec succès !', 'success');
        } else {
            showToast('Erreur lors de la génération: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showToast('Erreur de connexion', 'error');
    })
    .finally(() => {
        // Restaurer le bouton
        generateBtn.disabled = false;
        generateBtn.innerHTML = '<i class="fas fa-magic me-1"></i>Générer avec l\'IA';
    });
}

/**
 * Affiche le contenu généré par l'IA
 */
function displayGeneratedContent(data) {
    const previewDiv = document.getElementById('aiContentPreview');
    const subjectInput = document.getElementById('id_subject');
    const contentTextarea = document.getElementById('id_generated_content');
    
    if (!previewDiv) return;
    
    // Mettre à jour les champs du formulaire
    if (subjectInput && data.subject) {
        subjectInput.value = data.subject;
    }
    
    if (contentTextarea && data.content) {
        contentTextarea.value = data.content;
    }
    
    // Afficher l'aperçu
    previewDiv.innerHTML = `
        <div class="ai-content-preview">
            <h6><i class="fas fa-eye me-2"></i>Aperçu du contenu généré</h6>
            <div class="row">
                <div class="col-md-6">
                    <strong>Sujet:</strong>
                    <p class="text-muted">${data.subject || 'Non défini'}</p>
                    
                    <strong>Texte de prévisualisation:</strong>
                    <p class="text-muted">${data.preview_text || 'Non défini'}</p>
                </div>
                <div class="col-md-6">
                    <strong>Appel à l'action:</strong>
                    <p class="text-muted">${data.call_to_action || 'Non défini'}</p>
                    
                    <strong>URL CTA:</strong>
                    <p class="text-muted">${data.cta_url || 'Non défini'}</p>
                </div>
            </div>
            
            <strong>Contenu HTML:</strong>
            <div class="content-preview">
                ${data.content || 'Aucun contenu généré'}
            </div>
            
            <div class="mt-3">
                <button type="button" class="btn btn-sm btn-primary" onclick="acceptGeneratedContent()">
                    <i class="fas fa-check me-1"></i>Utiliser ce contenu
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="regenerateContent()">
                    <i class="fas fa-redo me-1"></i>Régénérer
                </button>
            </div>
        </div>
    `;
    
    previewDiv.style.display = 'block';
}

/**
 * Accepte le contenu généré
 */
function acceptGeneratedContent() {
    const previewDiv = document.getElementById('aiContentPreview');
    previewDiv.style.display = 'none';
    showToast('Contenu accepté et intégré au formulaire', 'success');
}

/**
 * Régénère le contenu
 */
function regenerateContent() {
    const promptInput = document.getElementById('aiPrompt');
    const themeInput = document.getElementById('aiTheme');
    const audienceInput = document.getElementById('aiAudience');
    
    if (promptInput?.value.trim()) {
        generateContent(
            promptInput.value.trim(),
            themeInput?.value || 'newsletter',
            audienceInput?.value || 'utilisateurs'
        );
    }
}

/**
 * Initialise les actions de campagne
 */
function initCampaignActions() {
    // Bouton d'envoi de campagne
    const sendButtons = document.querySelectorAll('.send-campaign-btn');
    sendButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const campaignId = this.dataset.campaignId;
            confirmSendCampaign(campaignId);
        });
    });
    
    // Bouton de prévisualisation
    const previewButtons = document.querySelectorAll('.preview-campaign-btn');
    previewButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const campaignId = this.dataset.campaignId;
            previewCampaign(campaignId);
        });
    });
}

/**
 * Confirme l'envoi d'une campagne
 */
function confirmSendCampaign(campaignId) {
    if (confirm('Êtes-vous sûr de vouloir envoyer cette campagne ? Cette action est irréversible.')) {
        window.location.href = `/newsletter/campaigns/${campaignId}/send/`;
    }
}

/**
 * Prévisualise une campagne
 */
function previewCampaign(campaignId) {
    const previewUrl = `/newsletter/campaigns/${campaignId}/preview/`;
    window.open(previewUrl, 'preview', 'width=800,height=600,scrollbars=yes');
}

/**
 * Initialise le sélecteur de template
 */
function initTemplateSelector() {
    const templateOptions = document.querySelectorAll('.template-option');
    
    templateOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Désélectionner tous les autres
            templateOptions.forEach(opt => opt.classList.remove('selected'));
            
            // Sélectionner celui-ci
            this.classList.add('selected');
            
            // Mettre à jour le champ caché
            const templateId = this.dataset.templateId;
            const hiddenInput = document.getElementById('id_template');
            if (hiddenInput) {
                hiddenInput.value = templateId;
            }
        });
    });
}

/**
 * Initialise la modal de prévisualisation
 */
function initPreviewModal() {
    const previewModal = document.getElementById('previewModal');
    if (!previewModal) return;
    
    previewModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const previewUrl = button.getAttribute('data-preview-url');
        
        if (previewUrl) {
            const iframe = previewModal.querySelector('iframe');
            iframe.src = previewUrl;
        }
    });
}

/**
 * Initialise les graphiques de statistiques
 */
function initStatsCharts() {
    // Graphique des performances par campagne
    const campaignStatsDiv = document.getElementById('campaignStatsChart');
    if (campaignStatsDiv) {
        loadCampaignStats();
    }
    
    // Graphique d'évolution temporelle
    const timelineStatsDiv = document.getElementById('timelineStatsChart');
    if (timelineStatsDiv) {
        loadTimelineStats();
    }
}

/**
 * Charge les statistiques de campagne
 */
function loadCampaignStats() {
    fetch('/newsletter/api/stats/overview/')
        .then(response => response.json())
        .then(data => {
            renderCampaignStatsChart(data);
        })
        .catch(error => {
            console.error('Erreur chargement stats:', error);
        });
}

/**
 * Affiche le graphique des statistiques de campagne
 */
function renderCampaignStatsChart(data) {
    const chartData = [
        {
            x: ['Envoyés', 'Ouverts', 'Cliqués'],
            y: [data.overview.total_sent, data.overview.total_opens, data.overview.total_clicks],
            type: 'bar',
            marker: {
                color: ['#007bff', '#28a745', '#ffc107']
            }
        }
    ];
    
    const layout = {
        title: 'Performances globales',
        xaxis: { title: 'Métriques' },
        yaxis: { title: 'Nombre' },
        margin: { t: 40, r: 20, b: 40, l: 60 }
    };
    
    Plotly.newPlot('campaignStatsChart', chartData, layout, {responsive: true});
}

/**
 * Initialise les notifications toast
 */
function initToastNotifications() {
    // Créer le container de toasts s'il n'existe pas
    if (!document.querySelector('.toast-container')) {
        const container = document.createElement('div');
        container.className = 'toast-container';
        document.body.appendChild(container);
    }
}

/**
 * Affiche une notification toast
 */
function showToast(message, type = 'info') {
    const container = document.querySelector('.toast-container');
    const toastId = 'toast-' + Date.now();
    
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type}" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', toastHtml);
    
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 5000
    });
    
    toast.show();
    
    // Supprimer l'élément après fermeture
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

/**
 * Récupère le token CSRF
 */
function getCsrfToken() {
    const token = document.querySelector('[name=csrfmiddlewaretoken]');
    return token ? token.value : '';
}

/**
 * Formate les nombres pour l'affichage
 */
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

/**
 * Copie du texte dans le presse-papiers
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast('Copié dans le presse-papiers', 'success');
    }).catch(function() {
        showToast('Erreur lors de la copie', 'error');
    });
}

/**
 * Valide un email
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Debounce function pour limiter les appels
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Auto-save pour les formulaires
 */
function initAutoSave() {
    const forms = document.querySelectorAll('.newsletter-form');
    
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            input.addEventListener('input', debounce(() => {
                saveFormData(form);
            }, 2000));
        });
    });
}

/**
 * Sauvegarde automatique des données du formulaire
 */
function saveFormData(form) {
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    const storageKey = `newsletter_autosave_${window.location.pathname}`;
    localStorage.setItem(storageKey, JSON.stringify(data));
    
    showToast('Brouillon sauvegardé', 'info');
}
