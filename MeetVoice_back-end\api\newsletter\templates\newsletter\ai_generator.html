{% extends 'newsletter/base_newsletter.html' %}
{% load static %}

{% block title %}Générateur de Templates IA - Newsletter{% endblock %}

{% block head %}
{{ block.super }}
<style>
    .ai-generator-container {
        padding: 20px;
        min-height: calc(100vh - 70px);
    }
    
    .ai-header {
        background: linear-gradient(135deg, #4e385f 0%, #2A1D34 100%);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .ai-header h1 {
        margin: 0 0 10px 0;
        font-size: 2.5rem;
        font-weight: 700;
    }
    
    .ai-header p {
        margin: 0;
        font-size: 1.1rem;
        opacity: 0.9;
    }
    
    .generator-form {
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-label {
        display: block;
        font-weight: 600;
        color: #2A1D34;
        margin-bottom: 8px;
        font-size: 1.1rem;
    }
    
    .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #4e385f;
        outline: none;
        box-shadow: 0 0 0 3px rgba(78, 56, 95, 0.1);
    }
    
    .form-select {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 12px center;
        background-repeat: no-repeat;
        background-size: 16px 12px;
        padding-right: 40px;
    }
    
    .btn-ai {
        background: linear-gradient(135deg, #4e385f 0%, #2A1D34 100%);
        color: white;
        border: none;
        padding: 14px 28px;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-ai:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(78, 56, 95, 0.3);
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        cursor: pointer;
        margin-right: 10px;
    }
    
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .quick-action-card {
        background: white;
        padding: 25px;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .quick-action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }
    
    .quick-action-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        display: block;
    }
    
    .quick-action-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #2A1D34;
        margin-bottom: 10px;
    }
    
    .quick-action-desc {
        color: #6c757d;
        font-size: 0.95rem;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #4e385f;
        margin-bottom: 5px;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .preview-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-top: 30px;
        display: none;
    }
    
    .preview-header {
        background: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: between;
        align-items: center;
    }
    
    .preview-content {
        padding: 20px;
        max-height: 600px;
        overflow-y: auto;
    }
    
    /* Loading Overlay */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .loading-content {
        background: white;
        padding: 40px;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        max-width: 400px;
    }

    .spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #4e385f;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .alert {
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .alert-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .alert-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
</style>
{% endblock %}

{% block newsletter_content %}
<div class="ai-generator-container">
    <!-- Header -->
    <div class="ai-header">
        <h1>🤖 Générateur de Templates IA</h1>
        <p>Créez des newsletters professionnelles pour MeetVoice en quelques secondes avec l'Intelligence Artificielle</p>
    </div>
    

    
    <!-- Statistiques -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_templates }}</div>
            <div class="stat-label">Templates Total</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ stats.ai_templates }}</div>
            <div class="stat-label">Templates IA</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ stats.recent_templates|length }}</div>
            <div class="stat-label">Récents</div>
        </div>
    </div>
    
    <!-- Actions rapides -->
    <div class="quick-actions">
        <div class="quick-action-card" onclick="showSeasonalGenerator()">
            <span class="quick-action-icon">🌸</span>
            <div class="quick-action-title">Template Saisonnier</div>
            <div class="quick-action-desc">Générer un template adapté à la saison actuelle</div>
        </div>
        
        <div class="quick-action-card" onclick="showWelcomeGenerator()">
            <span class="quick-action-icon">👋</span>
            <div class="quick-action-title">Série de Bienvenue</div>
            <div class="quick-action-desc">Créer une série d'emails pour nouveaux utilisateurs</div>
        </div>
        
        <div class="quick-action-card" onclick="showVariationsGenerator()">
            <span class="quick-action-icon">🎨</span>
            <div class="quick-action-title">Variations Multiples</div>
            <div class="quick-action-desc">Générer plusieurs versions d'un même template</div>
        </div>
        
        <div class="quick-action-card" onclick="showCustomGenerator()">
            <span class="quick-action-icon">✨</span>
            <div class="quick-action-title">Template Personnalisé</div>
            <div class="quick-action-desc">Créer un template basé sur votre titre</div>
        </div>
    </div>
    
    <!-- Formulaire principal -->
    <div class="generator-form" id="mainForm">
        <h3>🎯 Créer un Template Personnalisé</h3>
        <form method="post" id="aiGeneratorForm">
            {% csrf_token %}
            
            <div class="form-group">
                <label class="form-label" for="title">
                    📝 Titre de la Newsletter
                    <small style="color: #6c757d; font-weight: normal;">(L'IA générera le contenu basé sur ce titre)</small>
                </label>
                <input type="text" 
                       class="form-control" 
                       id="title" 
                       name="title" 
                       placeholder="Ex: Nouvelles Fonctionnalités IA de MeetVoice"
                       required>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="style">🎨 Style du Template</label>
                <select class="form-control form-select" id="style" name="style">
                    <option value="moderne">Moderne - Design contemporain avec dégradés</option>
                    <option value="minimaliste">Minimaliste - Épuré et élégant</option>
                    <option value="promotionnel">Promotionnel - Accrocheur et énergique</option>
                    <option value="premium">Premium - Luxueux et sophistiqué</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="audience">👥 Audience Cible</label>
                <select class="form-control form-select" id="audience" name="audience">
                    <option value="general">Général - Tous les utilisateurs MeetVoice</option>
                    <option value="nouveaux_utilisateurs">Nouveaux Utilisateurs - Récemment inscrits</option>
                    <option value="utilisateurs_actifs">Utilisateurs Actifs - Réguliers et engagés</option>
                    <option value="premium">Premium - Abonnés payants</option>
                </select>
            </div>

            <!-- Informations automatiques -->
            <div class="alert alert-info" style="background: #e8f4fd; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px; margin-bottom: 25px;">
                <h6 style="margin: 0 0 10px 0; color: #0c5460;">
                    <i class="fas fa-magic me-2"></i>Génération Automatique
                </h6>
                <ul style="margin: 0; padding-left: 20px; color: #0c5460;">
                    <li><strong>Type :</strong> Toujours professionnel avec design avancé</li>
                    <li><strong>Images :</strong> Générées automatiquement en haute qualité</li>
                    <li><strong>Logo :</strong> MeetVoice intégré automatiquement</li>
                </ul>
            </div>

            <div class="form-group">
                <label class="form-label" for="custom_prompt">
                    💡 Prompt Personnalisé (Optionnel)
                    <small style="color: #6c757d; font-weight: normal;">(Instructions spécifiques pour l'IA)</small>
                </label>
                <textarea class="form-control"
                         id="custom_prompt"
                         name="custom_prompt"
                         rows="3"
                         placeholder="Ex: Mettre l'accent sur les nouvelles fonctionnalités vocales, inclure des témoignages d'utilisateurs, style moderne et dynamique..."></textarea>
            </div>

            <!-- Images automatiquement générées -->

            <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                <button type="submit" class="btn-ai">
                    <span>🚀</span>
                    Générer le Template
                </button>

                <button type="button" class="btn-secondary" onclick="previewTemplate()">
                    👁️ Prévisualiser
                </button>
            </div>
        </form>
    </div>
    
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingSpinner" style="display: none;">
        <div class="loading-content">
            <div class="spinner"></div>
            <p>🤖 L'IA génère votre template personnalisé...</p>
            <p><small>Génération du contenu et des images en cours...</small></p>
        </div>
    </div>
    
    <!-- Templates récents -->
    <div class="generator-form">
        <h3>📋 Templates Récemment Créés</h3>
        {% if stats.recent_templates %}
            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px;">
                {% for template in stats.recent_templates %}
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #4e385f;">
                    <h4 style="margin: 0 0 8px 0; color: #2A1D34; font-size: 1.1rem;">
                        {{ template.name }}
                    </h4>
                    <p style="margin: 0 0 10px 0; color: #6c757d; font-size: 0.9rem;">
                        {{ template.preview_text|truncatechars:80 }}
                    </p>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <small style="color: #6c757d;">
                            {{ template.created_at|date:"d/m/Y H:i" }}
                        </small>
                        <a href="{% url 'newsletter:template_detail' template.pk %}"
                           style="background: #4e385f; color: white; padding: 5px 12px; border-radius: 4px; text-decoration: none; font-size: 0.85rem;">
                            Voir
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <p style="color: #6c757d; text-align: center; padding: 20px;">
                Aucun template créé récemment. Utilisez le générateur ci-dessus pour créer votre premier template IA !
            </p>
        {% endif %}
    </div>

    <!-- Prévisualisation -->
    <div class="preview-container" id="previewContainer">
        <div class="preview-header">
            <h4>👁️ Prévisualisation du Template</h4>
            <button type="button" class="btn-secondary" onclick="hidePreview()">Fermer</button>
        </div>
        <div class="preview-content" id="previewContent">
            <!-- Le contenu sera injecté ici -->
        </div>
    </div>
</div>



<script>
// Variables globales pour éviter les conflits
let isGenerating = false;
let animationTimeout = null;

// Fonction d'animation corrigée
function showLoading() {
    if (isGenerating) {
        console.log('⚠️ Génération déjà en cours, ignoré');
        return;
    }

    isGenerating = true;
    console.log('🔄 Démarrage animation de chargement');

    const spinner = document.getElementById('loadingSpinner');
    const submitBtn = document.querySelector('button[type="submit"]');
    const previewBtn = document.querySelector('button[onclick="previewTemplate()"]');

    // Afficher l'overlay de chargement
    if (spinner) {
        spinner.style.display = 'flex';
        console.log('✅ Spinner affiché');
    } else {
        console.log('❌ Spinner non trouvé');
    }

    // Désactiver les boutons
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.textContent = 'Génération en cours...';
        console.log('✅ Bouton génération désactivé');
    }

    if (previewBtn) {
        previewBtn.disabled = true;
        previewBtn.textContent = 'Prévisualisation...';
        console.log('✅ Bouton prévisualisation désactivé');
    }
}

function hideLoading() {
    console.log('🛑 Arrêt animation de chargement');

    const spinner = document.getElementById('loadingSpinner');
    const submitBtn = document.querySelector('button[type="submit"]');
    const previewBtn = document.querySelector('button[onclick="previewTemplate()"]');

    // Masquer l'overlay
    if (spinner) {
        spinner.style.display = 'none';
        console.log('✅ Spinner masqué');
    }

    // Réactiver les boutons
    if (submitBtn) {
        submitBtn.disabled = false;
        submitBtn.textContent = '🚀 Générer le Template';
        console.log('✅ Bouton génération réactivé');
    }

    if (previewBtn) {
        previewBtn.disabled = false;
        previewBtn.textContent = '👁️ Prévisualiser';
        console.log('✅ Bouton prévisualisation réactivé');
    }

    isGenerating = false;
    console.log('✅ État de génération réinitialisé');
}

// Gestion du formulaire principal
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Page AI Generator chargée');

    const form = document.querySelector('#aiGeneratorForm');
    const submitBtn = document.querySelector('button[type="submit"]');
    const previewBtn = document.querySelector('button[onclick="previewTemplate()"]');

    console.log('🔍 Éléments trouvés:');
    console.log('   Form:', form ? '✅' : '❌');
    console.log('   Submit button:', submitBtn ? '✅' : '❌');
    console.log('   Preview button:', previewBtn ? '✅' : '❌');

    // Gestion de la soumission du formulaire
    if (form) {
        form.addEventListener('submit', function(e) {
            console.log('📤 Soumission formulaire détectée');

            // Vérifier les champs requis
            const title = document.getElementById('title')?.value?.trim();
            if (!title) {
                e.preventDefault();
                alert('Veuillez saisir un titre');
                return;
            }

            // Afficher l'animation
            showLoading();
            console.log('🔄 Animation démarrée pour génération');

            // Laisser le formulaire se soumettre normalement
        });
    }

    // Améliorer la gestion du bouton de prévisualisation
    if (previewBtn) {
        previewBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('👁️ Bouton prévisualisation cliqué');
            previewTemplate();
        });
    }
});

// Fonction de prévisualisation améliorée
function previewTemplate() {
    if (isGenerating) return;

    showLoading();

    const title = document.getElementById('title')?.value;
    const style = document.getElementById('style')?.value;
    const audience = document.getElementById('audience')?.value;
    const generate_images = document.getElementById('generate_images')?.checked;

    if (!title || !title.trim()) {
        hideLoading();
        alert('Veuillez saisir un titre');
        return;
    }

    console.log('🔍 PRÉVISUALISATION (sans sauvegarde)');
    console.log('   📝 Titre:', title);
    console.log('   🎨 Style:', style);
    console.log('   👥 Audience:', audience);
    console.log('   📸 Génération images professionnelles:', generate_images);

    fetch('/newsletter/ai-generator/preview/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value
        },
        body: JSON.stringify({
            title: title,
            style: style || 'moderne',
            audience: audience || 'general',
            generate_images: generate_images || false
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            console.log('✅ Prévisualisation réussie');
            console.log('   📸 Images professionnelles générées:', data.generate_images);
            console.log('   🎨 Images incluses dans HTML:', data.images_included);
            showPreview(data.html_content, true); // true = mode prévisualisation
        } else {
            alert('Erreur prévisualisation: ' + (data.error || 'Erreur inconnue'));
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erreur prévisualisation:', error);
        alert('Erreur technique: ' + error.message);
    });
}

function showPreview(htmlContent, isPreviewMode = false) {
    // Créer ou récupérer le conteneur de prévisualisation
    let previewContainer = document.getElementById('previewContainer');

    if (!previewContainer) {
        // Créer le conteneur s'il n'existe pas
        previewContainer = document.createElement('div');
        previewContainer.id = 'previewContainer';
        previewContainer.className = 'preview-container mt-4';
        document.querySelector('.ai-generator-container').appendChild(previewContainer);
    }

    const previewTitle = isPreviewMode ?
        '👁️ Aperçu du Template (non sauvegardé)' :
        '✅ Template Généré avec Succès';

    const previewClass = isPreviewMode ? 'preview-mode' : 'generated-mode';

    previewContainer.innerHTML = `
        <div class="card ${previewClass}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">${previewTitle}</h5>
                <div class="preview-actions">
                    ${isPreviewMode ?
                        '<button class="btn btn-sm btn-success" onclick="generateFromPreview()"><i class="fas fa-save me-1"></i>Générer et Sauvegarder</button>' :
                        '<a href="/newsletter/templates/" class="btn btn-sm btn-primary"><i class="fas fa-list me-1"></i>Voir tous les templates</a>'
                    }
                    <button class="btn btn-sm btn-secondary" onclick="closePreview()">
                        <i class="fas fa-times me-1"></i>Fermer
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="preview-iframe-container">
                    <iframe srcdoc="${htmlContent.replace(/"/g, '&quot;')}"
                            style="width: 100%; height: 600px; border: none; border-radius: 0 0 8px 8px;">
                    </iframe>
                </div>
            </div>
        </div>
    `;

    previewContainer.style.display = 'block';
    previewContainer.scrollIntoView({ behavior: 'smooth' });

    console.log(isPreviewMode ? '👁️ Aperçu affiché' : '✅ Template généré affiché');
}

function generateFromPreview() {
    // Soumettre le formulaire pour générer et sauvegarder
    console.log('🚀 Génération et sauvegarde depuis l\'aperçu');
    document.getElementById('aiGeneratorForm').submit();
}

function closePreview() {
    const container = document.getElementById('previewContainer');
    if (container) {
        container.style.display = 'none';
        console.log('❌ Aperçu fermé');
    }
}
</script>


</div>
{% endblock %}
