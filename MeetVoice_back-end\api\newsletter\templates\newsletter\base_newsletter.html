{% extends 'base.html' %}
{% load static %}

{% block head %}
<link rel="stylesheet" href="{% static 'newsletter/css/newsletter.css' %}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
{% endblock %}

{% block content %}
<div class="newsletter-wrapper">
    <!-- Sidebar Newsletter -->
    <div class="newsletter-sidebar">
        <div class="sidebar-header">
            <h5><i class="fas fa-envelope me-2"></i>Newsletter</h5>
        </div>
        
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {% if 'dashboard' in request.resolver_match.url_name %}active{% endif %}"
                       href="{% url 'newsletter:dashboard' %}">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'simple_stats' in request.resolver_match.url_name %}active{% endif %}"
                       href="{% url 'newsletter:simple_stats' %}">
                        <i class="fas fa-chart-simple me-2"></i>Stats Simples
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'campaign_list' in request.resolver_match.url_name %}active{% endif %}" 
                       href="{% url 'newsletter:campaign_list' %}">
                        <i class="fas fa-paper-plane me-2"></i>Campagnes
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'template_list' in request.resolver_match.url_name %}active{% endif %}"
                       href="{% url 'newsletter:template_list' %}">
                        <i class="fas fa-file-code me-2"></i>Templates
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'ai_generator' in request.resolver_match.url_name %}active{% endif %}"
                       href="{% url 'newsletter:ai_generator' %}">
                        <i class="fas fa-robot me-2"></i>Générateur IA
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'stats_dashboard' in request.resolver_match.url_name %}active{% endif %}" 
                       href="{% url 'newsletter:stats_dashboard' %}">
                        <i class="fas fa-chart-bar me-2"></i>Statistiques
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'settings' in request.resolver_match.url_name %}active{% endif %}" 
                       href="{% url 'newsletter:settings' %}">
                        <i class="fas fa-cog me-2"></i>Paramètres
                    </a>
                </li>
            </ul>
        </nav>
        
        <div class="sidebar-actions">
            <a href="{% url 'newsletter:campaign_create' %}" class="btn btn-primary btn-sm w-100 mb-2">
                <i class="fas fa-plus me-1"></i>Nouvelle campagne
            </a>
            <a href="{% url 'newsletter:ai_generator' %}" class="btn btn-success btn-sm w-100 mb-2">
                <i class="fas fa-robot me-1"></i>Générateur IA
            </a>
            <a href="{% url 'newsletter:template_create' %}" class="btn btn-outline-primary btn-sm w-100">
                <i class="fas fa-plus me-1"></i>Nouveau template
            </a>
        </div>
    </div>
    
    <!-- Contenu principal -->
    <div class="newsletter-content">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        {% block newsletter_content %}{% endblock %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/xml/xml.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/css/css.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/htmlmixed/htmlmixed.min.js"></script>
<script src="{% static 'newsletter/js/newsletter.js' %}"></script>
{% endblock %}
