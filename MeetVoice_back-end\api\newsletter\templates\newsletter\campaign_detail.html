{% extends 'newsletter/base_newsletter.html' %}
{% load static %}

{% block newsletter_content %}
<div class="campaign-detail-header mb-4">
    <div class="d-flex justify-content-between align-items-start">
        <div>
            <h2><i class="fas fa-paper-plane me-2"></i>{{ campaign.name }}</h2>
            <p class="text-muted">Détail de la campagne newsletter</p>
        </div>
        <div class="campaign-actions">
            {% if campaign.status == 'draft' %}
                <a href="{% url 'newsletter:campaign_edit' campaign.pk %}" class="btn btn-outline-primary me-2">
                    <i class="fas fa-edit me-1"></i>Modifier
                </a>
                <button class="btn btn-success" onclick="sendCampaign()">
                    <i class="fas fa-paper-plane me-1"></i>Envoyer
                </button>
            {% elif campaign.status == 'sent' %}
                <a href="{% url 'newsletter:campaign_preview' campaign.pk %}" target="_blank" class="btn btn-outline-info me-2">
                    <i class="fas fa-eye me-1"></i>Aperçu
                </a>
                <button class="btn btn-outline-secondary" onclick="duplicateCampaign()">
                    <i class="fas fa-copy me-1"></i>Dupliquer
                </button>
            {% endif %}
        </div>
    </div>
</div>

<!-- Informations générales -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle me-2"></i>Informations générales</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Nom :</strong></div>
                    <div class="col-sm-9">{{ campaign.name }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Sujet :</strong></div>
                    <div class="col-sm-9">{{ campaign.subject }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Statut :</strong></div>
                    <div class="col-sm-9">
                        {% if campaign.status == 'draft' %}
                            <span class="badge bg-secondary">Brouillon</span>
                        {% elif campaign.status == 'sending' %}
                            <span class="badge bg-warning">En cours d'envoi</span>
                        {% elif campaign.status == 'sent' %}
                            <span class="badge bg-success">Envoyée</span>
                        {% elif campaign.status == 'failed' %}
                            <span class="badge bg-danger">Échec</span>
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Créée le :</strong></div>
                    <div class="col-sm-9">{{ campaign.created_at|date:"d/m/Y à H:i" }}</div>
                </div>
                {% if campaign.sent_at %}
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Envoyée le :</strong></div>
                    <div class="col-sm-9">{{ campaign.sent_at|date:"d/m/Y à H:i" }}</div>
                </div>
                {% endif %}
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Créée par :</strong></div>
                    <div class="col-sm-9">{{ campaign.created_by.username }}</div>
                </div>
                {% if campaign.template %}
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Template :</strong></div>
                    <div class="col-sm-9">
                        <a href="{% url 'newsletter:template_detail' campaign.template.pk %}">
                            {{ campaign.template.name }}
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Statistiques -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar me-2"></i>Statistiques</h5>
            </div>
            <div class="card-body">
                <div class="stat-item mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Destinataires :</span>
                        <strong>{{ campaign.recipient_count|default:0 }}</strong>
                    </div>
                </div>
                
                {% if campaign.status == 'sent' %}
                <div class="stat-item mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Ouvertures :</span>
                        <strong>{{ campaign_stats.unique_opens|default:0 }}</strong>
                    </div>
                    <div class="progress mt-1">
                        <div class="progress-bar bg-info" style="width: {{ campaign_stats.open_rate|default:0 }}%"></div>
                    </div>
                    <small class="text-muted">{{ campaign_stats.open_rate|default:0 }}% de taux d'ouverture</small>
                </div>
                
                <div class="stat-item mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Clics :</span>
                        <strong>{{ campaign_stats.unique_clicks|default:0 }}</strong>
                    </div>
                    <div class="progress mt-1">
                        <div class="progress-bar bg-warning" style="width: {{ campaign_stats.click_rate|default:0 }}%"></div>
                    </div>
                    <small class="text-muted">{{ campaign_stats.click_rate|default:0 }}% de taux de clic</small>
                </div>
                {% endif %}
                
                <div class="d-grid">
                    <button class="btn btn-outline-info btn-sm" onclick="refreshStats()">
                        <i class="fas fa-sync me-1"></i>Actualiser
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contenu de la campagne -->
{% if campaign.generated_content or campaign.ai_prompt %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-file-alt me-2"></i>Contenu de la campagne</h5>
            </div>
            <div class="card-body">
                {% if campaign.generated_content %}
                    <div class="mb-3">
                        <h6>Contenu généré :</h6>
                        <div class="content-preview border p-3 bg-light">
                            {{ campaign.generated_content|safe|truncatewords:50 }}
                            {% if campaign.generated_content|length > 200 %}
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-outline-primary" onclick="toggleFullContent()">
                                        <i class="fas fa-expand me-1"></i>Voir le contenu complet
                                    </button>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
                
                {% if campaign.ai_prompt %}
                    <div class="mb-3">
                        <h6>Prompt IA utilisé :</h6>
                        <div class="ai-prompt border p-3 bg-light">
                            <small class="text-muted">{{ campaign.ai_prompt|truncatewords:30 }}</small>
                        </div>
                    </div>
                {% endif %}
                
                <div class="content-actions">
                    <a href="{% url 'newsletter:campaign_preview' campaign.pk %}" target="_blank" class="btn btn-outline-primary me-2">
                        <i class="fas fa-eye me-1"></i>Prévisualiser
                    </a>
                    {% if campaign.final_html %}
                        <button class="btn btn-outline-secondary" onclick="downloadHTML()">
                            <i class="fas fa-download me-1"></i>Télécharger HTML
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Détail des destinataires -->
{% if campaign.status == 'sent' %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-users me-2"></i>Détail des destinataires</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Email</th>
                                <th>Statut</th>
                                <th>Ouvertures</th>
                                <th>Clics</th>
                                <th>Dernière activité</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for tracking in campaign_tracking %}
                            <tr>
                                <td>
                                    <strong>{{ tracking.recipient.email }}</strong>
                                    <br>
                                    <small class="text-muted">{{ tracking.recipient.first_name }} {{ tracking.recipient.last_name }}</small>
                                </td>
                                <td>
                                    {% if tracking.delivered %}
                                        <span class="badge bg-success">Livré</span>
                                    {% else %}
                                        <span class="badge bg-warning">En cours</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if tracking.opens_count > 0 %}
                                        <span class="badge bg-info">{{ tracking.opens_count }} fois</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Aucune</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if tracking.clicks_count > 0 %}
                                        <span class="badge bg-warning">{{ tracking.clicks_count }} clics</span>
                                    {% else %}
                                        <span class="badge bg-light text-dark">Aucun</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if tracking.last_activity %}
                                        <small>{{ tracking.last_activity|date:"d/m H:i" }}</small>
                                    {% else %}
                                        <small class="text-muted">-</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="/newsletter/track/open/{{ tracking.tracking.tracking_hash }}/" 
                                           target="_blank" class="btn btn-outline-info btn-sm" 
                                           title="Tester l'ouverture">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-outline-secondary btn-sm" 
                                                onclick="showTrackingDetails('{{ tracking.tracking.tracking_hash }}')"
                                                title="Détails du tracking">
                                            <i class="fas fa-info"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Actions -->
<div class="row">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body text-center">
                <h6>Actions disponibles</h6>
                <a href="{% url 'newsletter:campaign_list' %}" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                </a>
                <a href="{% url 'newsletter:simple_stats' %}" class="btn btn-info me-2">
                    <i class="fas fa-chart-simple me-1"></i>Voir les stats
                </a>
                {% if campaign.status == 'sent' %}
                    <button class="btn btn-outline-success" onclick="exportCampaignData()">
                        <i class="fas fa-download me-1"></i>Exporter les données
                    </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function refreshStats() {
    location.reload();
}

function sendCampaign() {
    if (confirm('Êtes-vous sûr de vouloir envoyer cette campagne ?')) {
        // Rediriger vers l'URL d'envoi
        window.location.href = "{% url 'newsletter:campaign_send' campaign.pk %}";
    }
}

function duplicateCampaign() {
    if (confirm('Voulez-vous créer une copie de cette campagne ?')) {
        // Implémenter la duplication
        showToast('Fonctionnalité de duplication bientôt disponible', 'info');
    }
}

function toggleFullContent() {
    // Implémenter l'affichage du contenu complet
    showToast('Affichage du contenu complet bientôt disponible', 'info');
}

function downloadHTML() {
    // Télécharger le HTML de la campagne
    window.open("{% url 'newsletter:campaign_preview' campaign.pk %}", '_blank');
}

function showTrackingDetails(hash) {
    // Afficher les détails du tracking
    showToast('Détails du tracking : ' + hash.substring(0, 8) + '...', 'info');
}

function exportCampaignData() {
    // Exporter les données de la campagne
    showToast('Export des données bientôt disponible', 'info');
}

function showToast(message, type = 'info') {
    // Créer une notification toast simple
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Supprimer après 3 secondes
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}
</script>

<style>
.stat-item {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.stat-item:last-child {
    border-bottom: none;
}

.content-preview {
    max-height: 200px;
    overflow-y: auto;
}

.ai-prompt {
    font-style: italic;
}

.campaign-actions .btn {
    min-width: 120px;
}
</style>
{% endblock %}
