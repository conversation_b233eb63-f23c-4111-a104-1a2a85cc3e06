{% extends 'newsletter/base_newsletter.html' %}
{% load static %}

{% block newsletter_content %}
<div class="campaign-form-header mb-4">
    <h2>
        <i class="fas fa-paper-plane me-2"></i>
        {% if campaign %}Modifier la campagne{% else %}Créer une campagne{% endif %}
    </h2>
    <p class="text-muted">
        {% if campaign %}
            Modifiez les paramètres de votre campagne newsletter
        {% else %}
            Créez une nouvelle campagne newsletter avec génération IA
        {% endif %}
    </p>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-edit me-2"></i>Informations de la campagne</h5>
            </div>
            <div class="card-body">
                <form method="post" class="newsletter-form">
                    {% csrf_token %}
                    
                    <!-- Informations de base -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger small">{{ form.name.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.audience_type.id_for_label }}" class="form-label">{{ form.audience_type.label }}</label>
                            {{ form.audience_type }}
                            {% if form.audience_type.errors %}
                                <div class="text-danger small">{{ form.audience_type.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.subject.id_for_label }}" class="form-label">{{ form.subject.label }}</label>
                        {{ form.subject }}
                        {% if form.subject.errors %}
                            <div class="text-danger small">{{ form.subject.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Sélection du template -->
                    <div class="mb-3">
                        <label for="{{ form.template.id_for_label }}" class="form-label">{{ form.template.label }}</label>
                        {{ form.template }}
                        {% if form.template.errors %}
                            <div class="text-danger small">{{ form.template.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Génération IA -->
                    <div class="ai-generator mb-4">
                        <h5><i class="fas fa-magic me-2"></i>Génération de contenu IA</h5>
                        <p class="mb-3">Décrivez le contenu que vous souhaitez générer pour cette newsletter</p>
                        
                        <div class="mb-3">
                            <label for="{{ form.ai_prompt.id_for_label }}" class="form-label text-white">{{ form.ai_prompt.label }}</label>
                            {{ form.ai_prompt }}
                            {% if form.ai_prompt.errors %}
                                <div class="text-danger small">{{ form.ai_prompt.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <label for="aiTheme" class="form-label text-white">Thème</label>
                                <input type="text" id="aiTheme" class="form-control" value="newsletter" placeholder="newsletter, promotion, actualités...">
                            </div>
                            <div class="col-md-6">
                                <label for="aiAudience" class="form-label text-white">Audience</label>
                                <select id="aiAudience" class="form-select">
                                    <option value="utilisateurs">Tous les utilisateurs</option>
                                    <option value="nouveaux">Nouveaux utilisateurs</option>
                                    <option value="actifs">Utilisateurs actifs</option>
                                    <option value="premium">Utilisateurs premium</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button type="button" id="generateContentBtn" class="btn btn-light">
                                <i class="fas fa-magic me-1"></i>Générer avec l'IA
                            </button>
                        </div>
                    </div>
                    
                    <!-- Aperçu du contenu généré -->
                    <div id="aiContentPreview" style="display: none;"></div>
                    
                    <!-- Contenu généré (caché) -->
                    <div style="display: none;">
                        {{ form.generated_content }}
                    </div>
                    
                    <!-- Programmation -->
                    <div class="mb-3">
                        <label for="{{ form.scheduled_at.id_for_label }}" class="form-label">{{ form.scheduled_at.label }}</label>
                        {{ form.scheduled_at }}
                        {% if form.scheduled_at.errors %}
                            <div class="text-danger small">{{ form.scheduled_at.errors.0 }}</div>
                        {% endif %}
                        <div class="form-text">{{ form.scheduled_at.help_text }}</div>
                    </div>
                    
                    <!-- Boutons d'action -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'newsletter:campaign_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Retour
                        </a>
                        <div>
                            <button type="submit" name="action" value="save_draft" class="btn btn-outline-primary me-2">
                                <i class="fas fa-save me-1"></i>Sauvegarder brouillon
                            </button>
                            <button type="submit" name="action" value="save_and_preview" class="btn btn-primary">
                                <i class="fas fa-eye me-1"></i>Sauvegarder et prévisualiser
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Aide et conseils -->
        <div class="card mb-3">
            <div class="card-header">
                <h6><i class="fas fa-lightbulb me-2"></i>Conseils pour l'IA</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-1"></i>
                        Soyez précis dans vos instructions
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-1"></i>
                        Mentionnez le ton souhaité (professionnel, décontracté...)
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-1"></i>
                        Indiquez les points clés à aborder
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-1"></i>
                        Précisez l'appel à l'action désiré
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Exemples de prompts -->
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-examples me-2"></i>Exemples de prompts</h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <strong>Newsletter mensuelle :</strong>
                    <p class="text-muted mb-2">"Créer une newsletter mensuelle présentant les nouvelles fonctionnalités de MeetVoice, avec un ton chaleureux et professionnel. Inclure un appel à l'action pour tester les nouvelles fonctionnalités."</p>
                    
                    <strong>Promotion :</strong>
                    <p class="text-muted mb-2">"Annoncer une promotion spéciale pour les abonnements premium avec 30% de réduction. Ton marketing mais pas trop agressif. CTA vers la page d'abonnement."</p>
                    
                    <strong>Conseils :</strong>
                    <p class="text-muted">"Partager 5 conseils pour améliorer ses conversations vocales sur l'app. Ton éducatif et bienveillant. CTA vers l'app mobile."</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialiser les champs cachés pour l'IA
document.addEventListener('DOMContentLoaded', function() {
    const promptInput = document.getElementById('{{ form.ai_prompt.id_for_label }}');
    if (promptInput) {
        window.aiPrompt = promptInput;
    }
});
</script>
{% endblock %}
