{% extends 'newsletter/base_newsletter.html' %}
{% load static %}

{% block newsletter_content %}
<div class="campaign-list-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-paper-plane me-2"></i>Campagnes Newsletter</h2>
            <p class="text-muted">Gérez vos campagnes d'email marketing</p>
        </div>
        <a href="{% url 'newsletter:campaign_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>Nouvelle campagne
        </a>
    </div>
</div>

<!-- Filtres et recherche -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Rechercher</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="Nom ou sujet de campagne...">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Statut</label>
                <select class="form-select" id="status" name="status">
                    <option value="">Tous les statuts</option>
                    {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>Filtrer
                    </button>
                    <a href="{% url 'newsletter:campaign_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Reset
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Liste des campagnes -->
<div class="row">
    {% for campaign in page_obj %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card campaign-card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">{{ campaign.name|truncatechars:30 }}</h6>
                    <span class="badge badge-{{ campaign.status }}">
                        {{ campaign.get_status_display }}
                    </span>
                </div>
                <div class="card-body">
                    <p class="card-text text-muted small mb-2">
                        <strong>Sujet:</strong> {{ campaign.subject|truncatechars:50 }}
                    </p>
                    
                    <div class="campaign-stats mb-3">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-item">
                                    <div class="stat-value">{{ campaign.recipient_count }}</div>
                                    <div class="stat-label">Destinataires</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <div class="stat-value">{{ campaign.open_rate }}%</div>
                                    <div class="stat-label">Ouvertures</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <div class="stat-value">{{ campaign.click_rate }}%</div>
                                    <div class="stat-label">Clics</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="campaign-meta small text-muted">
                        <div><i class="fas fa-user me-1"></i>{{ campaign.created_by.username }}</div>
                        <div><i class="fas fa-calendar me-1"></i>{{ campaign.created_at|date:"d/m/Y H:i" }}</div>
                        {% if campaign.sent_at %}
                            <div><i class="fas fa-paper-plane me-1"></i>{{ campaign.sent_at|date:"d/m/Y H:i" }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="card-footer">
                    <div class="campaign-actions d-flex flex-wrap gap-1">
                        <a href="{% url 'newsletter:campaign_detail' campaign.pk %}" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye me-1"></i>Voir
                        </a>
                        
                        {% if campaign.status == 'draft' %}
                            <a href="{% url 'newsletter:campaign_edit' campaign.pk %}" 
                               class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-edit me-1"></i>Modifier
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-success send-campaign-btn"
                                    data-campaign-id="{{ campaign.pk }}">
                                <i class="fas fa-paper-plane me-1"></i>Envoyer
                            </button>
                        {% endif %}
                        
                        {% if campaign.status == 'sent' %}
                            <a href="{% url 'newsletter:campaign_stats' campaign.pk %}" 
                               class="btn btn-sm btn-outline-info">
                                <i class="fas fa-chart-bar me-1"></i>Stats
                            </a>
                        {% endif %}
                        
                        <button type="button" class="btn btn-sm btn-outline-info preview-campaign-btn"
                                data-campaign-id="{{ campaign.pk }}">
                            <i class="fas fa-search me-1"></i>Aperçu
                        </button>
                    </div>
                </div>
            </div>
        </div>
    {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">Aucune campagne trouvée</h4>
                <p class="text-muted">
                    {% if search or status %}
                        Aucune campagne ne correspond à vos critères de recherche.
                    {% else %}
                        Commencez par créer votre première campagne newsletter.
                    {% endif %}
                </p>
                <a href="{% url 'newsletter:campaign_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Créer une campagne
                </a>
            </div>
        </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if page_obj.has_other_pages %}
    <nav aria-label="Pagination des campagnes">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}">
                            {{ num }}
                        </a>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
{% endif %}

<!-- Actions en lot -->
<div class="card mt-4">
    <div class="card-header">
        <h6><i class="fas fa-tasks me-2"></i>Actions rapides</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <a href="{% url 'newsletter:campaign_create' %}" class="btn btn-outline-primary w-100 mb-2">
                    <i class="fas fa-plus me-2"></i>Nouvelle campagne
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'newsletter:template_list' %}" class="btn btn-outline-secondary w-100 mb-2">
                    <i class="fas fa-file-code me-2"></i>Gérer les templates
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'newsletter:stats_dashboard' %}" class="btn btn-outline-info w-100 mb-2">
                    <i class="fas fa-chart-bar me-2"></i>Voir les statistiques
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'newsletter:export_stats' %}" class="btn btn-outline-success w-100 mb-2">
                    <i class="fas fa-download me-2"></i>Exporter les données
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
