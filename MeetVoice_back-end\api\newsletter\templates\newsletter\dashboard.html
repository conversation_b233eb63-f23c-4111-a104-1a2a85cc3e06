{% extends 'newsletter/base_newsletter.html' %}
{% load static %}

{% block newsletter_content %}
<div class="dashboard-header mb-4">
    <h2><i class="fas fa-tachometer-alt me-2"></i>Dashboard Newsletter</h2>
    <p class="text-muted"><PERSON><PERSON>rez vos campagnes d'email et suivez les performances</p>
</div>

<!-- Statistiques générales -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-primary">
                <i class="fas fa-paper-plane"></i>
            </div>
            <div class="stat-content">
                <h3>{{ stats.total_campaigns }}</h3>
                <p>Campagnes totales</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-success">
                <i class="fas fa-envelope"></i>
            </div>
            <div class="stat-content">
                <h3>{{ stats.total_sent|floatformat:0 }}</h3>
                <p>Emails envoyés</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-info">
                <i class="fas fa-eye"></i>
            </div>
            <div class="stat-content">
                <h3>{{ stats.avg_open_rate }}%</h3>
                <p>Taux d'ouverture moyen</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-warning">
                <i class="fas fa-mouse-pointer"></i>
            </div>
            <div class="stat-content">
                <h3>{{ stats.avg_click_rate }}%</h3>
                <p>Taux de clic moyen</p>
            </div>
        </div>
    </div>
</div>

<!-- Graphique des performances -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line me-2"></i>Performances des campagnes</h5>
            </div>
            <div class="card-body">
                <div id="performanceChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie me-2"></i>Répartition des ouvertures</h5>
            </div>
            <div class="card-body">
                <div id="openRateChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- Campagnes récentes et en cours -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-clock me-2"></i>Campagnes récentes</h5>
                <a href="{% url 'newsletter:campaign_list' %}" class="btn btn-sm btn-outline-primary">Voir tout</a>
            </div>
            <div class="card-body">
                {% if recent_campaigns %}
                    <div class="list-group list-group-flush">
                        {% for campaign in recent_campaigns %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ campaign.name }}</h6>
                                    <small class="text-muted">{{ campaign.created_at|date:"d/m/Y H:i" }}</small>
                                </div>
                                <div>
                                    <span class="badge bg-{{ campaign.status|yesno:'success,warning,secondary' }}">
                                        {{ campaign.get_status_display }}
                                    </span>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center py-3">Aucune campagne récente</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-play me-2"></i>Campagnes en cours</h5>
            </div>
            <div class="card-body">
                {% if active_campaigns %}
                    <div class="list-group list-group-flush">
                        {% for campaign in active_campaigns %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ campaign.name }}</h6>
                                    <small class="text-muted">
                                        {% if campaign.scheduled_at %}
                                            Programmée pour {{ campaign.scheduled_at|date:"d/m/Y H:i" }}
                                        {% else %}
                                            En cours d'envoi...
                                        {% endif %}
                                    </small>
                                </div>
                                <div>
                                    <a href="{% url 'newsletter:campaign_detail' campaign.pk %}" class="btn btn-sm btn-outline-primary">
                                        Voir
                                    </a>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <p class="text-muted mb-3">Aucune campagne en cours</p>
                        <a href="{% url 'newsletter:campaign_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Créer une campagne
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt me-2"></i>Actions rapides</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="{% url 'newsletter:campaign_create' %}" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-plus me-2"></i>Nouvelle campagne
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'newsletter:template_create' %}" class="btn btn-outline-secondary w-100 mb-2">
                            <i class="fas fa-file-code me-2"></i>Nouveau template
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'newsletter:stats_dashboard' %}" class="btn btn-outline-info w-100 mb-2">
                            <i class="fas fa-chart-bar me-2"></i>Voir statistiques
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'newsletter:export_stats' %}" class="btn btn-outline-success w-100 mb-2">
                            <i class="fas fa-download me-2"></i>Exporter données
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>




<script>
document.addEventListener('DOMContentLoaded', function() {
    // Graphique des performances avec DONNÉES À ZÉRO
    const performanceData = [
        {
            x: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
            y: [0, 0, 0, 0, 0, 0],
            type: 'scatter',
            mode: 'lines+markers',
            name: 'Taux d\'ouverture',
            line: { color: '#007bff' },
            marker: { size: 8 }
        },
        {
            x: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
            y: [0, 0, 0, 0, 0, 0],
            type: 'scatter',
            mode: 'lines+markers',
            name: 'Taux de clic',
            line: { color: '#28a745' },
            marker: { size: 8 }
        }
    ];
    
    const performanceLayout = {
        title: '',
        xaxis: { title: 'Mois' },
        yaxis: { title: 'Pourcentage (%)' },
        margin: { t: 20, r: 20, b: 40, l: 40 },
        showlegend: true,
        hovermode: 'x unified'
    };
    
    Plotly.newPlot('performanceChart', performanceData, performanceLayout, {responsive: true});
    
    // Graphique en camembert avec DONNÉES À ZÉRO
    const openRateData = [{
        values: [0, 100],
        labels: ['Ouvertures (0)', 'Non ouvertes (0)'],
        type: 'pie',
        marker: {
            colors: ['#28a745', '#dc3545']
        },
        textinfo: 'label+percent',
        textposition: 'outside'
    }];
    
    const openRateLayout = {
        title: '',
        margin: { t: 20, r: 20, b: 20, l: 20 },
        showlegend: true
    };
    
    Plotly.newPlot('openRateChart', openRateData, openRateLayout, {responsive: true});
    
    // Mise à jour des statistiques à zéro
    updateStatsDisplay();
});

function updateStatsDisplay() {
    // Statistiques à zéro
    const stats = {
        'total_campaigns': 0,
        'sent_campaigns': 0,
        'total_sent': 0,
        'total_opens': 0,
        'total_clicks': 0,
        'open_rate': 0,
        'click_rate': 0
    };
    
    // Mettre à jour les éléments avec data-stat
    Object.keys(stats).forEach(key => {
        const elements = document.querySelectorAll(`[data-stat="${key}"]`);
        elements.forEach(el => {
            if (key.includes('rate')) {
                el.textContent = stats[key] + '%';
            } else {
                el.textContent = stats[key];
            }
        });
    });
    
    // Mettre à jour les éléments avec les classes stats
    const statElements = {
        '.total-campaigns': '0',
        '.sent-campaigns': '0', 
        '.total-sent': '0',
        '.total-opens': '0',
        '.total-clicks': '0',
        '.open-rate': '0%',
        '.click-rate': '0%'
    };
    
    Object.keys(statElements).forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
            el.textContent = statElements[selector];
        });
    });
}
</script>



{% endblock %}
