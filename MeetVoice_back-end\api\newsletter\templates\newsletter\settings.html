{% extends 'newsletter/base_newsletter.html' %}
{% load static %}

{% block newsletter_content %}
<div class="settings-header mb-4">
    <h2><i class="fas fa-cog me-2"></i>Paramètres Newsletter</h2>
    <p class="text-muted">Configurez les paramètres d'envoi et d'intégration IA</p>
</div>

<form method="post" class="newsletter-form">
    {% csrf_token %}
    
    <div class="row">
        <div class="col-md-8">
            <!-- Configuration SMTP -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-server me-2"></i>Configuration SMTP</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <label for="{{ form.smtp_host.id_for_label }}" class="form-label">{{ form.smtp_host.label }}</label>
                            {{ form.smtp_host }}
                            {% if form.smtp_host.errors %}
                                <div class="text-danger small">{{ form.smtp_host.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <label for="{{ form.smtp_port.id_for_label }}" class="form-label">{{ form.smtp_port.label }}</label>
                            {{ form.smtp_port }}
                            {% if form.smtp_port.errors %}
                                <div class="text-danger small">{{ form.smtp_port.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.smtp_username.id_for_label }}" class="form-label">{{ form.smtp_username.label }}</label>
                            {{ form.smtp_username }}
                            {% if form.smtp_username.errors %}
                                <div class="text-danger small">{{ form.smtp_username.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.smtp_password.id_for_label }}" class="form-label">{{ form.smtp_password.label }}</label>
                            {{ form.smtp_password }}
                            {% if form.smtp_password.errors %}
                                <div class="text-danger small">{{ form.smtp_password.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="form-check">
                        {{ form.smtp_use_tls }}
                        <label class="form-check-label" for="{{ form.smtp_use_tls.id_for_label }}">
                            {{ form.smtp_use_tls.label }}
                        </label>
                        {% if form.smtp_use_tls.errors %}
                            <div class="text-danger small">{{ form.smtp_use_tls.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Configuration expéditeur -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-envelope me-2"></i>Configuration expéditeur</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.from_email.id_for_label }}" class="form-label">{{ form.from_email.label }}</label>
                            {{ form.from_email }}
                            {% if form.from_email.errors %}
                                <div class="text-danger small">{{ form.from_email.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.from_name.id_for_label }}" class="form-label">{{ form.from_name.label }}</label>
                            {{ form.from_name }}
                            {% if form.from_name.errors %}
                                <div class="text-danger small">{{ form.from_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.reply_to.id_for_label }}" class="form-label">{{ form.reply_to.label }}</label>
                        {{ form.reply_to }}
                        {% if form.reply_to.errors %}
                            <div class="text-danger small">{{ form.reply_to.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Configuration IA -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-robot me-2"></i>Configuration IA</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <label for="{{ form.ai_api_key.id_for_label }}" class="form-label">{{ form.ai_api_key.label }}</label>
                            {{ form.ai_api_key }}
                            {% if form.ai_api_key.errors %}
                                <div class="text-danger small">{{ form.ai_api_key.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">{{ form.ai_api_key.help_text }}</div>
                        </div>
                        <div class="col-md-4">
                            <label for="{{ form.ai_model.id_for_label }}" class="form-label">{{ form.ai_model.label }}</label>
                            {{ form.ai_model }}
                            {% if form.ai_model.errors %}
                                <div class="text-danger small">{{ form.ai_model.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">{{ form.ai_model.help_text }}</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Limites d'envoi -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-shield-alt me-2"></i>Limites d'envoi</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.max_emails_per_hour.id_for_label }}" class="form-label">{{ form.max_emails_per_hour.label }}</label>
                            {{ form.max_emails_per_hour }}
                            {% if form.max_emails_per_hour.errors %}
                                <div class="text-danger small">{{ form.max_emails_per_hour.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">{{ form.max_emails_per_hour.help_text }}</div>
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.max_emails_per_day.id_for_label }}" class="form-label">{{ form.max_emails_per_day.label }}</label>
                            {{ form.max_emails_per_day }}
                            {% if form.max_emails_per_day.errors %}
                                <div class="text-danger small">{{ form.max_emails_per_day.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">{{ form.max_emails_per_day.help_text }}</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Boutons d'action -->
            <div class="d-flex justify-content-between">
                <a href="{% url 'newsletter:dashboard' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Retour au dashboard
                </a>
                <div>
                    <button type="button" id="testConfigBtn" class="btn btn-outline-info me-2">
                        <i class="fas fa-vial me-1"></i>Tester la configuration
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Sauvegarder
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Aide configuration SMTP -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6><i class="fas fa-question-circle me-2"></i>Aide configuration SMTP</h6>
                </div>
                <div class="card-body">
                    <div class="small">
                        <p><strong>Gmail :</strong></p>
                        <ul class="list-unstyled">
                            <li>Serveur: smtp.gmail.com</li>
                            <li>Port: 587 (TLS)</li>
                            <li>Utilisez un mot de passe d'application</li>
                        </ul>
                        
                        <p><strong>Outlook :</strong></p>
                        <ul class="list-unstyled">
                            <li>Serveur: smtp-mail.outlook.com</li>
                            <li>Port: 587 (TLS)</li>
                        </ul>
                        
                        <p><strong>Yahoo :</strong></p>
                        <ul class="list-unstyled">
                            <li>Serveur: smtp.mail.yahoo.com</li>
                            <li>Port: 587 (TLS)</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Statut de la configuration -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle me-2"></i>Statut de la configuration</h6>
                </div>
                <div class="card-body">
                    <div class="config-status">
                        <div class="status-item d-flex justify-content-between align-items-center mb-2">
                            <span>SMTP</span>
                            <span class="badge bg-warning">À configurer</span>
                        </div>
                        <div class="status-item d-flex justify-content-between align-items-center mb-2">
                            <span>IA Gemini</span>
                            <span class="badge bg-warning">À configurer</span>
                        </div>
                        <div class="status-item d-flex justify-content-between align-items-center mb-2">
                            <span>Expéditeur</span>
                            <span class="badge bg-success">Configuré</span>
                        </div>
                        <div class="status-item d-flex justify-content-between align-items-center">
                            <span>Limites</span>
                            <span class="badge bg-success">Configuré</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sécurité -->
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-shield-alt me-2"></i>Sécurité</h6>
                </div>
                <div class="card-body">
                    <div class="small">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-1"></i>
                                Utilisez toujours TLS/SSL
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-1"></i>
                                Mots de passe d'application recommandés
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-1"></i>
                                Limitez les envois pour éviter le spam
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-1"></i>
                                Surveillez la réputation de votre domaine
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Test de configuration
    document.getElementById('testConfigBtn').addEventListener('click', function() {
        testConfiguration();
    });
});

function testConfiguration() {
    const btn = document.getElementById('testConfigBtn');
    const originalText = btn.innerHTML;
    
    // État de chargement
    btn.disabled = true;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Test en cours...';
    
    // Simuler un test (à remplacer par un vrai appel AJAX)
    setTimeout(() => {
        btn.disabled = false;
        btn.innerHTML = originalText;
        
        // Afficher le résultat
        showToast('Configuration testée avec succès !', 'success');
    }, 2000);
}
</script>
{% endblock %}
