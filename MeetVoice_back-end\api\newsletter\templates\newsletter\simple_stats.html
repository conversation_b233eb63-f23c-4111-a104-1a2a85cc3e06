{% extends 'newsletter/base_newsletter.html' %}
{% load static %}

{% block newsletter_content %}
<div class="simple-stats-header mb-4">
    <h2><i class="fas fa-chart-bar me-2"></i>Statistiques Newsletter - Simple</h2>
    <p class="text-muted">Vue claire et compréhensible de vos campagnes</p>
</div>

<!-- Résumé global très simple -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">📊 Résumé Global</h4>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="stat-box">
                            <h2 class="text-primary">{{ stats.total_campaigns }}</h2>
                            <p class="mb-0">Campagnes créées</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-box">
                            <h2 class="text-success">{{ stats.total_sent }}</h2>
                            <p class="mb-0">Emails envoyés</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-box">
                            <h2 class="text-info">{{ stats.total_opens }}</h2>
                            <p class="mb-0">Emails ouverts</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-box">
                            <h2 class="text-warning">{{ stats.total_clicks }}</h2>
                            <p class="mb-0">Clics enregistrés</p>
                        </div>
                    </div>
                </div>
                
                <hr class="my-4">
                
                <div class="row text-center">
                    <div class="col-md-6">
                        <div class="performance-box">
                            <h3 class="text-info">{{ stats.avg_open_rate }}%</h3>
                            <p class="mb-0">Taux d'ouverture global</p>
                            <small class="text-muted">{{ stats.total_opens }} ouvertures sur {{ stats.total_sent }} envois</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="performance-box">
                            <h3 class="text-warning">{{ stats.avg_click_rate }}%</h3>
                            <p class="mb-0">Taux de clic global</p>
                            <small class="text-muted">{{ stats.total_clicks }} clics sur {{ stats.total_sent }} envois</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Détail de la dernière campagne -->
{% if latest_campaign %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">📧 Dernière Campagne : {{ latest_campaign.name }}</h4>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>📝 Sujet :</strong> {{ latest_campaign.subject }}</p>
                        <p><strong>📅 Envoyée le :</strong> {{ latest_campaign.sent_at|date:"d/m/Y à H:i" }}</p>
                        <p><strong>👥 Destinataires :</strong> {{ latest_campaign.recipient_count }}</p>
                    </div>
                    <div class="col-md-6">
                        {% with campaign_stats=latest_campaign_stats %}
                        <div class="campaign-summary">
                            <h5>Résultats :</h5>
                            <ul class="list-unstyled">
                                <li><strong>📬 Emails livrés :</strong> {{ campaign_stats.total_sent }}</li>
                                <li><strong>👁️ Emails ouverts :</strong> {{ campaign_stats.unique_opens }} ({{ campaign_stats.open_rate }}%)</li>
                                <li><strong>🖱️ Clics :</strong> {{ campaign_stats.unique_clicks }} ({{ campaign_stats.click_rate }}%)</li>
                            </ul>
                        </div>
                        {% endwith %}
                    </div>
                </div>
                
                <h5>📋 Détail par destinataire :</h5>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>📧 Email</th>
                                <th>📬 Livraison</th>
                                <th>👁️ Ouvertures</th>
                                <th>🖱️ Clics</th>
                                <th>⏰ Dernière activité</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for tracking in latest_campaign_tracking %}
                            <tr>
                                <td>
                                    <strong>{{ tracking.recipient.email }}</strong>
                                </td>
                                <td>
                                    {% if tracking.delivered %}
                                        <span class="badge bg-success">✅ Livré</span>
                                    {% else %}
                                        <span class="badge bg-warning">⏳ En cours</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if tracking.opens_count > 0 %}
                                        <span class="badge bg-info">👁️ {{ tracking.opens_count }} fois</span>
                                    {% else %}
                                        <span class="badge bg-secondary">❌ Pas encore ouvert</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if tracking.clicks_count > 0 %}
                                        <span class="badge bg-warning">🖱️ {{ tracking.clicks_count }} clics</span>
                                    {% else %}
                                        <span class="badge bg-light text-dark">❌ Aucun clic</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if tracking.last_activity %}
                                        <small>{{ tracking.last_activity|date:"d/m H:i" }}</small>
                                    {% else %}
                                        <small class="text-muted">Aucune</small>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Bouton pour tester le tracking -->
                <div class="mt-3">
                    <h6>🧪 Test du tracking :</h6>
                    <p class="text-muted">Cliquez sur ces liens pour simuler des ouvertures :</p>
                    <div class="row">
                        {% for tracking in latest_campaign_tracking %}
                        <div class="col-md-4 mb-2">
                            <a href="/newsletter/track/open/{{ tracking.tracking.tracking_hash }}/" 
                               target="_blank" class="btn btn-outline-info btn-sm">
                                👁️ Simuler ouverture {{ tracking.recipient.email|truncatechars:15 }}
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Toutes les campagnes -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list me-2"></i>Toutes les campagnes</h5>
            </div>
            <div class="card-body">
                {% if campaigns %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Statut</th>
                                <th>Envoyés</th>
                                <th>Ouverts</th>
                                <th>Clics</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for campaign in campaigns %}
                            <tr>
                                <td><strong>{{ campaign.name|truncatechars:30 }}</strong></td>
                                <td>
                                    {% if campaign.status == 'sent' %}
                                        <span class="badge bg-success">Envoyée</span>
                                    {% elif campaign.status == 'draft' %}
                                        <span class="badge bg-secondary">Brouillon</span>
                                    {% else %}
                                        <span class="badge bg-warning">{{ campaign.get_status_display }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ campaign.recipient_count|default:0 }}</td>
                                <td>
                                    {% with opens=campaign.opens_count %}
                                    {% if opens > 0 %}
                                        <span class="text-info">{{ opens }}</span>
                                    {% else %}
                                        <span class="text-muted">0</span>
                                    {% endif %}
                                    {% endwith %}
                                </td>
                                <td>
                                    {% with clicks=campaign.clicks_count %}
                                    {% if clicks > 0 %}
                                        <span class="text-warning">{{ clicks }}</span>
                                    {% else %}
                                        <span class="text-muted">0</span>
                                    {% endif %}
                                    {% endwith %}
                                </td>
                                <td>
                                    {% if campaign.sent_at %}
                                        {{ campaign.sent_at|date:"d/m/Y" }}
                                    {% else %}
                                        {{ campaign.created_at|date:"d/m/Y" }}
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'newsletter:campaign_detail' campaign.pk %}" 
                                       class="btn btn-sm btn-outline-primary">Voir</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune campagne trouvée</h5>
                    <a href="{% url 'newsletter:campaign_create' %}" class="btn btn-primary">
                        Créer votre première campagne
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Explication du tracking -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">ℹ️ Comment fonctionne le tracking des ouvertures ?</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <p><strong>Le tracking n'est pas toujours en temps réel car :</strong></p>
                        <ul class="small">
                            <li>📧 <strong>Images bloquées</strong> : Beaucoup de clients email bloquent les images par défaut</li>
                            <li>🔒 <strong>Mode privé</strong> : Certains clients email empêchent le tracking</li>
                            <li>📱 <strong>Notifications</strong> : L'ouverture de notification ne charge pas les images</li>
                            <li>⏰ <strong>Délai</strong> : Il peut y avoir un délai entre l'ouverture et l'enregistrement</li>
                        </ul>
                        <p class="small text-muted">
                            <strong>Pour tester :</strong> Cliquez sur les liens de tracking ci-dessus ou ouvrez l'email et autorisez l'affichage des images.
                        </p>
                    </div>
                    <div class="col-md-4 text-center">
                        <button onclick="refreshStats()" class="btn btn-info mb-2" id="refreshBtn">
                            <i class="fas fa-sync me-1"></i>Actualiser maintenant
                        </button>
                        <br>
                        <small class="text-muted">Dernière mise à jour : <span id="lastUpdate">{{ "now"|date:"H:i:s" }}</span></small>
                        <br>
                        <div class="form-check form-switch mt-2">
                            <input class="form-check-input" type="checkbox" id="autoRefresh">
                            <label class="form-check-label small" for="autoRefresh">
                                Auto-actualisation (30s)
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body text-center">
                <h6>🚀 Actions rapides</h6>
                <a href="{% url 'newsletter:campaign_create' %}" class="btn btn-primary me-2">
                    <i class="fas fa-plus me-1"></i>Nouvelle campagne
                </a>
                <a href="{% url 'newsletter:dashboard' %}" class="btn btn-secondary me-2">
                    <i class="fas fa-chart-bar me-1"></i>Dashboard complet
                </a>
                <button onclick="location.reload()" class="btn btn-info">
                    <i class="fas fa-sync me-1"></i>Actualiser
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.stat-box {
    padding: 20px;
    border-radius: 8px;
    background: #f8f9fa;
    margin-bottom: 10px;
}

.performance-box {
    padding: 15px;
    border-radius: 8px;
    background: #ffffff;
    border: 1px solid #dee2e6;
}

.campaign-summary {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
}

.table th {
    border-top: none;
}

.badge {
    font-size: 0.9em;
}
</style>

<script>
let autoRefreshInterval;

function refreshStats() {
    const btn = document.getElementById('refreshBtn');
    const originalText = btn.innerHTML;

    // Animation de chargement
    btn.disabled = true;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Actualisation...';

    // Actualiser la page
    setTimeout(() => {
        location.reload();
    }, 500);
}

function toggleAutoRefresh() {
    const checkbox = document.getElementById('autoRefresh');

    if (checkbox.checked) {
        // Démarrer l'auto-actualisation toutes les 30 secondes
        autoRefreshInterval = setInterval(() => {
            updateLastRefreshTime();
            location.reload();
        }, 30000);

        // Démarrer le compteur
        startCountdown();
    } else {
        // Arrêter l'auto-actualisation
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
        stopCountdown();
    }
}

function updateLastRefreshTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    document.getElementById('lastUpdate').textContent = timeString;
}

let countdownInterval;

function startCountdown() {
    let seconds = 30;
    const lastUpdateSpan = document.getElementById('lastUpdate');

    countdownInterval = setInterval(() => {
        seconds--;
        if (seconds > 0) {
            lastUpdateSpan.textContent = `Actualisation dans ${seconds}s`;
        } else {
            lastUpdateSpan.textContent = 'Actualisation...';
            seconds = 30; // Reset
        }
    }, 1000);
}

function stopCountdown() {
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }
    updateLastRefreshTime();
}

// Événements
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh toggle
    document.getElementById('autoRefresh').addEventListener('change', toggleAutoRefresh);

    // Mettre à jour l'heure au chargement
    updateLastRefreshTime();

    // Notification de nouvelles ouvertures
    const totalOpens = {{ stats.total_opens|default:0 }};
    if (localStorage.getItem('lastOpenCount')) {
        const lastCount = parseInt(localStorage.getItem('lastOpenCount'));
        if (totalOpens > lastCount) {
            showNotification(`🎉 ${totalOpens - lastCount} nouvelle(s) ouverture(s) détectée(s) !`);
        }
    }
    localStorage.setItem('lastOpenCount', totalOpens);
});

function showNotification(message) {
    // Créer une notification toast
    const toast = document.createElement('div');
    toast.className = 'toast position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        <div class="toast-header bg-success text-white">
            <strong class="me-auto">📊 Newsletter Stats</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    `;

    document.body.appendChild(toast);

    // Afficher le toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Supprimer après fermeture
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

// Nettoyage au déchargement de la page
window.addEventListener('beforeunload', function() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }
});
</script>
{% endblock %}
