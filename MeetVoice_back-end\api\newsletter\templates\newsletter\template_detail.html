{% extends 'newsletter/base_newsletter.html' %}
{% load static %}

{% block title %}{{ template.name }} - Template{% endblock %}

{% block extra_css %}
<!-- CSS responsive maintenant dans newsletter.css -->
{% endblock %}

{% block newsletter_content %}
<div class="template-detail-container">
    <!-- Header avec actions -->
    <div class="template-header mb-4">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{% url 'newsletter:template_list' %}">
                                <i class="fas fa-file-code me-1"></i>Templates
                            </a>
                        </li>
                        <li class="breadcrumb-item active">{{ template.name|truncatechars:30 }}</li>
                    </ol>
                </nav>
                
                <h2 class="mb-2">
                    <i class="fas fa-file-code me-2"></i>{{ template.name }}
                    {% if template.is_active %}
                        <span class="badge bg-success ms-2">Actif</span>
                    {% else %}
                        <span class="badge bg-secondary ms-2">Inactif</span>
                    {% endif %}
                </h2>
                
                <p class="text-muted mb-0">
                    <i class="fas fa-user me-1"></i>Créé par {{ template.created_by.username }}
                    le {{ template.created_at|date:"d/m/Y à H:i" }}
                </p>
                
                {% if template.updated_at != template.created_at %}
                    <p class="text-muted small">
                        <i class="fas fa-edit me-1"></i>Modifié le {{ template.updated_at|date:"d/m/Y à H:i" }}
                    </p>
                {% endif %}
            </div>
            
            <div class="template-actions">
                <div class="btn-group" role="group">
                    <a href="{% url 'newsletter:template_edit' template.pk %}" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-edit me-1"></i>Modifier
                    </a>
                    
                    <a href="{% url 'newsletter:template_preview' template.pk %}" 
                       target="_blank" class="btn btn-outline-info">
                        <i class="fas fa-external-link-alt me-1"></i>Aperçu
                    </a>
                    
                    <a href="{% url 'newsletter:campaign_create' %}?template={{ template.pk }}" 
                       class="btn btn-success">
                        <i class="fas fa-paper-plane me-1"></i>Utiliser
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Informations du template -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informations du Template
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="form-label fw-bold">Nom du template :</label>
                                <p class="mb-0">{{ template.name }}</p>
                            </div>
                            
                            <div class="info-item mb-3">
                                <label class="form-label fw-bold">Sujet de l'email :</label>
                                <p class="mb-0">{{ template.subject_template }}</p>
                            </div>
                            
                            {% if template.preview_text %}
                                <div class="info-item mb-3">
                                    <label class="form-label fw-bold">Texte de prévisualisation :</label>
                                    <p class="mb-0">{{ template.preview_text }}</p>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="form-label fw-bold">Statut :</label>
                                <p class="mb-0">
                                    {% if template.is_active %}
                                        <span class="badge bg-success">Actif</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Inactif</span>
                                    {% endif %}
                                </p>
                            </div>
                            
                            <div class="info-item mb-3">
                                <label class="form-label fw-bold">Créé par :</label>
                                <p class="mb-0">{{ template.created_by.username }}</p>
                            </div>
                            
                            <div class="info-item mb-3">
                                <label class="form-label fw-bold">Date de création :</label>
                                <p class="mb-0">{{ template.created_at|date:"d/m/Y H:i" }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statistiques
                    </h5>
                </div>
                <div class="card-body">
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Campagnes utilisant ce template :</span>
                            <strong>{{ template.campaign_set.count }}</strong>
                        </div>
                    </div>
                    
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Taille du header :</span>
                            <strong>{{ template.header_html|length }} caractères</strong>
                        </div>
                    </div>
                    
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Taille du contenu :</span>
                            <strong>{{ template.content_html|length }} caractères</strong>
                        </div>
                    </div>
                    
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Taille du footer :</span>
                            <strong>{{ template.footer_html|length }} caractères</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Aperçu du template -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-eye me-2"></i>Aperçu du Template
            </h5>
            <div class="preview-controls">
                <button class="btn btn-sm btn-outline-secondary" onclick="togglePreviewMode()">
                    <i class="fas fa-mobile-alt me-1"></i>Mobile
                </button>
                <a href="{% url 'newsletter:template_preview' template.pk %}"
                   target="_blank" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-external-link-alt me-1"></i>Plein écran
                </a>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="template-preview-container" id="previewContainer">
                <!-- Essayer d'abord avec iframe -->
                <iframe src="{% url 'newsletter:template_preview' template.pk %}"
                        class="template-preview-iframe"
                        id="previewIframe"
                        style="width: 100%; height: 600px; border: none;"
                        onload="handleIframeLoad()"
                        onerror="handleIframeError()">
                </iframe>

                <!-- Fallback si iframe ne fonctionne pas -->
                <div id="previewFallback" style="display: none; padding: 20px; text-align: center;">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        L'aperçu intégré n'est pas disponible.
                        <a href="{% url 'newsletter:template_preview' template.pk %}" target="_blank" class="alert-link">
                            Cliquez ici pour voir l'aperçu complet
                        </a>
                    </div>

                    <!-- Aperçu simplifié -->
                    <div class="simplified-preview">
                        <div class="preview-header" style="background: linear-gradient(135deg, #4e385f 0%, #2A1D34 100%); color: white; padding: 20px; text-align: center;">
                            <h3>🎙️ MeetVoice</h3>
                            <p>{{ template.subject_template }}</p>
                        </div>

                        <div class="preview-content" style="padding: 20px; background: white;">
                            <h4 style="color: #4e385f;">Aperçu du contenu</h4>
                            <p>{{ template.content_html|striptags|truncatechars:200 }}</p>

                            {% if template.preview_text %}
                                <p class="text-muted"><em>{{ template.preview_text }}</em></p>
                            {% endif %}
                        </div>

                        <div class="preview-footer" style="background: #f8f9fa; padding: 15px; text-align: center; border-top: 1px solid #dee2e6;">
                            <small class="text-muted">© 2025 MeetVoice. Tous droits réservés.</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted">
                    Aperçu responsive du template
                </small>
                <div class="preview-actions">
                    <button class="btn btn-sm btn-outline-info me-2" onclick="refreshPreview()">
                        <i class="fas fa-sync-alt me-1"></i>Actualiser
                    </button>
                    <a href="{% url 'newsletter:template_preview' template.pk %}"
                       target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-external-link-alt me-1"></i>Nouvel onglet
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sections du template -->
    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-header me-2"></i>Header HTML
                    </h6>
                </div>
                <div class="card-body">
                    <div class="code-preview">
                        <pre><code>{{ template.header_html|truncatechars:200 }}</code></pre>
                    </div>
                    {% if template.header_html|length > 200 %}
                        <small class="text-muted">... et {{ template.header_html|length|add:"-200" }} caractères de plus</small>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-align-left me-2"></i>Contenu HTML
                    </h6>
                </div>
                <div class="card-body">
                    <div class="code-preview">
                        <pre><code>{{ template.content_html|truncatechars:200 }}</code></pre>
                    </div>
                    {% if template.content_html|length > 200 %}
                        <small class="text-muted">... et {{ template.content_html|length|add:"-200" }} caractères de plus</small>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-footer me-2"></i>Footer HTML
                    </h6>
                </div>
                <div class="card-body">
                    <div class="code-preview">
                        <pre><code>{{ template.footer_html|truncatechars:200 }}</code></pre>
                    </div>
                    {% if template.footer_html|length > 200 %}
                        <small class="text-muted">... et {{ template.footer_html|length|add:"-200" }} caractères de plus</small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Styles CSS -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-paint-brush me-2"></i>Styles CSS
            </h6>
        </div>
        <div class="card-body">
            <div class="code-preview">
                <pre><code>{{ template.css_styles|truncatechars:500 }}</code></pre>
            </div>
            {% if template.css_styles|length > 500 %}
                <small class="text-muted">... et {{ template.css_styles|length|add:"-500" }} caractères de plus</small>
            {% endif %}
        </div>
    </div>
    
    <!-- Actions supplémentaires -->
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-tools me-2"></i>Actions Avancées
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <a href="{% url 'newsletter:template_edit' template.pk %}" 
                       class="btn btn-outline-primary w-100 mb-2">
                        <i class="fas fa-edit me-2"></i>Modifier le template
                    </a>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-secondary w-100 mb-2" onclick="duplicateTemplate({{ template.pk }})">
                        <i class="fas fa-copy me-2"></i>Dupliquer
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-info w-100 mb-2" onclick="exportTemplate({{ template.pk }})">
                        <i class="fas fa-download me-2"></i>Exporter
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-danger w-100 mb-2" onclick="deleteTemplate({{ template.pk }})">
                        <i class="fas fa-trash me-2"></i>Supprimer
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CSS déplacé vers newsletter.css -->

<script>
// Gestion de l'aperçu
let iframeLoaded = false;
let isMobileView = false;

function handleIframeLoad() {
    console.log('Iframe chargée avec succès');
    iframeLoaded = true;
    document.getElementById('previewFallback').style.display = 'none';
    document.getElementById('previewIframe').style.display = 'block';
}

function handleIframeError() {
    console.log('Erreur de chargement de l\'iframe');
    showFallbackPreview();
}

function showFallbackPreview() {
    document.getElementById('previewIframe').style.display = 'none';
    document.getElementById('previewFallback').style.display = 'block';
}

function togglePreviewMode() {
    const iframe = document.getElementById('previewIframe');
    const button = event.target.closest('button');

    if (!isMobileView) {
        // Passer en mode mobile
        iframe.style.width = '375px';
        iframe.style.margin = '0 auto';
        iframe.style.display = 'block';
        button.innerHTML = '<i class="fas fa-desktop me-1"></i>Desktop';
        isMobileView = true;
    } else {
        // Passer en mode desktop
        iframe.style.width = '100%';
        iframe.style.margin = '0';
        button.innerHTML = '<i class="fas fa-mobile-alt me-1"></i>Mobile';
        isMobileView = false;
    }
}

function refreshPreview() {
    const iframe = document.getElementById('previewIframe');
    iframe.src = iframe.src; // Recharger l'iframe
    showToast('Aperçu actualisé', 'success');
}

// Vérifier si l'iframe se charge après un délai
setTimeout(() => {
    if (!iframeLoaded) {
        console.log('Iframe non chargée après 3 secondes, affichage du fallback');
        showFallbackPreview();
    }
}, 3000);

function duplicateTemplate(templateId) {
    if (confirm('Voulez-vous dupliquer ce template ?')) {
        // Fonctionnalité à implémenter
        showToast('Fonctionnalité de duplication bientôt disponible', 'info');
    }
}

function exportTemplate(templateId) {
    // Fonctionnalité à implémenter
    showToast('Fonctionnalité d\'export bientôt disponible', 'info');
}

function deleteTemplate(templateId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce template ? Cette action est irréversible.')) {
        // Rediriger vers la page de suppression
        window.location.href = `/newsletter/templates/${templateId}/delete/`;
    }
}

function showToast(message, type = 'info') {
    // Fonction simple pour afficher des notifications
    const alertClass = type === 'info' ? 'alert-info' : 
                      type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    
    const toast = document.createElement('div');
    toast.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}
</script>
{% endblock %}
