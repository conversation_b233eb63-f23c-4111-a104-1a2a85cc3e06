{% extends 'newsletter/base_newsletter.html' %}
{% load static %}

{% block newsletter_content %}
<div class="template-form-header mb-4">
    <h2>
        <i class="fas fa-file-code me-2"></i>
        {% if template %}Modifier le template{% else %}<PERSON><PERSON><PERSON> un template{% endif %}
    </h2>
    <p class="text-muted">
        {% if template %}
            Modifiez votre template d'email réutilisable
        {% else %}
            Créez un nouveau template d'email avec éditeur de code
        {% endif %}
    </p>
</div>

<form method="post" class="newsletter-form">
    {% csrf_token %}
    
    <div class="row">
        <div class="col-md-8">
            <!-- Informations générales -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle me-2"></i>Informations générales</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger small">{{ form.name.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.subject_template.id_for_label }}" class="form-label">{{ form.subject_template.label }}</label>
                            {{ form.subject_template }}
                            {% if form.subject_template.errors %}
                                <div class="text-danger small">{{ form.subject_template.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">{{ form.subject_template.help_text }}</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.preview_text.id_for_label }}" class="form-label">{{ form.preview_text.label }}</label>
                        {{ form.preview_text }}
                        {% if form.preview_text.errors %}
                            <div class="text-danger small">{{ form.preview_text.errors.0 }}</div>
                        {% endif %}
                        <div class="form-text">{{ form.preview_text.help_text }}</div>
                    </div>
                </div>
            </div>
            
            <!-- Contenu HTML -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-code me-2"></i>Contenu HTML</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="{{ form.header_html.id_for_label }}" class="form-label">{{ form.header_html.label }}</label>
                        {{ form.header_html }}
                        {% if form.header_html.errors %}
                            <div class="text-danger small">{{ form.header_html.errors.0 }}</div>
                        {% endif %}
                        <div class="form-text">{{ form.header_html.help_text }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.content_html.id_for_label }}" class="form-label">{{ form.content_html.label }}</label>
                        {{ form.content_html }}
                        {% if form.content_html.errors %}
                            <div class="text-danger small">{{ form.content_html.errors.0 }}</div>
                        {% endif %}
                        <div class="form-text">{{ form.content_html.help_text }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.footer_html.id_for_label }}" class="form-label">{{ form.footer_html.label }}</label>
                        {{ form.footer_html }}
                        {% if form.footer_html.errors %}
                            <div class="text-danger small">{{ form.footer_html.errors.0 }}</div>
                        {% endif %}
                        <div class="form-text">{{ form.footer_html.help_text }}</div>
                    </div>
                </div>
            </div>
            
            <!-- Styles CSS -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-paint-brush me-2"></i>Styles CSS</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="{{ form.css_styles.id_for_label }}" class="form-label">{{ form.css_styles.label }}</label>
                        {{ form.css_styles }}
                        {% if form.css_styles.errors %}
                            <div class="text-danger small">{{ form.css_styles.errors.0 }}</div>
                        {% endif %}
                        <div class="form-text">{{ form.css_styles.help_text }}</div>
                    </div>
                </div>
            </div>
            
            <!-- Boutons d'action -->
            <div class="d-flex justify-content-between">
                <a href="{% url 'newsletter:template_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Retour
                </a>
                <div>
                    {% if template %}
                        <a href="{% url 'newsletter:template_preview' template.pk %}" 
                           target="_blank" class="btn btn-outline-info me-2">
                            <i class="fas fa-eye me-1"></i>Aperçu actuel
                        </a>
                    {% endif %}
                    <button type="button" id="previewBtn" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search me-1"></i>Prévisualiser
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        {% if template %}Mettre à jour{% else %}Créer le template{% endif %}
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Aide et variables -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6><i class="fas fa-question-circle me-2"></i>Variables disponibles</h6>
                </div>
                <div class="card-body">
                    <div class="small">
                        <p><strong>Dans le contenu :</strong></p>
                        <ul class="list-unstyled">
                            <li><code>{% verbatim %}{{ ai_generated_content }}{% endverbatim %}</code> - Contenu IA</li>
                            <li><code>{% verbatim %}{{ recipient_name }}{% endverbatim %}</code> - Nom du destinataire</li>
                            <li><code>{% verbatim %}{{ campaign_name }}{% endverbatim %}</code> - Nom de la campagne</li>
                            <li><code>{% verbatim %}{{ date }}{% endverbatim %}</code> - Date d'envoi</li>
                        </ul>
                        
                        <p><strong>Dans le footer :</strong></p>
                        <ul class="list-unstyled">
                            <li><code>{% verbatim %}{{ unsubscribe_url }}{% endverbatim %}</code> - Lien de désinscription</li>
                            <li><code>{% verbatim %}{{ tracking_url }}{% endverbatim %}</code> - Pixel de tracking</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Conseils de compatibilité -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6><i class="fas fa-lightbulb me-2"></i>Conseils de compatibilité</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-1"></i>
                            Utilisez des styles inline
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-1"></i>
                            Évitez les CSS complexes
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-1"></i>
                            Testez sur Gmail, Outlook, Yahoo
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-1"></i>
                            Largeur max 600px
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-1"></i>
                            Images avec alt text
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Aperçu en temps réel -->
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-eye me-2"></i>Aperçu en temps réel</h6>
                </div>
                <div class="card-body p-0">
                    <div id="livePreview" style="height: 300px; overflow-y: auto; border: 1px solid #dee2e6;">
                        <div class="text-center text-muted p-4">
                            <i class="fas fa-eye fa-2x mb-2"></i>
                            <p>L'aperçu apparaîtra ici</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Modal de prévisualisation -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Aperçu du template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <iframe id="previewFrame" style="width: 100%; height: 600px; border: none;"></iframe>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Bouton de prévisualisation
    document.getElementById('previewBtn').addEventListener('click', function() {
        generatePreview();
    });
    
    // Aperçu en temps réel (debounced)
    const editors = document.querySelectorAll('.code-editor');
    editors.forEach(editor => {
        editor.addEventListener('input', debounce(updateLivePreview, 1000));
    });
});

function generatePreview() {
    const headerHtml = document.getElementById('{{ form.header_html.id_for_label }}').value;
    const contentHtml = document.getElementById('{{ form.content_html.id_for_label }}').value;
    const footerHtml = document.getElementById('{{ form.footer_html.id_for_label }}').value;
    const cssStyles = document.getElementById('{{ form.css_styles.id_for_label }}').value;
    
    const fullHtml = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>${cssStyles}</style>
        </head>
        <body>
            ${headerHtml}
            <div class="content">
                ${contentHtml.replace('{{ ai_generated_content }}', '<h2>Contenu généré par l\'IA</h2><p>Ceci est un exemple de contenu qui sera généré par l\'intelligence artificielle selon vos instructions.</p>')}
            </div>
            ${footerHtml.replace('{{ unsubscribe_url }}', '#').replace('{{ tracking_url }}', '#')}
        </body>
        </html>
    `;
    
    const previewFrame = document.getElementById('previewFrame');
    previewFrame.srcdoc = fullHtml;
    
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
}

function updateLivePreview() {
    // Mise à jour de l'aperçu en temps réel
    const livePreview = document.getElementById('livePreview');
    // Implémentation simplifiée
    livePreview.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-sync fa-spin"></i><p>Mise à jour...</p></div>';
}
</script>
{% endblock %}
