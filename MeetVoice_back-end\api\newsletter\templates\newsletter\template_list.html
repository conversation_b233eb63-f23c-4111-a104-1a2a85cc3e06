{% extends 'newsletter/base_newsletter.html' %}
{% load static %}

{% block newsletter_content %}
<div class="template-list-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-file-code me-2"></i>Templates d'Email</h2>
            <p class="text-muted">Gérez vos modèles d'email réutilisables</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{% url 'newsletter:ai_generator' %}" class="btn btn-success">
                <i class="fas fa-robot me-1"></i>Générateur IA
            </a>
            <a href="{% url 'newsletter:template_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Nouveau template
            </a>
        </div>
    </div>
</div>

<!-- Liste des templates -->
<div class="row">
    {% for template in templates %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card template-card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">{{ template.name|truncatechars:25 }}</h6>
                    {% if template.is_active %}
                        <span class="badge bg-success">Actif</span>
                    {% else %}
                        <span class="badge bg-secondary">Inactif</span>
                    {% endif %}
                </div>
                
                <!-- Aperçu du template -->
                <div class="template-preview">
                    <iframe src="{% url 'newsletter:template_preview' template.pk %}" 
                            style="width: 100%; height: 200px; border: none; transform: scale(0.3); transform-origin: top left; pointer-events: none;">
                    </iframe>
                </div>
                
                <div class="card-body">
                    <p class="card-text text-muted small mb-2">
                        <strong>Sujet:</strong> {{ template.subject_template|truncatechars:40 }}
                    </p>
                    
                    {% if template.preview_text %}
                        <p class="card-text text-muted small mb-2">
                            <strong>Aperçu:</strong> {{ template.preview_text|truncatechars:50 }}
                        </p>
                    {% endif %}
                    
                    <div class="template-meta small text-muted">
                        <div><i class="fas fa-user me-1"></i>{{ template.created_by.username }}</div>
                        <div><i class="fas fa-calendar me-1"></i>{{ template.created_at|date:"d/m/Y H:i" }}</div>
                        <div><i class="fas fa-edit me-1"></i>{{ template.updated_at|date:"d/m/Y H:i" }}</div>
                    </div>
                </div>
                
                <div class="card-footer">
                    <div class="template-actions d-flex flex-wrap gap-1">
                        <a href="{% url 'newsletter:template_detail' template.pk %}" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye me-1"></i>Voir
                        </a>
                        
                        <a href="{% url 'newsletter:template_edit' template.pk %}" 
                           class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-edit me-1"></i>Modifier
                        </a>
                        
                        <a href="{% url 'newsletter:template_preview' template.pk %}" 
                           target="_blank" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-external-link-alt me-1"></i>Aperçu
                        </a>
                        
                        <a href="{% url 'newsletter:campaign_create' %}?template={{ template.pk }}" 
                           class="btn btn-sm btn-outline-success">
                            <i class="fas fa-paper-plane me-1"></i>Utiliser
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-file-code fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">Aucun template trouvé</h4>
                <p class="text-muted">Commencez par créer votre premier template d'email.</p>
                <a href="{% url 'newsletter:template_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Créer un template
                </a>
            </div>
        </div>
    {% endfor %}
</div>

<!-- Templates par défaut -->
<div class="card mt-4">
    <div class="card-header">
        <h6><i class="fas fa-star me-2"></i>Templates recommandés</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="template-suggestion p-3 border rounded">
                    <h6>Newsletter Moderne</h6>
                    <p class="small text-muted">Design moderne avec dégradés et animations CSS</p>
                    <button class="btn btn-sm btn-outline-primary" onclick="createFromTemplate('moderne')">
                        <i class="fas fa-plus me-1"></i>Créer
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="template-suggestion p-3 border rounded">
                    <h6>Newsletter Minimaliste</h6>
                    <p class="small text-muted">Design épuré et professionnel</p>
                    <button class="btn btn-sm btn-outline-primary" onclick="createFromTemplate('minimal')">
                        <i class="fas fa-plus me-1"></i>Créer
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="template-suggestion p-3 border rounded">
                    <h6>Newsletter Promotionnelle</h6>
                    <p class="small text-muted">Optimisé pour les offres et promotions</p>
                    <button class="btn btn-sm btn-outline-primary" onclick="createFromTemplate('promo')">
                        <i class="fas fa-plus me-1"></i>Créer
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="card mt-4">
    <div class="card-header">
        <h6><i class="fas fa-bolt me-2"></i>Actions rapides</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <a href="{% url 'newsletter:template_create' %}" class="btn btn-outline-primary w-100 mb-2">
                    <i class="fas fa-plus me-2"></i>Nouveau template
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'newsletter:campaign_list' %}" class="btn btn-outline-secondary w-100 mb-2">
                    <i class="fas fa-paper-plane me-2"></i>Voir les campagnes
                </a>
            </div>
            <div class="col-md-3">
                <button class="btn btn-outline-info w-100 mb-2" onclick="importTemplate()">
                    <i class="fas fa-upload me-2"></i>Importer template
                </button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-outline-success w-100 mb-2" onclick="exportTemplates()">
                    <i class="fas fa-download me-2"></i>Exporter templates
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function createFromTemplate(type) {
    // Rediriger vers la création avec un type prédéfini
    window.location.href = "{% url 'newsletter:template_create' %}?type=" + type;
}

function importTemplate() {
    // Fonctionnalité d'import (à implémenter)
    showToast('Fonctionnalité d\'import bientôt disponible', 'info');
}

function exportTemplates() {
    // Fonctionnalité d'export (à implémenter)
    showToast('Fonctionnalité d\'export bientôt disponible', 'info');
}
</script>
{% endblock %}
