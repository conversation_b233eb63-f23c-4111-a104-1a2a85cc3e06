from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from . import views

app_name = 'newsletter'

# Configuration du router DRF
router = DefaultRouter()
router.register(r'templates', views.EmailTemplateViewSet)
router.register(r'campaigns', views.CampaignViewSet)
router.register(r'settings', views.NewsletterSettingsViewSet)

urlpatterns = [
    # API REST
    path('api/', include(router.urls)),

    # Dashboard et vues principales
    path('', views.dashboard, name='dashboard'),
    path('stats/', views.simple_stats, name='simple_stats'),

    # Gestion des campagnes
    path('campaigns/', views.campaign_list, name='campaign_list'),
    path('campaigns/create/', views.campaign_create, name='campaign_create'),
    path('campaigns/<int:pk>/', views.campaign_detail, name='campaign_detail'),
    path('campaigns/<int:pk>/edit/', views.campaign_edit, name='campaign_edit'),
    path('campaigns/<int:pk>/delete/', views.campaign_delete, name='campaign_delete'),
    path('campaigns/<int:pk>/send/', views.campaign_send, name='campaign_send'),
    path('campaigns/<int:pk>/preview/', views.campaign_preview, name='campaign_preview'),
    
    # Gestion des templates
    path('templates/', views.template_list, name='template_list'),
    path('templates/create/', views.template_create, name='template_create'),
    path('templates/<int:pk>/', views.template_detail, name='template_detail'),
    path('templates/<int:pk>/edit/', views.template_edit, name='template_edit'),
    path('templates/<int:pk>/delete/', views.template_delete, name='template_delete'),
    path('templates/<int:pk>/preview/', views.template_preview, name='template_preview'),
    path('templates/delete-all/', views.delete_all_templates, name='delete_all_templates'),
    path('templates/delete-all/', views.delete_all_templates, name='delete_all_templates'),
    
    # Génération IA
    path('generate-content/', views.generate_content, name='generate_content'),

    # Générateur de Templates IA
    path('ai-generator/', views.ai_template_generator, name='ai_generator'),
    path('ai-generator/preview/', views.ai_template_preview, name='ai_preview'),
    path('ai-generator/variations/', views.ai_template_variations, name='ai_variations'),
    path('ai-generator/seasonal/', views.ai_seasonal_template, name='ai_seasonal'),
    path('ai-generator/welcome/', views.ai_welcome_series, name='ai_welcome'),
    
    # Tracking
    path('track/open/<str:hash>/', views.track_open, name='track_open'),
    path('track/click/<str:hash>/', views.track_click, name='track_click'),
    
    # Statistiques et KPI
    path('stats/', views.stats_dashboard, name='stats_dashboard'),
    path('stats/campaign/<int:pk>/', views.campaign_stats, name='campaign_stats'),
    path('stats/export/', views.export_stats, name='export_stats'),
    
    # API pour les graphiques
    path('api/stats/overview/', views.api_stats_overview, name='api_stats_overview'),
    path('api/stats/campaign/<int:pk>/', views.api_campaign_stats, name='api_campaign_stats'),
    
    # Paramètres
    path('settings/', views.newsletter_settings, name='settings'),
]
