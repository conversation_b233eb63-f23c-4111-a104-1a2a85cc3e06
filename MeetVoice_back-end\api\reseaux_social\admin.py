from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import ReseauxSocialPost, PostTemplate


@admin.register(ReseauxSocialPost)
class ReseauxSocialPostAdmin(admin.ModelAdmin):
    list_display = [
        'titre', 'plateforme', 'statut', 'type_contenu',
        'auteur', 'date_creation', 'date_programmee', 'actions_admin'
    ]
    list_filter = [
        'statut', 'plateforme', 'type_contenu', 'date_creation', 'auteur'
    ]
    search_fields = ['titre', 'contenu', 'hashtags']
    readonly_fields = [
        'date_creation', 'date_modification', 'date_publie',
        'post_id_facebook', 'post_id_instagram', 'post_id_twitter', 'post_id_linkedin',
        'vues', 'likes', 'partages', 'commentaires'
    ]

    fieldsets = (
        ('Informations de base', {
            'fields': ('titre', 'contenu', 'hashtags', 'auteur')
        }),
        ('Configuration', {
            'fields': ('plateforme', 'type_contenu', 'statut')
        }),
        ('Médias', {
            'fields': ('image_url', 'image_prompt', 'video_url', 'lien_externe'),
            'classes': ('collapse',)
        }),
        ('Programmation', {
            'fields': ('date_programmee', 'date_publie'),
            'classes': ('collapse',)
        }),
        ('IDs de publication', {
            'fields': ('post_id_facebook', 'post_id_instagram', 'post_id_twitter', 'post_id_linkedin'),
            'classes': ('collapse',)
        }),
        ('Statistiques', {
            'fields': ('vues', 'likes', 'partages', 'commentaires'),
            'classes': ('collapse',)
        }),
        ('Métadonnées', {
            'fields': ('date_creation', 'date_modification', 'erreur_publication'),
            'classes': ('collapse',)
        }),
    )

    def actions_admin(self, obj):
        """Affiche les actions disponibles pour le post"""
        actions = []

        if obj.peut_etre_publie:
            publish_url = reverse('admin:reseaux_social_reseauxsocialpost_change', args=[obj.pk])
            actions.append(f'<a href="{publish_url}" class="button">Publier</a>')

        if obj.statut == 'publie':
            actions.append('<span style="color: green;">✓ Publié</span>')
        elif obj.statut == 'echec':
            actions.append('<span style="color: red;">✗ Échec</span>')

        return format_html(' '.join(actions))

    actions_admin.short_description = 'Actions'

    def save_model(self, request, obj, form, change):
        if not change:  # Nouveau post
            obj.auteur = request.user
        super().save_model(request, obj, form, change)


@admin.register(PostTemplate)
class PostTemplateAdmin(admin.ModelAdmin):
    list_display = ['nom', 'plateforme_recommandee', 'actif', 'date_creation']
    list_filter = ['plateforme_recommandee', 'actif', 'date_creation']
    search_fields = ['nom', 'description', 'contenu_template']

    fieldsets = (
        ('Informations de base', {
            'fields': ('nom', 'description', 'actif')
        }),
        ('Contenu', {
            'fields': ('contenu_template', 'hashtags_defaut', 'plateforme_recommandee')
        }),
    )
