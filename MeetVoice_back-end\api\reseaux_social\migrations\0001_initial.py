# Generated by Django 5.2.3 on 2025-06-22 05:14

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PostTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100, verbose_name='Nom du template')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('contenu_template', models.TextField(help_text='Utilisez {variable} pour les variables dynamiques', verbose_name='Contenu du template')),
                ('hashtags_defaut', models.CharField(blank=True, max_length=500, verbose_name='Hashtags par défaut')),
                ('plateforme_recommandee', models.CharField(choices=[('facebook', 'Facebook'), ('instagram', 'Instagram'), ('twitter', 'Twitter'), ('linkedin', 'LinkedIn'), ('tiktok', 'TikTok'), ('youtube', 'YouTube'), ('all', 'Toutes les plateformes')], default='all', max_length=20, verbose_name='Plateforme recommandée')),
                ('actif', models.BooleanField(default=True, verbose_name='Template actif')),
                ('date_creation', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
            ],
            options={
                'verbose_name': 'Template de Post',
                'verbose_name_plural': 'Templates de Posts',
                'ordering': ['nom'],
            },
        ),
        migrations.CreateModel(
            name='ReseauxSocialPost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('titre', models.CharField(help_text='Titre interne pour identifier le post', max_length=200, verbose_name='Titre du post')),
                ('contenu', models.TextField(help_text='Texte du post (max 2200 caractères pour LinkedIn)', validators=[django.core.validators.MaxLengthValidator(2200)], verbose_name='Contenu du post')),
                ('hashtags', models.CharField(blank=True, help_text='Hashtags séparés par des espaces (ex: #meetvoice #rencontres #vocal)', max_length=500, verbose_name='Hashtags')),
                ('plateforme', models.CharField(choices=[('facebook', 'Facebook'), ('instagram', 'Instagram'), ('twitter', 'Twitter'), ('linkedin', 'LinkedIn'), ('tiktok', 'TikTok'), ('youtube', 'YouTube'), ('all', 'Toutes les plateformes')], default='all', max_length=20, verbose_name='Plateforme cible')),
                ('type_contenu', models.CharField(choices=[('texte', 'Texte seul'), ('image', 'Image + Texte'), ('video', 'Vidéo + Texte'), ('lien', 'Lien + Texte'), ('carrousel', "Carrousel d'images")], default='texte', max_length=20, verbose_name='Type de contenu')),
                ('image_url', models.URLField(blank=True, help_text="URL de l'image générée ou uploadée", null=True, verbose_name="URL de l'image")),
                ('image_prompt', models.CharField(blank=True, help_text="Prompt utilisé pour générer l'image via Pollinations.ai", max_length=500, verbose_name="Prompt pour génération d'image")),
                ('video_url', models.URLField(blank=True, null=True, verbose_name='URL de la vidéo')),
                ('lien_externe', models.URLField(blank=True, help_text='Lien à partager dans le post', null=True, verbose_name='Lien externe')),
                ('statut', models.CharField(choices=[('brouillon', 'Brouillon'), ('programme', 'Programmé'), ('publie', 'Publié'), ('echec', 'Échec de publication'), ('archive', 'Archivé')], default='brouillon', max_length=20, verbose_name='Statut')),
                ('date_programmee', models.DateTimeField(blank=True, null=True, verbose_name='Date de publication programmée')),
                ('date_publie', models.DateTimeField(blank=True, null=True, verbose_name='Date de publication effective')),
                ('date_creation', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('date_modification', models.DateTimeField(auto_now=True, verbose_name='Dernière modification')),
                ('post_id_facebook', models.CharField(blank=True, max_length=100, null=True, verbose_name='ID du post Facebook')),
                ('post_id_instagram', models.CharField(blank=True, max_length=100, null=True, verbose_name='ID du post Instagram')),
                ('post_id_twitter', models.CharField(blank=True, max_length=100, null=True, verbose_name='ID du post Twitter')),
                ('post_id_linkedin', models.CharField(blank=True, max_length=100, null=True, verbose_name='ID du post LinkedIn')),
                ('vues', models.PositiveIntegerField(default=0, verbose_name='Nombre de vues')),
                ('likes', models.PositiveIntegerField(default=0, verbose_name='Nombre de likes')),
                ('partages', models.PositiveIntegerField(default=0, verbose_name='Nombre de partages')),
                ('commentaires', models.PositiveIntegerField(default=0, verbose_name='Nombre de commentaires')),
                ('erreur_publication', models.TextField(blank=True, help_text="Détails de l'erreur en cas d'échec de publication", verbose_name="Message d'erreur")),
                ('auteur', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='posts_reseaux_social', to=settings.AUTH_USER_MODEL, verbose_name='Auteur')),
            ],
            options={
                'verbose_name': 'Post Réseaux Sociaux',
                'verbose_name_plural': 'Posts Réseaux Sociaux',
                'ordering': ['-date_creation'],
                'indexes': [models.Index(fields=['statut', 'plateforme'], name='reseaux_soc_statut_2776d2_idx'), models.Index(fields=['date_programmee'], name='reseaux_soc_date_pr_f39ce6_idx'), models.Index(fields=['auteur', 'date_creation'], name='reseaux_soc_auteur__5643c9_idx')],
            },
        ),
    ]
