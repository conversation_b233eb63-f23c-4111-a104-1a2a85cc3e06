# Generated by Django 5.2.3 on 2025-06-23 21:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('reseaux_social', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='reseauxsocialpost',
            name='image_file',
            field=models.ImageField(blank=True, help_text='Image stockée localement dans media/reseaux_social/', null=True, upload_to='reseaux_social/', verbose_name='Fichier image local'),
        ),
        migrations.AlterField(
            model_name='reseauxsocialpost',
            name='image_url',
            field=models.URLField(blank=True, help_text="URL de l'image générée via Pollinations.ai (fallback)", null=True, verbose_name="URL de l'image externe"),
        ),
    ]
