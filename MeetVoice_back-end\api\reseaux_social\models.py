from django.db import models
from django.conf import settings
from django.utils import timezone
from django.core.validators import MaxLengthValidator


class ReseauxSocialPost(models.Model):
    """Modèle pour gérer les posts sur les réseaux sociaux"""

    PLATEFORME_CHOICES = [
        ('facebook', 'Facebook'),
        ('instagram', 'Instagram'),
        ('twitter', 'Twitter'),
        ('linkedin', 'LinkedIn'),
        ('tiktok', 'TikTok'),
        ('youtube', 'YouTube'),
        ('all', 'Toutes les plateformes'),
    ]

    STATUT_CHOICES = [
        ('brouillon', 'Brouillon'),
        ('programme', 'Programmé'),
        ('publie', 'Publié'),
        ('echec', 'Échec de publication'),
        ('archive', 'Archivé'),
    ]

    TYPE_CONTENU_CHOICES = [
        ('texte', 'Texte seul'),
        ('image', 'Image + Texte'),
        ('video', 'Vidéo + Texte'),
        ('lien', 'Lien + Texte'),
        ('carrousel', 'Carrousel d\'images'),
    ]

    # Informations de base
    titre = models.CharField(
        max_length=200,
        verbose_name="Titre du post",
        help_text="Titre interne pour identifier le post"
    )

    contenu = models.TextField(
        verbose_name="Contenu du post",
        validators=[MaxLengthValidator(2200)],  # Limite pour LinkedIn
        help_text="Texte du post (max 2200 caractères pour LinkedIn)"
    )

    hashtags = models.CharField(
        max_length=500,
        blank=True,
        verbose_name="Hashtags",
        help_text="Hashtags séparés par des espaces (ex: #meetvoice #rencontres #vocal)"
    )

    # Plateforme et type
    plateforme = models.CharField(
        max_length=20,
        choices=PLATEFORME_CHOICES,
        default='all',
        verbose_name="Plateforme cible"
    )

    type_contenu = models.CharField(
        max_length=20,
        choices=TYPE_CONTENU_CHOICES,
        default='texte',
        verbose_name="Type de contenu"
    )

    # Médias
    image_url = models.URLField(
        blank=True,
        null=True,
        verbose_name="URL de l'image externe",
        help_text="URL de l'image générée via Pollinations.ai (fallback)"
    )

    image_file = models.ImageField(
        upload_to='reseaux_social/',
        blank=True,
        null=True,
        verbose_name="Fichier image local",
        help_text="Image stockée localement dans media/reseaux_social/"
    )

    image_prompt = models.CharField(
        max_length=500,
        blank=True,
        verbose_name="Prompt pour génération d'image",
        help_text="Prompt utilisé pour générer l'image via Pollinations.ai"
    )

    video_url = models.URLField(
        blank=True,
        null=True,
        verbose_name="URL de la vidéo"
    )

    lien_externe = models.URLField(
        blank=True,
        null=True,
        verbose_name="Lien externe",
        help_text="Lien à partager dans le post"
    )

    # Statut et programmation
    statut = models.CharField(
        max_length=20,
        choices=STATUT_CHOICES,
        default='brouillon',
        verbose_name="Statut"
    )

    date_programmee = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name="Date de publication programmée"
    )

    date_publie = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name="Date de publication effective"
    )

    # Métadonnées
    auteur = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name="Auteur",
        related_name="posts_reseaux_social"
    )

    date_creation = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Date de création"
    )

    date_modification = models.DateTimeField(
        auto_now=True,
        verbose_name="Dernière modification"
    )

    # Données de publication
    post_id_facebook = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="ID du post Facebook"
    )

    post_id_instagram = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="ID du post Instagram"
    )

    post_id_twitter = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="ID du post Twitter"
    )

    post_id_linkedin = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="ID du post LinkedIn"
    )

    # Statistiques
    vues = models.PositiveIntegerField(
        default=0,
        verbose_name="Nombre de vues"
    )

    likes = models.PositiveIntegerField(
        default=0,
        verbose_name="Nombre de likes"
    )

    partages = models.PositiveIntegerField(
        default=0,
        verbose_name="Nombre de partages"
    )

    commentaires = models.PositiveIntegerField(
        default=0,
        verbose_name="Nombre de commentaires"
    )

    # Messages d'erreur
    erreur_publication = models.TextField(
        blank=True,
        verbose_name="Message d'erreur",
        help_text="Détails de l'erreur en cas d'échec de publication"
    )

    class Meta:
        verbose_name = "Post Réseaux Sociaux"
        verbose_name_plural = "Posts Réseaux Sociaux"
        ordering = ['-date_creation']
        indexes = [
            models.Index(fields=['statut', 'plateforme']),
            models.Index(fields=['date_programmee']),
            models.Index(fields=['auteur', 'date_creation']),
        ]

    def __str__(self):
        return f"{self.titre} - {self.get_plateforme_display()} ({self.get_statut_display()})"

    @property
    def contenu_complet(self):
        """Retourne le contenu avec les hashtags"""
        contenu = self.contenu
        if self.hashtags:
            contenu += f"\n\n{self.hashtags}"
        return contenu

    @property
    def image_display_url(self):
        """Retourne l'URL d'affichage de l'image (locale prioritaire, sinon externe)"""
        if self.image_file:
            return self.image_file.url
        elif self.image_url:
            return self.image_url
        return None

    @property
    def has_image(self):
        """Vérifie si le post a une image (locale ou externe)"""
        return bool(self.image_file or self.image_url)

    @property
    def est_programme(self):
        """Vérifie si le post est programmé pour plus tard"""
        if self.date_programmee and self.statut == 'programme':
            return self.date_programmee > timezone.now()
        return False

    @property
    def peut_etre_publie(self):
        """Vérifie si le post peut être publié"""
        return self.statut in ['brouillon', 'programme', 'echec']

    def marquer_comme_publie(self, post_ids=None):
        """Marque le post comme publié avec les IDs des plateformes"""
        self.statut = 'publie'
        self.date_publie = timezone.now()

        if post_ids:
            if 'facebook' in post_ids:
                self.post_id_facebook = post_ids['facebook']
            if 'instagram' in post_ids:
                self.post_id_instagram = post_ids['instagram']
            if 'twitter' in post_ids:
                self.post_id_twitter = post_ids['twitter']
            if 'linkedin' in post_ids:
                self.post_id_linkedin = post_ids['linkedin']

        self.save()

    def marquer_comme_echec(self, erreur):
        """Marque le post comme ayant échoué avec le message d'erreur"""
        self.statut = 'echec'
        self.erreur_publication = erreur
        self.save()

    def delete(self, *args, **kwargs):
        """Supprimer le post et son image locale"""
        import os
        from django.conf import settings

        # Supprimer l'image locale si elle existe
        if self.image_file:
            try:
                # Construire le chemin complet du fichier
                image_path = os.path.join(settings.MEDIA_ROOT, str(self.image_file))

                # Supprimer le fichier s'il existe
                if os.path.exists(image_path):
                    os.remove(image_path)
                    print(f"✅ Image supprimée: {self.image_file}")
                else:
                    print(f"⚠️ Image non trouvée: {image_path}")

            except Exception as e:
                print(f"❌ Erreur suppression image: {e}")

        # Supprimer le post
        super().delete(*args, **kwargs)


class PostTemplate(models.Model):
    """Modèle pour les templates de posts réutilisables"""

    nom = models.CharField(
        max_length=100,
        verbose_name="Nom du template"
    )

    description = models.TextField(
        blank=True,
        verbose_name="Description"
    )

    contenu_template = models.TextField(
        verbose_name="Contenu du template",
        help_text="Utilisez {variable} pour les variables dynamiques"
    )

    hashtags_defaut = models.CharField(
        max_length=500,
        blank=True,
        verbose_name="Hashtags par défaut"
    )

    plateforme_recommandee = models.CharField(
        max_length=20,
        choices=ReseauxSocialPost.PLATEFORME_CHOICES,
        default='all',
        verbose_name="Plateforme recommandée"
    )

    actif = models.BooleanField(
        default=True,
        verbose_name="Template actif"
    )

    date_creation = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Date de création"
    )

    class Meta:
        verbose_name = "Template de Post"
        verbose_name_plural = "Templates de Posts"
        ordering = ['nom']

    def __str__(self):
        return self.nom
