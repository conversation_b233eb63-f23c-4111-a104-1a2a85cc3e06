from rest_framework import serializers
from django.contrib.auth.models import User
from .models import ReseauxSocialPost


class AuthorSerializer(serializers.ModelSerializer):
    """Serializer pour les informations de l'auteur"""
    
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'full_name']
    
    def get_full_name(self, obj):
        """Retourne le nom complet de l'auteur"""
        if obj.first_name and obj.last_name:
            return f"{obj.first_name} {obj.last_name}"
        return obj.username


class PostSerializer(serializers.ModelSerializer):
    """Serializer pour les posts des réseaux sociaux"""

    auteur_nom = serializers.SerializerMethodField()
    plateformes_list = serializers.SerializerMethodField()
    engagement_rate = serializers.SerializerMethodField()

    class Meta:
        model = ReseauxSocialPost
        fields = [
            'id',
            'titre',
            'contenu',
            'auteur',
            'auteur_nom',
            'plateformes',
            'plateformes_list',
            'type_contenu',
            'image_url',
            'image_file',
            'image_prompt',
            'video_url',
            'statut',
            'date_publication_prevue',
            'date_publication_reelle',
            'date_creation',
            'date_modification',
            'post_id_facebook',
            'post_id_instagram',
            'post_id_twitter',
            'post_id_linkedin',
            'vues',
            'likes',
            'partages',
            'commentaires_count',
            'engagement_rate',
            'hashtags',
            'mentions',
            'localisation',
            'audience_cible',
            'budget_promotion',
            'objectif_campagne',
            'notes_internes'
        ]
        read_only_fields = [
            'id', 'date_creation', 'date_modification', 'date_publication_reelle',
            'post_id_facebook', 'post_id_instagram', 'post_id_twitter', 'post_id_linkedin',
            'vues', 'likes', 'partages', 'commentaires_count'
        ]
    
    def get_auteur_nom(self, obj):
        """Retourne le nom complet de l'auteur"""
        if obj.auteur:
            if obj.auteur.first_name and obj.auteur.last_name:
                return f"{obj.auteur.first_name} {obj.auteur.last_name}"
            return obj.auteur.username
        return None
    
    def get_plateformes_list(self, obj):
        """Retourne la liste des plateformes"""
        if obj.plateformes:
            return [p.strip() for p in obj.plateformes.split(',') if p.strip()]
        return []
    
    def get_engagement_rate(self, obj):
        """Calcule le taux d'engagement"""
        if obj.vues > 0:
            engagement = obj.likes + obj.partages + obj.commentaires_count
            return round((engagement / obj.vues) * 100, 2)
        return 0


class PostListSerializer(serializers.ModelSerializer):
    """Serializer simplifié pour la liste des posts"""

    auteur_nom = serializers.SerializerMethodField()
    plateformes_list = serializers.SerializerMethodField()
    statut_display = serializers.SerializerMethodField()

    class Meta:
        model = ReseauxSocialPost
        fields = [
            'id',
            'titre',
            'auteur_nom',
            'plateformes_list',
            'type_contenu',
            'statut',
            'statut_display',
            'date_publication_prevue',
            'date_publication_reelle',
            'vues',
            'likes',
            'partages',
            'commentaires_count',
            'date_creation'
        ]
    
    def get_auteur_nom(self, obj):
        """Retourne le nom complet de l'auteur"""
        if obj.auteur:
            if obj.auteur.first_name and obj.auteur.last_name:
                return f"{obj.auteur.first_name} {obj.auteur.last_name}"
            return obj.auteur.username
        return None
    
    def get_plateformes_list(self, obj):
        """Retourne la liste des plateformes"""
        if obj.plateformes:
            return [p.strip() for p in obj.plateformes.split(',') if p.strip()]
        return []
    
    def get_statut_display(self, obj):
        """Retourne le statut en français"""
        return obj.get_statut_display()


class PostCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création de posts"""

    class Meta:
        model = ReseauxSocialPost
        fields = [
            'titre',
            'contenu',
            'plateformes',
            'type_contenu',
            'image_prompt',
            'video_url',
            'date_publication_prevue',
            'hashtags',
            'mentions',
            'localisation',
            'audience_cible',
            'budget_promotion',
            'objectif_campagne',
            'notes_internes'
        ]
    
    def validate_titre(self, value):
        """Validation du titre"""
        if len(value.strip()) < 3:
            raise serializers.ValidationError("Le titre doit contenir au moins 3 caractères.")
        return value.strip()
    
    def validate_contenu(self, value):
        """Validation du contenu"""
        if len(value.strip()) < 10:
            raise serializers.ValidationError("Le contenu doit contenir au moins 10 caractères.")
        
        # Vérification de la limite de caractères pour les réseaux sociaux
        if len(value) > 2200:
            raise serializers.ValidationError("Le contenu ne peut pas dépasser 2200 caractères.")
        
        return value.strip()
    
    def validate_plateformes(self, value):
        """Validation des plateformes"""
        if not value:
            raise serializers.ValidationError("Au moins une plateforme doit être sélectionnée.")
        
        plateformes_valides = ['facebook', 'instagram', 'twitter', 'linkedin']
        plateformes_list = [p.strip().lower() for p in value.split(',') if p.strip()]
        
        for plateforme in plateformes_list:
            if plateforme not in plateformes_valides:
                raise serializers.ValidationError(f"Plateforme non valide: {plateforme}")
        
        return ', '.join(plateformes_list)
    
    def validate_budget_promotion(self, value):
        """Validation du budget de promotion"""
        if value is not None and value < 0:
            raise serializers.ValidationError("Le budget de promotion ne peut pas être négatif.")
        return value


class PostUpdateSerializer(serializers.ModelSerializer):
    """Serializer pour la mise à jour de posts"""

    class Meta:
        model = ReseauxSocialPost
        fields = [
            'titre',
            'contenu',
            'plateformes',
            'type_contenu',
            'image_prompt',
            'video_url',
            'statut',
            'date_publication_prevue',
            'hashtags',
            'mentions',
            'localisation',
            'audience_cible',
            'budget_promotion',
            'objectif_campagne',
            'notes_internes'
        ]
    
    def validate_titre(self, value):
        """Validation du titre"""
        if len(value.strip()) < 3:
            raise serializers.ValidationError("Le titre doit contenir au moins 3 caractères.")
        return value.strip()
    
    def validate_contenu(self, value):
        """Validation du contenu"""
        if len(value.strip()) < 10:
            raise serializers.ValidationError("Le contenu doit contenir au moins 10 caractères.")
        
        # Vérification de la limite de caractères pour les réseaux sociaux
        if len(value) > 2200:
            raise serializers.ValidationError("Le contenu ne peut pas dépasser 2200 caractères.")
        
        return value.strip()


class PostStatsSerializer(serializers.ModelSerializer):
    """Serializer pour les statistiques des posts"""

    auteur_nom = serializers.SerializerMethodField()
    plateformes_list = serializers.SerializerMethodField()
    engagement_rate = serializers.SerializerMethodField()
    reach_rate = serializers.SerializerMethodField()

    class Meta:
        model = ReseauxSocialPost
        fields = [
            'id',
            'titre',
            'auteur_nom',
            'plateformes_list',
            'statut',
            'date_publication_reelle',
            'vues',
            'likes',
            'partages',
            'commentaires_count',
            'engagement_rate',
            'reach_rate'
        ]
    
    def get_auteur_nom(self, obj):
        """Retourne le nom complet de l'auteur"""
        if obj.auteur:
            if obj.auteur.first_name and obj.auteur.last_name:
                return f"{obj.auteur.first_name} {obj.auteur.last_name}"
            return obj.auteur.username
        return None
    
    def get_plateformes_list(self, obj):
        """Retourne la liste des plateformes"""
        if obj.plateformes:
            return [p.strip() for p in obj.plateformes.split(',') if p.strip()]
        return []
    
    def get_engagement_rate(self, obj):
        """Calcule le taux d'engagement"""
        if obj.vues > 0:
            engagement = obj.likes + obj.partages + obj.commentaires_count
            return round((engagement / obj.vues) * 100, 2)
        return 0
    
    def get_reach_rate(self, obj):
        """Calcule le taux de portée (exemple basique)"""
        # Cette métrique pourrait être plus complexe selon les données disponibles
        if obj.vues > 0:
            return round((obj.vues / 1000) * 100, 2)  # Exemple simplifié
        return 0
