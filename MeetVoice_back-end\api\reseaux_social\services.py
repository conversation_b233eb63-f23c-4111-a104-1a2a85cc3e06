"""
Services pour les intégrations API des réseaux sociaux
"""
import requests
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, Optional, List
from django.conf import settings
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.utils import timezone
import google.generativeai as genai

logger = logging.getLogger(__name__)


class PollinationsImageService:
    """Service pour générer et stocker des images via Pollinations.ai"""

    @staticmethod
    def generate_image(prompt: str, width: int = 1024, height: int = 1024, wait_for_generation: bool = True) -> Optional[str]:
        """
        Génère une image via Pollinations.ai (retourne URL externe)

        Args:
            prompt: Description de l'image à générer
            width: Largeur de l'image
            height: Hauteur de l'image
            wait_for_generation: Attendre que l'image soit générée

        Returns:
            URL de l'image générée ou None en cas d'erreur
        """
        import time

        try:
            # Nettoyer et encoder le prompt
            clean_prompt = prompt.replace(' ', '%20').replace(',', '%2C')

            # URL de l'API Pollinations
            url = f"{settings.POLLINATIONS_API_URL}{clean_prompt}?width={width}&height={height}&model=flux&enhance=true"

            logger.info(f"Génération d'image avec Pollinations: {prompt}")

            if wait_for_generation:
                # Attendre que l'image soit générée (max 3 tentatives)
                for attempt in range(3):
                    response = requests.get(url, timeout=30)

                    if response.status_code == 200 and len(response.content) > 0:
                        logger.info(f"Image générée avec succès ({len(response.content)} bytes)")
                        return url
                    elif response.status_code == 200:
                        logger.info(f"Image en cours de génération, tentative {attempt + 1}/3...")
                        time.sleep(3)
                    else:
                        logger.error(f"Erreur Pollinations: {response.status_code}")
                        return None

                logger.warning("Image non générée après 3 tentatives, retour de l'URL")
                return url
            else:
                # Juste vérifier que l'URL est valide
                response = requests.head(url, timeout=30)

                if response.status_code == 200:
                    logger.info("URL d'image générée")
                    return url
                else:
                    logger.error(f"Erreur Pollinations: {response.status_code}")
                    return None

        except Exception as e:
            logger.error(f"Erreur lors de la génération d'image: {str(e)}")
            return None

    @staticmethod
    def download_and_save_image(image_url: str, filename_prefix: str = "reseaux_social", max_retries: int = 3) -> Optional[str]:
        """
        Télécharge une image depuis une URL et la sauvegarde localement
        Gère la génération asynchrone de Pollinations.ai

        Args:
            image_url: URL de l'image à télécharger
            filename_prefix: Préfixe pour le nom du fichier
            max_retries: Nombre maximum de tentatives

        Returns:
            Chemin relatif du fichier sauvegardé ou None en cas d'erreur
        """
        import time

        try:
            logger.info(f"Téléchargement de l'image: {image_url}")

            for attempt in range(max_retries):
                logger.info(f"Tentative {attempt + 1}/{max_retries}")

                # Télécharger l'image
                response = requests.get(image_url, timeout=30)

                if response.status_code == 200 and len(response.content) > 0:
                    # Générer un nom de fichier unique
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    filename = f"{filename_prefix}_{timestamp}.jpg"
                    filepath = f"reseaux_social/{filename}"

                    # Sauvegarder le fichier
                    file_content = ContentFile(response.content)
                    saved_path = default_storage.save(filepath, file_content)

                    logger.info(f"Image sauvegardée: {saved_path} ({len(response.content)} bytes)")
                    return saved_path
                elif response.status_code == 200 and len(response.content) == 0:
                    # Image en cours de génération, attendre
                    logger.info(f"Image en cours de génération, attente de 3 secondes...")
                    time.sleep(3)
                else:
                    logger.error(f"Erreur téléchargement: {response.status_code}")
                    break

            logger.error(f"Échec après {max_retries} tentatives")
            return None

        except Exception as e:
            logger.error(f"Erreur lors du téléchargement: {str(e)}")
            return None

    @staticmethod
    def generate_and_save_image(prompt: str, filename_prefix: str = "reseaux_social", width: int = 1024, height: int = 1024) -> Dict[str, Optional[str]]:
        """
        Génère une image et la sauvegarde localement

        Args:
            prompt: Description de l'image à générer
            filename_prefix: Préfixe pour le nom du fichier
            width: Largeur de l'image
            height: Hauteur de l'image

        Returns:
            Dict avec 'url' (externe) et 'file_path' (local)
        """
        result = {'url': None, 'file_path': None}

        try:
            # Générer l'image (URL externe)
            image_url = PollinationsImageService.generate_image(prompt, width, height)
            result['url'] = image_url

            if image_url:
                # Télécharger et sauvegarder localement
                file_path = PollinationsImageService.download_and_save_image(image_url, filename_prefix)
                result['file_path'] = file_path

                logger.info(f"Image générée et sauvegardée: URL={image_url}, File={file_path}")

            return result

        except Exception as e:
            logger.error(f"Erreur génération et sauvegarde: {str(e)}")
            return result


class GeminiContentService:
    """Service pour générer du contenu via Google Gemini"""
    
    def __init__(self):
        if settings.GOOGLE_GEMINI_API_KEY:
            genai.configure(api_key=settings.GOOGLE_GEMINI_API_KEY)
            self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
        else:
            self.model = None
            logger.warning("Clé API Gemini non configurée")
    
    def generate_post_content(self, theme: str, plateforme: str = 'all', tone: str = 'professionnel') -> Optional[str]:
        """
        Génère du contenu pour un post
        
        Args:
            theme: Thème du post
            plateforme: Plateforme cible
            tone: Ton du contenu (professionnel, décontracté, etc.)
            
        Returns:
            Contenu généré ou None en cas d'erreur
        """
        if not self.model:
            return None
            
        try:
            # Construire le prompt selon la plateforme avec limites strictes
            platform_specs = {
                'instagram': "Post Instagram (MAXIMUM 2200 caractères, avec émojis)",
                'facebook': "Post Facebook (MAXIMUM 2200 caractères, engageant)",
                'twitter': "Tweet (MAXIMUM 280 caractères, concis)",
                'linkedin': "Post LinkedIn (MAXIMUM 2200 caractères, professionnel)",
                'tiktok': "Description TikTok (MAXIMUM 2200 caractères, tendance)",
                'all': "Post polyvalent pour tous les réseaux sociaux (MAXIMUM 2200 caractères)"
            }
            
            platform_spec = platform_specs.get(plateforme, platform_specs['all'])
            
            # Déterminer la limite de caractères
            char_limits = {
                'instagram': 2200,
                'facebook': 2200,
                'twitter': 280,
                'linkedin': 2200,
                'tiktok': 2200,
                'all': 2200
            }

            char_limit = char_limits.get(plateforme, 2200)

            prompt = f"""
            Créez un {platform_spec} sur le thème: {theme}

            CONTRAINTE ABSOLUE: Le contenu final doit faire MAXIMUM {char_limit} caractères (espaces inclus).

            CONTEXTE COMPLET MEETVOICE:
            MeetVoice est une plateforme révolutionnaire de rencontres basée sur la voix et l'intelligence artificielle.

            CARACTÉRISTIQUES CLÉS:
            - 🎙️ Rencontres VOCALES (pas de photos au début)
            - ❤️ Tous types de relations: amicales, amoureuses, libertines
            - 🤖 IA pour le matching intelligent entre utilisateurs
            - ⚡ Speed dating vocal automatisé
            - 🎯 Algorithmes IA pour compatibilité vocale
            - 🔊 Focus sur la personnalité et la voix, pas l'apparence
            - 💬 Conversations authentiques avant tout
            - 🌟 Innovation dans les rencontres en ligne

            Ton: {tone}

            Instructions STRICTES:
            - RESPECTEZ IMPÉRATIVEMENT la limite de {char_limit} caractères
            - Mettez en avant l'aspect VOCAL et IA de MeetVoice
            - Mentionnez la diversité des rencontres (amour, amitié, libertin)
            - Soulignez l'innovation IA (matching, speed dating)
            - Créez un contenu engageant et authentique
            - Incluez un appel à l'action subtil
            - Adaptez le style à la plateforme {plateforme}
            - Utilisez des émojis appropriés mais avec parcimonie
            - Privilégiez la qualité à la quantité
            - Soyez concis et impactant

            IMPORTANT: Comptez vos caractères et restez sous {char_limit} caractères.

            Contenu:
            """
            
            response = self.model.generate_content(prompt)

            if response and response.text:
                content = response.text.strip()

                # Vérification et troncature si nécessaire
                if len(content) > char_limit:
                    logger.warning(f"Contenu trop long ({len(content)} caractères), troncature à {char_limit}")
                    # Tronquer en gardant les mots entiers
                    content = content[:char_limit-3].rsplit(' ', 1)[0] + '...'

                logger.info(f"Contenu généré pour {plateforme}: {theme} ({len(content)} caractères)")
                return content
            else:
                logger.error("Aucun contenu généré par Gemini")
                return None
                
        except Exception as e:
            logger.error(f"Erreur lors de la génération de contenu: {str(e)}")
            return None
    
    def improve_content(self, content: str, plateforme: str = 'all') -> Optional[str]:
        """
        Améliore un contenu existant
        
        Args:
            content: Contenu à améliorer
            plateforme: Plateforme cible
            
        Returns:
            Contenu amélioré ou None en cas d'erreur
        """
        if not self.model:
            return None
            
        try:
            prompt = f"""
            Améliorez ce contenu pour {plateforme}:
            
            "{content}"
            
            Instructions:
            - Gardez le message principal
            - Améliorez l'engagement et la clarté
            - Optimisez pour {plateforme}
            - Ajoutez des émojis appropriés
            - Respectez les limites de caractères
            
            Contenu amélioré:
            """
            
            response = self.model.generate_content(prompt)
            
            if response and response.text:
                logger.info(f"Contenu amélioré pour {plateforme}")
                return response.text.strip()
            else:
                return content  # Retourner l'original si échec
                
        except Exception as e:
            logger.error(f"Erreur lors de l'amélioration: {str(e)}")
            return content
    
    def generate_hashtags(self, content: str, plateforme: str = 'all', max_hashtags: int = 10) -> List[str]:
        """
        Génère des hashtags pertinents
        
        Args:
            content: Contenu du post
            plateforme: Plateforme cible
            max_hashtags: Nombre maximum de hashtags
            
        Returns:
            Liste de hashtags
        """
        if not self.model:
            return ['#meetvoice', '#rencontres', '#vocal']
            
        try:
            prompt = f"""
            Générez {max_hashtags} hashtags pertinents pour ce contenu sur {plateforme}:

            "{content}"

            CONTEXTE: MeetVoice = plateforme de rencontres vocales avec IA (matching, speed dating)
            Types de rencontres: amicales, amoureuses, libertines

            Instructions:
            - Hashtags en français
            - Incluez TOUJOURS #MeetVoice
            - Mélangez: rencontres vocales, IA, speed dating, amour, amitié
            - Populaires sur {plateforme}
            - Format: #hashtag (un par ligne)
            - Variez entre génériques et spécifiques

            Exemples: #MeetVoice #RencontresVocales #IA #SpeedDating #Amour #Amitié #Dating #Innovation

            Hashtags:
            """
            
            response = self.model.generate_content(prompt)
            
            if response and response.text:
                hashtags = []
                for line in response.text.strip().split('\n'):
                    line = line.strip()
                    if line.startswith('#'):
                        hashtags.append(line)
                    elif line and not line.startswith('#'):
                        hashtags.append(f"#{line}")
                
                logger.info(f"Hashtags générés: {len(hashtags)}")
                return hashtags[:max_hashtags]
            else:
                return ['#meetvoice', '#rencontres', '#vocal']
                
        except Exception as e:
            logger.error(f"Erreur lors de la génération de hashtags: {str(e)}")
            return ['#meetvoice', '#rencontres', '#vocal']


class SimpleFacebookService:
    """Service simplifié pour publier sur Facebook avec token personnel"""

    @staticmethod
    def get_page_access_token() -> Optional[str]:
        """
        Récupérer le token d'accès de la page Facebook

        Returns:
            Token de la page ou None en cas d'erreur
        """
        try:
            user_token = settings.FACEBOOK_ACCESS_TOKEN
            page_id = settings.FACEBOOK_PAGE_ID

            if not user_token or not page_id:
                return None

            # Récupérer le token de la page
            url = f"https://graph.facebook.com/v18.0/{page_id}"
            params = {
                'fields': 'access_token',
                'access_token': user_token
            }

            response = requests.get(url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                return data.get('access_token')
            else:
                logger.error(f"Erreur récupération token page: {response.status_code}")
                return user_token  # Fallback sur le token utilisateur

        except Exception as e:
            logger.error(f"Exception récupération token page: {str(e)}")
            return settings.FACEBOOK_ACCESS_TOKEN  # Fallback

    @staticmethod
    def publish_to_facebook(message: str, image_url: str = None) -> Optional[str]:
        """
        Publier sur la page Facebook MeetVoice

        Args:
            message: Contenu du post
            image_url: URL de l'image (optionnel)

        Returns:
            ID du post publié ou None en cas d'erreur
        """
        try:
            page_id = settings.FACEBOOK_PAGE_ID

            if not page_id:
                logger.error("ID de page Facebook manquant")
                return None

            # Récupérer le token de la page
            page_token = SimpleFacebookService.get_page_access_token()

            if not page_token:
                logger.error("Token de page Facebook manquant")
                return None

            logger.info(f"Publication Facebook page: {message[:50]}...")

            # Si on a une image, essayer de publier avec l'image
            if image_url and image_url.startswith('http'):
                try:
                    # Méthode 1: Publier une photo avec caption
                    photo_url = f"https://graph.facebook.com/v18.0/{page_id}/photos"
                    photo_data = {
                        'url': image_url,
                        'caption': message,
                        'access_token': page_token
                    }

                    photo_response = requests.post(photo_url, data=photo_data, timeout=30)

                    if photo_response.status_code == 200:
                        result = photo_response.json()
                        post_id = result.get('post_id') or result.get('id')
                        logger.info(f"Post Facebook avec image publié: {post_id}")
                        return post_id
                    else:
                        logger.warning(f"Échec publication avec image, essai sans image")

                except Exception as e:
                    logger.warning(f"Erreur publication avec image: {e}, essai sans image")

            # Méthode 2: Publier sans image (fallback)
            url = f"https://graph.facebook.com/v18.0/{page_id}/feed"

            # Données du post
            data = {
                'message': message,
                'access_token': page_token
            }

            response = requests.post(url, data=data, timeout=30)

            if response.status_code == 200:
                result = response.json()
                post_id = result.get('id')
                logger.info(f"Post Facebook publié (sans image): {post_id}")
                return post_id
            else:
                error_data = response.json() if response.content else {}
                error_msg = error_data.get('error', {}).get('message', 'Erreur inconnue')
                logger.error(f"Erreur Facebook page: {response.status_code} - {error_msg}")
                return None

        except Exception as e:
            logger.error(f"Exception Facebook page: {str(e)}")
            return None

    @staticmethod
    def test_connection() -> Dict[str, any]:
        """
        Tester la connexion Facebook

        Returns:
            Dict avec les informations de test
        """
        try:
            access_token = settings.FACEBOOK_ACCESS_TOKEN

            if not access_token:
                return {'success': False, 'error': 'Token manquant'}

            url = "https://graph.facebook.com/v18.0/me"
            params = {'access_token': access_token}

            response = requests.get(url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                return {
                    'success': True,
                    'user_name': data.get('name'),
                    'user_id': data.get('id')
                }
            else:
                error_data = response.json() if response.content else {}
                return {
                    'success': False,
                    'error': error_data.get('error', {}).get('message', 'Erreur inconnue')
                }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    @staticmethod
    def delete_facebook_post(post_id: str) -> Dict[str, any]:
        """
        Supprimer un post sur Facebook

        Args:
            post_id: ID du post Facebook à supprimer

        Returns:
            Dict avec le résultat de la suppression
        """
        try:
            access_token = settings.FACEBOOK_ACCESS_TOKEN

            if not access_token:
                return {'success': False, 'error': 'Token Facebook manquant'}

            # Récupérer le token de page si possible
            page_token = SimpleFacebookService.get_page_access_token()
            token_to_use = page_token if page_token else access_token

            # URL pour supprimer le post
            url = f"https://graph.facebook.com/v18.0/{post_id}"
            params = {'access_token': token_to_use}

            logger.info(f"Suppression post Facebook: {post_id}")

            response = requests.delete(url, params=params, timeout=30)

            if response.status_code == 200:
                result = response.json()
                success = result.get('success', False)

                if success:
                    logger.info(f"Post Facebook supprimé: {post_id}")
                    return {
                        'success': True,
                        'post_id': post_id,
                        'message': 'Post supprimé avec succès sur Facebook'
                    }
                else:
                    logger.error(f"Échec suppression Facebook: {result}")
                    return {
                        'success': False,
                        'error': 'Facebook a refusé la suppression'
                    }
            else:
                error_data = response.json() if response.content else {}
                error_msg = error_data.get('error', {}).get('message', 'Erreur inconnue')
                logger.error(f"Erreur suppression Facebook: {response.status_code} - {error_msg}")
                return {
                    'success': False,
                    'error': f"Erreur {response.status_code}: {error_msg}"
                }

        except Exception as e:
            logger.error(f"Exception suppression Facebook: {str(e)}")
            return {
                'success': False,
                'error': f"Exception: {str(e)}"
            }


class FacebookInstagramService:
    """Service pour publier sur Facebook et Instagram"""
    
    def __init__(self):
        self.access_token = settings.FACEBOOK_ACCESS_TOKEN
        self.base_url = "https://graph.facebook.com/v18.0"
    
    def publish_to_facebook(self, message: str, image_url: Optional[str] = None, link: Optional[str] = None) -> Optional[str]:
        """
        Publie un post sur Facebook

        Args:
            message: Contenu du post
            image_url: URL de l'image (optionnel)
            link: Lien externe (optionnel)

        Returns:
            ID du post publié ou None en cas d'erreur
        """
        try:
            # URL pour publier sur la page Facebook
            url = f"{self.base_url}/me/feed"

            data = {
                'message': message,
                'access_token': self.access_token
            }

            # Si on a une image mais pas de lien, on utilise un lien par défaut
            if image_url and not link:
                link = "https://meetvoice.com"  # Lien par défaut vers votre site

            if link:
                data['link'] = link

            if image_url:
                data['picture'] = image_url

            response = requests.post(url, data=data, timeout=30)

            if response.status_code == 200:
                result = response.json()
                post_id = result.get('id')
                logger.info(f"Post publié sur Facebook: {post_id}")
                return post_id
            else:
                logger.error(f"Erreur Facebook: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"Erreur lors de la publication Facebook: {str(e)}")
            return None
    
    def publish_to_instagram(self, message: str, image_url: str) -> Optional[str]:
        """
        Publie un post sur Instagram
        
        Args:
            message: Caption du post
            image_url: URL de l'image (obligatoire pour Instagram)
            
        Returns:
            ID du post publié ou None en cas d'erreur
        """
        try:
            # Instagram nécessite une image
            if not image_url:
                logger.error("Image requise pour Instagram")
                return None
            
            # Récupérer l'ID du compte Instagram Business via Facebook API
            page_id = settings.FACEBOOK_PAGE_ID
            page_url = f"https://graph.facebook.com/v18.0/{page_id}"
            page_params = {
                'fields': 'instagram_business_account',
                'access_token': self.access_token
            }

            page_response = requests.get(page_url, params=page_params, timeout=10)

            if page_response.status_code != 200:
                logger.error("Impossible d'accéder à la page Facebook")
                return None

            page_data = page_response.json()
            instagram_account = page_data.get('instagram_business_account')

            if not instagram_account:
                logger.error("Aucun compte Instagram Business lié à cette page Facebook")
                return None

            instagram_id = instagram_account.get('id')
            logger.info(f"Compte Instagram Business trouvé: {instagram_id}")

            # Étape 1: Créer le container média via l'API Instagram officielle
            container_url = f"https://graph.instagram.com/{instagram_id}/media"
            container_data = {
                'image_url': image_url,
                'caption': message,
                'access_token': self.access_token
            }
            
            container_response = requests.post(container_url, data=container_data, timeout=30)
            
            if container_response.status_code != 200:
                logger.error(f"Erreur création container Instagram: {container_response.text}")
                return None
            
            container_id = container_response.json().get('id')
            
            # Étape 2: Publier le média via l'API Instagram officielle
            publish_url = f"https://graph.instagram.com/{instagram_id}/media_publish"
            publish_data = {
                'creation_id': container_id,
                'access_token': self.access_token
            }
            
            publish_response = requests.post(publish_url, data=publish_data, timeout=30)
            
            if publish_response.status_code == 200:
                result = publish_response.json()
                post_id = result.get('id')
                logger.info(f"Post publié sur Instagram: {post_id}")
                return post_id
            else:
                logger.error(f"Erreur publication Instagram: {publish_response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Erreur lors de la publication Instagram: {str(e)}")
            return None


class SocialMediaPublisher:
    """Service principal pour publier sur tous les réseaux sociaux"""
    
    def __init__(self):
        self.facebook_service = FacebookInstagramService()
        self.gemini_service = GeminiContentService()
        self.image_service = PollinationsImageService()
    
    def publish_post(self, post, test_mode=False) -> Dict[str, Optional[str]]:
        """
        Publie un post sur les plateformes sélectionnées

        Args:
            post: Instance de ReseauxSocialPost
            test_mode: Si True, simule la publication sans vraiment publier

        Returns:
            Dictionnaire avec les IDs des posts publiés par plateforme
        """
        results = {}

        try:
            # Préparer le contenu
            content = post.contenu_complet
            image_url = post.image_url
            link = post.lien_externe

            logger.info(f"Publication du post {post.id} - Mode test: {test_mode}")

            if test_mode:
                # Mode simulation pour les tests
                if post.plateforme in ['facebook', 'all']:
                    results['facebook'] = f"test_facebook_{post.id}_{timezone.now().timestamp()}"
                    logger.info(f"Simulation publication Facebook: {results['facebook']}")

                if post.plateforme in ['instagram', 'all'] and image_url:
                    results['instagram'] = f"test_instagram_{post.id}_{timezone.now().timestamp()}"
                    logger.info(f"Simulation publication Instagram: {results['instagram']}")

                if post.plateforme in ['twitter', 'all']:
                    results['twitter'] = f"test_twitter_{post.id}_{timezone.now().timestamp()}"
                    logger.info(f"Simulation publication Twitter: {results['twitter']}")

                if post.plateforme in ['linkedin', 'all']:
                    results['linkedin'] = f"test_linkedin_{post.id}_{timezone.now().timestamp()}"
                    logger.info(f"Simulation publication LinkedIn: {results['linkedin']}")

            else:
                # Publication réelle
                if post.plateforme in ['facebook', 'all']:
                    facebook_id = self.facebook_service.publish_to_facebook(
                        message=content,
                        image_url=image_url,
                        link=link
                    )
                    results['facebook'] = facebook_id

                if post.plateforme in ['instagram', 'all'] and image_url:
                    instagram_id = self.facebook_service.publish_to_instagram(
                        message=content,
                        image_url=image_url
                    )
                    results['instagram'] = instagram_id

                # TODO: Ajouter d'autres plateformes (Twitter, LinkedIn, etc.)

            logger.info(f"Publication terminée pour le post {post.id}: {results}")
            return results

        except Exception as e:
            logger.error(f"Erreur lors de la publication: {str(e)}")
            return results
