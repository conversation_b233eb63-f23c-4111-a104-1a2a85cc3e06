"""
🔄 SIGNAUX DJANGO POUR RESEAUX SOCIAUX
=====================================

Signaux pour gérer automatiquement les fichiers lors des suppressions.
"""

import os
import logging
from django.db.models.signals import post_delete, pre_delete
from django.dispatch import receiver
from django.conf import settings
from .models import ReseauxSocialPost

logger = logging.getLogger(__name__)

@receiver(pre_delete, sender=ReseauxSocialPost)
def delete_post_image_file(sender, instance, **kwargs):
    """
    Signal pour supprimer automatiquement l'image locale
    quand un post est supprimé
    """
    if instance.image_file:
        try:
            # Construire le chemin complet du fichier
            image_path = os.path.join(settings.MEDIA_ROOT, str(instance.image_file))
            
            # Supprimer le fichier s'il existe
            if os.path.exists(image_path):
                os.remove(image_path)
                logger.info(f"Image supprimée automatiquement: {instance.image_file}")
            else:
                logger.warning(f"Image non trouvée lors de la suppression: {image_path}")
                
        except Exception as e:
            logger.error(f"Erreur lors de la suppression automatique de l'image: {e}")

@receiver(post_delete, sender=ReseauxSocialPost)
def log_post_deletion(sender, instance, **kwargs):
    """
    Signal pour logger la suppression d'un post
    """
    logger.info(f"Post supprimé: '{instance.titre}' (ID: {instance.id})")
    
    if instance.post_id_facebook:
        logger.info(f"Post avait un ID Facebook: {instance.post_id_facebook}")
    
    if instance.image_file:
        logger.info(f"Post avait une image locale: {instance.image_file}")
