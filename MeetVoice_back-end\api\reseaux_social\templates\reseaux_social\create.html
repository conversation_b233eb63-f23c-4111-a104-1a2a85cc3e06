{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - Meet Voice{% endblock %}

{% block head %}
<meta name="description" content="Créer un nouveau post pour les réseaux sociaux">
<link rel="stylesheet" href="{% static 'reseaux_social/css/reseaux_social.css' %}" />
<style>
/* Styles critiques pour l'interface réseaux sociaux */
:root {
    --admin-primary: #2a1d34;
    --admin-secondary: #3d2a4a;
    --admin-accent: #667eea;
    --admin-success: #27ae60;
    --admin-warning: #f39c12;
    --admin-danger: #e74c3c;
    --admin-info: #3498db;
    --admin-light: #ecf0f1;
    --admin-dark: #2a1d34;
    --sidebar-width: 250px;
    --border-radius: 0.5rem;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
}

.admin-interface {
    display: flex;
    min-height: 100vh;
    background: #f8f9fa;
}

.admin-sidebar {
    width: var(--sidebar-width);
    background: var(--admin-primary);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.admin-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.admin-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-1px);
}

.admin-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-left-color: var(--admin-accent);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.admin-nav .nav-link i {
    width: 20px;
    margin-right: 0.5rem;
    text-align: center;
}

.admin-content {
    margin-left: var(--sidebar-width);
    flex: 1;
    min-height: 100vh;
    background: #f8f9fa;
}

.admin-header {
    background: white;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 2rem;
}

.admin-header h1 {
    margin-bottom: 0.5rem;
    color: var(--admin-dark);
}

@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }

    .admin-sidebar.show {
        transform: translateX(0);
    }

    .admin-content {
        margin-left: 0;
    }

    .admin-header {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Interface Administrative avec Sidebar -->
<div class="admin-interface">
    <!-- Sidebar Navigation -->
    <nav class="admin-sidebar">
        <div class="text-center p-3 border-bottom">
            <h5 class="text-white mb-1">
                <i class="fas fa-share-alt me-2"></i>Réseaux Sociaux
            </h5>
            <small class="text-white-50">Créer un Post</small>
        </div>
        <ul class="nav flex-column admin-nav">
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:dashboard' %}">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'abonnement:liste' %}">
                    <i class="fas fa-tags"></i>Gestion Abonnements
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:liste' %}">
                    <i class="fas fa-share-alt"></i>Réseaux Sociaux
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:traffic_manager' %}">
                    <i class="fas fa-chart-line"></i>Traffic Manager
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:google_analytics' %}">
                    <i class="fab fa-google"></i>Google Analytics
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:articles' %}">
                    <i class="fas fa-newspaper"></i>Articles
                </a>
            </li>
            <hr style="border-color: rgba(255, 255, 255, 0.2)" />
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:stats' %}">
                    <i class="fas fa-chart-bar"></i>Statistiques
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:calendar' %}">
                    <i class="fas fa-calendar"></i>Calendrier
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:templates' %}">
                    <i class="fas fa-file-alt"></i>Templates
                </a>
            </li>
            <hr style="border-color: rgba(255, 255, 255, 0.2)" />
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin:index' %}">
                    <i class="fas fa-tools"></i>Admin Django
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'index' %}">
                    <i class="fas fa-home"></i>Retour au site
                </a>
            </li>
        </ul>
    </nav>
    
    <!-- Contenu Principal -->
    <div class="admin-content">
        <!-- Header Admin -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-plus me-2"></i>Créer un Post</h1>
                    <p class="text-muted mb-0">Créez un nouveau post pour vos réseaux sociaux avec l'aide de l'IA</p>
                </div>
                <div>
                    <a href="{% url 'reseaux_social:liste' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                </div>
            </div>
        </div>

        <!-- Messages -->
        {% if messages %}
        <div class="container-fluid">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Formulaire de Création -->
        <div class="container-fluid">
            <div class="row">
                <!-- Formulaire Principal -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-edit me-2"></i>Informations du Post
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="post" id="postForm">
                                {% csrf_token %}
                                
                                <!-- Titre -->
                                <div class="mb-3">
                                    <label for="{{ form.titre.id_for_label }}" class="form-label">
                                        <i class="fas fa-heading me-1"></i>Titre du Post
                                    </label>
                                    {{ form.titre }}
                                    {% if form.titre.errors %}
                                        <div class="text-danger small">{{ form.titre.errors }}</div>
                                    {% endif %}
                                </div>

                                <!-- Plateforme et Type -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.plateforme.id_for_label }}" class="form-label">
                                                <i class="fas fa-share-alt me-1"></i>Plateforme
                                            </label>
                                            {{ form.plateforme }}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.type_contenu.id_for_label }}" class="form-label">
                                                <i class="fas fa-tag me-1"></i>Type de Contenu
                                            </label>
                                            {{ form.type_contenu }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Contenu -->
                                <div class="mb-3">
                                    <label for="{{ form.contenu.id_for_label }}" class="form-label">
                                        <i class="fas fa-align-left me-1"></i>Contenu du Post
                                    </label>
                                    {{ form.contenu }}
                                    <div class="form-text">
                                        <small id="charCountContainer" class="text-muted">
                                            <span id="charCount">0</span> / 2200 caractères
                                            <span id="charWarning" class="text-warning ms-2" style="display: none;">
                                                <i class="fas fa-exclamation-triangle"></i> Proche de la limite
                                            </span>
                                            <span id="charError" class="text-danger ms-2" style="display: none;">
                                                <i class="fas fa-times-circle"></i> Limite dépassée
                                            </span>
                                        </small>
                                    </div>
                                    {% if form.contenu.errors %}
                                        <div class="text-danger small">{{ form.contenu.errors }}</div>
                                    {% endif %}
                                </div>

                                <!-- Hashtags -->
                                <div class="mb-3">
                                    <label for="{{ form.hashtags.id_for_label }}" class="form-label">
                                        <i class="fas fa-hashtag me-1"></i>Hashtags
                                    </label>
                                    {{ form.hashtags }}
                                    <div class="form-text">
                                        <small class="text-muted">Séparez les hashtags par des espaces (ex: #meetvoice #dating #voice)</small>
                                    </div>
                                </div>

                                <!-- Image -->
                                <div class="mb-3">
                                    <label for="{{ form.image_prompt.id_for_label }}" class="form-label">
                                        <i class="fas fa-image me-1"></i>Prompt pour l'Image
                                    </label>
                                    <div class="input-group">
                                        {{ form.image_prompt }}
                                        <button type="button" class="btn btn-outline-primary" onclick="generateImageFromPrompt()">
                                            <i class="fas fa-magic me-1"></i>Générer Image
                                        </button>
                                    </div>
                                    <div class="form-text">
                                        <small class="text-muted">Décrivez l'image que vous souhaitez générer avec Pollinations.ai</small>
                                    </div>
                                </div>

                                <!-- Aperçu de l'image -->
                                <div class="mb-3" id="imagePreview" style="display: none;">
                                    <label class="form-label">Aperçu de l'Image</label>
                                    <div id="imageContainer" class="border rounded p-3 text-center">
                                        <!-- L'image générée apparaîtra ici -->
                                    </div>
                                </div>

                                <!-- Lien externe -->
                                <div class="mb-3">
                                    <label for="{{ form.lien_externe.id_for_label }}" class="form-label">
                                        <i class="fas fa-link me-1"></i>Lien Externe (optionnel)
                                    </label>
                                    {{ form.lien_externe }}
                                </div>

                                <!-- Date programmée -->
                                <div class="mb-3">
                                    <label for="{{ form.date_programmee.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>Date de Publication (optionnel)
                                    </label>
                                    {{ form.date_programmee }}
                                    <div class="form-text">
                                        <small class="text-muted">Laissez vide pour publier immédiatement</small>
                                    </div>
                                </div>

                                <!-- Boutons d'action -->
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Créer le Post
                                    </button>
                                    <button type="button" class="btn btn-outline-success" onclick="saveAndPublish()">
                                        <i class="fas fa-share me-2"></i>Créer et Publier
                                    </button>
                                    <a href="{% url 'reseaux_social:liste' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Annuler
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Panneau d'Aide IA -->
                <div class="col-lg-4">
                    <!-- Génération de Contenu IA -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-robot me-2"></i>Assistant IA Gemini
                                {% if user.is_authenticated and user.is_staff %}
                                    <span class="badge bg-success ms-2">Disponible</span>
                                {% else %}
                                    <span class="badge bg-warning ms-2">Connexion requise</span>
                                {% endif %}
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info border-0 bg-info bg-opacity-10 mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle text-info me-2"></i>
                                    <small class="text-info mb-0">
                                        <strong>L'IA connaît MeetVoice :</strong> Rencontres vocales, IA matching, speed dating,
                                        relations amicales/amoureuses/libertines
                                    </small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="aiTheme" class="form-label">
                                    <i class="fas fa-lightbulb me-1"></i>Thème du Post
                                </label>
                                <input type="text" class="form-control" id="aiTheme"
                                       placeholder="Ex: rencontre vocal, speed dating IA, matching vocal...">
                                <div class="form-text">
                                    <small class="text-muted">L'IA adaptera automatiquement le contenu à MeetVoice</small>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="aiTone" class="form-label">
                                    <i class="fas fa-palette me-1"></i>Ton
                                </label>
                                <select class="form-select" id="aiTone">
                                    <option value="engageant">🎯 Engageant (recommandé)</option>
                                    <option value="professionnel">💼 Professionnel</option>
                                    <option value="decontracte">😊 Décontracté</option>
                                    <option value="enthousiaste">🚀 Enthousiaste</option>
                                    <option value="informatif">📚 Informatif</option>
                                </select>
                            </div>

                            <div class="d-flex align-items-center justify-content-between">
                                <button type="button" class="btn btn-primary btn-lg" onclick="generateAIContent()">
                                    <i class="fas fa-magic me-2"></i>Générer le contenu IA
                                </button>
                                <div class="text-end">
                                    <small class="text-muted">
                                        <i class="fas fa-shield-alt me-1"></i>Limite: 2200 caractères<br>
                                        <i class="fas fa-clock me-1"></i>~10-15 secondes
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Templates Disponibles -->
                    {% if templates %}
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-file-alt me-2"></i>Templates Disponibles
                            </h6>
                        </div>
                        <div class="card-body">
                            {% for template in templates %}
                            <div class="template-item mb-2 p-2 border rounded">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>{{ template.nom }}</strong>
                                        <br>
                                        <small class="text-muted">{{ template.description|truncatewords:10 }}</small>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                            onclick="useTemplate({{ template.pk }})">
                                        Utiliser
                                    </button>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal Amélioré -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-body text-center p-4">
                <div class="mb-3">
                    <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
                <h5 id="loadingText" class="text-primary mb-2">🤖 Génération en cours...</h5>
                <p id="loadingSubtext" class="text-muted mb-3">L'IA MeetVoice crée votre contenu personnalisé</p>
                <div class="progress" style="height: 6px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 100%"></div>
                </div>
                <small class="text-muted mt-2 d-block">
                    <i class="fas fa-magic me-1"></i>Optimisé pour les rencontres vocales
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Succès -->
<div class="modal fade" id="successModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-success text-white border-0">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>Contenu Généré avec Succès !
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4">
                <div class="text-center mb-3">
                    <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center"
                         style="width: 60px; height: 60px;">
                        <i class="fas fa-robot text-success" style="font-size: 24px;"></i>
                    </div>
                </div>
                <div class="text-center">
                    <h6 class="text-success mb-2">🎯 Contenu Optimisé MeetVoice</h6>
                    <p id="successStats" class="text-muted mb-3">
                        📝 <span id="successCharCount">0</span> caractères générés<br>
                        🎙️ Adapté pour les rencontres vocales<br>
                        🤖 Optimisé par l'IA Gemini
                    </p>
                    <div class="alert alert-info border-0 bg-info bg-opacity-10">
                        <small class="text-info">
                            <i class="fas fa-lightbulb me-1"></i>
                            Le contenu met en avant l'aspect vocal et IA de MeetVoice
                        </small>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0 pt-0">
                <button type="button" class="btn btn-success w-100" data-bs-dismiss="modal">
                    <i class="fas fa-thumbs-up me-2"></i>Parfait, continuer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'Erreur -->
<div class="modal fade" id="errorModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-danger text-white border-0">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>Erreur de Génération
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4">
                <div class="text-center mb-3">
                    <div class="bg-danger bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center"
                         style="width: 60px; height: 60px;">
                        <i class="fas fa-times text-danger" style="font-size: 24px;"></i>
                    </div>
                </div>
                <div class="text-center">
                    <h6 class="text-danger mb-2">Oops ! Quelque chose s'est mal passé</h6>
                    <p id="errorMessage" class="text-muted mb-3">
                        Une erreur est survenue lors de la génération du contenu.
                    </p>
                    <div class="alert alert-warning border-0 bg-warning bg-opacity-10">
                        <small class="text-warning">
                            <i class="fas fa-info-circle me-1"></i>
                            Vérifiez votre connexion et réessayez
                        </small>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0 pt-0">
                <button type="button" class="btn btn-outline-secondary me-2" data-bs-dismiss="modal">
                    Annuler
                </button>
                <button type="button" class="btn btn-primary" onclick="retryGeneration()">
                    <i class="fas fa-redo me-2"></i>Réessayer
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'reseaux_social/js/reseaux_social.js' %}"></script>
<script>
// Compteur de caractères avec alertes
document.getElementById('id_contenu').addEventListener('input', function() {
    const length = this.value.length;
    const limit = 2200;

    document.getElementById('charCount').textContent = length;

    // Gestion des alertes visuelles
    const container = document.getElementById('charCountContainer');
    const warning = document.getElementById('charWarning');
    const error = document.getElementById('charError');

    if (length > limit) {
        // Dépassement de limite
        container.className = 'text-danger';
        warning.style.display = 'none';
        error.style.display = 'inline';
    } else if (length > limit * 0.9) {
        // Proche de la limite (90%)
        container.className = 'text-warning';
        warning.style.display = 'inline';
        error.style.display = 'none';
    } else {
        // Normal
        container.className = 'text-muted';
        warning.style.display = 'none';
        error.style.display = 'none';
    }
});

// Fonction de retry pour la génération
function retryGeneration() {
    // Fermer le modal d'erreur
    const errorModal = bootstrap.Modal.getInstance(document.getElementById('errorModal'));
    errorModal.hide();

    // Relancer la génération
    setTimeout(() => {
        generateAIContent();
    }, 300);
}

// Générer du contenu IA
async function generateAIContent() {
    const theme = document.getElementById('aiTheme').value;
    const tone = document.getElementById('aiTone').value;
    const plateforme = document.getElementById('id_plateforme').value;

    if (!theme.trim()) {
        alert('Veuillez saisir un thème pour générer le contenu');
        return;
    }

    // Vérifier l'authentification
    {% if not user.is_authenticated or not user.is_staff %}
    alert('Vous devez être connecté en tant qu\'administrateur pour utiliser l\'IA');
    window.location.href = '/accounts/login/?next=' + encodeURIComponent(window.location.pathname);
    return;
    {% endif %}
    
    // Afficher le modal de chargement amélioré
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    document.getElementById('loadingText').textContent = '🤖 Génération du contenu avec Gemini...';
    document.getElementById('loadingSubtext').textContent = 'L\'IA MeetVoice analyse votre thème et crée un contenu optimisé';
    loadingModal.show();
    
    try {
        const result = await generateContent(theme, plateforme, tone);
        if (result) {
            // Remplir automatiquement le titre si vide
            if (!document.getElementById('id_titre').value) {
                document.getElementById('id_titre').value = theme;
            }

            // Déclencher l'événement input pour mettre à jour le compteur
            document.getElementById('id_contenu').dispatchEvent(new Event('input'));

            // Afficher le modal de succès
            const contentLength = document.getElementById('id_contenu').value.length;
            document.getElementById('successCharCount').textContent = contentLength;

            loadingModal.hide();

            // Attendre que le modal de chargement se ferme avant d'ouvrir le succès
            setTimeout(() => {
                const successModal = new bootstrap.Modal(document.getElementById('successModal'));
                successModal.show();
            }, 300);
        }
    } catch (error) {
        console.error('Erreur:', error);

        // Afficher le modal d'erreur
        document.getElementById('errorMessage').textContent =
            error.message || 'Une erreur est survenue lors de la génération du contenu.';

        loadingModal.hide();

        setTimeout(() => {
            const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
            errorModal.show();
        }, 300);
    }
}

// Générer une image depuis le prompt
async function generateImageFromPrompt() {
    const prompt = document.getElementById('id_image_prompt').value;
    
    if (!prompt.trim()) {
        alert('Veuillez saisir un prompt pour générer l\'image');
        return;
    }
    
    // Afficher le modal de chargement
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    document.getElementById('loadingText').textContent = 'Génération de l\'image avec Pollinations.ai...';
    loadingModal.show();
    
    try {
        const imageUrl = await generateImage(prompt, 'imageContainer');
        if (imageUrl) {
            document.getElementById('imagePreview').style.display = 'block';
        }
    } catch (error) {
        console.error('Erreur:', error);
    } finally {
        loadingModal.hide();
    }
}

// Sauvegarder et publier
function saveAndPublish() {
    if (confirm('Créer et publier ce post immédiatement ?')) {
        const form = document.getElementById('postForm');
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'publish_immediately';
        input.value = 'true';
        form.appendChild(input);
        form.submit();
    }
}
</script>
{% endblock %}
