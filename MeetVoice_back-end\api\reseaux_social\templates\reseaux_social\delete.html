{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - Meet Voice{% endblock %}

{% block head %}
<meta name="description" content="Supprimer le post pour les réseaux sociaux">
<link rel="stylesheet" href="{% static 'reseaux_social/css/reseaux_social.css' %}" />
<style>
/* Styles critiques pour l'interface réseaux sociaux */
:root {
    --admin-primary: #2a1d34;
    --admin-secondary: #3d2a4a;
    --admin-accent: #667eea;
    --admin-success: #27ae60;
    --admin-warning: #f39c12;
    --admin-danger: #e74c3c;
    --admin-info: #3498db;
    --admin-light: #ecf0f1;
    --admin-dark: #2a1d34;
    --sidebar-width: 250px;
    --border-radius: 0.5rem;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
}

.admin-interface {
    display: flex;
    min-height: 100vh;
    background: #f8f9fa;
}

.admin-sidebar {
    width: var(--sidebar-width);
    background: var(--admin-primary);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.admin-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.admin-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-1px);
}

.admin-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-left-color: var(--admin-accent);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.admin-nav .nav-link i {
    width: 20px;
    margin-right: 0.5rem;
    text-align: center;
}

.admin-content {
    margin-left: var(--sidebar-width);
    flex: 1;
    min-height: 100vh;
    background: #f8f9fa;
}

.admin-header {
    background: white;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 2rem;
}

.admin-header h1 {
    margin-bottom: 0.5rem;
    color: var(--admin-dark);
}

.delete-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-left: 4px solid var(--admin-warning);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 2rem;
}

.post-summary {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-sidebar.show {
        transform: translateX(0);
    }
    
    .admin-content {
        margin-left: 0;
    }
    
    .admin-header {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Interface Administrative avec Sidebar -->
<div class="admin-interface">
    <!-- Sidebar Navigation -->
    <nav class="admin-sidebar">
        <div class="text-center p-3 border-bottom">
            <h5 class="text-white mb-1">
                <i class="fas fa-share-alt me-2"></i>Réseaux Sociaux
            </h5>
            <small class="text-white-50">Supprimer un Post</small>
        </div>
        <ul class="nav flex-column admin-nav">
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:dashboard' %}">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'abonnement:liste' %}">
                    <i class="fas fa-tags"></i>Gestion Abonnements
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{% url 'reseaux_social:liste' %}">
                    <i class="fas fa-share-alt"></i>Réseaux Sociaux
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:traffic_manager' %}">
                    <i class="fas fa-chart-line"></i>Traffic Manager
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:google_analytics' %}">
                    <i class="fab fa-google"></i>Google Analytics
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:articles' %}">
                    <i class="fas fa-newspaper"></i>Articles
                </a>
            </li>
            <hr style="border-color: rgba(255, 255, 255, 0.2)" />
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:stats' %}">
                    <i class="fas fa-chart-bar"></i>Statistiques
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:calendar' %}">
                    <i class="fas fa-calendar"></i>Calendrier
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:templates' %}">
                    <i class="fas fa-file-alt"></i>Templates
                </a>
            </li>
            <hr style="border-color: rgba(255, 255, 255, 0.2)" />
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin:index' %}">
                    <i class="fas fa-tools"></i>Admin Django
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'index' %}">
                    <i class="fas fa-home"></i>Retour au site
                </a>
            </li>
        </ul>
    </nav>
    
    <!-- Contenu Principal -->
    <div class="admin-content">
        <!-- Header Admin -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-trash me-2 text-danger"></i>Supprimer le Post</h1>
                    <p class="text-muted mb-0">Cette action est irréversible</p>
                </div>
                <div>
                    <a href="{% url 'reseaux_social:detail' object.pk %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour aux détails
                    </a>
                </div>
            </div>
        </div>

        <!-- Messages -->
        {% if messages %}
        <div class="container-fluid">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Contenu de Suppression -->
        <div class="container-fluid">
            <!-- Avertissement -->
            <div class="delete-warning">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning me-3"></i>
                    <div>
                        <h5 class="mb-1">Attention !</h5>
                        <p class="mb-0">Vous êtes sur le point de supprimer définitivement ce post. Cette action ne peut pas être annulée.</p>
                    </div>
                </div>
            </div>

            <!-- Résumé du Post -->
            <div class="post-summary">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>Post à supprimer
                </h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <strong>Titre:</strong>
                            <div>{{ object.titre }}</div>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Plateforme:</strong>
                            <div>{{ object.get_plateforme_display }}</div>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Statut:</strong>
                            <div>{{ object.get_statut_display }}</div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <strong>Auteur:</strong>
                            <div>{{ object.auteur.username }}</div>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Créé le:</strong>
                            <div>{{ object.date_creation|date:"d/m/Y à H:i" }}</div>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Type:</strong>
                            <div>{{ object.get_type_contenu_display }}</div>
                        </div>
                    </div>
                </div>
                
                {% if object.contenu %}
                <div class="mb-3">
                    <strong>Contenu:</strong>
                    <div class="bg-light p-2 rounded mt-1">
                        {{ object.contenu|truncatewords:30 }}
                    </div>
                </div>
                {% endif %}
                
                {% if object.image_url %}
                <div class="mb-3">
                    <strong>Image:</strong>
                    <div class="mt-1">
                        <img src="{{ object.image_url }}" alt="Image du post" class="img-thumbnail" style="max-height: 150px;">
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Formulaire de Confirmation -->
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-trash me-2"></i>Confirmation de Suppression
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if object.post_id_facebook %}
                        <div class="mb-3">
                            <div class="alert alert-info">
                                <i class="fas fa-facebook me-2"></i>
                                <strong>Post publié sur Facebook</strong>
                                <div class="small">ID: {{ object.post_id_facebook }}</div>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="delete_facebook" id="deleteFacebook">
                                <label class="form-check-label" for="deleteFacebook">
                                    <strong>Supprimer aussi sur Facebook</strong>
                                    <div class="small text-muted">Le post sera supprimé de votre page Facebook MeetVoice</div>
                                </label>
                            </div>
                        </div>
                        {% endif %}

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                                <label class="form-check-label" for="confirmDelete">
                                    Je confirme vouloir supprimer définitivement ce post
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-danger" id="deleteButton" disabled>
                                <i class="fas fa-trash me-2"></i>Supprimer Définitivement
                            </button>
                            <a href="{% url 'reseaux_social:detail' object.pk %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Activer le bouton de suppression seulement si la case est cochée
document.getElementById('confirmDelete').addEventListener('change', function() {
    document.getElementById('deleteButton').disabled = !this.checked;
});
</script>
{% endblock %}
