{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - Meet Voice{% endblock %}

{% block head %}
<meta name="description" content="Détails du post pour les réseaux sociaux">
<link rel="stylesheet" href="{% static 'reseaux_social/css/reseaux_social.css' %}" />
<style>
/* Styles critiques pour l'interface réseaux sociaux */
:root {
    --admin-primary: #2a1d34;
    --admin-secondary: #3d2a4a;
    --admin-accent: #667eea;
    --admin-success: #27ae60;
    --admin-warning: #f39c12;
    --admin-danger: #e74c3c;
    --admin-info: #3498db;
    --admin-light: #ecf0f1;
    --admin-dark: #2a1d34;
    --sidebar-width: 250px;
    --border-radius: 0.5rem;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
}

.admin-interface {
    display: flex;
    min-height: 100vh;
    background: #f8f9fa;
}

.admin-sidebar {
    width: var(--sidebar-width);
    background: var(--admin-primary);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.admin-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.admin-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-1px);
}

.admin-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-left-color: var(--admin-accent);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.admin-nav .nav-link i {
    width: 20px;
    margin-right: 0.5rem;
    text-align: center;
}

.admin-content {
    margin-left: var(--sidebar-width);
    flex: 1;
    min-height: 100vh;
    background: #f8f9fa;
}

.admin-header {
    background: white;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 2rem;
}

.admin-header h1 {
    margin-bottom: 0.5rem;
    color: var(--admin-dark);
}

.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.badge-brouillon {
    background-color: #6c757d;
    color: white;
}

.badge-programme {
    background-color: var(--admin-warning);
    color: white;
}

.badge-publie {
    background-color: var(--admin-success);
    color: white;
}

.badge-echec {
    background-color: var(--admin-danger);
    color: white;
}

.badge-archive {
    background-color: #495057;
    color: white;
}

.badge-plateforme {
    background-color: var(--admin-info);
    color: white;
}

.timeline {
    position: relative;
    padding-left: 1.5rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 1rem;
}

.timeline-marker {
    position: absolute;
    left: -1.5rem;
    top: 0.25rem;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    padding-left: 0.5rem;
}

.post-content-preview {
    background: #f8f9fa;
    border-left: 4px solid var(--admin-accent);
    padding: 1rem;
    border-radius: 0.25rem;
    font-size: 1.1rem;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }

    .admin-sidebar.show {
        transform: translateX(0);
    }

    .admin-content {
        margin-left: 0;
    }

    .admin-header {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Interface Administrative avec Sidebar -->
<div class="admin-interface">
    <!-- Sidebar Navigation -->
    <nav class="admin-sidebar">
        <div class="text-center p-3 border-bottom">
            <h5 class="text-white mb-1">
                <i class="fas fa-share-alt me-2"></i>Réseaux Sociaux
            </h5>
            <small class="text-white-50">Détails du Post</small>
        </div>
        <ul class="nav flex-column admin-nav">
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:dashboard' %}">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'abonnement:liste' %}">
                    <i class="fas fa-tags"></i>Gestion Abonnements
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{% url 'reseaux_social:liste' %}">
                    <i class="fas fa-share-alt"></i>Réseaux Sociaux
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:traffic_manager' %}">
                    <i class="fas fa-chart-line"></i>Traffic Manager
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:google_analytics' %}">
                    <i class="fab fa-google"></i>Google Analytics
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:articles' %}">
                    <i class="fas fa-newspaper"></i>Articles
                </a>
            </li>
            <hr style="border-color: rgba(255, 255, 255, 0.2)" />
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:stats' %}">
                    <i class="fas fa-chart-bar"></i>Statistiques
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:calendar' %}">
                    <i class="fas fa-calendar"></i>Calendrier
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:templates' %}">
                    <i class="fas fa-file-alt"></i>Templates
                </a>
            </li>
            <hr style="border-color: rgba(255, 255, 255, 0.2)" />
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin:index' %}">
                    <i class="fas fa-tools"></i>Admin Django
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'index' %}">
                    <i class="fas fa-home"></i>Retour au site
                </a>
            </li>
        </ul>
    </nav>
    
    <!-- Contenu Principal -->
    <div class="admin-content">
        <!-- Header Admin -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-eye me-2"></i>{{ post.titre }}</h1>
                    <p class="text-muted mb-0">Détails et actions pour ce post</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{% url 'reseaux_social:edit' post.pk %}" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>Modifier
                    </a>
                    {% if post.peut_etre_publie %}
                    <button type="button" class="btn btn-success me-2" onclick="publishPost({{ post.pk }}, this)">
                        <i class="fas fa-share me-2"></i>Publier
                    </button>
                    <button type="button" class="btn btn-primary me-2" onclick="autoPublishPost({{ post.pk }}, this)">
                        <i class="fas fa-rocket me-2"></i>Auto-Publier
                    </button>
                    <button type="button" class="btn btn-outline-info me-2" onclick="copyPostContent()">
                        <i class="fas fa-copy me-2"></i>Copier le contenu
                    </button>
                    {% if post.post_id_facebook %}
                    <button type="button" class="btn btn-outline-danger" onclick="deleteFacebookPost({{ post.pk }})">
                        <i class="fab fa-facebook me-2"></i>Supprimer de Facebook
                    </button>
                    {% endif %}
                    {% endif %}
                    <a href="{% url 'reseaux_social:liste' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                </div>
            </div>
        </div>

        <!-- Messages -->
        {% if messages %}
        <div class="container-fluid">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Contenu du Post -->
        <div class="container-fluid">
            <div class="row">
                <!-- Informations Principales -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Informations du Post
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>Plateforme:</strong>
                                    <span class="badge badge-plateforme ms-2">{{ post.get_plateforme_display }}</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>Type:</strong>
                                    <span class="badge bg-info text-white ms-2">{{ post.get_type_contenu_display }}</span>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>Statut:</strong>
                                    <span class="badge badge-{{ post.statut }} ms-2">{{ post.get_statut_display }}</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>Auteur:</strong>
                                    <span class="ms-2">{{ post.auteur.username }}</span>
                                </div>
                            </div>

                            {% if post.date_programmee %}
                            <div class="row mb-3">
                                <div class="col-12">
                                    <strong>Date programmée:</strong>
                                    <span class="ms-2 text-info">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ post.date_programmee|date:"d/m/Y à H:i" }}
                                    </span>
                                </div>
                            </div>
                            {% endif %}

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>Créé le:</strong>
                                    <span class="ms-2">{{ post.date_creation|date:"d/m/Y à H:i" }}</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>Modifié le:</strong>
                                    <span class="ms-2">{{ post.date_modification|date:"d/m/Y à H:i" }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contenu -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-align-left me-2"></i>Contenu du Post
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="post-content-preview post-content">
                                {{ post.contenu|linebreaks }}
                            </div>

                            {% if post.hashtags %}
                            <div class="mt-3">
                                <strong>Hashtags:</strong>
                                <div class="post-hashtags mt-2">
                                    {{ post.hashtags }}
                                </div>
                            </div>
                            {% endif %}

                            {% if post.lien_externe %}
                            <div class="mt-3">
                                <strong>Lien externe:</strong>
                                <div class="mt-2">
                                    <a href="{{ post.lien_externe }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-external-link-alt me-1"></i>
                                        {{ post.lien_externe|truncatechars:50 }}
                                    </a>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Image -->
                    {% if post.has_image %}
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-image me-2"></i>Image du Post
                            </h5>
                            {% if post.image_file %}
                                <span class="badge bg-success">
                                    <i class="fas fa-hdd me-1"></i>Stockée localement
                                </span>
                            {% else %}
                                <span class="badge bg-info">
                                    <i class="fas fa-cloud me-1"></i>URL externe
                                </span>
                            {% endif %}
                        </div>
                        <div class="card-body text-center">
                            <img src="{{ post.image_display_url }}" alt="Image du post" class="img-fluid rounded shadow">
                            
                            {% if post.image_prompt %}
                            <div class="mt-3">
                                <small class="text-muted">
                                    <strong>Prompt utilisé:</strong> {{ post.image_prompt }}
                                </small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Actions et Métadonnées -->
                <div class="col-lg-4">
                    <!-- Actions Rapides -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>Actions Rapides
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{% url 'reseaux_social:edit' post.pk %}" class="btn btn-outline-primary">
                                    <i class="fas fa-edit me-2"></i>Modifier
                                </a>
                                
                                <a href="{% url 'reseaux_social:preview' post.pk %}" class="btn btn-outline-info" target="_blank">
                                    <i class="fas fa-search me-2"></i>Prévisualiser
                                </a>
                                
                                <button type="button" class="btn btn-outline-secondary" onclick="duplicatePost({{ post.pk }})">
                                    <i class="fas fa-copy me-2"></i>Dupliquer
                                </button>
                                
                                {% if post.peut_etre_publie %}
                                <button type="button" class="btn btn-success me-2" onclick="publishPost({{ post.pk }})">
                                    <i class="fas fa-share me-2"></i>Publier Maintenant
                                </button>
                                <button type="button" class="btn btn-primary" onclick="autoPublishPost({{ post.pk }})">
                                    <i class="fas fa-rocket me-2"></i>Auto-Publier
                                </button>
                                {% endif %}
                                
                                <hr>
                                
                                <a href="{% url 'reseaux_social:delete' post.pk %}" class="btn btn-outline-danger">
                                    <i class="fas fa-trash me-2"></i>Supprimer
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Statistiques -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>Informations Techniques
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <small class="text-muted">Longueur du contenu:</small>
                                <div><strong>{{ post.contenu|length }} caractères</strong></div>
                            </div>
                            
                            {% if post.hashtags %}
                            <div class="mb-2">
                                <small class="text-muted">Nombre de hashtags:</small>
                                <div><strong>{{ post.hashtags|wordcount }} hashtags</strong></div>
                            </div>
                            {% endif %}
                            
                            <div class="mb-2">
                                <small class="text-muted">ID du post:</small>
                                <div><strong>#{{ post.id }}</strong></div>
                            </div>
                            
                            {% if post.resultats_publication %}
                            <div class="mb-2">
                                <small class="text-muted">Résultats publication:</small>
                                <div class="mt-1">
                                    <pre class="small bg-light p-2 rounded">{{ post.resultats_publication }}</pre>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Historique -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-history me-2"></i>Historique
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <small class="text-muted">{{ post.date_creation|date:"d/m/Y H:i" }}</small>
                                        <div>Post créé par {{ post.auteur.username }}</div>
                                    </div>
                                </div>
                                
                                {% if post.date_modification != post.date_creation %}
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-warning"></div>
                                    <div class="timeline-content">
                                        <small class="text-muted">{{ post.date_modification|date:"d/m/Y H:i" }}</small>
                                        <div>Post modifié</div>
                                    </div>
                                </div>
                                {% endif %}
                                
                                {% if post.date_publication %}
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <small class="text-muted">{{ post.date_publication|date:"d/m/Y H:i" }}</small>
                                        <div>Post publié</div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'reseaux_social/js/reseaux_social.js' %}"></script>
{% endblock %}
