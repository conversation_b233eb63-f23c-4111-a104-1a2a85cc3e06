{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - Meet Voice{% endblock %}

{% block head %}
<meta name="description" content="Modifier le post pour les réseaux sociaux">
<link rel="stylesheet" href="{% static 'reseaux_social/css/reseaux_social.css' %}" />
<style>
/* Styles critiques pour l'interface réseaux sociaux */
:root {
    --admin-primary: #2a1d34;
    --admin-secondary: #3d2a4a;
    --admin-accent: #667eea;
    --admin-success: #27ae60;
    --admin-warning: #f39c12;
    --admin-danger: #e74c3c;
    --admin-info: #3498db;
    --admin-light: #ecf0f1;
    --admin-dark: #2a1d34;
    --sidebar-width: 250px;
    --border-radius: 0.5rem;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
}

.admin-interface {
    display: flex;
    min-height: 100vh;
    background: #f8f9fa;
}

.admin-sidebar {
    width: var(--sidebar-width);
    background: var(--admin-primary);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.admin-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.admin-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-1px);
}

.admin-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-left-color: var(--admin-accent);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.admin-nav .nav-link i {
    width: 20px;
    margin-right: 0.5rem;
    text-align: center;
}

.admin-content {
    margin-left: var(--sidebar-width);
    flex: 1;
    min-height: 100vh;
    background: #f8f9fa;
}

.admin-header {
    background: white;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 2rem;
}

.admin-header h1 {
    margin-bottom: 0.5rem;
    color: var(--admin-dark);
}

@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-sidebar.show {
        transform: translateX(0);
    }
    
    .admin-content {
        margin-left: 0;
    }
    
    .admin-header {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Interface Administrative avec Sidebar -->
<div class="admin-interface">
    <!-- Sidebar Navigation -->
    <nav class="admin-sidebar">
        <div class="text-center p-3 border-bottom">
            <h5 class="text-white mb-1">
                <i class="fas fa-share-alt me-2"></i>Réseaux Sociaux
            </h5>
            <small class="text-white-50">Modifier un Post</small>
        </div>
        <ul class="nav flex-column admin-nav">
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:dashboard' %}">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'abonnement:liste' %}">
                    <i class="fas fa-tags"></i>Gestion Abonnements
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{% url 'reseaux_social:liste' %}">
                    <i class="fas fa-share-alt"></i>Réseaux Sociaux
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:traffic_manager' %}">
                    <i class="fas fa-chart-line"></i>Traffic Manager
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:google_analytics' %}">
                    <i class="fab fa-google"></i>Google Analytics
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:articles' %}">
                    <i class="fas fa-newspaper"></i>Articles
                </a>
            </li>
            <hr style="border-color: rgba(255, 255, 255, 0.2)" />
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:stats' %}">
                    <i class="fas fa-chart-bar"></i>Statistiques
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:calendar' %}">
                    <i class="fas fa-calendar"></i>Calendrier
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:templates' %}">
                    <i class="fas fa-file-alt"></i>Templates
                </a>
            </li>
            <hr style="border-color: rgba(255, 255, 255, 0.2)" />
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin:index' %}">
                    <i class="fas fa-tools"></i>Admin Django
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'index' %}">
                    <i class="fas fa-home"></i>Retour au site
                </a>
            </li>
        </ul>
    </nav>
    
    <!-- Contenu Principal -->
    <div class="admin-content">
        <!-- Header Admin -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-edit me-2"></i>Modifier le Post</h1>
                    <p class="text-muted mb-0">Modifiez les informations de votre post</p>
                </div>
                <div>
                    <a href="{% url 'reseaux_social:detail' object.pk %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour aux détails
                    </a>
                </div>
            </div>
        </div>

        <!-- Messages -->
        {% if messages %}
        <div class="container-fluid">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Formulaire de Modification -->
        <div class="container-fluid">
            <div class="row">
                <!-- Formulaire Principal -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-edit me-2"></i>Informations du Post
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="post" id="postForm">
                                {% csrf_token %}
                                
                                <!-- Titre -->
                                <div class="mb-3">
                                    <label for="{{ form.titre.id_for_label }}" class="form-label">
                                        <i class="fas fa-heading me-1"></i>Titre du Post
                                    </label>
                                    {{ form.titre }}
                                    {% if form.titre.errors %}
                                        <div class="text-danger small">{{ form.titre.errors }}</div>
                                    {% endif %}
                                </div>

                                <!-- Plateforme et Type -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.plateforme.id_for_label }}" class="form-label">
                                                <i class="fas fa-share-alt me-1"></i>Plateforme
                                            </label>
                                            {{ form.plateforme }}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.type_contenu.id_for_label }}" class="form-label">
                                                <i class="fas fa-tag me-1"></i>Type de Contenu
                                            </label>
                                            {{ form.type_contenu }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Statut -->
                                <div class="mb-3">
                                    <label for="{{ form.statut.id_for_label }}" class="form-label">
                                        <i class="fas fa-flag me-1"></i>Statut
                                    </label>
                                    {{ form.statut }}
                                </div>

                                <!-- Contenu -->
                                <div class="mb-3">
                                    <label for="{{ form.contenu.id_for_label }}" class="form-label">
                                        <i class="fas fa-align-left me-1"></i>Contenu du Post
                                    </label>
                                    {{ form.contenu }}
                                    <div class="form-text">
                                        <small class="text-muted">
                                            <span id="charCount">{{ form.contenu.value|length|default:0 }}</span> caractères
                                        </small>
                                    </div>
                                    {% if form.contenu.errors %}
                                        <div class="text-danger small">{{ form.contenu.errors }}</div>
                                    {% endif %}
                                </div>

                                <!-- Hashtags -->
                                <div class="mb-3">
                                    <label for="{{ form.hashtags.id_for_label }}" class="form-label">
                                        <i class="fas fa-hashtag me-1"></i>Hashtags
                                    </label>
                                    {{ form.hashtags }}
                                    <div class="form-text">
                                        <small class="text-muted">Séparez les hashtags par des espaces (ex: #meetvoice #dating #voice)</small>
                                    </div>
                                </div>

                                <!-- Image -->
                                <div class="mb-3">
                                    <label for="{{ form.image_prompt.id_for_label }}" class="form-label">
                                        <i class="fas fa-image me-1"></i>Prompt pour l'Image
                                    </label>
                                    <div class="input-group">
                                        {{ form.image_prompt }}
                                        <button type="button" class="btn btn-outline-primary" onclick="generateImageFromPrompt()">
                                            <i class="fas fa-magic me-1"></i>Générer Image
                                        </button>
                                    </div>
                                    <div class="form-text">
                                        <small class="text-muted">Décrivez l'image que vous souhaitez générer avec Pollinations.ai</small>
                                    </div>
                                </div>

                                <!-- Aperçu de l'image actuelle -->
                                {% if object.image_url %}
                                <div class="mb-3">
                                    <label class="form-label">Image Actuelle</label>
                                    <div class="border rounded p-3 text-center">
                                        <img src="{{ object.image_url }}" alt="Image actuelle" class="img-fluid" style="max-height: 200px;">
                                    </div>
                                </div>
                                {% endif %}

                                <!-- Aperçu de la nouvelle image -->
                                <div class="mb-3" id="imagePreview" style="display: none;">
                                    <label class="form-label">Nouvelle Image</label>
                                    <div id="imageContainer" class="border rounded p-3 text-center">
                                        <!-- L'image générée apparaîtra ici -->
                                    </div>
                                </div>

                                <!-- Lien externe -->
                                <div class="mb-3">
                                    <label for="{{ form.lien_externe.id_for_label }}" class="form-label">
                                        <i class="fas fa-link me-1"></i>Lien Externe (optionnel)
                                    </label>
                                    {{ form.lien_externe }}
                                </div>

                                <!-- Date programmée -->
                                <div class="mb-3">
                                    <label for="{{ form.date_programmee.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>Date de Publication (optionnel)
                                    </label>
                                    {{ form.date_programmee }}
                                    <div class="form-text">
                                        <small class="text-muted">Laissez vide pour publier immédiatement</small>
                                    </div>
                                </div>

                                <!-- Boutons d'action -->
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Sauvegarder
                                    </button>
                                    <a href="{% url 'reseaux_social:preview' object.pk %}" class="btn btn-outline-info" target="_blank">
                                        <i class="fas fa-search me-2"></i>Prévisualiser
                                    </a>
                                    <a href="{% url 'reseaux_social:detail' object.pk %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Annuler
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Panneau d'Aide -->
                <div class="col-lg-4">
                    <!-- Actions Rapides -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>Actions Rapides
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{% url 'reseaux_social:preview' object.pk %}" class="btn btn-outline-info" target="_blank">
                                    <i class="fas fa-search me-2"></i>Prévisualiser
                                </a>
                                
                                <button type="button" class="btn btn-outline-secondary" onclick="duplicatePost({{ object.pk }})">
                                    <i class="fas fa-copy me-2"></i>Dupliquer
                                </button>
                                
                                {% if object.peut_etre_publie %}
                                <button type="button" class="btn btn-success" onclick="publishPost({{ object.pk }})">
                                    <i class="fas fa-share me-2"></i>Publier
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Informations -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Informations
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <small class="text-muted">Créé le:</small>
                                <div><strong>{{ object.date_creation|date:"d/m/Y à H:i" }}</strong></div>
                            </div>
                            
                            <div class="mb-2">
                                <small class="text-muted">Modifié le:</small>
                                <div><strong>{{ object.date_modification|date:"d/m/Y à H:i" }}</strong></div>
                            </div>
                            
                            <div class="mb-2">
                                <small class="text-muted">Auteur:</small>
                                <div><strong>{{ object.auteur.username }}</strong></div>
                            </div>
                            
                            <div class="mb-2">
                                <small class="text-muted">ID:</small>
                                <div><strong>#{{ object.id }}</strong></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'reseaux_social/js/reseaux_social.js' %}"></script>
<script>
// Compteur de caractères
document.getElementById('id_contenu').addEventListener('input', function() {
    document.getElementById('charCount').textContent = this.value.length;
});

// Générer une image depuis le prompt
async function generateImageFromPrompt() {
    const prompt = document.getElementById('id_image_prompt').value;
    
    if (!prompt.trim()) {
        alert('Veuillez saisir un prompt pour générer l\'image');
        return;
    }
    
    try {
        const imageUrl = await generateImage(prompt, 'imageContainer');
        if (imageUrl) {
            document.getElementById('imagePreview').style.display = 'block';
        }
    } catch (error) {
        console.error('Erreur:', error);
    }
}
</script>
{% endblock %}
