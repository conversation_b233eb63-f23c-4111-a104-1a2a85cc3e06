{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - Meet Voice{% endblock %}

{% block head %}
<meta name="description" content="Interface de gestion des posts pour les réseaux sociaux Meet Voice">
<link rel="stylesheet" href="{% static 'reseaux_social/css/reseaux_social.css' %}" />
<style>
/* Styles critiques pour l'interface réseaux sociaux */
:root {
    --admin-primary: #2a1d34;
    --admin-secondary: #3d2a4a;
    --admin-accent: #667eea;
    --admin-success: #27ae60;
    --admin-warning: #f39c12;
    --admin-danger: #e74c3c;
    --admin-info: #3498db;
    --admin-light: #ecf0f1;
    --admin-dark: #2a1d34;
    --sidebar-width: 250px;
    --border-radius: 0.5rem;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
}

.admin-interface {
    display: flex;
    min-height: 100vh;
    background: #f8f9fa;
}

.admin-sidebar {
    width: var(--sidebar-width);
    background: var(--admin-primary);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.admin-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.admin-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-1px);
}

.admin-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-left-color: var(--admin-accent);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.admin-nav .nav-link i {
    width: 20px;
    margin-right: 0.5rem;
    text-align: center;
}

.admin-content {
    margin-left: var(--sidebar-width);
    flex: 1;
    min-height: 100vh;
    background: #f8f9fa;
}

.admin-header {
    background: white;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 2rem;
}

.admin-header h1 {
    margin-bottom: 0.5rem;
    color: var(--admin-dark);
}

.stats-card {
    background: var(--admin-primary);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.2s ease;
    margin-bottom: 1rem;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.stats-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.9;
}

.post-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.post-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-hover);
}

.post-header {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
}

.post-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--admin-dark);
}

.post-meta {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.post-body {
    padding: 1rem;
    flex-grow: 1;
}

.post-image {
    margin-bottom: 1rem;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.post-image img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.post-content p {
    margin-bottom: 0.5rem;
    color: #6c757d;
    line-height: 1.5;
}

.post-hashtags {
    color: var(--admin-accent);
    font-size: 0.9rem;
    font-weight: 500;
}

.post-footer {
    padding: 1rem;
    border-top: 1px solid #f0f0f0;
    background: #f8f9fa;
}

.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.badge-brouillon {
    background-color: #6c757d;
    color: white;
}

.badge-programme {
    background-color: var(--admin-warning);
    color: white;
}

.badge-publie {
    background-color: var(--admin-success);
    color: white;
}

.badge-echec {
    background-color: var(--admin-danger);
    color: white;
}

.badge-archive {
    background-color: #495057;
    color: white;
}

.badge-plateforme {
    background-color: var(--admin-info);
    color: white;
}

@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }

    .admin-sidebar.show {
        transform: translateX(0);
    }

    .admin-content {
        margin-left: 0;
    }

    .admin-header {
        padding: 1rem;
    }

    .post-card {
        margin-bottom: 1rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Interface Administrative avec Sidebar -->
<div class="admin-interface">
    <!-- Sidebar Navigation -->
    <nav class="admin-sidebar">
        <div class="text-center p-3 border-bottom">
            <h5 class="text-white mb-1">
                <i class="fas fa-share-alt me-2"></i>Réseaux Sociaux
            </h5>
            <small class="text-white-50">Gestion des Posts</small>
        </div>
        <ul class="nav flex-column admin-nav">
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:dashboard' %}">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'abonnement:liste' %}">
                    <i class="fas fa-tags"></i>Gestion Abonnements
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{% url 'reseaux_social:liste' %}">
                    <i class="fas fa-share-alt"></i>Réseaux Sociaux
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:traffic_manager' %}">
                    <i class="fas fa-chart-line"></i>Traffic Manager
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:google_analytics' %}">
                    <i class="fab fa-google"></i>Google Analytics
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:articles' %}">
                    <i class="fas fa-newspaper"></i>Articles
                </a>
            </li>
            <hr style="border-color: rgba(255, 255, 255, 0.2)" />
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:stats' %}">
                    <i class="fas fa-chart-bar"></i>Statistiques
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:calendar' %}">
                    <i class="fas fa-calendar"></i>Calendrier
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:templates' %}">
                    <i class="fas fa-file-alt"></i>Templates
                </a>
            </li>
            <hr style="border-color: rgba(255, 255, 255, 0.2)" />
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin:index' %}">
                    <i class="fas fa-tools"></i>Admin Django
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'index' %}">
                    <i class="fas fa-home"></i>Retour au site
                </a>
            </li>
        </ul>
    </nav>
    
    <!-- Contenu Principal -->
    <div class="admin-content">
        <!-- Header Admin -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-share-alt me-2"></i>Gestion des Posts Réseaux Sociaux</h1>
                    <p class="text-muted mb-0">Créez, gérez et publiez vos posts sur tous les réseaux sociaux</p>
                </div>
                <div>
                    <a href="{% url 'reseaux_social:create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Créer un Post
                    </a>
                </div>
            </div>
        </div>

        <!-- Messages -->
        {% if messages %}
        <div class="container-fluid">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Statistiques Rapides -->
        <div class="container-fluid mb-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="stats-card bg-primary text-white">
                        <div class="stats-value">{{ stats.total }}</div>
                        <div class="stats-label">Total Posts</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card bg-success text-white">
                        <div class="stats-value">{{ stats.publies }}</div>
                        <div class="stats-label">Publiés</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card bg-warning text-white">
                        <div class="stats-value">{{ stats.programmes }}</div>
                        <div class="stats-label">Programmés</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card bg-secondary text-white">
                        <div class="stats-value">{{ stats.brouillons }}</div>
                        <div class="stats-label">Brouillons</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtres et Recherche -->
        <div class="container-fluid mb-4">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">Recherche</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ current_filters.search }}" placeholder="Titre, contenu...">
                        </div>
                        <div class="col-md-3">
                            <label for="statut" class="form-label">Statut</label>
                            <select class="form-control" id="statut" name="statut">
                                <option value="">Tous les statuts</option>
                                {% for value, label in statuts %}
                                <option value="{{ value }}" {% if current_filters.statut == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="plateforme" class="form-label">Plateforme</label>
                            <select class="form-control" id="plateforme" name="plateforme">
                                <option value="">Toutes les plateformes</option>
                                {% for value, label in plateformes %}
                                <option value="{{ value }}" {% if current_filters.plateforme == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>Filtrer
                            </button>
                            <a href="{% url 'reseaux_social:liste' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Reset
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Liste des Posts -->
        <div class="container-fluid">
            {% if posts %}
            <div class="row">
                {% for post in posts %}
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="post-card">
                        <!-- Header de la carte -->
                        <div class="post-header">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h5 class="post-title">{{ post.titre }}</h5>
                                    <div class="post-meta">
                                        <span class="badge badge-{{ post.statut }}">{{ post.get_statut_display }}</span>
                                        <span class="badge badge-plateforme">{{ post.get_plateforme_display }}</span>
                                    </div>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{% url 'reseaux_social:detail' post.pk %}">
                                            <i class="fas fa-eye me-2"></i>Voir
                                        </a></li>
                                        <li><a class="dropdown-item" href="{% url 'reseaux_social:edit' post.pk %}">
                                            <i class="fas fa-edit me-2"></i>Modifier
                                        </a></li>
                                        <li><a class="dropdown-item" href="{% url 'reseaux_social:preview' post.pk %}">
                                            <i class="fas fa-search me-2"></i>Prévisualiser
                                        </a></li>
                                        {% if post.peut_etre_publie %}
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-success" href="#" onclick="publishPost({{ post.pk }}, this)">
                                            <i class="fas fa-share me-2"></i>Publier
                                        </a></li>
                                        {% endif %}
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="{% url 'reseaux_social:duplicate' post.pk %}" 
                                               onclick="return confirm('Dupliquer ce post ?')">
                                            <i class="fas fa-copy me-2"></i>Dupliquer
                                        </a></li>
                                        <li><a class="dropdown-item text-danger" href="{% url 'reseaux_social:delete' post.pk %}">
                                            <i class="fas fa-trash me-2"></i>Supprimer
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Contenu de la carte -->
                        <div class="post-body">
                            {% if post.has_image %}
                            <div class="post-image">
                                <img src="{{ post.image_display_url }}" alt="Image du post" class="img-fluid">
                                {% if post.image_file %}
                                    <small class="text-success position-absolute top-0 end-0 bg-success text-white px-2 py-1 rounded">
                                        <i class="fas fa-hdd"></i> Local
                                    </small>
                                {% else %}
                                    <small class="text-info position-absolute top-0 end-0 bg-info text-white px-2 py-1 rounded">
                                        <i class="fas fa-cloud"></i> Externe
                                    </small>
                                {% endif %}
                            </div>
                            {% endif %}
                            
                            <div class="post-content">
                                <p>{{ post.contenu|truncatewords:20 }}</p>
                                {% if post.hashtags %}
                                <div class="post-hashtags">
                                    {{ post.hashtags|truncatewords:5 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Footer de la carte -->
                        <div class="post-footer">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>{{ post.auteur.username }}
                                    <br>
                                    <i class="fas fa-clock me-1"></i>{{ post.date_creation|date:"d/m/Y H:i" }}
                                </small>
                                {% if post.date_programmee %}
                                <small class="text-info">
                                    <i class="fas fa-calendar me-1"></i>{{ post.date_programmee|date:"d/m/Y H:i" }}
                                </small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Navigation des posts">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.statut %}&statut={{ request.GET.statut }}{% endif %}{% if request.GET.plateforme %}&plateforme={{ request.GET.plateforme }}{% endif %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.statut %}&statut={{ request.GET.statut }}{% endif %}{% if request.GET.plateforme %}&plateforme={{ request.GET.plateforme }}{% endif %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} / {{ page_obj.paginator.num_pages }}</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.statut %}&statut={{ request.GET.statut }}{% endif %}{% if request.GET.plateforme %}&plateforme={{ request.GET.plateforme }}{% endif %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.statut %}&statut={{ request.GET.statut }}{% endif %}{% if request.GET.plateforme %}&plateforme={{ request.GET.plateforme }}{% endif %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-share-alt fa-3x text-muted mb-3"></i>
                <h3>Aucun post trouvé</h3>
                <p class="text-muted">Commencez par créer votre premier post pour les réseaux sociaux.</p>
                <a href="{% url 'reseaux_social:create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Créer le premier post
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'reseaux_social/js/reseaux_social.js' %}"></script>
{% endblock %}
