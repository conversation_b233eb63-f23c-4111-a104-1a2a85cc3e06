{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - Meet Voice{% endblock %}

{% block head %}
<meta name="description" content="Prévisualisation du post pour les réseaux sociaux">
<link rel="stylesheet" href="{% static 'reseaux_social/css/reseaux_social.css' %}" />
<style>
/* Styles critiques pour l'interface réseaux sociaux */
:root {
    --admin-primary: #2a1d34;
    --admin-secondary: #3d2a4a;
    --admin-accent: #667eea;
    --admin-success: #27ae60;
    --admin-warning: #f39c12;
    --admin-danger: #e74c3c;
    --admin-info: #3498db;
    --admin-light: #ecf0f1;
    --admin-dark: #2a1d34;
    --sidebar-width: 250px;
    --border-radius: 0.5rem;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
}

.admin-interface {
    display: flex;
    min-height: 100vh;
    background: #f8f9fa;
}

.admin-sidebar {
    width: var(--sidebar-width);
    background: var(--admin-primary);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.admin-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.admin-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-1px);
}

.admin-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-left-color: var(--admin-accent);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.admin-nav .nav-link i {
    width: 20px;
    margin-right: 0.5rem;
    text-align: center;
}

.admin-content {
    margin-left: var(--sidebar-width);
    flex: 1;
    min-height: 100vh;
    background: #f8f9fa;
}

.admin-header {
    background: white;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 2rem;
}

.admin-header h1 {
    margin-bottom: 0.5rem;
    color: var(--admin-dark);
}

.preview-container {
    max-width: 600px;
    margin: 0 auto;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.preview-header {
    background: #f8f9fa;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.preview-platform {
    font-weight: 600;
    color: var(--admin-primary);
}

.preview-content {
    padding: 1.5rem;
}

.preview-text {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    white-space: pre-wrap;
}

.preview-hashtags {
    color: var(--admin-accent);
    font-weight: 500;
    margin-bottom: 1rem;
}

.preview-image {
    margin-bottom: 1rem;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.preview-image img {
    width: 100%;
    height: auto;
    display: block;
}

.preview-link {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 0.75rem;
    margin-bottom: 1rem;
}

.preview-link a {
    color: var(--admin-accent);
    text-decoration: none;
    font-weight: 500;
}

.preview-link a:hover {
    text-decoration: underline;
}

.preview-meta {
    background: #f8f9fa;
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    font-size: 0.9rem;
    color: #6c757d;
}

@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-sidebar.show {
        transform: translateX(0);
    }
    
    .admin-content {
        margin-left: 0;
    }
    
    .admin-header {
        padding: 1rem;
    }
    
    .preview-container {
        margin: 0 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Interface Administrative avec Sidebar -->
<div class="admin-interface">
    <!-- Sidebar Navigation -->
    <nav class="admin-sidebar">
        <div class="text-center p-3 border-bottom">
            <h5 class="text-white mb-1">
                <i class="fas fa-share-alt me-2"></i>Réseaux Sociaux
            </h5>
            <small class="text-white-50">Prévisualisation</small>
        </div>
        <ul class="nav flex-column admin-nav">
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:dashboard' %}">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'abonnement:liste' %}">
                    <i class="fas fa-tags"></i>Gestion Abonnements
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{% url 'reseaux_social:liste' %}">
                    <i class="fas fa-share-alt"></i>Réseaux Sociaux
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:traffic_manager' %}">
                    <i class="fas fa-chart-line"></i>Traffic Manager
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:google_analytics' %}">
                    <i class="fab fa-google"></i>Google Analytics
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'backoffice:articles' %}">
                    <i class="fas fa-newspaper"></i>Articles
                </a>
            </li>
            <hr style="border-color: rgba(255, 255, 255, 0.2)" />
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:stats' %}">
                    <i class="fas fa-chart-bar"></i>Statistiques
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:calendar' %}">
                    <i class="fas fa-calendar"></i>Calendrier
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'reseaux_social:templates' %}">
                    <i class="fas fa-file-alt"></i>Templates
                </a>
            </li>
            <hr style="border-color: rgba(255, 255, 255, 0.2)" />
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin:index' %}">
                    <i class="fas fa-tools"></i>Admin Django
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'index' %}">
                    <i class="fas fa-home"></i>Retour au site
                </a>
            </li>
        </ul>
    </nav>
    
    <!-- Contenu Principal -->
    <div class="admin-content">
        <!-- Header Admin -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-search me-2"></i>Prévisualisation du Post</h1>
                    <p class="text-muted mb-0">Aperçu du post tel qu'il apparaîtra sur {{ post.get_plateforme_display }}</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{% url 'reseaux_social:edit' post.pk %}" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>Modifier
                    </a>
                    <a href="{% url 'reseaux_social:detail' post.pk %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour
                    </a>
                </div>
            </div>
        </div>

        <!-- Prévisualisation -->
        <div class="container-fluid">
            <div class="preview-container">
                <!-- Header de la prévisualisation -->
                <div class="preview-header">
                    <i class="fas fa-share-alt me-2"></i>
                    <span class="preview-platform">{{ post.get_plateforme_display }}</span>
                    <span class="ms-auto text-muted">{{ post.get_type_contenu_display }}</span>
                </div>

                <!-- Contenu du post -->
                <div class="preview-content">
                    <!-- Texte du post -->
                    <div class="preview-text">{{ post.contenu }}</div>

                    <!-- Hashtags -->
                    {% if post.hashtags %}
                    <div class="preview-hashtags">{{ post.hashtags }}</div>
                    {% endif %}

                    <!-- Image -->
                    {% if post.image_url %}
                    <div class="preview-image">
                        <img src="{{ post.image_url }}" alt="Image du post">
                    </div>
                    {% endif %}

                    <!-- Lien externe -->
                    {% if post.lien_externe %}
                    <div class="preview-link">
                        <a href="{{ post.lien_externe }}" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i>
                            {{ post.lien_externe }}
                        </a>
                    </div>
                    {% endif %}
                </div>

                <!-- Métadonnées -->
                <div class="preview-meta">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-user me-1"></i>{{ post.auteur.username }}
                        </div>
                        <div>
                            <i class="fas fa-clock me-1"></i>
                            {% if post.date_programmee %}
                                Programmé pour {{ post.date_programmee|date:"d/m/Y à H:i" }}
                            {% else %}
                                {{ post.date_creation|date:"d/m/Y à H:i" }}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="text-center mt-4">
                <div class="d-flex justify-content-center gap-2">
                    <a href="{% url 'reseaux_social:edit' post.pk %}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Modifier le Post
                    </a>
                    {% if post.peut_etre_publie %}
                    <button type="button" class="btn btn-success" onclick="publishPost({{ post.pk }}, this)">
                        <i class="fas fa-share me-2"></i>Publier Maintenant
                    </button>
                    {% endif %}
                    <a href="{% url 'reseaux_social:detail' post.pk %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour aux Détails
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'reseaux_social/js/reseaux_social.js' %}"></script>
{% endblock %}
