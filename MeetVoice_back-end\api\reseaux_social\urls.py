"""
URLs pour l'application de gestion des posts réseaux sociaux
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'reseaux_social'

# Configuration du router DRF
router = DefaultRouter()
router.register(r'posts', views.PostViewSet)

urlpatterns = [
    # API REST
    path('api/', include(router.urls)),

    # Interface principale de gestion
    path('', views.PostListView.as_view(), name='liste'),
    path('create/', views.PostCreateView.as_view(), name='create'),
    path('<int:pk>/', views.PostDetailView.as_view(), name='detail'),
    path('<int:pk>/edit/', views.PostUpdateView.as_view(), name='edit'),
    path('<int:pk>/delete/', views.PostDeleteView.as_view(), name='delete'),
    
    # Actions sur les posts
    path('<int:pk>/publish/', views.PublishPostView.as_view(), name='publish'),
    path('<int:pk>/preview/', views.PreviewPostView.as_view(), name='preview'),
    path('<int:pk>/duplicate/', views.DuplicatePostView.as_view(), name='duplicate'),
    
    # API endpoints
    path('api/generate-image/', views.GenerateImageAPIView.as_view(), name='api_generate_image'),
    path('api/generate-content/', views.GenerateContentAPIView.as_view(), name='api_generate_content'),
    path('api/publish-post/', views.PublishPostAPIView.as_view(), name='api_publish_post'),
    path('api/auto-publish/<int:post_id>/', views.AutoPublishAPIView.as_view(), name='api_auto_publish'),
    path('api/delete-facebook/<int:post_id>/', views.DeleteFacebookPostAPIView.as_view(), name='api_delete_facebook'),
    path('api/get-stats/', views.GetStatsAPIView.as_view(), name='api_get_stats'),
    
    # Templates
    path('templates/', views.TemplateListView.as_view(), name='templates'),
    path('templates/create/', views.TemplateCreateView.as_view(), name='template_create'),
    path('templates/<int:pk>/edit/', views.TemplateUpdateView.as_view(), name='template_edit'),
    path('templates/<int:pk>/delete/', views.TemplateDeleteView.as_view(), name='template_delete'),
    path('templates/<int:pk>/use/', views.UseTemplateView.as_view(), name='use_template'),
    
    # Statistiques et rapports
    path('stats/', views.StatsView.as_view(), name='stats'),
    path('calendar/', views.CalendarView.as_view(), name='calendar'),
]
