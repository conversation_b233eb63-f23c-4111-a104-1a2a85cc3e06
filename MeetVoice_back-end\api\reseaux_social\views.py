from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.urls import reverse_lazy, reverse
from django.http import JsonResponse
from django.views import View
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import Q, Count
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
import json
import logging

# DRF imports
from rest_framework import viewsets, status as drf_status, filters
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend

from .models import ReseauxSocialPost, PostTemplate
from .services import SocialMediaPublisher, GeminiContentService, PollinationsImageService, SimpleFacebookService
from .serializers import (
    PostSerializer, PostListSerializer, PostCreateSerializer,
    PostUpdateSerializer, PostStatsSerializer
)

logger = logging.getLogger(__name__)


# ============================================================================
# API REST VIEWSETS
# ============================================================================

class PostViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion des posts réseaux sociaux via API REST"""

    queryset = ReseauxSocialPost.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['statut', 'type_contenu', 'plateformes']
    search_fields = ['titre', 'contenu', 'hashtags', 'mentions']
    ordering_fields = ['titre', 'date_creation', 'date_publication_prevue', 'vues', 'likes']
    ordering = ['-date_creation']

    def get_serializer_class(self):
        """Retourne le serializer approprié selon l'action"""
        if self.action == 'list':
            return PostListSerializer
        elif self.action == 'create':
            return PostCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return PostUpdateSerializer
        elif self.action == 'stats':
            return PostStatsSerializer
        return PostSerializer

    def get_queryset(self):
        """Filtre les posts selon les permissions"""
        queryset = ReseauxSocialPost.objects.all()

        # Les utilisateurs non-staff ne voient que leurs propres posts
        if not self.request.user.is_staff:
            queryset = queryset.filter(auteur=self.request.user)

        return queryset

    def perform_create(self, serializer):
        """Définit l'auteur lors de la création"""
        serializer.save(auteur=self.request.user)

    @action(detail=False, methods=['get'])
    def draft(self, request):
        """Retourne les posts en brouillon"""
        draft_posts = self.get_queryset().filter(statut='brouillon')
        serializer = self.get_serializer(draft_posts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def scheduled(self, request):
        """Retourne les posts programmés"""
        scheduled_posts = self.get_queryset().filter(statut='programme')
        serializer = self.get_serializer(scheduled_posts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def published(self, request):
        """Retourne les posts publiés"""
        published_posts = self.get_queryset().filter(statut='publie')
        serializer = self.get_serializer(published_posts, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def generate_image(self, request, pk=None):
        """Génère une image pour le post"""
        post = self.get_object()
        prompt = request.data.get('prompt', post.image_prompt)

        if not prompt:
            return Response(
                {'error': 'Prompt requis pour générer l\'image'},
                status=drf_status.HTTP_400_BAD_REQUEST
            )

        try:
            # Utiliser le service de génération d'images
            image_service = PollinationsImageService()
            image_url = image_service.generate_image(prompt)

            if image_url:
                post.image_url = image_url
                post.image_prompt = prompt
                post.save()

                return Response({
                    'message': 'Image générée avec succès',
                    'image_url': image_url
                })
            else:
                return Response(
                    {'error': 'Erreur lors de la génération de l\'image'},
                    status=drf_status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return Response(
                {'error': f'Erreur: {str(e)}'},
                status=drf_status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def generate_content(self, request, pk=None):
        """Génère du contenu pour le post"""
        post = self.get_object()
        prompt = request.data.get('prompt', '')

        if not prompt:
            return Response(
                {'error': 'Prompt requis pour générer le contenu'},
                status=drf_status.HTTP_400_BAD_REQUEST
            )

        try:
            # Utiliser le service de génération de contenu
            content_service = GeminiContentService()
            generated_content = content_service.generate_content(prompt)

            if generated_content:
                post.contenu = generated_content
                post.save()

                return Response({
                    'message': 'Contenu généré avec succès',
                    'content': generated_content
                })
            else:
                return Response(
                    {'error': 'Erreur lors de la génération du contenu'},
                    status=drf_status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return Response(
                {'error': f'Erreur: {str(e)}'},
                status=drf_status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def publish(self, request, pk=None):
        """Publie le post sur les réseaux sociaux"""
        post = self.get_object()

        if post.statut == 'publie':
            return Response(
                {'error': 'Ce post est déjà publié'},
                status=drf_status.HTTP_400_BAD_REQUEST
            )

        try:
            # Utiliser le service de publication
            publisher = SocialMediaPublisher()
            result = publisher.publish_post(post)

            if result.get('success'):
                post.statut = 'publie'
                post.date_publication_reelle = timezone.now()
                post.save()

                return Response({
                    'message': 'Post publié avec succès',
                    'details': result
                })
            else:
                return Response(
                    {'error': f'Erreur lors de la publication: {result.get("error")}'},
                    status=drf_status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return Response(
                {'error': f'Erreur: {str(e)}'},
                status=drf_status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Retourne les statistiques des posts"""
        posts = self.get_queryset().filter(statut='publie')
        serializer = PostStatsSerializer(posts, many=True)
        return Response(serializer.data)


# ============================================================================
# VUES DJANGO TRADITIONNELLES (EXISTANTES)
# ============================================================================


class StaffRequiredMixin(UserPassesTestMixin):
    """Mixin pour vérifier que l'utilisateur est un employé"""

    def test_func(self):
        return self.request.user.is_authenticated and self.request.user.is_staff

    def handle_no_permission(self):
        messages.error(self.request, "Accès réservé aux employés.")
        return redirect('index')


class PostListView(StaffRequiredMixin, ListView):
    """Vue pour lister tous les posts"""
    model = ReseauxSocialPost
    template_name = 'reseaux_social/liste.html'
    context_object_name = 'posts'
    paginate_by = 20

    def get_queryset(self):
        queryset = ReseauxSocialPost.objects.select_related('auteur')

        # Filtres
        statut = self.request.GET.get('statut')
        plateforme = self.request.GET.get('plateforme')
        search = self.request.GET.get('search')

        if statut:
            queryset = queryset.filter(statut=statut)

        if plateforme:
            queryset = queryset.filter(plateforme=plateforme)

        if search:
            queryset = queryset.filter(
                Q(titre__icontains=search) |
                Q(contenu__icontains=search) |
                Q(hashtags__icontains=search)
            )

        return queryset.order_by('-date_creation')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['navbar'] = 'reseaux_social'
        context['title'] = 'Gestion des Posts Réseaux Sociaux'

        # Statistiques
        context['stats'] = {
            'total': ReseauxSocialPost.objects.count(),
            'brouillons': ReseauxSocialPost.objects.filter(statut='brouillon').count(),
            'programmes': ReseauxSocialPost.objects.filter(statut='programme').count(),
            'publies': ReseauxSocialPost.objects.filter(statut='publie').count(),
            'echecs': ReseauxSocialPost.objects.filter(statut='echec').count(),
        }

        # Filtres pour le template
        context['statuts'] = ReseauxSocialPost.STATUT_CHOICES
        context['plateformes'] = ReseauxSocialPost.PLATEFORME_CHOICES
        context['current_filters'] = {
            'statut': self.request.GET.get('statut', ''),
            'plateforme': self.request.GET.get('plateforme', ''),
            'search': self.request.GET.get('search', ''),
        }

        return context


class PostCreateView(StaffRequiredMixin, CreateView):
    """Vue pour créer un nouveau post"""
    model = ReseauxSocialPost
    template_name = 'reseaux_social/create.html'
    fields = [
        'titre', 'contenu', 'hashtags', 'plateforme', 'type_contenu',
        'image_prompt', 'lien_externe', 'date_programmee'
    ]

    def form_valid(self, form):
        form.instance.auteur = self.request.user

        # Générer l'image si un prompt est fourni
        if form.instance.image_prompt:
            try:
                image_service = PollinationsImageService()
                image_url = image_service.generate_image(form.instance.image_prompt)
                if image_url:
                    form.instance.image_url = image_url
                    logger.info(f"Image générée avec succès: {image_url}")
            except Exception as e:
                logger.error(f"Erreur lors de la génération d'image: {str(e)}")
                messages.warning(self.request, "Impossible de générer l'image, mais le post a été créé.")

        # Sauvegarder le post
        response = super().form_valid(form)

        # Publier immédiatement si demandé
        if self.request.POST.get('publish_immediately'):
            try:
                if self.object.peut_etre_publie:
                    publisher = SocialMediaPublisher()
                    results = publisher.publish_post(self.object)

                    if any(results.values()):
                        self.object.marquer_comme_publie(results)
                        messages.success(self.request, f"Post '{self.object.titre}' créé et publié avec succès!")
                    else:
                        messages.warning(self.request, f"Post '{self.object.titre}' créé mais échec de la publication.")
                else:
                    messages.warning(self.request, f"Post '{self.object.titre}' créé mais ne peut pas être publié.")
            except Exception as e:
                logger.error(f"Erreur lors de la publication: {str(e)}")
                messages.error(self.request, f"Post créé mais erreur lors de la publication: {str(e)}")
        else:
            messages.success(self.request, f"Post '{self.object.titre}' créé avec succès.")

        return response

    def get_success_url(self):
        return reverse('reseaux_social:detail', kwargs={'pk': self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['navbar'] = 'reseaux_social'
        context['title'] = 'Créer un Post'
        context['templates'] = PostTemplate.objects.filter(actif=True)
        return context


class PostDetailView(StaffRequiredMixin, DetailView):
    """Vue pour afficher les détails d'un post"""
    model = ReseauxSocialPost
    template_name = 'reseaux_social/detail.html'
    context_object_name = 'post'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['navbar'] = 'reseaux_social'
        context['title'] = f'Post: {self.object.titre}'
        return context


class PostUpdateView(StaffRequiredMixin, UpdateView):
    """Vue pour modifier un post"""
    model = ReseauxSocialPost
    template_name = 'reseaux_social/edit.html'
    fields = [
        'titre', 'contenu', 'hashtags', 'plateforme', 'type_contenu',
        'image_prompt', 'lien_externe', 'date_programmee', 'statut'
    ]

    def form_valid(self, form):
        # Régénérer l'image si le prompt a changé
        if form.instance.image_prompt and form.instance.image_prompt != form.initial.get('image_prompt'):
            image_service = PollinationsImageService()
            image_url = image_service.generate_image(form.instance.image_prompt)
            if image_url:
                form.instance.image_url = image_url

        messages.success(self.request, f"Post '{form.instance.titre}' modifié avec succès.")
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('reseaux_social:detail', kwargs={'pk': self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['navbar'] = 'reseaux_social'
        context['title'] = f'Modifier: {self.object.titre}'
        return context


class PostDeleteView(StaffRequiredMixin, DeleteView):
    """Vue pour supprimer un post"""
    model = ReseauxSocialPost
    template_name = 'reseaux_social/delete.html'
    success_url = reverse_lazy('reseaux_social:liste')

    def delete(self, request, *args, **kwargs):
        post = self.get_object()

        # Vérifier si on doit aussi supprimer sur Facebook
        delete_facebook = request.POST.get('delete_facebook') == 'on'

        facebook_results = []

        # Si le post a un ID Facebook et que l'utilisateur a coché la case
        if delete_facebook and post.post_id_facebook:
            # Supprimer sur Facebook
            facebook_result = SimpleFacebookService.delete_facebook_post(post.post_id_facebook)
            facebook_results.append(facebook_result)

            if facebook_result['success']:
                messages.success(request, f"Post supprimé sur Facebook: {post.post_id_facebook}")
            else:
                messages.warning(request, f"Erreur suppression Facebook: {facebook_result['error']}")
        elif post.post_id_facebook and not delete_facebook:
            # Avertir que le post reste sur Facebook
            messages.info(request, f"Le post reste publié sur Facebook (ID: {post.post_id_facebook})")

        # Message de succès pour la suppression locale
        if facebook_results and any(r['success'] for r in facebook_results):
            messages.success(request, f"Post '{post.titre}' supprimé de MeetVoice et Facebook.")
        else:
            messages.success(request, f"Post '{post.titre}' supprimé de MeetVoice.")

        return super().delete(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['navbar'] = 'reseaux_social'
        context['title'] = f'Supprimer: {self.object.titre}'
        return context


class PublishPostView(StaffRequiredMixin, View):
    """Vue pour publier un post"""

    def post(self, request, pk):
        post = get_object_or_404(ReseauxSocialPost, pk=pk)

        if not post.peut_etre_publie:
            messages.error(request, "Ce post ne peut pas être publié.")
            return redirect('reseaux_social:detail', pk=pk)

        try:
            publisher = SocialMediaPublisher()
            results = publisher.publish_post(post)

            if any(results.values()):
                post.marquer_comme_publie(results)
                messages.success(request, f"Post '{post.titre}' publié avec succès!")
            else:
                post.marquer_comme_echec("Aucune plateforme n'a pu publier le post")
                messages.error(request, "Échec de la publication sur toutes les plateformes.")

        except Exception as e:
            post.marquer_comme_echec(str(e))
            messages.error(request, f"Erreur lors de la publication: {str(e)}")

        return redirect('reseaux_social:detail', pk=pk)


class PreviewPostView(StaffRequiredMixin, DetailView):
    """Vue pour prévisualiser un post"""
    model = ReseauxSocialPost
    template_name = 'reseaux_social/preview.html'
    context_object_name = 'post'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['navbar'] = 'reseaux_social'
        context['title'] = f'Prévisualisation: {self.object.titre}'
        return context


class DuplicatePostView(StaffRequiredMixin, View):
    """Vue pour dupliquer un post"""

    def post(self, request, pk):
        original_post = get_object_or_404(ReseauxSocialPost, pk=pk)

        # Créer une copie
        new_post = ReseauxSocialPost.objects.create(
            titre=f"Copie de {original_post.titre}",
            contenu=original_post.contenu,
            hashtags=original_post.hashtags,
            plateforme=original_post.plateforme,
            type_contenu=original_post.type_contenu,
            image_url=original_post.image_url,
            image_prompt=original_post.image_prompt,
            lien_externe=original_post.lien_externe,
            auteur=request.user,
            statut='brouillon'
        )

        messages.success(request, f"Post dupliqué: '{new_post.titre}'")
        return redirect('reseaux_social:edit', pk=new_post.pk)


# Vues pour les templates
class TemplateListView(StaffRequiredMixin, ListView):
    """Vue pour lister les templates"""
    model = PostTemplate
    template_name = 'reseaux_social/templates.html'
    context_object_name = 'templates'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['navbar'] = 'reseaux_social'
        context['title'] = 'Templates de Posts'
        return context


class TemplateCreateView(StaffRequiredMixin, CreateView):
    """Vue pour créer un template"""
    model = PostTemplate
    template_name = 'reseaux_social/template_form.html'
    fields = ['nom', 'description', 'contenu_template', 'hashtags_defaut', 'plateforme_recommandee']
    success_url = reverse_lazy('reseaux_social:templates')

    def form_valid(self, form):
        messages.success(self.request, f"Template '{form.instance.nom}' créé avec succès.")
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['navbar'] = 'reseaux_social'
        context['title'] = 'Créer un Template'
        return context


class TemplateUpdateView(StaffRequiredMixin, UpdateView):
    """Vue pour modifier un template"""
    model = PostTemplate
    template_name = 'reseaux_social/template_form.html'
    fields = ['nom', 'description', 'contenu_template', 'hashtags_defaut', 'plateforme_recommandee', 'actif']
    success_url = reverse_lazy('reseaux_social:templates')

    def form_valid(self, form):
        messages.success(self.request, f"Template '{form.instance.nom}' modifié avec succès.")
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['navbar'] = 'reseaux_social'
        context['title'] = f'Modifier: {self.object.nom}'
        return context


class TemplateDeleteView(StaffRequiredMixin, DeleteView):
    """Vue pour supprimer un template"""
    model = PostTemplate
    template_name = 'reseaux_social/template_delete.html'
    success_url = reverse_lazy('reseaux_social:templates')

    def delete(self, request, *args, **kwargs):
        template = self.get_object()
        messages.success(request, f"Template '{template.nom}' supprimé avec succès.")
        return super().delete(request, *args, **kwargs)


class UseTemplateView(StaffRequiredMixin, View):
    """Vue pour utiliser un template"""

    def get(self, request, pk):
        template = get_object_or_404(PostTemplate, pk=pk)

        # Créer un nouveau post basé sur le template
        new_post = ReseauxSocialPost.objects.create(
            titre=f"Post basé sur {template.nom}",
            contenu=template.contenu_template,
            hashtags=template.hashtags_defaut,
            plateforme=template.plateforme_recommandee,
            auteur=request.user,
            statut='brouillon'
        )

        messages.success(request, f"Nouveau post créé à partir du template '{template.nom}'")
        return redirect('reseaux_social:edit', pk=new_post.pk)


# Vues API
class GenerateImageAPIView(APIView):
    """API pour générer une image via Pollinations.ai"""

    def post(self, request):
        if not request.user.is_staff:
            return Response({'error': 'Accès non autorisé'}, status=status.HTTP_403_FORBIDDEN)

        prompt = request.data.get('prompt')
        if not prompt:
            return Response({'error': 'Prompt requis'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            image_service = PollinationsImageService()

            # Générer et sauvegarder l'image localement
            result = image_service.generate_and_save_image(prompt, "reseaux_social_api")

            if result['url'] or result['file_path']:
                response_data = {
                    'success': True,
                    'prompt': prompt,
                    'external_url': result['url'],
                    'local_file': result['file_path']
                }

                # URL d'affichage (priorité au fichier local)
                if result['file_path']:
                    response_data['image_url'] = f"/media/{result['file_path']}"
                else:
                    response_data['image_url'] = result['url']

                return Response(response_data)
            else:
                return Response({
                    'success': False,
                    'error': 'Impossible de générer l\'image'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class GenerateContentAPIView(APIView):
    """API pour générer du contenu via Gemini"""

    def post(self, request):
        if not request.user.is_staff:
            return Response({'error': 'Accès non autorisé'}, status=status.HTTP_403_FORBIDDEN)

        theme = request.data.get('theme')
        plateforme = request.data.get('plateforme', 'all')
        tone = request.data.get('tone', 'professionnel')

        if not theme:
            return Response({'error': 'Thème requis'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            gemini_service = GeminiContentService()

            # Générer le contenu
            content = gemini_service.generate_post_content(theme, plateforme, tone)

            # Générer les hashtags
            hashtags = gemini_service.generate_hashtags(content or theme, plateforme)

            if content:
                return Response({
                    'success': True,
                    'content': content,
                    'hashtags': ' '.join(hashtags),
                    'theme': theme,
                    'plateforme': plateforme
                })
            else:
                return Response({
                    'success': False,
                    'error': 'Impossible de générer le contenu'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PublishPostAPIView(APIView):
    """API pour publier un post"""

    def post(self, request):
        if not request.user.is_staff:
            return Response({'error': 'Accès non autorisé'}, status=status.HTTP_403_FORBIDDEN)

        post_id = request.data.get('post_id')
        if not post_id:
            return Response({'error': 'ID du post requis'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            post = ReseauxSocialPost.objects.get(pk=post_id)

            if not post.peut_etre_publie:
                return Response({
                    'success': False,
                    'error': 'Ce post ne peut pas être publié'
                }, status=status.HTTP_400_BAD_REQUEST)

            publisher = SocialMediaPublisher()
            results = publisher.publish_post(post)

            if any(results.values()):
                post.marquer_comme_publie(results)
                return Response({
                    'success': True,
                    'message': 'Post publié avec succès',
                    'results': results
                })
            else:
                post.marquer_comme_echec("Aucune plateforme n'a pu publier le post")
                return Response({
                    'success': False,
                    'error': 'Échec de la publication sur toutes les plateformes'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except ReseauxSocialPost.DoesNotExist:
            return Response({'error': 'Post non trouvé'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class GetStatsAPIView(APIView):
    """API pour récupérer les statistiques"""

    def get(self, request):
        if not request.user.is_staff:
            return Response({'error': 'Accès non autorisé'}, status=status.HTTP_403_FORBIDDEN)

        try:
            # Statistiques générales
            stats = {
                'total_posts': ReseauxSocialPost.objects.count(),
                'posts_par_statut': {},
                'posts_par_plateforme': {},
                'posts_recents': ReseauxSocialPost.objects.filter(
                    date_creation__gte=timezone.now() - timezone.timedelta(days=7)
                ).count(),
                'posts_programmes': ReseauxSocialPost.objects.filter(
                    statut='programme',
                    date_programmee__gte=timezone.now()
                ).count()
            }

            # Posts par statut
            for statut, label in ReseauxSocialPost.STATUT_CHOICES:
                count = ReseauxSocialPost.objects.filter(statut=statut).count()
                stats['posts_par_statut'][statut] = {
                    'count': count,
                    'label': label
                }

            # Posts par plateforme
            for plateforme, label in ReseauxSocialPost.PLATEFORME_CHOICES:
                count = ReseauxSocialPost.objects.filter(plateforme=plateforme).count()
                stats['posts_par_plateforme'][plateforme] = {
                    'count': count,
                    'label': label
                }

            return Response({
                'success': True,
                'stats': stats
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Vues pour les statistiques et le calendrier
class StatsView(StaffRequiredMixin, TemplateView):
    """Vue pour afficher les statistiques"""
    template_name = 'reseaux_social/stats.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['navbar'] = 'reseaux_social'
        context['title'] = 'Statistiques des Réseaux Sociaux'

        # Statistiques détaillées
        context['stats'] = {
            'total_posts': ReseauxSocialPost.objects.count(),
            'posts_publies': ReseauxSocialPost.objects.filter(statut='publie').count(),
            'posts_programmes': ReseauxSocialPost.objects.filter(statut='programme').count(),
            'posts_brouillons': ReseauxSocialPost.objects.filter(statut='brouillon').count(),
            'posts_echecs': ReseauxSocialPost.objects.filter(statut='echec').count(),
        }

        # Posts récents
        context['posts_recents'] = ReseauxSocialPost.objects.select_related('auteur').order_by('-date_creation')[:10]

        return context


class CalendarView(StaffRequiredMixin, TemplateView):
    """Vue pour afficher le calendrier des publications"""
    template_name = 'reseaux_social/calendar.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['navbar'] = 'reseaux_social'
        context['title'] = 'Calendrier des Publications'

        # Posts programmés
        context['posts_programmes'] = ReseauxSocialPost.objects.filter(
            statut='programme',
            date_programmee__isnull=False
        ).order_by('date_programmee')

        return context


class AutoPublishAPIView(APIView):
    """API pour publier automatiquement sur Facebook et Instagram"""

    def post(self, request, post_id):
        if not request.user.is_staff:
            return Response({'error': 'Accès non autorisé'}, status=status.HTTP_403_FORBIDDEN)

        try:
            post = get_object_or_404(ReseauxSocialPost, id=post_id)

            # Vérifier que le post a du contenu
            if not post.contenu:
                return Response({
                    'success': False,
                    'error': 'Le post doit avoir du contenu'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Initialiser le service de publication
            facebook_service = SimpleFacebookService()

            results = {'successes': [], 'errors': []}

            # Préparer le contenu et l'image
            content = post.contenu_complet
            image_url = None

            # Utiliser l'image externe si disponible (plus fiable que les images locales)
            if post.image_url and post.image_url.startswith('http'):
                image_url = post.image_url
            elif post.image_file:
                # Construire l'URL complète pour l'image locale
                request_host = request.get_host()
                if post.image_file.url.startswith('http'):
                    image_url = post.image_file.url
                else:
                    image_url = f"http://{request_host}{post.image_file.url}"
            else:
                image_url = None

            logger.info(f"Publication automatique du post #{post.id}")
            logger.info(f"Contenu: {content[:100]}...")
            logger.info(f"Image: {image_url}")

            # Publier sur Facebook si la plateforme le permet
            if post.plateforme in ['facebook', 'all']:
                try:
                    facebook_id = facebook_service.publish_to_facebook(
                        message=content,
                        image_url=image_url
                    )

                    if facebook_id:
                        # Sauvegarder l'ID Facebook dans le post
                        post.post_id_facebook = facebook_id
                        post.save()

                        results['successes'].append({
                            'platform': 'facebook',
                            'post_id': facebook_id,
                            'message': 'Publié avec succès sur Facebook'
                        })
                        logger.info(f"Post publié sur Facebook: {facebook_id}")
                    else:
                        results['errors'].append({
                            'platform': 'facebook',
                            'error': 'Échec de publication sur Facebook (vérifiez les permissions du token)'
                        })

                except Exception as e:
                    results['errors'].append({
                        'platform': 'facebook',
                        'error': f'Erreur Facebook: {str(e)}'
                    })

            # Instagram nécessite une page Facebook Business (non disponible avec token personnel)
            if post.plateforme in ['instagram', 'all']:
                results['errors'].append({
                    'platform': 'instagram',
                    'error': 'Instagram nécessite une page Facebook Business et un compte Instagram Business lié'
                })

            # Mettre à jour le statut du post si au moins une publication a réussi
            if results['successes']:
                post.statut = 'publie'
                post.date_publication = timezone.now()
                post.save()

            # Préparer la réponse
            total_successes = len(results['successes'])
            total_errors = len(results['errors'])

            return Response({
                'success': total_successes > 0,
                'post_id': post.id,
                'results': results,
                'summary': {
                    'successes': total_successes,
                    'errors': total_errors,
                    'total_platforms': total_successes + total_errors
                },
                'message': f'Publication terminée: {total_successes} succès, {total_errors} erreurs'
            })

        except Exception as e:
            logger.error(f"Erreur lors de la publication automatique: {str(e)}")
            return Response({
                'success': False,
                'error': f'Erreur: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DeleteFacebookPostAPIView(APIView):
    """API pour supprimer un post sur Facebook"""

    def delete(self, request, post_id):
        if not request.user.is_staff:
            return Response({'error': 'Accès non autorisé'}, status=status.HTTP_403_FORBIDDEN)

        try:
            post = get_object_or_404(ReseauxSocialPost, id=post_id)

            if not post.post_id_facebook:
                return Response({
                    'success': False,
                    'error': 'Ce post n\'a pas été publié sur Facebook'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Supprimer sur Facebook
            result = SimpleFacebookService.delete_facebook_post(post.post_id_facebook)

            if result['success']:
                # Effacer l'ID Facebook du post local
                post.post_id_facebook = ''
                post.save()

                logger.info(f"Post Facebook supprimé: {post.post_id_facebook}")

                return Response({
                    'success': True,
                    'message': 'Post supprimé avec succès sur Facebook',
                    'facebook_post_id': post.post_id_facebook
                })
            else:
                return Response({
                    'success': False,
                    'error': result['error']
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la suppression Facebook: {str(e)}")
            return Response({
                'success': False,
                'error': f'Erreur: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
