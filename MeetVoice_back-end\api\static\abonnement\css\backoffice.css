/* CSS pour la gestion des abonnements dans le back-office */

/* Cartes d'abonnements */
.abonnement-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid #e9ecef;
}

.abonnement-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.abonnement-card.popular {
    border-color: #ffc107;
    background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
}

.abonnement-card.inactive {
    opacity: 0.7;
    background-color: #f8f9fa;
}

/* Badges de statut */
.badge-stripe-sync {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.badge-stripe-sync.synced {
    background-color: #28a745;
    color: white;
}

.badge-stripe-sync.not-synced {
    background-color: #dc3545;
    color: white;
}

/* Prix et tarification */
.price-display {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
}

.price-details {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Statistiques */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.stats-card .card-body {
    text-align: center;
    padding: 1.5rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Actions et boutons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-stripe {
    background-color: #635bff;
    border-color: #635bff;
    color: white;
}

.btn-stripe:hover {
    background-color: #5a52e8;
    border-color: #5a52e8;
    color: white;
}

.btn-stripe:focus {
    box-shadow: 0 0 0 0.2rem rgba(99, 91, 255, 0.25);
}

/* Formulaires */
.form-section {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section h5 {
    color: #495057;
    margin-bottom: 1rem;
    font-weight: 600;
}

/* Informations Stripe */
.stripe-info {
    background-color: #f8f9fa;
    border-left: 4px solid #635bff;
    padding: 1rem;
    border-radius: 0.375rem;
    margin-top: 1rem;
}

.stripe-info code {
    background-color: #e9ecef;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

/* Fonctionnalités */
.features-list {
    list-style: none;
    padding: 0;
}

.features-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.features-list li:last-child {
    border-bottom: none;
}

.features-list li::before {
    content: "✓";
    color: #28a745;
    font-weight: bold;
    margin-right: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .price-display {
        font-size: 1.25rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Loading states */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Messages d'alerte */
.alert-stripe {
    border-left: 4px solid #635bff;
    background-color: #f8f9ff;
    border-color: #e6e3ff;
}

/* Tables responsive */
.table-responsive {
    border-radius: 0.375rem;
    box-shadow: 0 0 0 1px rgba(0,0,0,0.05);
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

/* Modals */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* Tooltips personnalisés */
.tooltip-stripe {
    background-color: #635bff;
}

.tooltip-stripe .tooltip-arrow {
    border-top-color: #635bff;
}

/* États des abonnements */
.status-active {
    color: #28a745;
    font-weight: 600;
}

.status-inactive {
    color: #6c757d;
    font-weight: 600;
}

.status-popular {
    color: #ffc107;
    font-weight: 600;
}

/* Indicateurs visuels */
.indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.indicator.active {
    background-color: #28a745;
}

.indicator.inactive {
    background-color: #6c757d;
}

.indicator.synced {
    background-color: #17a2b8;
}

.indicator.not-synced {
    background-color: #dc3545;
}
