/**
 * JavaScript pour la page liste des abonnements
 * Interface hybride client/employé
 */

// Variables globales
let deleteAbonnementId = null;
let stripe = null;

// Fonction pour récupérer le token CSRF
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Initialisation Stripe
function initializeStripe(publishableKey) {
    if (publishableKey && publishableKey.trim() !== '') {
        try {
            stripe = Stripe(publishableKey);
            console.log('Stripe initialisé avec succès');
        } catch (error) {
            console.error('Erreur initialisation Stripe:', error);
            stripe = null;
        }
    } else {
        console.warn('Clé publique Stripe non configurée');
        stripe = null;
    }
}

// Toggle FAQ (pour les clients)
function toggleFaq(element) {
    if (!element) return;
    
    const faqBody = element.nextElementSibling;
    const icon = element.querySelector('i');
    
    if (!faqBody || !icon) return;
    
    if (faqBody.style.display === 'block') {
        faqBody.style.display = 'none';
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
    } else {
        faqBody.style.display = 'block';
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
    }
}

// Sélection d'un plan (pour les clients)
async function selectPlan(abonnementId, nom, prix) {
    console.log('selectPlan appelée:', abonnementId, nom, prix);
    
    // Vérifier si Stripe est configuré
    if (!stripe) {
        alert('Stripe n\'est pas configuré. Veuillez contacter l\'administrateur.');
        return;
    }
    
    const button = document.getElementById(`btn-${abonnementId}`);
    const spinner = document.getElementById(`spinner-${abonnementId}`);
    
    if (!button) {
        console.error('Bouton non trouvé:', `btn-${abonnementId}`);
        return;
    }
    
    // Afficher le spinner
    button.disabled = true;
    if (spinner) {
        spinner.style.display = 'inline-block';
    }
    button.innerHTML = `<span class="loading-spinner"></span>Traitement...`;
    
    try {
        // Créer une session Stripe Checkout
        const response = await fetch('/abonnement/checkout/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                abonnement_id: abonnementId
            })
        });
        
        const data = await response.json();
        
        if (data.checkout_url) {
            // Rediriger vers Stripe Checkout
            window.location.href = data.checkout_url;
        } else {
            throw new Error(data.error || 'Erreur lors de la création de la session de paiement');
        }
        
    } catch (error) {
        console.error('Erreur:', error);
        alert('Une erreur est survenue: ' + error.message);
        
        // Restaurer le bouton
        button.disabled = false;
        if (spinner) {
            spinner.style.display = 'none';
        }
        button.innerHTML = 'Choisir ce plan';
    }
}

// ==========================================
// FONCTIONS ADMINISTRATIVES POUR EMPLOYÉS
// ==========================================

// Ouvrir le modal de création
function openCreateModal() {
    console.log('openCreateModal appelée');
    const modalElement = document.getElementById('createAbonnementModal');
    if (modalElement) {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    } else {
        console.error('Modal createAbonnementModal non trouvé');
    }
}

// Modifier un abonnement
function editAbonnement(abonnementId) {
    console.log('editAbonnement appelée:', abonnementId);
    window.location.href = `/backoffice/abonnements/${abonnementId}/edit/`;
}

// Supprimer un abonnement
function deleteAbonnement(abonnementId, nom) {
    console.log('deleteAbonnement appelée:', abonnementId, nom);
    deleteAbonnementId = abonnementId;
    const nameElement = document.getElementById('deleteAbonnementName');
    const modalElement = document.getElementById('deleteAbonnementModal');
    
    if (nameElement && modalElement) {
        nameElement.textContent = nom;
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    } else {
        console.error('Éléments modal de suppression non trouvés');
    }
}

function confirmDeleteAbonnement() {
    console.log('confirmDeleteAbonnement appelée:', deleteAbonnementId);
    if (!deleteAbonnementId) return;
    
    fetch(`/backoffice/abonnements/${deleteAbonnementId}/delete/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => {
        if (response.ok) {
            location.reload();
        } else {
            alert('Erreur lors de la suppression');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur technique lors de la suppression');
    });
    
    const modalElement = document.getElementById('deleteAbonnementModal');
    if (modalElement) {
        const modal = bootstrap.Modal.getInstance(modalElement);
        if (modal) {
            modal.hide();
        }
    }
    deleteAbonnementId = null;
}

// Synchroniser avec Stripe
function syncStripe(abonnementId) {
    console.log('syncStripe appelée:', abonnementId);
    if (!confirm('Synchroniser ce plan avec Stripe ?')) return;
    
    fetch(`/backoffice/api/abonnements/${abonnementId}/sync-stripe/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Plan synchronisé avec Stripe');
            location.reload();
        } else {
            alert(data.message || 'Erreur lors de la synchronisation');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur technique lors de la synchronisation');
    });
}

// Animation au scroll pour les clients
function initializeAnimations() {
    const cards = document.querySelectorAll('.pricing-card');
    
    if (cards.length === 0) return;
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    });
    
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
}

// Initialisation du formulaire de création
function initializeCreateForm() {
    const createForm = document.getElementById('createAbonnementForm');
    if (createForm) {
        createForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            
            // Convertir les checkboxes
            data.is_popular = document.getElementById('is_popular')?.checked || false;
            data.is_active = document.getElementById('is_active')?.checked || false;
            data.create_stripe = document.getElementById('create_stripe')?.checked || false;
            
            try {
                const response = await fetch('/backoffice/abonnements/create/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: new URLSearchParams(data)
                });
                
                if (response.ok) {
                    location.reload();
                } else {
                    alert('Erreur lors de la création du plan');
                }
            } catch (error) {
                console.error('Erreur:', error);
                alert('Erreur technique lors de la création');
            }
        });
    }
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM chargé, initialisation...');
    
    // Initialiser les animations
    initializeAnimations();
    
    // Initialiser le formulaire de création (si présent)
    initializeCreateForm();
    
    console.log('Initialisation terminée');
});

// Exposer les fonctions globalement pour les onclick
window.toggleFaq = toggleFaq;
window.selectPlan = selectPlan;
window.openCreateModal = openCreateModal;
window.editAbonnement = editAbonnement;
window.deleteAbonnement = deleteAbonnement;
window.confirmDeleteAbonnement = confirmDeleteAbonnement;
window.syncStripe = syncStripe;
window.initializeStripe = initializeStripe;
