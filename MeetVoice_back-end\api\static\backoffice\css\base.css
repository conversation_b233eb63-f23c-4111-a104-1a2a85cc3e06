/* ==========================================
   STYLES POUR LE BACK-OFFICE MEET VOICE
   ========================================== */

/* Variables CSS - Couleurs cohérentes avec la navbar */
:root {
    --admin-primary: #2a1d34;
    --admin-secondary: #3d2a4a;
    --admin-accent: #667eea;
    --admin-success: #27ae60;
    --admin-warning: #f39c12;
    --admin-danger: #e74c3c;
    --admin-light: #ecf0f1;
    --admin-dark: #2a1d34;
    --sidebar-width: 250px;
}

/* Layout de base */
.backoffice-container {
    display: flex;
    min-height: 100vh;
    background: #f8f9fa;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: var(--admin-primary);
    color: white;
    position: fixed;
    height: 100vh;
    top: 70px; /* Ajout pour éviter le chevauchement avec la navbar */
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-1px);
}

.sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-left-color: var(--admin-accent);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.sidebar .nav-link i {
    width: 20px;
    margin-right: 0.5rem;
    text-align: center;
}

/* Contenu principal */
.main-content {
    margin-left: var(--sidebar-width);
    margin-top: 70px; /* Ajout pour éviter le chevauchement avec la navbar */
    flex: 1;
    min-height: calc(100vh - 70px);
    background: #f8f9fa;
}

/* Responsivité */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        width: 100%;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    :root {
        --sidebar-width: 200px;
    }
}

/* Header de page */
.page-header {
    background: white;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 2rem;
}

.page-header h1 {
    margin-bottom: 0.5rem;
    color: var(--admin-dark);
}

.breadcrumb-nav {
    margin-bottom: 1rem;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: var(--admin-accent);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #6c757d;
}

/* Cards et composants */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 0.5rem;
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Boutons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--admin-accent);
    border-color: var(--admin-accent);
}

.btn-primary:hover {
    background: #2980b9;
    border-color: #2980b9;
}

.btn-success {
    background: var(--admin-success);
    border-color: var(--admin-success);
}

.btn-warning {
    background: var(--admin-warning);
    border-color: var(--admin-warning);
}

.btn-danger {
    background: var(--admin-danger);
    border-color: var(--admin-danger);
}

/* Tables */
.table {
    background: white;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--admin-dark);
    background: #f8f9fa;
}

.table-hover tbody tr:hover {
    background: rgba(52, 152, 219, 0.05);
}

/* Formulaires */
.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: var(--admin-accent);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Badges et labels */
.badge {
    font-weight: 500;
    border-radius: 0.375rem;
}

/* Alertes */
.alert {
    border: none;
    border-radius: 0.5rem;
    border-left: 4px solid;
}

.alert-success {
    background: rgba(39, 174, 96, 0.1);
    border-left-color: var(--admin-success);
    color: #155724;
}

.alert-warning {
    background: rgba(243, 156, 18, 0.1);
    border-left-color: var(--admin-warning);
    color: #856404;
}

.alert-danger {
    background: rgba(231, 76, 60, 0.1);
    border-left-color: var(--admin-danger);
    color: #721c24;
}

.alert-info {
    background: rgba(52, 152, 219, 0.1);
    border-left-color: var(--admin-accent);
    color: #0c5460;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .page-header {
        padding: 1rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Utilitaires */
.text-admin-primary { color: var(--admin-primary) !important; }
.text-admin-accent { color: var(--admin-accent) !important; }
.bg-admin-light { background-color: var(--admin-light) !important; }

/* Sections de formulaire */
.form-section {
    background: white;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-section-header {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.5rem 0.5rem 0 0;
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--admin-dark);
}

.form-section-body {
    padding: 1.5rem;
}

/* Statistiques */
.stats-card {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}
