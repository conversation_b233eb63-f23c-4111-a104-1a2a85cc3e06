/* 📧 STYLES NEWSLETTER MEETVOICE */

/* Variables CSS */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Layout général */
.newsletter-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Dashboard */
.newsletter-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.dashboard-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    border-left: 4px solid var(--primary-color);
}

.dashboard-card h3 {
    margin: 0 0 15px 0;
    color: var(--dark-color);
    font-size: 1.2rem;
}

.dashboard-stat {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.dashboard-label {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

/* Campagnes */
.campaign-list {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.campaign-item {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: between;
    align-items: center;
}

.campaign-item:last-child {
    border-bottom: none;
}

.campaign-info h4 {
    margin: 0 0 5px 0;
    color: var(--dark-color);
}

.campaign-meta {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.campaign-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.status-draft {
    background: #fff3cd;
    color: #856404;
}

.status-sent {
    background: #d4edda;
    color: #155724;
}

.status-sending {
    background: #cce7ff;
    color: #004085;
}

.status-scheduled {
    background: #e2e3e5;
    color: #383d41;
}

/* Formulaires */
.newsletter-form {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: var(--dark-color);
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.invalid-feedback {
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: 5px;
}

/* Boutons */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-sm {
    padding: 5px 15px;
    font-size: 0.875rem;
}

/* Templates */
.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.template-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.template-card:hover {
    transform: translateY(-5px);
}

.template-preview {
    height: 200px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--secondary-color);
}

.template-info {
    padding: 15px;
}

.template-info h4 {
    margin: 0 0 10px 0;
    color: var(--dark-color);
}

.template-meta {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 15px;
}

/* Statistiques */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.stat-label {
    color: var(--secondary-color);
    font-size: 1rem;
}

.stat-opens .stat-number { color: var(--info-color); }
.stat-clicks .stat-number { color: var(--success-color); }
.stat-bounces .stat-number { color: var(--danger-color); }
.stat-unsubscribes .stat-number { color: var(--warning-color); }

/* Alertes */
.alert {
    padding: 15px 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border-left: 4px solid;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border-left-color: var(--success-color);
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
    border-left-color: var(--danger-color);
}

.alert-warning {
    background: #fff3cd;
    color: #856404;
    border-left-color: var(--warning-color);
}

.alert-info {
    background: #cce7ff;
    color: #004085;
    border-left-color: var(--info-color);
}

/* Responsive */
@media (max-width: 768px) {
    .newsletter-container {
        padding: 10px;
    }
    
    .newsletter-dashboard {
        grid-template-columns: 1fr;
    }
    
    .campaign-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .template-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
