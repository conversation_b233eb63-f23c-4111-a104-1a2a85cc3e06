/* ==========================================
   STYLES POUR LA GESTION DES RÉSEAUX SOCIAUX
   ========================================== */

/* Variables CSS - Couleurs cohérentes avec la navbar */
:root {
    --admin-primary: #2a1d34;
    --admin-secondary: #3d2a4a;
    --admin-accent: #667eea;
    --admin-success: #27ae60;
    --admin-warning: #f39c12;
    --admin-danger: #e74c3c;
    --admin-info: #3498db;
    --admin-light: #ecf0f1;
    --admin-dark: #2a1d34;
    --sidebar-width: 250px;
    --border-radius: 0.5rem;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
}

/* Layout de base */
.admin-interface {
    display: flex;
    min-height: 100vh;
    background: #f8f9fa;
}

/* Sidebar */
.admin-sidebar {
    width: var(--sidebar-width);
    background: var(--admin-primary);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.admin-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.admin-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-1px);
}

.admin-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-left-color: var(--admin-accent);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.admin-nav .nav-link i {
    width: 20px;
    margin-right: 0.5rem;
    text-align: center;
}

/* Contenu principal */
.admin-content {
    margin-left: var(--sidebar-width);
    flex: 1;
    min-height: 100vh;
    background: #f8f9fa;
}

/* Header de page */
.admin-header {
    background: white;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 2rem;
}

.admin-header h1 {
    margin-bottom: 0.5rem;
    color: var(--admin-dark);
}

/* Cartes de statistiques */
.stats-card {
    background: var(--admin-primary);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.2s ease;
    margin-bottom: 1rem;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.stats-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.9;
}

/* Cartes de posts */
.post-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.post-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-hover);
}

.post-header {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
}

.post-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--admin-dark);
}

.post-meta {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.post-body {
    padding: 1rem;
    flex-grow: 1;
}

.post-image {
    margin-bottom: 1rem;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.post-image img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.post-content p {
    margin-bottom: 0.5rem;
    color: #6c757d;
    line-height: 1.5;
}

.post-hashtags {
    color: var(--admin-accent);
    font-size: 0.9rem;
    font-weight: 500;
}

.post-footer {
    padding: 1rem;
    border-top: 1px solid #f0f0f0;
    background: #f8f9fa;
}

/* Badges de statut */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.badge-brouillon {
    background-color: #6c757d;
    color: white;
}

.badge-programme {
    background-color: var(--admin-warning);
    color: white;
}

.badge-publie {
    background-color: var(--admin-success);
    color: white;
}

.badge-echec {
    background-color: var(--admin-danger);
    color: white;
}

.badge-archive {
    background-color: #495057;
    color: white;
}

.badge-plateforme {
    background-color: var(--admin-info);
    color: white;
}

/* Formulaires */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: var(--admin-accent);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Boutons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--admin-accent);
    border-color: var(--admin-accent);
}

.btn-primary:hover {
    background: #5a6fd8;
    border-color: #5a6fd8;
    transform: translateY(-1px);
}

.btn-success {
    background: var(--admin-success);
    border-color: var(--admin-success);
}

.btn-warning {
    background: var(--admin-warning);
    border-color: var(--admin-warning);
}

.btn-danger {
    background: var(--admin-danger);
    border-color: var(--admin-danger);
}

/* Cards */
.card {
    border: none;
    box-shadow: var(--shadow);
    border-radius: var(--border-radius);
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Alertes */
.alert {
    border: none;
    border-radius: var(--border-radius);
    border-left: 4px solid;
}

.alert-success {
    background: rgba(39, 174, 96, 0.1);
    border-left-color: var(--admin-success);
    color: #155724;
}

.alert-warning {
    background: rgba(243, 156, 18, 0.1);
    border-left-color: var(--admin-warning);
    color: #856404;
}

.alert-danger {
    background: rgba(231, 76, 60, 0.1);
    border-left-color: var(--admin-danger);
    color: #721c24;
}

.alert-info {
    background: rgba(52, 152, 219, 0.1);
    border-left-color: var(--admin-info);
    color: #0c5460;
}

/* Pagination */
.pagination .page-link {
    color: var(--admin-accent);
    border-color: #dee2e6;
    border-radius: var(--border-radius);
    margin: 0 0.125rem;
}

.pagination .page-item.active .page-link {
    background-color: var(--admin-accent);
    border-color: var(--admin-accent);
}

.pagination .page-link:hover {
    color: #5a6fd8;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

/* Responsive */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-sidebar.show {
        transform: translateX(0);
    }
    
    .admin-content {
        margin-left: 0;
    }
    
    .admin-header {
        padding: 1rem;
    }
    
    .post-card {
        margin-bottom: 1rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Timeline */
.timeline {
    position: relative;
    padding-left: 1.5rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 1rem;
}

.timeline-marker {
    position: absolute;
    left: -1.5rem;
    top: 0.25rem;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    padding-left: 0.5rem;
}

/* Prévisualisation du contenu */
.post-content-preview {
    background: #f8f9fa;
    border-left: 4px solid var(--admin-accent);
    padding: 1rem;
    border-radius: 0.25rem;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Utilitaires */
.text-admin-primary { color: var(--admin-primary) !important; }
.text-admin-accent { color: var(--admin-accent) !important; }
.bg-admin-light { background-color: var(--admin-light) !important; }
