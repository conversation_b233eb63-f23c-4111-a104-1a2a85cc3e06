/**
 * JavaScript pour la gestion des réseaux sociaux
 * Interface administrative Meet Voice
 */

// Variables globales
let isPublishing = false;

// Fonction pour récupérer le token CSRF
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Publier un post
async function publishPost(postId, buttonElement = null) {
    if (isPublishing) {
        alert('Publication en cours, veuillez patienter...');
        return;
    }

    if (!confirm('Êtes-vous sûr de vouloir publier ce post ?')) {
        return;
    }

    isPublishing = true;

    // Trouver le bouton qui a déclenché l'action
    let button = buttonElement;
    if (!button && typeof event !== 'undefined' && event && event.target) {
        button = event.target.closest('button') || event.target.closest('a');
    }

    let originalText = '';
    if (button) {
        originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Publication...';
        button.classList.add('disabled');
        if (button.tagName === 'BUTTON') {
            button.disabled = true;
        }
    }

    try {
        const response = await fetch('/reseaux-social/api/publish-post/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                post_id: postId
            })
        });

        const data = await response.json();

        if (data.success) {
            alert('Post publié avec succès !');
            location.reload();
        } else {
            alert('Erreur lors de la publication : ' + (data.error || 'Erreur inconnue'));

            // Restaurer le bouton en cas d'erreur
            if (button) {
                button.innerHTML = originalText || '<i class="fas fa-share me-2"></i>Publier';
                button.classList.remove('disabled');
                if (button.tagName === 'BUTTON') {
                    button.disabled = false;
                }
            }
        }

    } catch (error) {
        console.error('Erreur:', error);
        alert('Erreur technique lors de la publication');

        // Restaurer le bouton en cas d'erreur
        if (button) {
            button.innerHTML = originalText || '<i class="fas fa-share me-2"></i>Publier';
            button.classList.remove('disabled');
            if (button.tagName === 'BUTTON') {
                button.disabled = false;
            }
        }
    } finally {
        isPublishing = false;
    }
}

// Générer une image via Pollinations.ai
async function generateImage(prompt, targetElementId) {
    if (!prompt || prompt.trim() === '') {
        alert('Veuillez saisir un prompt pour générer l\'image');
        return;
    }
    
    try {
        // Afficher un indicateur de chargement
        const targetElement = document.getElementById(targetElementId);
        if (targetElement) {
            targetElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Génération en cours...';
        }
        
        const response = await fetch('/reseaux-social/api/generate-image/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                prompt: prompt
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Afficher l'image générée
            if (targetElement) {
                targetElement.innerHTML = `
                    <img src="${data.image_url}" alt="Image générée" class="img-fluid rounded">
                    <p class="mt-2 text-muted small">Prompt: ${data.prompt}</p>
                `;
            }
            
            // Mettre à jour le champ URL si présent
            const urlField = document.getElementById('id_image_url');
            if (urlField) {
                urlField.value = data.image_url;
            }
            
            return data.image_url;
        } else {
            if (targetElement) {
                targetElement.innerHTML = `<div class="alert alert-danger">Erreur: ${data.error}</div>`;
            }
            alert('Erreur lors de la génération : ' + data.error);
            return null;
        }
        
    } catch (error) {
        console.error('Erreur:', error);
        const targetElement = document.getElementById(targetElementId);
        if (targetElement) {
            targetElement.innerHTML = `<div class="alert alert-danger">Erreur technique</div>`;
        }
        alert('Erreur technique lors de la génération');
        return null;
    }
}

// Générer du contenu via Gemini
async function generateContent(theme, plateforme = 'all', tone = 'professionnel') {
    if (!theme || theme.trim() === '') {
        alert('Veuillez saisir un thème pour générer le contenu');
        return;
    }
    
    try {
        const response = await fetch('/reseaux-social/api/generate-content/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                theme: theme,
                plateforme: plateforme,
                tone: tone
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Mettre à jour les champs du formulaire
            const contentField = document.getElementById('id_contenu');
            const hashtagsField = document.getElementById('id_hashtags');
            
            if (contentField) {
                contentField.value = data.content;
            }
            
            if (hashtagsField) {
                hashtagsField.value = data.hashtags;
            }
            
            alert('Contenu généré avec succès !');
            return data;
        } else {
            alert('Erreur lors de la génération : ' + data.error);
            return null;
        }
        
    } catch (error) {
        console.error('Erreur:', error);
        alert('Erreur technique lors de la génération');
        return null;
    }
}

// Prévisualiser un post
function previewPost(postId) {
    const url = `/reseaux-social/${postId}/preview/`;
    window.open(url, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
}

// Dupliquer un post
function duplicatePost(postId) {
    if (confirm('Dupliquer ce post ?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/reseaux-social/${postId}/duplicate/`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrfmiddlewaretoken';
        csrfToken.value = getCookie('csrftoken');
        form.appendChild(csrfToken);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Utiliser un template
function useTemplate(templateId) {
    if (confirm('Créer un nouveau post basé sur ce template ?')) {
        window.location.href = `/reseaux-social/templates/${templateId}/use/`;
    }
}

// Charger les statistiques
async function loadStats() {
    try {
        const response = await fetch('/reseaux-social/api/get-stats/', {
            method: 'GET',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            updateStatsDisplay(data.stats);
        }
        
    } catch (error) {
        console.error('Erreur lors du chargement des statistiques:', error);
    }
}

// Mettre à jour l'affichage des statistiques
function updateStatsDisplay(stats) {
    // Mettre à jour les cartes de statistiques
    const elements = {
        'total-posts': stats.total_posts,
        'posts-publies': stats.posts_par_statut.publie?.count || 0,
        'posts-programmes': stats.posts_par_statut.programme?.count || 0,
        'posts-brouillons': stats.posts_par_statut.brouillon?.count || 0
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Interface réseaux sociaux initialisée');
    
    // Charger les statistiques si on est sur la page principale
    if (window.location.pathname.includes('/reseaux-social/')) {
        loadStats();
    }
    
    // Initialiser les tooltips Bootstrap
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialiser les dropdowns Bootstrap
    const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
    dropdownElementList.map(function (dropdownToggleEl) {
        return new bootstrap.Dropdown(dropdownToggleEl);
    });
    
    // Animation des cartes au scroll
    const cards = document.querySelectorAll('.post-card, .stats-card');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    });
    
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});

// Publier automatiquement sur Facebook/Instagram
async function autoPublishPost(postId, buttonElement = null) {
    if (isPublishing) {
        alert('Publication en cours, veuillez patienter...');
        return;
    }

    if (!confirm('Publier automatiquement ce post sur Facebook et Instagram ?')) {
        return;
    }

    isPublishing = true;

    // Trouver le bouton qui a déclenché l'action
    let button = buttonElement;
    if (!button && typeof event !== 'undefined' && event && event.target) {
        button = event.target.closest('button') || event.target.closest('a');
    }

    let originalText = '';
    if (button) {
        originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Publication en cours...';
        button.classList.add('disabled');
        if (button.tagName === 'BUTTON') {
            button.disabled = true;
        }
    }

    try {
        const response = await fetch(`/reseaux-social/api/auto-publish/${postId}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            }
        });

        const data = await response.json();

        if (data.success) {
            let message = `Publication automatique réussie !\n\n`;
            message += `✅ Succès: ${data.summary.successes}\n`;
            message += `❌ Erreurs: ${data.summary.errors}\n\n`;

            if (data.results.successes.length > 0) {
                message += `Publié sur:\n`;
                data.results.successes.forEach(success => {
                    message += `• ${success.platform.toUpperCase()}: ${success.post_id}\n`;
                });
            }

            if (data.results.errors.length > 0) {
                message += `\nErreurs:\n`;
                data.results.errors.forEach(error => {
                    message += `• ${error.platform.toUpperCase()}: ${error.error}\n`;
                });
            }

            alert(message);
            location.reload();
        } else {
            alert('Erreur lors de la publication automatique : ' + (data.error || 'Erreur inconnue'));

            // Restaurer le bouton en cas d'erreur
            if (button) {
                button.innerHTML = originalText || '<i class="fas fa-rocket me-2"></i>Publier sur Facebook/Instagram';
                button.classList.remove('disabled');
                if (button.tagName === 'BUTTON') {
                    button.disabled = false;
                }
            }
        }

    } catch (error) {
        console.error('Erreur:', error);
        alert('Erreur technique lors de la publication automatique');

        // Restaurer le bouton en cas d'erreur
        if (button) {
            button.innerHTML = originalText || '<i class="fas fa-rocket me-2"></i>Publier sur Facebook/Instagram';
            button.classList.remove('disabled');
            if (button.tagName === 'BUTTON') {
                button.disabled = false;
            }
        }
    } finally {
        isPublishing = false;
    }
}

// Copier le contenu du post dans le presse-papiers
function copyPostContent() {
    // Récupérer le contenu du post
    const contentElement = document.querySelector('.post-content');
    const hashtagsElement = document.querySelector('.post-hashtags');

    let content = '';

    if (contentElement) {
        content += contentElement.textContent.trim();
    }

    if (hashtagsElement) {
        content += '\n\n' + hashtagsElement.textContent.trim();
    }

    if (!content) {
        alert('Aucun contenu à copier');
        return;
    }

    // Copier dans le presse-papiers
    if (navigator.clipboard && window.isSecureContext) {
        // API moderne
        navigator.clipboard.writeText(content).then(() => {
            alert('✅ Contenu copié dans le presse-papiers !\n\nVous pouvez maintenant le coller sur Facebook ou Instagram.');
        }).catch(err => {
            console.error('Erreur copie:', err);
            fallbackCopyTextToClipboard(content);
        });
    } else {
        // Fallback pour les navigateurs plus anciens
        fallbackCopyTextToClipboard(content);
    }
}

// Fonction de fallback pour copier le texte
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Éviter le scroll vers le textarea
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            alert('✅ Contenu copié dans le presse-papiers !\n\nVous pouvez maintenant le coller sur Facebook ou Instagram.');
        } else {
            alert('❌ Impossible de copier automatiquement.\n\nSélectionnez et copiez manuellement le contenu.');
        }
    } catch (err) {
        console.error('Erreur copie fallback:', err);
        alert('❌ Impossible de copier automatiquement.\n\nSélectionnez et copiez manuellement le contenu.');
    }

    document.body.removeChild(textArea);
}

// Supprimer un post sur Facebook uniquement
async function deleteFacebookPost(postId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce post sur Facebook ?\n\nCette action est irréversible.')) {
        return;
    }

    try {
        const response = await fetch(`/reseaux-social/api/delete-facebook/${postId}/`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            }
        });

        const data = await response.json();

        if (data.success) {
            alert('✅ Post supprimé avec succès sur Facebook !');
            location.reload();
        } else {
            alert('❌ Erreur lors de la suppression sur Facebook :\n' + data.error);
        }

    } catch (error) {
        console.error('Erreur:', error);
        alert('❌ Erreur technique lors de la suppression sur Facebook');
    }
}

// Exposer les fonctions globalement pour les onclick
window.publishPost = publishPost;
window.autoPublishPost = autoPublishPost;
window.copyPostContent = copyPostContent;
window.deleteFacebookPost = deleteFacebookPost;
window.generateImage = generateImage;
window.generateContent = generateContent;
window.previewPost = previewPost;
window.duplicatePost = duplicatePost;
window.useTemplate = useTemplate;
window.loadStats = loadStats;
