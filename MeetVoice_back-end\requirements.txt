# Django Core
Django==5.2.3
djangorestframework==3.16.0
djangorestframework-simplejwt==5.5.0

# Django Extensions
django-cors-headers==4.7.0
django-filter==25.1
django-import-export==4.3.8
django-ckeditor==6.7.3
django-js-asset==3.1.2
django-resized==1.0.3

# API Documentation
drf-yasg==1.21.10

# WebSocket Support
channels==4.2.2
channels-redis==4.2.1

# Database & Cache
redis==6.2.0

# Authentication & Security
PyJWT==2.9.0

# Text-to-Speech (TTS)
gtts==2.5.4
pyttsx3==2.91
pydub==0.25.1

# Audio Processing (optional for enhanced TTS)
librosa==0.10.2
soundfile==0.12.1

# AI & Content Generation
google-generativeai==0.8.5
google-ai-generativelanguage==0.6.15
google-api-python-client==2.174.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.2
googleapis-common-protos==1.70.0

# Data Processing
pandas==2.3.0
numpy==2.3.1
plotly==6.2.0

# Image Processing
Pillow==11.2.1

# Payment Processing
stripe==12.2.0

# Environment & Configuration
python-decouple==3.8
python-dotenv==1.1.1

# HTTP Requests
requests==2.32.4
requests-oauthlib==2.0.0

# Utilities
colorama==0.4.6
tqdm==4.67.1
inflection==0.5.1
packaging==25.0

# Testing
pytest==8.4.1
pytest-django==4.11.1
factory-boy==3.3.3
Faker==37.4.0

# Development Tools
diff-match-patch==20241021

# Date & Time
python-dateutil==2.9.0.post0
pytz==2025.2
tzdata==2025.2

# Parsing & Validation
PyYAML==6.0.2
pydantic==2.11.7
pydantic-core==2.33.2
pyparsing==3.2.3

# Syntax Highlighting
Pygments==2.19.2

# Data Export
tablib==3.8.0

# Protocol Buffers & gRPC
protobuf==5.29.5
grpcio==1.73.1
grpcio-status==1.71.0

# Cryptography
rsa==4.9.1
pyasn1==0.6.1
pyasn1-modules==0.4.2

# Other Dependencies
certifi==2025.6.15
charset-normalizer==3.4.2
idna==3.10
urllib3==2.5.0
six==1.17.0
sqlparse==0.5.3
msgpack==1.1.1
cachetools==5.5.2
asgiref==3.8.1
annotated-types==0.7.0
typing-extensions==4.14.0
typing-inspection==0.4.1
uriTemplate==4.2.0
httplib2==0.22.0
oauthlib==3.3.1
proto-plus==1.26.1
iniconfig==2.1.0
pluggy==1.6.0
narwhals==1.44.0
dotenv==0.9.9
